package com.ctsi.aop;

import com.alibaba.fastjson.JSONObject;
import com.ctsi.hndx.annotations.Dict;
import com.ctsi.hndx.result.ResultVO;
import com.ctsi.hndx.utils.ConvertUtils;
import com.ctsi.hndx.utils.StringUtils;
import com.ctsi.ssdc.model.PageResult;
import com.ctsi.system.entity.dto.TSysDictRecordDTO;
import com.ctsi.system.service.ITSysDictRecordService;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.List;

/**
 * @Description: 字典aop类
 * @Author: dangzhenghui
 * @Date: 2019-3-17 21:50
 * @Version: 1.0
 */
@Aspect
@Component
@Slf4j
public class DictAspect {

    @Autowired
    private ITSysDictRecordService tSysDictRecordService;
    @Autowired
    private ObjectMapper objectMapper;

    // 定义切点Pointcut
    @Pointcut("@within(org.springframework.web.bind.annotation.RestController) || @annotation(org.springframework.web.bind.annotation.RestController)")
    public void excudeService() {
    }

    @Around("excudeService()")
    public Object doAround(ProceedingJoinPoint pjp) throws Throwable {
        Object result = pjp.proceed();
        this.parseDictText(result);
        return result;
    }

    /**
     * 本方法针对返回对象为Result 的IPage的分页列表数据进行动态字典注入
     * 字典注入实现 通过对实体类添加注解@dict 来标识需要的字典内容,字典分为单字典code即可 ，table字典 code table text配合使用与原来jeecg的用法相同
     * 示例为SysUser   字段为sex 添加了注解@Dict(dicCode = "sex") 会在字典服务立马查出来对应的text 然后在请求list的时候将这个字典text，已字段名称加_dictText形式返回到前端
     * 例输入当前返回值的就会多出一个sex_dictText字段
     * {
     *      sex:1,
     *      sex_dictText:"男"
     * }
     * 前端直接取值sext_dictText在table里面无需再进行前端的字典转换了
     *  customRender:function (text) {
     *               if(text==1){
     *                 return "男";
     *               }else if(text==2){
     *                 return "女";
     *               }else{
     *                 return text;
     *               }
     *             }
     *             目前vue是这么进行字典渲染到table上的多了就很麻烦了 这个直接在服务端渲染完成前端可以直接用
     * @param result
     */
    private void parseDictText(Object result) {
        List listResult = null;
        PageResult pageResult = null;
        if(result instanceof PageResult){
            pageResult = (PageResult) result;
            listResult = pageResult.getData();
        }else if ( result instanceof ResultVO){
            ResultVO resultVO = (ResultVO) result;
            Object object = resultVO.getResultData();
            if(object instanceof PageResult){
                pageResult= (PageResult) object;
                listResult = pageResult.getData();
            }
        }
        if(CollectionUtils.isNotEmpty(listResult)){
            List<JSONObject> items = new ArrayList<>();
            for (Object record : listResult) {
                /*ObjectMapper mapper = new ObjectMapper();
                // 初始化JavaTimeModule
                JavaTimeModule javaTimeModule = new JavaTimeModule();

                //处理LocalDateTime
                DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern(SysConstant.DATE_TIME_PATTERN);
                javaTimeModule.addSerializer(LocalDateTime.class, new LocalDateTimeSerializer(dateTimeFormatter));
                javaTimeModule.addDeserializer(LocalDateTime.class, new LocalDateTimeDeserializer(dateTimeFormatter));

                //处理LocalDate
                DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern(SysConstant.DATE_PATTERN);
                javaTimeModule.addSerializer(LocalDate.class, new LocalDateSerializer(dateFormatter));
                javaTimeModule.addDeserializer(LocalDate.class, new LocalDateDeserializer(dateFormatter));

                //处理LocalTime
                DateTimeFormatter timeFormatter = DateTimeFormatter.ofPattern(SysConstant.TIME_PATTERN);
                javaTimeModule.addSerializer(LocalTime.class, new LocalTimeSerializer(timeFormatter));
                javaTimeModule.addDeserializer(LocalTime.class, new LocalTimeDeserializer(timeFormatter));

                //注册时间模块, 支持支持jsr310, 即新的时间类(java.time包下的时间类)
                mapper.registerModule(javaTimeModule);*/
                String json="{}";
                try {
                    //解决@JsonFormat注解解析不了的问题详见SysAnnouncement类的@JsonFormat
                    json = objectMapper.writeValueAsString(record);
                } catch (JsonProcessingException e) {
                    log.error("json解析失败"+e.getMessage(),e);
                }
                JSONObject item = JSONObject.parseObject(json);
                for (Field field : ConvertUtils.getAllFields(record)) {
                    if (field.getAnnotation(Dict.class) != null) {
                        String code = field.getAnnotation(Dict.class).dicCode();
                        String text = field.getAnnotation(Dict.class).dicName();
                        String key = String.valueOf(item.get(field.getName()));
                        String textValue = key;
                        if(StringUtils.isNotEmpty(key) && !"null".equals(key)){
                            TSysDictRecordDTO tSysDictRecordDTO =  tSysDictRecordService.getDictRecord(code, key,null);
                            if (tSysDictRecordDTO != null){
                                textValue = tSysDictRecordDTO.getName();
                            }
                        }

                        log.info(" 字典Val : "+ textValue);
                        log.info(" __翻译字典字段__ "+field.getName() + "_dictText： "+ textValue);
                        item.put(field.getName() + "_dictText", textValue);
                    }
                }
                items.add(item);
            }
            pageResult.setData(items);
        }
    }
}
