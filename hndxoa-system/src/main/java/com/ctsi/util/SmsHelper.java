package com.ctsi.util;

import java.util.Set;

/**
 * 短信的帮助类
 */
public class SmsHelper {

    /**
     *保存到短信业务表待轮询发送，不做是否发送短信判断
     * @param phone
     * @param content
     */
    public void saveSmsNoSend(String mobile,String smsContent){

    }

    /**
     *保存到短信业务表待轮询发送
     * @param phone
     * @param content
     */
    public void saveSmsNoSend(Set<String> mobileSet, String smsContent){

    }

    /**
     * 调用异步发送短信，会校验请求参数中是否含有发送短信按钮，
     * 会获取当前请求的requset，然后判断里面的参数 hasSms是否为发送短信
     * hasSms = 1 发送短信
     * @param mobile
     * @param smsContent
     */
    public void sendHasSms(String mobile,String smsContent){

    }



}
