package com.ctsi.system.entity.dto;

import com.ctsi.hndx.common.BaseDtoEntity;
import java.io.Serializable;
import java.math.BigInteger;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 数据字典的对应关系表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-24
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value="TDictRecordOrgDTO对象", description="数据字典的对应关系表")
public class TDictRecordOrgDTO extends BaseDtoEntity {

    private static final long serialVersionUID = 1L;

    private Long orgId;

    /**
     * 记录编码
     */
    @ApiModelProperty(value = "记录编码")
    private String code;

    /**
     * 记录名称
     */
    @ApiModelProperty(value = "记录名称")
    private String name;

    /**
     * 排序
     */
    @ApiModelProperty(value = "排序")
    private Integer sorted;

    /**
     * 是否为默认数据: 0表示非默认显示，1表示默认显示
     */
    @ApiModelProperty(value = "是否为默认数据: 0表示非默认显示，1表示默认显示")
    private Integer defaults;

    @ApiModelProperty(value = "保留字段")
    private String dictTypeCode;

    /**
     * 数据字典类别名称
     */
    @ApiModelProperty(value = "数据字典类别名称,保留字段")
    private String dictTypeName;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remarks;


}
