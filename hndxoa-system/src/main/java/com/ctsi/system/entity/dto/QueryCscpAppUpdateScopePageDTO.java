package com.ctsi.system.entity.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class QueryCscpAppUpdateScopePageDTO {

    @ApiModelProperty(value = "app升级版本id")
    private String appVersionId;

    @ApiModelProperty(value = "单位名称")
    private String companyName;

    @ApiModelProperty(value = "部门名称")
    private String departmentName;

}
