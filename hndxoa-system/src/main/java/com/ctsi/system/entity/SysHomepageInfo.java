package com.ctsi.system.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.ctsi.hndx.common.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 首页自定义
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-09
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sys_homepage_info")
@ApiModel(value="SysHomepageInfo对象", description="首页自定义")
public class SysHomepageInfo extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 标题图标
     */
    @ApiModelProperty(value = "标题图标")
    private String icon;

    /**
     * 元素名称
     */
    @ApiModelProperty(value = "元素名称")
    private String typeName;

    /**
     * 标题
     */
    @ApiModelProperty(value = "标题")
    private String title;

    /**
     * 行标题
     */
    @ApiModelProperty(value = "行标题")
    private String rowTitle;

    /**
     * 行数据字段名
     */
    @ApiModelProperty(value = "行数据字段名")
    private String rowKey;

    /**
     * 组件名
     */
    @ApiModelProperty(value = "组件名")
    private String componentName;

    /**
     * 获取数据的接口地址
     */
    @ApiModelProperty(value = "获取数据的接口地址")
    private String dataUrl;

    /**
     * 右上角按钮
     */
    @ApiModelProperty(value = "右上角按钮")
    private String hasExtra;

    /**
     * 每页多少条数据
     */
    @ApiModelProperty(value = "每页多少条数据")
    private Integer pageSize;

    /**
     * 宽度比
     */
    @ApiModelProperty(value = "宽度比")
    private String span;

    /**
     * 排序号
     */
    @ApiModelProperty(value = "排序号")
    private Integer sort;

    /**
     * 详情地址
     */
    @ApiModelProperty(value = "详情地址")
    private String detailPath;

    /**
     * 是否需要角标 0:不需要 1:需要
     */
    @ApiModelProperty(value = "是否需要角标")
    private Integer hasCount;

    /**
     * 行高
     */
    @ApiModelProperty(value = "行高")
    private String lineHeight;

//    /**
//     * 总高度
//     */
//    @ApiModelProperty(value = "总高度")
//    private String height1;

    /**
     * 是否分页 0:不需要 1:需要
     */
    @ApiModelProperty(value = "是否分页")
    private Integer hasPage;

    /**
     * 是否分页 0:不是 1:是
     */
    @ApiModelProperty(value = "是否单位默认")
    private Integer companyOn;

    /**
     * 启用首页配置：1-单位，0-个人
     */
    @ApiModelProperty(value = "自定义首页显示：1-单位，0-个人")
    private Integer companyOrUser;

    /**
     * 模块横坐标值
     */
    @ApiModelProperty(value = "模块横坐标值")
    private Integer numericalX;
    /**
     * 模块纵坐标值
     */
    @ApiModelProperty(value = "模块纵坐标值")
    private Integer numericalY;
    /**
     * 模块宽度
     */
    @ApiModelProperty(value = "模块宽度")
    private Integer width;
    /**
     * 模块高度
     */
    @ApiModelProperty(value = "模块高度")
    private Integer height;
    /**
     * 模块最小宽度
     */
    @ApiModelProperty(value = "模块最小宽度")
    private Integer minWidth;
    /**
     * 模块最大宽度
     */
    @ApiModelProperty(value = "模块最大宽度")
    private Integer maxWidth;
    /**
     * 模块最小高度
     */
    @ApiModelProperty(value = "模块最小高度")
    private Integer minHeight;
    /**
     * 模块最大高度
     */
    @ApiModelProperty(value = "模块最大高度")
    private Integer maxHeight;
    /**
     * 其他参数
     */
    @ApiModelProperty(value = "其他参数")
    private Integer params;

    /**
     * 模块id
     */
    @ApiModelProperty(value = "模块id")
    private String moduleId;

}
