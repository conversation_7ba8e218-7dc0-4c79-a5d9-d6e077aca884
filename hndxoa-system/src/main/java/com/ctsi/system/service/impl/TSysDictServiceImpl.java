package com.ctsi.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.common.SysBaseServiceImpl;
import com.ctsi.hndx.exception.BusinessException;
import com.ctsi.hndx.utils.BeanConvertUtils;
import com.ctsi.hndx.utils.ListCopyUtil;
import com.ctsi.ssdc.security.SecurityUtils;
import com.ctsi.system.entity.TSysDict;
import com.ctsi.system.entity.TSysDictRecord;
import com.ctsi.system.entity.dto.TSysDictDTO;
import com.ctsi.system.mapper.TSysDictMapper;
import com.ctsi.system.service.ITSysDictRecordService;
import com.ctsi.system.service.ITSysDictService;
import io.jsonwebtoken.lang.Assert;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-03
 */

@Slf4j
@Service
public class TSysDictServiceImpl extends SysBaseServiceImpl<TSysDictMapper, TSysDict> implements ITSysDictService {

    @Autowired
    private TSysDictMapper tSysDictMapper;

    @Autowired
    private ITSysDictRecordService itSysDictRecordService;




    /**
     * 翻页
     *
     * @param entityDTO
     * @param basePageForm
     * @return
     */
    @Override
    public IPage<TSysDictDTO> queryListPage(TSysDictDTO entityDTO, BasePageForm basePageForm) {
        //设置条件
//        LambdaQueryWrapper<TSysDict> queryWrapper = new LambdaQueryWrapper();
//        if (StringUtils.isNotBlank(entityDTO.getName())) {
//            queryWrapper.like(TSysDict::getName, entityDTO.getName());
//        }
//        if (StringUtils.isNotBlank(entityDTO.getCode())) {
//            queryWrapper.like(TSysDict::getCode, entityDTO.getCode());
//        }
//        if (SecurityUtils.isSystemName()){
//            queryWrapper.isNull(TSysDict::getCompanyId).isNull(TSysDict::getTenantId);
//        }
//        queryWrapper.orderByDesc(TSysDict::getCreateTime);
//        IPage<TSysDict> pageData = tSysDictMapper.selectPage(
//             PageHelperUtil.getMPlusPageByBasePage(basePageForm), queryWrapper);
//        //返回
//        IPage<TSysDictDTO> data  = pageData.convert(entity -> BeanConvertUtils.copyProperties(entity,TSysDictDTO.class));
//        return data;
        LambdaQueryWrapper<TSysDict> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.like(StringUtils.isNotEmpty(entityDTO.getName()),TSysDict::getName,entityDTO.getName());
        queryWrapper.like(StringUtils.isNotEmpty(entityDTO.getCode()),TSysDict::getCode,entityDTO.getCode());
        queryWrapper.eq(entityDTO.getDeptId() != null,TSysDict::getDeptId,entityDTO.getDeptId());

        queryWrapper.and((wrapper)->{
            wrapper.eq(TSysDict::getCompanyId,SecurityUtils.getCurrentCompanyId()).or()
                    .eq(TSysDict::getTenantId,SecurityUtils.getCurrentCscpUserDetail().getTenantId()).or()
                    .eq(TSysDict::getCreateBy, 2L);
        });
        List<TSysDict> tSysDictList = tSysDictMapper.selectListNoAdd(queryWrapper);
        Map<String,TSysDict> tSysDictMap = new HashMap<String,TSysDict>();
        // TODO: 2023/4/4 wubin 去除重复，如果单位有字典采用单位的，否则如果租户有采用租户的，否则采用admin的
        for (TSysDict tSysDict:tSysDictList){
            String code=tSysDict.getCode();
            if(null!=tSysDictMap&&tSysDictMap.containsKey(code)){
                TSysDict tsd = tSysDictMap.get(code);
                if(null!=tSysDict.getCompanyId()){
                    tSysDictMap.put(code,tSysDict);
                }else if(null==tsd.getTenantId()&&null!=tSysDict.getTenantId()){
                    tSysDictMap.put(code,tSysDict);
                }else if(tsd.getCreateBy() == 2 && tSysDict.getCreateBy() != 2L) {
                    tSysDictMap.put(code,tSysDict);
                }
            }else {
                tSysDictMap.put(code,tSysDict);
            }
        }
        List<TSysDict> result =tSysDictMap.values().stream().collect(Collectors.toList());
        IPage<TSysDict> pageData = new Page<TSysDict>();
        pageData.setTotal(result.size());
        pageData.setCurrent(basePageForm.getCurrentPage());//当前页数
        pageData.setSize(basePageForm.getPageSize());//每页条数
        long current = basePageForm.getCurrentPage();
        long offset = pageData.offset();
        long maxsize = 0;
        if(result.size()<offset+basePageForm.getPageSize()){
            maxsize = result.size();
        }else {
            maxsize = offset+basePageForm.getPageSize();
        }

        List<TSysDict>  tRecordsList = new ArrayList<>();
        for(long i=offset;i<maxsize;i++){
            tRecordsList.add(result.get((int)i));
        }
        pageData.setRecords(tRecordsList);
        //返回
        IPage<TSysDictDTO> data  = pageData.convert(entity -> BeanConvertUtils.copyProperties(entity,TSysDictDTO.class));
        return data;
    }

    /**
     * 列表查询
     *
     * @param entityDTO
     * @return
     */
    @Override
    public List<TSysDictDTO> queryList(TSysDictDTO entityDTO) {
//        LambdaQueryWrapper<TSysDict> queryWrapper = new LambdaQueryWrapper();
//            List<TSysDict> listData = tSysDictMapper.selectList(queryWrapper);
        LambdaQueryWrapper<TSysDict> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.like(StringUtils.isNotEmpty(entityDTO.getName()),TSysDict::getName,entityDTO.getName());
        queryWrapper.like(StringUtils.isNotEmpty(entityDTO.getCode()),TSysDict::getCode,entityDTO.getCode());
        queryWrapper.and((wrapper)->{
            wrapper.eq(TSysDict::getCompanyId,SecurityUtils.getCurrentCompanyId()).or()
                    .eq(TSysDict::getTenantId,SecurityUtils.getCurrentCscpUserDetail().getTenantId()).or()
                    .eq(TSysDict::getCreateBy,2L);
        });
        List<TSysDict> tSysDictList = tSysDictMapper.selectListNoAdd(queryWrapper);
        Map<String,TSysDict> tSysDictMap = new HashMap<String,TSysDict>();
        // TODO: 2023/4/4 wubin 去除重复，如果单位有字典采用单位的，否则如果租户有采用租户的，否则采用admin的
        for (TSysDict tSysDict:tSysDictList){
            String code=tSysDict.getCode();
            if(null!=tSysDictMap&&tSysDictMap.containsKey(code)){
                TSysDict tsd = tSysDictMap.get(code);
                if(null!=tSysDict.getCompanyId()){
                    tSysDictMap.put(code,tSysDict);
                }else if(null==tsd.getTenantId()&&null!=tSysDict.getTenantId()){
                    tSysDictMap.put(code,tSysDict);
                }else if("admin".equals(tsd.getCreateName())&&(!"admin".equals(tSysDict.getCreateName()))){
                    tSysDictMap.put(code,tSysDict);
                }
            }else {
                tSysDictMap.put(code,tSysDict);
            }
        }
        List<TSysDict> result =tSysDictMap.values().stream().collect(Collectors.toList());
        List<TSysDictDTO> TSysDictDTOList = ListCopyUtil.copy(result, TSysDictDTO.class);
        return TSysDictDTOList;
    }

    /**
     * 单个查询
     *
     * @param id the id of the entity
     * @return
     */
    @Override
    public TSysDictDTO findOne(Long id) {
        TSysDict  tSysDict =  tSysDictMapper.selectById(id);
        return  BeanConvertUtils.copyProperties(tSysDict,TSysDictDTO.class);
    }


    /**
     * 新增
     *
     * @param entityDTO the entity to create
     * @return
     */
    @Override
    @Transactional
    public TSysDictDTO create(TSysDictDTO entityDTO) {

        // 先判断此code是否有值
        LambdaQueryWrapper<TSysDict> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.eq(TSysDict::getCode, entityDTO.getCode())
                .eq(entityDTO.getDeptId() != null ,TSysDict::getDeptId,entityDTO.getDeptId())
        ;
        if( !SecurityUtils.isSystemName() &&  entityDTO.getDeptId() == null){
            throw  new BusinessException("非admin账户,deptId不能为空!");
        }
        if(SecurityUtils.isSystemName() && entityDTO.getDeptId() == null ){
            queryWrapper.isNull(TSysDict::getTenantId);
        }


        TSysDict sysDictOrigal = tSysDictMapper.selectOne(queryWrapper);
        if (sysDictOrigal != null){
            throw  new BusinessException("已经存在此code值{},不能重复添加",entityDTO.getCode());
        }
        TSysDict tSysDict =  BeanConvertUtils.copyProperties(entityDTO,TSysDict.class);
        save(tSysDict);
        return  BeanConvertUtils.copyProperties(tSysDict,TSysDictDTO.class);
    }

    /**
     * 修改
     *
     * @param entity the entity to update
     * @return
     */
    @Override
    @Transactional
    public int update(TSysDictDTO entity) {
        // 先判断是否更改code值
        TSysDict tSysDictOrignal = tSysDictMapper.selectById(entity.getId());
        boolean isSame = true;
        if (!tSysDictOrignal.getCode().equals(entity.getCode())){

            // 部门修改? 单位修改? admin 修改?

            LambdaQueryWrapper<TSysDict> queryWrapper = new LambdaQueryWrapper();
            queryWrapper.eq(TSysDict::getCode, entity.getCode())
                    .eq(entity.getDeptId() != null ,TSysDict::getDeptId,entity.getDeptId())
                    .isNull(entity.getDeptId() == null , TSysDict::getTenantId);
            ;

            TSysDict sysDictOrigal = tSysDictMapper.selectOne(queryWrapper);
            if (sysDictOrigal != null){
                throw  new BusinessException("已经存在此code值{},不能重复添加",entity.getCode());
            }
            isSame = false;
        }
        TSysDict tSysDict = BeanConvertUtils.copyProperties(entity,TSysDict.class);
        int size =  tSysDictMapper.updateById(tSysDict);
        if (!isSame){
            itSysDictRecordService.delRedisByDictCode(tSysDictOrignal.getCode());
            itSysDictRecordService.delRedisByDictCode(entity.getCode());
        }
        return size;
    }

    /**
     * 删除
     *
     * @param id the id of the entity
     */
    @Override
    @Transactional
    public int delete(Long id) {
        LambdaQueryWrapper<TSysDictRecord> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(TSysDictRecord::getDictId,id);
        List<TSysDictRecord> tSysDictRecords = itSysDictRecordService.selectListNoAdd(lambdaQueryWrapper);
        if (CollectionUtils.isNotEmpty(tSysDictRecords)){
            throw new BusinessException("数据字典存在对应的码值，请先删除码值");
        }
        //采用真实删除
        if(tSysDictMapper.deleteTrueById(id)){
            return 1;
        }else {
            return 0;
        }
//        return tSysDictMapper.deleteById(id);
    }


    /**
     * 验证是否存在
     *
     * @param TSysDictId
     * @return
     */
    @Override
    public boolean existByTSysDictId(Long TSysDictId) {
        if (TSysDictId != null) {
            LambdaQueryWrapper<TSysDict> queryWrapper = new LambdaQueryWrapper();
            queryWrapper.eq(TSysDict::getId, TSysDictId);
            List<TSysDict> result = tSysDictMapper.selectList(queryWrapper);
            return result.size() > 0;
        }
        return true;
    }



    @Override
    public TSysDictDTO getCompanyDictByDictCode(String dictCode,Long companyId) {
        Assert.hasLength(dictCode,"传入的数据字典的编码不能为空");
        LambdaQueryWrapper<TSysDict> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.eq(TSysDict::getCode, dictCode);
        if (SecurityUtils.isSystemName()){
            queryWrapper.isNull(TSysDict::getTenantId);
        }
        TSysDict sysDict = null;
        if (companyId != null){
            // 取单位数据
            queryWrapper.eq(TSysDict::getDeptId,companyId);
            sysDict =  tSysDictMapper.selectOneNoAdd(queryWrapper);
        }else {
            sysDict =  tSysDictMapper.selectOne(queryWrapper);
        }

        TSysDictDTO tSysDictDTO = BeanConvertUtils.copyProperties(sysDict,TSysDictDTO.class);
        return tSysDictDTO;
    }



    @Override
    public TSysDictDTO getDeptDictByDictCode(String dictCode,Long deptId) {
        Assert.hasLength(dictCode,"传入的数据字典的编码不能为空");
        LambdaQueryWrapper<TSysDict> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.eq(TSysDict::getCode, dictCode)
                .eq(TSysDict::getDeptId, deptId)
        ;
        TSysDict sysDict = tSysDictMapper.selectOneNoAdd(queryWrapper);
        TSysDictDTO tSysDictDTO = BeanConvertUtils.copyProperties(sysDict,TSysDictDTO.class);
        return tSysDictDTO;
    }

    /**
     * 根据code值获取系统对应的数据字典的编码
     * @param dictCode
     * @return
     */
    @Override
    public TSysDictDTO getSysDictByDictCode(String dictCode) {
        Assert.hasLength(dictCode,"传入的数据字典的编码不能为空");
        LambdaQueryWrapper<TSysDict> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.eq(TSysDict::getCode, dictCode);
        queryWrapper.isNull(TSysDict::getCompanyId);
        queryWrapper.isNull(TSysDict::getTenantId);
        TSysDict sysDict = tSysDictMapper.selectOneNoAdd(queryWrapper);
        TSysDictDTO tSysDictDTO = BeanConvertUtils.copyProperties(sysDict,TSysDictDTO.class);
        return tSysDictDTO;
    }

    @Override
    public IPage<TSysDict> queryFormTSysDictPage(TSysDictDTO entityDTO, BasePageForm basePageForm) {
        return queryFormTSysDictPage(entityDTO,basePageForm,null);
    }



    @Override
    public IPage<TSysDict> queryFormTSysDictPage(TSysDictDTO entityDTO, BasePageForm basePageForm,Long deptId) {
        LambdaQueryWrapper<TSysDict> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.like(StringUtils.isNotEmpty(entityDTO.getName()),TSysDict::getName,entityDTO.getName());
        queryWrapper.like(StringUtils.isNotEmpty(entityDTO.getCode()),TSysDict::getCode,entityDTO.getCode());
        queryWrapper.eq(entityDTO.getDeptId() != null,TSysDict::getDeptId,deptId);

        queryWrapper.and((wrapper)->{
            wrapper.eq(TSysDict::getDeptId,SecurityUtils.getCurrentCompanyId()).or()
                    .eq(TSysDict::getTenantId,SecurityUtils.getCurrentCscpUserDetail().getTenantId()).or()
                    .eq(TSysDict::getCreateBy,2L);
        });
        List<TSysDict> tSysDictList = tSysDictMapper.selectListNoAdd(queryWrapper);

        // 需要过滤掉部门的数据, 表单配置的数据字典显示  单位和admin数据,重复的取单位数据
        if(null == deptId){
            tSysDictList = tSysDictList.stream().filter(i -> {
                // i.getCompanyId().equals( i.getDeptId())
                if (i.getCompanyId() != null && i.getDeptId() != null) {
                    return i.getCompanyId().equals(i.getDeptId());
                }
                return true;
            }).collect(Collectors.toList());
        }


        Map<String,TSysDict> tSysDictMap = new HashMap<String,TSysDict>();
        // TODO: 2023/4/4 wubin 去除重复，如果单位有字典采用单位的，否则如果租户有采用租户的，否则采用admin的
        for (TSysDict tSysDict:tSysDictList){
            String code=tSysDict.getCode();
            if(null!=tSysDictMap&&tSysDictMap.containsKey(code)){
                TSysDict tsd = tSysDictMap.get(code);
                if(null!=tSysDict.getCompanyId() &&tSysDict.getCompanyId().longValue() == SecurityUtils.getCurrentCompanyId().longValue()){
                    tSysDictMap.put(code,tSysDict);
                }else if(null==tSysDict.getCompanyId() && null!=tsd.getTenantId()&&null!=tSysDict.getTenantId()){
                    tSysDictMap.put(code,tSysDict);
                }else if(tsd.getCreateBy() == 2 && tSysDict.getCreateBy() != 2L) {
                    tSysDictMap.put(code,tSysDict);
                }
            }else {
                if(null!=tSysDict.getCompanyId() &&tSysDict.getCompanyId().longValue() == SecurityUtils.getCurrentCompanyId().longValue()){
                    tSysDictMap.put(code,tSysDict);
                }else if(null==tSysDict.getCompanyId() && null!=tSysDict.getTenantId()){
                    tSysDictMap.put(code,tSysDict);
                }else if(tSysDict.getCreateBy() == 2L){
                    tSysDictMap.put(code,tSysDict);
                }
            }
        }
        List<TSysDict> result =tSysDictMap.values().stream().collect(Collectors.toList());
        IPage<TSysDict> pageData = new Page<TSysDict>();
        pageData.setTotal(result.size());
        pageData.setCurrent(basePageForm.getCurrentPage());//当前页数
        pageData.setSize(basePageForm.getPageSize());//每页条数
        long current = basePageForm.getCurrentPage();
        long offset = pageData.offset();
        long maxsize = 0;
        if(result.size()<offset+basePageForm.getPageSize()){
            maxsize = result.size();
        }else {
            maxsize = offset+basePageForm.getPageSize();
        }

        List<TSysDict>  tRecordsList = new ArrayList<>();


        for(long i=offset;i<maxsize;i++){
            tRecordsList.add(result.get((int)i));
        }
        pageData.setRecords(tRecordsList);
        return pageData;

//        if (SecurityUtils.isSystemName()){
//            LambdaQueryWrapper<TSysDict> queryAdminWrapper = new LambdaQueryWrapper();
//            queryAdminWrapper.eq(TSysDict::getCreateBy,Long.valueOf("1"));
//            queryAdminWrapper.like(TSysDict::getName,entityDTO.getName());
//            return tSysDictMapper.selectPageNoAdd(PageHelperUtil.getMPlusPageByBasePage(basePageForm), queryAdminWrapper);
//        }
////        entityDTO.setCompanyId(SecurityUtils.getCurrentCscpUserDetail().getCompanyId());
////        IPage<TSysDictDTO> pageData = tSysDictMapper.queryFormTSysDictPage(
////                PageHelperUtil.getMPlusPageByBasePage(basePageForm), entityDTO);
//        //查询单位的数据字典
//        LambdaQueryWrapper<TSysDict> queryWrapper = new LambdaQueryWrapper();
//        queryWrapper.eq(TSysDict::getCompanyId,SecurityUtils.getCurrentCscpUserDetail().getCompanyId());
//        queryWrapper.like(TSysDict::getName,entityDTO.getName());
//        IPage<TSysDict> pageData = tSysDictMapper.selectPageNoAdd(PageHelperUtil.getMPlusPageByBasePage(basePageForm), queryWrapper);
//        if(pageData.getRecords().size()>0){
//            return pageData;
//        }
////        //查询租户的数据字典
//        LambdaQueryWrapper<TSysDict> queryTenantWrapper = new LambdaQueryWrapper();
//        queryTenantWrapper.eq(TSysDict::getTenantId,SecurityUtils.getCurrentCscpUserDetail().getTenantId());
//        queryTenantWrapper.like(TSysDict::getName,entityDTO.getName());
//        IPage<TSysDict> pageData2 = tSysDictMapper.selectPageNoAdd(PageHelperUtil.getMPlusPageByBasePage(basePageForm), queryTenantWrapper);
//        if(pageData2.getRecords().size()>0){
//            return pageData2;
//        }
//        //查询系统管理员的数据字典
//        LambdaQueryWrapper<TSysDict> queryAdminWrapper = new LambdaQueryWrapper();
//        queryAdminWrapper.eq(TSysDict::getCreateName,"admin");
//        queryAdminWrapper.like(TSysDict::getName,entityDTO.getName());
//        IPage<TSysDict> pageData3 = tSysDictMapper.selectPageNoAdd(PageHelperUtil.getMPlusPageByBasePage(basePageForm), queryAdminWrapper);
//        if(pageData3.getRecords().size()>0){
//            return pageData3;
//        }
//        return new Page<TSysDict>();
    }


}
