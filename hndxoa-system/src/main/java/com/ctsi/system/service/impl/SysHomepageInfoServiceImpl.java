package com.ctsi.system.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.common.SysBaseServiceImpl;
import com.ctsi.hndx.utils.BeanConvertUtils;
import com.ctsi.hndx.utils.ListCopyUtil;
import com.ctsi.hndx.utils.PageHelperUtil;
import com.ctsi.hndx.utils.SysTenantUtils;
import com.ctsi.ssdc.model.PageResult;
import com.ctsi.ssdc.security.CscpUserDetail;
import com.ctsi.ssdc.security.SecurityUtils;
import com.ctsi.system.entity.SysHomepageInfo;
import com.ctsi.system.entity.dto.SysHomepageInfoDTO;
import com.ctsi.system.mapper.SysHomepageInfoMapper;
import com.ctsi.system.service.ISysHomepageInfoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Objects;

/**
 * <p>
 * 首页自定义 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-09
 */
@Slf4j
@Service
public class SysHomepageInfoServiceImpl extends SysBaseServiceImpl<SysHomepageInfoMapper, SysHomepageInfo> implements ISysHomepageInfoService {

    @Autowired
    private SysHomepageInfoMapper sysHomepageInfoMapper;

    /**
     * 翻页
     *
     * @param entityDTO
     * @param basePageForm
     * @return
     */
    @Override
    public PageResult<SysHomepageInfoDTO> queryListPage(SysHomepageInfoDTO entityDTO, BasePageForm basePageForm) {
        // 设置条件
        LambdaQueryWrapper<SysHomepageInfo> queryWrapper = new LambdaQueryWrapper();
        CscpUserDetail cscpUserDetail = SecurityUtils.getCurrentCscpUserDetail();
        IPage<SysHomepageInfo> pageData = null;
        if (SecurityUtils.isGeneralName()){
            // 找单位
            Long complayId = cscpUserDetail.getCompanyId();
            queryWrapper.eq(SysHomepageInfo::getCompanyId , complayId);
            pageData = sysHomepageInfoMapper.selectPage(
                    PageHelperUtil.getMPlusPageByBasePage(basePageForm) , queryWrapper);
        }
        if ( (SecurityUtils.isGeneralName() && pageData.getSize() <= 0) || SecurityUtils.isTenantName() ){
            // 找租户
            queryWrapper = new LambdaQueryWrapper<SysHomepageInfo>()
                    .eq(SysHomepageInfo::getTenantId , cscpUserDetail.getTenantId())
                    .isNull(SysHomepageInfo::getCompanyId);
            pageData = sysHomepageInfoMapper.selectPage(
                    PageHelperUtil.getMPlusPageByBasePage(basePageForm) , queryWrapper);
            if (pageData.getSize() <= 0) {
                // 找顶级租户
                Long topFloorTenamtId = SysTenantUtils.getTopFloorTenamtId(cscpUserDetail.getTenantId());
                queryWrapper = new LambdaQueryWrapper<SysHomepageInfo>().eq(SysHomepageInfo::getTenantId , topFloorTenamtId)
                        .isNull(SysHomepageInfo::getCompanyId);
                pageData = sysHomepageInfoMapper.selectPageNoAdd(
                        PageHelperUtil.getMPlusPageByBasePage(basePageForm) , queryWrapper);
            }

        }
        // 返回
        IPage<SysHomepageInfoDTO> data = pageData.convert(entity -> BeanConvertUtils.copyProperties(entity , SysHomepageInfoDTO.class));

        return new PageResult<SysHomepageInfoDTO>(data.getRecords() ,
                data.getTotal() , data.getCurrent());
    }


    @Override
    public int change(Integer companyOrUserFlag) {
        LambdaUpdateWrapper<SysHomepageInfo> updateWrap = new LambdaUpdateWrapper<>();
        updateWrap.eq(SysHomepageInfo::getCreateBy, SecurityUtils.getCurrentUserId());

        //启用首页配置：1-单位，0-个人
        if (ObjectUtil.equal(companyOrUserFlag, 0)) {
            updateWrap.set(SysHomepageInfo::getCompanyOrUser, 0);
        } else {
            updateWrap.set(SysHomepageInfo::getCompanyOrUser, 1);
        }
        return sysHomepageInfoMapper.update(null, updateWrap);
    }

    /**
     * 列表查询 单位/租户/顶级租户
     *
     * @param entityDTO
     * @return
     */
    @Override
    public List<SysHomepageInfoDTO> queryList(SysHomepageInfoDTO entityDTO) {
        LambdaQueryWrapper<SysHomepageInfo> queryWrapper = new LambdaQueryWrapper();
        CscpUserDetail cscpUserDetail = SecurityUtils.getCurrentCscpUserDetail();
        List<SysHomepageInfo> listData = null;
        queryWrapper.eq(SysHomepageInfo::getCreateBy , SecurityUtils.getCurrentUserId());
        listData = sysHomepageInfoMapper.selectList( queryWrapper);

        //自定义首页显示：1-单位，0-个人
        if (CollectionUtil.isNotEmpty(listData) && ObjectUtil.equal(listData.get(0).getCompanyOrUser(), 0)) {
            return ListCopyUtil.copy(listData, SysHomepageInfoDTO.class);
        }

        //个人自定义为空或选择显示单位市，自定义首页显示：单位
        if (SecurityUtils.isGeneralName()) {
            // 找单位
            queryWrapper = new LambdaQueryWrapper();
            Long complayId = cscpUserDetail.getCompanyId();
            queryWrapper.eq(SysHomepageInfo::getCompanyId, complayId).eq(SysHomepageInfo::getCompanyOn, 1);
//                queryWrapper.eq(SysHomepageInfo::getCompanyOn , 1);

            listData = sysHomepageInfoMapper.selectList(queryWrapper);
        }
        if ((SecurityUtils.isGeneralName() && CollectionUtil.isEmpty(listData)) || SecurityUtils.isTenantName()) {
            // 找租户
            queryWrapper = new LambdaQueryWrapper<SysHomepageInfo>()
                    .eq(SysHomepageInfo::getTenantId, cscpUserDetail.getTenantId())
                    .isNull(SysHomepageInfo::getCompanyId);
            listData = sysHomepageInfoMapper.selectList(queryWrapper);
            if (listData.isEmpty()) {
                // 找顶级租户
                if (Objects.isNull(SysTenantUtils.getTopFloorTenamtId(cscpUserDetail.getTenantId()))) {

                } else {
                    Long topFloorTenamtId = SysTenantUtils.getTopFloorTenamtId(cscpUserDetail.getTenantId());
                    queryWrapper = new LambdaQueryWrapper<SysHomepageInfo>()
                            .eq(SysHomepageInfo::getTenantId, topFloorTenamtId)
                            .isNull(SysHomepageInfo::getCompanyId);
                    listData = sysHomepageInfoMapper.selectListNoAdd(queryWrapper);
                }

            }
        }



        List<SysHomepageInfoDTO>  SysHomepageInfoDTOList = ListCopyUtil.copy(listData , SysHomepageInfoDTO.class);

        return SysHomepageInfoDTOList;
    }

    /**
     * 单个查询
     *
     * @param id the id of the entity
     * @return
     */
    @Override
    public SysHomepageInfoDTO findOne(Long id) {
        SysHomepageInfo sysHomepageInfo = sysHomepageInfoMapper.selectById(id);
        return BeanConvertUtils.copyProperties(sysHomepageInfo , SysHomepageInfoDTO.class);
    }

    /**
     * 新增
     *
     * @param entityDTO the entity to create
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public SysHomepageInfoDTO create(SysHomepageInfoDTO entityDTO) {
        SysHomepageInfo sysHomepageInfo = BeanConvertUtils.copyProperties(entityDTO , SysHomepageInfo.class);
        save(sysHomepageInfo);
        return BeanConvertUtils.copyProperties(sysHomepageInfo , SysHomepageInfoDTO.class);
    }

    /**
     * 修改
     *
     * @param entity the entity to update
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int update(SysHomepageInfoDTO entity) {
        SysHomepageInfo sysHomepageInfo = BeanConvertUtils.copyProperties(entity , SysHomepageInfo.class);
        return sysHomepageInfoMapper.updateById(sysHomepageInfo);
    }

    /**
     * 删除
     *
     * @param id the id of the entity
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int delete(Long id) {
        LambdaQueryWrapper<SysHomepageInfo> queryWrapper = new LambdaQueryWrapper();
        if(id==null) {
            queryWrapper.eq(SysHomepageInfo::getCreateBy, SecurityUtils.getCurrentUserId());
        }else {
            queryWrapper.eq(SysHomepageInfo::getCreateBy, id);
        }
        queryWrapper.eq(SysHomepageInfo::getCompanyOn,0);
        sysHomepageInfoMapper.delete(queryWrapper);
        return sysHomepageInfoMapper.deleteById(id);
    }


    /**
     * 验证是否存在
     *
     * @param SysHomepageInfoId
     * @return
     */
    @Override
    public boolean existBySysHomepageInfoId(Long SysHomepageInfoId) {
        if (SysHomepageInfoId != null) {
            LambdaQueryWrapper<SysHomepageInfo> queryWrapper = new LambdaQueryWrapper();
            queryWrapper.eq(SysHomepageInfo::getId, SysHomepageInfoId);
            List<SysHomepageInfo> result = sysHomepageInfoMapper.selectList(queryWrapper);
            return result.size() > 0;
        }
        return true;
    }

    /**
     * 批量新增
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertBatch(List<SysHomepageInfoDTO> dataList) {
        LambdaQueryWrapper<SysHomepageInfo> queryWrapper = new LambdaQueryWrapper();
        if(SecurityUtils.isTenantName()){
            queryWrapper.eq(SysHomepageInfo::getTenantId, SecurityUtils.getCurrentCscpUserDetail().getTenantId())
                    .isNull(SysHomepageInfo::getCompanyId);
        }else {
            if (CollectionUtil.isNotEmpty(dataList)){
                SysHomepageInfoDTO sysHomepageInfoDTO = dataList.get(0);
                Integer integer = sysHomepageInfoDTO.getCompanyOn();
                if (integer == 1){
                    // 表示单位的删除
                    Long currentCompanyId = SecurityUtils.getCurrentCompanyId();
                    queryWrapper.eq(SysHomepageInfo::getCompanyId,currentCompanyId);
                    queryWrapper.eq(SysHomepageInfo::getCompanyOn,1);
                }else {
                    queryWrapper.eq(SysHomepageInfo::getCreateBy,SecurityUtils.getCurrentUserId());
                    queryWrapper.eq(SysHomepageInfo::getCompanyOn,0);
                }
            }

        }
        sysHomepageInfoMapper.delete(queryWrapper);
        // 如果前端传入空集合，则直接返回
        if (CollectionUtil.isEmpty(dataList)) {
            return true;
        }
        List<SysHomepageInfo> result = ListCopyUtil.copy(dataList, SysHomepageInfo.class);
        return saveBatch(result);
    }


}
