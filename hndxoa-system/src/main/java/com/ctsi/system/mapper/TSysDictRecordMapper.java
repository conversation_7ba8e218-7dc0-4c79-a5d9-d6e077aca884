package com.ctsi.system.mapper;

import com.ctsi.hndx.common.MybatisBaseMapper;
import com.ctsi.hndx.mybatisplus.sort.SortMapper;
import com.ctsi.system.entity.TSysDictRecord;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-03
 */
public interface TSysDictRecordMapper extends MybatisBaseMapper<TSysDictRecord> , SortMapper {


    /**
     * 物理删除数字字典
     * @param id
     * @return
     */
    int deleteTrue(Long id);

    Integer queryUserSecurityType(@Param("userId") Long userId);
}
