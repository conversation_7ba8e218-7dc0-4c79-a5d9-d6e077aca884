package com.ctsi.system.controller;

import java.io.BufferedInputStream;
import java.io.InputStream;
import java.net.URISyntaxException;
import java.util.List;

import com.ctsi.hndx.enums.DBOperation;
import com.ctsi.ssdc.annotation.OperationLog;
import com.ctsi.ssdc.model.PageResult;
import com.ctsi.ssdc.model.ResResult;
import com.ctsi.ssdc.security.SecurityUtils;
import com.ctsi.system.entity.dto.CscpAppEditionDTO;
import com.ctsi.system.entity.dto.CscpAppUpdateScopeDTO;
import com.ctsi.system.entity.dto.QueryCscpAppEditionPageDTO;
import com.ctsi.system.entity.dto.QueryCscpAppUpdateScopePageDTO;
import com.ctsi.system.service.ICscpAppEditionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import com.ctsi.hndx.common.BasePageForm;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.util.Assert;
import com.ctsi.hndx.common.BaseController;
import com.ctsi.hndx.annotations.ResponseResultVo;
import com.ctsi.hndx.result.ResultCode;
import com.ctsi.hndx.result.ResultVO;


/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-19
 */

@Slf4j
@RestController
@ResponseResultVo
@RequestMapping("/api/cscpAppEdition")
@Api(value = "app版本号接口", tags = "app版本号 接口")
public class CscpAppEditionController extends BaseController {

    private static final String ENTITY_NAME = "cscpAppEdition";

    @Autowired
    private ICscpAppEditionService cscpAppEditionService;


    /**
     * 新增数据.
     */
    @PostMapping("/create")
    @ApiOperation(value = "app版本号新增", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.ADD,message = "新增app版本号")
    public ResultVO<CscpAppEditionDTO> create(@RequestBody CscpAppEditionDTO cscpAppEditionDTO) throws URISyntaxException {
        CscpAppEditionDTO result = cscpAppEditionService.create(cscpAppEditionDTO);
        return ResultVO.success(result);
    }

    /**
     * 更新存在数据.
     */
    @PostMapping("/update")
    @ApiOperation(value = "更新存在数据", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.UPDATE,message = "更新app版本号")
    public ResultVO update(@RequestBody CscpAppEditionDTO cscpAppEditionDTO) {
        Assert.notNull(cscpAppEditionDTO.getId(), "general.IdNotNull");
        int count = cscpAppEditionService.update(cscpAppEditionDTO);
        if (count > 0) {
            return ResultVO.success();
        } else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }

    /**
     * 查询编辑.
     */
    @PostMapping("/findOne/{id}")
    @ApiOperation(value = "查询编辑.", notes = "传入参数")
    public ResultVO findOne(@PathVariable Long id) {
        return ResultVO.success(cscpAppEditionService.findOne(id));
    }

    /**
     * 删除存在数据.
     */
    @DeleteMapping("/delete/{id}")
    @ApiOperation(value = "删除存在数据", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.DELETE,message = "删除app版本号")
    public ResultVO delete(@PathVariable Long id) {
        int count = cscpAppEditionService.delete(id);
        if (count > 0) {
            return ResultVO.success();
        } else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }

    /**
     * 分页查询多条数据.
     */
    @GetMapping("/queryCscpAppEditionPage")
    @ApiOperation(value = "翻页查询多条数据", notes = "传入参数")
    public ResultVO<PageResult<CscpAppEditionDTO>> queryCscpAppEditionPage(QueryCscpAppEditionPageDTO queryCscpAppEditionPageDTO, BasePageForm basePageForm) {
        return ResultVO.success(cscpAppEditionService.queryListPage(queryCscpAppEditionPageDTO, basePageForm));
    }


    /**
     * 查询最新的软件版本号.
     */
    @GetMapping("/newVersionNumber")
    @ApiOperation(value = "查询最新的软件版本号", notes = "传入参数")
    public ResultVO<CscpAppEditionDTO> newVersionNumber() {
        Long companyId = null;
        try {
            companyId = SecurityUtils.getCurrentCompanyId();
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return ResultVO.success(cscpAppEditionService.newVersionNumber(companyId));
    }

    /**
     * 分页查询多条数据 查询app升级范围
     */
    @GetMapping("/queryAppUpdateScopePage")
    @ApiOperation(value = "翻页查询多条数据", notes = "传入参数")
    public ResultVO<PageResult<CscpAppUpdateScopeDTO>> queryAppUpdateScopePage(QueryCscpAppUpdateScopePageDTO scopePageDTO,
                                                                               BasePageForm basePageForm) {
        return ResultVO.success(cscpAppEditionService.queryAppUpdateScopePage(scopePageDTO, basePageForm));
    }

}
