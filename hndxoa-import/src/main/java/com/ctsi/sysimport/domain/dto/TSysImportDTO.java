package com.ctsi.sysimport.domain.dto;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.ctsi.hndx.common.BaseDtoEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * <p>
 * 导入结果管理表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-28
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value="TSysImportDTO对象", description="导入结果管理表")
public class TSysImportDTO extends BaseDtoEntity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "导入数据类型(单位、部门、用户)")
    private String type;

    /**
     * 导入成功条数
     */
    @ApiModelProperty(value = "导入成功条数")
    private Integer successNo;

    /**
     * 导入失败条数
     */
    @ApiModelProperty(value = "导入失败条数")
    private Integer failedNo;

    /**
     * 记录总条数
     */
    @ApiModelProperty(value = "记录总条数")
    private Integer totalNo;

    /**
     * 说明文件相对路径
     */
    @ApiModelProperty(value = "说明文件相对路径")
    private String filePath;


    @ApiModelProperty(value = "创建人ID")
    private Long createBy;

    @ApiModelProperty(value = "创建人名称")
    private String createName;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "导入文件的标题")
    private String title;


}
