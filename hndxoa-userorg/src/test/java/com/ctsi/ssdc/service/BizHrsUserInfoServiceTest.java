package com.ctsi.ssdc.service;

import com.ctsi.ssdc.service.impl.BizHrsUserInfoServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.mock.web.MockHttpServletResponse;

/**
 * 人社厅人员信息服务测试类
 *
 * <AUTHOR>
 * @since 2025-07-29
 */
@Slf4j
@SpringBootTest
public class BizHrsUserInfoServiceTest {

    @Autowired
    private IBizHrsUserInfoService bizHrsUserInfoService;

    /**
     * 测试导出所有人社厅人员数据按单位分组
     */
    @Test
    public void testExportAllUsersByUnit() {
        try {
            MockHttpServletResponse response = new MockHttpServletResponse();
            Boolean result = bizHrsUserInfoService.exportAllUsersByUnit(response);
            
            log.info("导出结果: {}", result);
            log.info("响应内容类型: {}", response.getContentType());
            log.info("响应头: {}", response.getHeaderNames());
            
            if (result) {
                log.info("导出成功，生成的文件大小: {} bytes", response.getContentAsByteArray().length);
            } else {
                log.warn("导出失败");
            }
            
        } catch (Exception e) {
            log.error("测试导出功能时发生异常", e);
        }
    }
}
