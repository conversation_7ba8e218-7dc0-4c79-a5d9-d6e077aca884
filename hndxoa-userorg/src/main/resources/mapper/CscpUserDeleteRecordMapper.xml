<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ctsi.ssdc.admin.repository.CscpUserDeleteRecordRepository">

    <resultMap id="BaseResultMap" type="com.ctsi.ssdc.admin.domain.CscpUserDeleteRecord">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="user_id" property="userId" jdbcType="BIGINT"/>
        <result column="login_name" property="loginName" jdbcType="VARCHAR"/>
        <result column="real_name_start" property="realNameStart" jdbcType="VARCHAR"/>
        <result column="real_name_end" property="realNameEnd" jdbcType="VARCHAR"/>
        <result column="password" property="password" jdbcType="VARCHAR"/>
        <result column="real_name" property="realName" jdbcType="VARCHAR"/>
        <result column="email" property="email" jdbcType="VARCHAR"/>
        <result column="mobile_end" property="mobileEnd" jdbcType="VARCHAR"/>
        <result column="mobile_middle" property="mobileMiddle" jdbcType="VARCHAR"/>
        <result column="mobile_start" property="mobileStart" jdbcType="VARCHAR"/>
        <result column="mobile" property="mobile" jdbcType="VARCHAR"/>
        <result column="last_login" property="lastLogin" jdbcType="TIMESTAMP"/>
        <result column="tenant_id" property="tenantId" jdbcType="BIGINT"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="create_by" property="createBy" jdbcType="BIGINT"/>
        <result column="create_name" property="createName" jdbcType="VARCHAR"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="update_by" property="updateBy" jdbcType="BIGINT"/>
        <result column="update_name" property="updateName" jdbcType="VARCHAR"/>
        <result column="order_by" property="orderBy" jdbcType="INTEGER"/>
        <result column="status" property="status" jdbcType="INTEGER"/>
        <result column="display" property="display" jdbcType="INTEGER"/>
        <result column="office_phone" property="officePhone" jdbcType="VARCHAR"/>
        <result column="app_version" property="appVersion" jdbcType="INTEGER"/>
        <result column="statistics" property="statistics" jdbcType="INTEGER"/>
        <result column="main_tariff" property="mainTariff" jdbcType="VARCHAR"/>
        <result column="surcharge" property="surcharge" jdbcType="VARCHAR"/>
        <result column="deleted" property="deleted" jdbcType="INTEGER"/>
        <result column="sex" property="sex" jdbcType="INTEGER"/>
        <result column="is_write_sign" property="isWriteSign" jdbcType="INTEGER"/>
        <result column="app_version_name" property="appVersionName" jdbcType="VARCHAR"/>
        <result column="withdraw_condition" property="withdrawCondition" jdbcType="INTEGER"/>
        <result column="audit_sms" property="auditSms" jdbcType="INTEGER"/>
        <result column="is_address_unit" property="isAddressUnit" jdbcType="INTEGER"/>
        <result column="backup_mobile" property="backupMobile" jdbcType="VARCHAR"/>
        <result column="str_id" property="strId" jdbcType="VARCHAR"/>
        <result column="str_classified" property="strClassified" jdbcType="VARCHAR"/>
        <result column="str_id_card_no" property="strIdCardNo" jdbcType="VARCHAR"/>
        <result column="str_unit_trust_no" property="strUnitTrustNo" jdbcType="VARCHAR"/>
        <result column="user_origin" property="userOrigin" jdbcType="VARCHAR"/>
        <result column="signature_image_url" property="signatureImageURL" jdbcType="VARCHAR"/>
        <result column="sjs_str_id" property="sjsStrId" jdbcType="VARCHAR"/>
        <result column="security_classification_code" property="securityClassificationCode" jdbcType="VARCHAR"/>
        <result column="security_classification_code_name" property="securityClassificationCodeName" jdbcType="VARCHAR"/>
        <result column="examine_status" property="examineStatus" jdbcType="INTEGER"/>
        <result column="stamp_url" property="stampUrl" jdbcType="VARCHAR"/>
        <result column="start_no" property="startNo" jdbcType="INTEGER"/>
        <result column="westone_user_id" property="westoneUserId" jdbcType="VARCHAR"/>
        <result column="hmac_mobile" property="hmacMobile" jdbcType="VARCHAR"/>
        <result column="id_card_no" property="idCardNo" jdbcType="VARCHAR"/>
        <result column="person_label" property="personLabel" jdbcType="VARCHAR"/>
        <result column="is_display" property="isDisplay" jdbcType="INTEGER"/>
        <result column="delete_name" property="deleteName" jdbcType="VARCHAR"/>
        <result column="delete_time" property="deleteTime" jdbcType="TIMESTAMP"/>
        <result column="delete_by" property="deleteBy" jdbcType="BIGINT"/>
    </resultMap>

</mapper>
