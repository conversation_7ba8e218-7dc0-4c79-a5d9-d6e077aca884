<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ctsi.ssdc.mapper.TDeptManagementAuthorityMapper">

    <update  id="deleteByUserIdAndRoleId">
        UPDATE cscp_user_role
        SET deleted = 1
        WHERE user_id = #{userId}
        AND role_id = #{roleId}
        AND deleted = 0;
    </update >
</mapper>
