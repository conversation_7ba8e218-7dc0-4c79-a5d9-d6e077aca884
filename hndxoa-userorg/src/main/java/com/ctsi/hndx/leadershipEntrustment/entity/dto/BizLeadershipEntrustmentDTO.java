package com.ctsi.hndx.leadershipEntrustment.entity.dto;

import com.ctsi.hndx.common.BaseDtoEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;

/**
 * <p>
 * 领导委托
 * </p>
 *
 * <AUTHOR>
 * @since 2022-02-09
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "BizLeadershipEntrustmentDTO对象", description = "领导委托")
public class BizLeadershipEntrustmentDTO extends BaseDtoEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 联络员id
     */
    @ApiModelProperty(value = "联络员id", required = true)
    @NotNull(message = "联络员id不能为空")
    private Long liaisonManId;

    /**
     * 联络员名称
     */
    @ApiModelProperty(value = "联络员名称", required = true)
    @NotNull(message = "联络员名称不能为空")
    private String liaisonManName;

    /**
     * 联络员单位id
     */
    @ApiModelProperty(value = "联络员单位id", required = true)
    @NotNull(message = "联络员单位id不能为空")
    private Long liaisonManUnitId;

    /**
     * 联络员单位名称
     */
    @ApiModelProperty(value = "联络员单位名称", required = true)
    @NotNull(message = "联络员单位名称不能为空")
    private String liaisonManUnitName;

    /**
     * 联络员部门id
     */
    @ApiModelProperty(value = "联络员部门id", required = true)
    @NotNull(message = "联络员部门id不能为空")
    private Long liaisonManBranchId;

    /**
     * 联络员部门名称
     */
    @ApiModelProperty(value = "联络员部门名称", required = true)
    @NotNull(message = "联络员部门名称不能为空")
    private String liaisonManBranchName;

    /**
     * 联络员手机号码
     */
    @ApiModelProperty(value = "联络员手机号码", required = true)
    @NotNull(message = "联络员手机号码不能为空")
    private String liaisonManTelephone;

    /**
     * 领导id
     */
    @ApiModelProperty(value = "领导id", required = true)
    @NotNull(message = "领导id不能为空")
    private Long leaderId;

    /**
     * 领导名称
     */
    @ApiModelProperty(value = "领导名称", required = true)
    @NotNull(message = "领导名称不能为空")
    private String leaderName;

    /**
     * 领导单位id
     */
    @ApiModelProperty(value = "领导单位id", required = true)
    @NotNull(message = "领导单位id不能为空")
    private Long leaderUnitId;

    /**
     * 领导单位名称
     */
    @ApiModelProperty(value = "领导单位名称", required = true)
    @NotNull(message = "领导单位名称不能为空")
    private String leaderUnitName;

    /**
     * 领导部门id
     */
    @ApiModelProperty(value = "领导部门id", required = true)
    @NotNull(message = "领导部门id不能为空")
    private Long leaderBranchId;

    /**
     * 领导部门名称
     */
    @ApiModelProperty(value = "领导部门名称", required = true)
    @NotNull(message = "领导部门名称不能为空")
    private String leaderBranchName;

    /**
     * 领导手机号码
     */
    @ApiModelProperty(value = "领导手机号码", required = true)
    @NotNull(message = "领导手机号码不能为空")
    private String leaderTelephone;

    /**
     * 主联络员 0否 1是
     */
    @ApiModelProperty(value = "主联络员 0否 1是")
    private int mainLiaisonMan;
}
