package com.ctsi.hndx.birthdayWishes.controller;

import com.ctsi.hndx.birthdayWishes.entity.dto.SysBirthdayStatusDTO;
import com.ctsi.hndx.birthdayWishes.entity.dto.SysBirthdayWishesDTO;
import com.ctsi.hndx.birthdayWishes.service.ISysBirthdayWishesService;
import com.ctsi.hndx.annotations.ResponseResultVo;
import com.ctsi.hndx.common.BaseController;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.enums.DBOperation;
import com.ctsi.hndx.result.ResultCode;
import com.ctsi.hndx.result.ResultVO;
import com.ctsi.ssdc.annotation.OperationLog;
import com.ctsi.ssdc.model.PageResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.text.ParseException;
import java.util.List;


/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-02-18
 */

@Slf4j
@RestController
@ResponseResultVo
@RequestMapping("/api/birthdayWishes")
@Api(value = "生日祝福", tags = "生日祝福接口")
public class SysBirthdayWishesController extends BaseController {

    @Autowired
    private ISysBirthdayWishesService sysBirthdayWishesService;


    /**
     * 新增生日祝福语.
     */
    @PostMapping("/create")
    @ApiOperation(value = "新增生日祝福语", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.ADD, message = "新增生日祝福语")
    // @PreAuthorize("@permissionService.hasPermi('cscp.bizCloudDisk.add')")
    public ResultVO<SysBirthdayWishesDTO> create(@RequestBody SysBirthdayWishesDTO birthdayWishesDTO) {
        SysBirthdayWishesDTO result = sysBirthdayWishesService.create(birthdayWishesDTO);
        return ResultVO.success(result);
    }

    /**
     * 编辑生日祝福语.
     */
    @PostMapping("/updateWishes")
    @ApiOperation(value = "编辑生日祝福语", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.ADD, message = "编辑")
    public ResultVO updateWishes(@RequestBody SysBirthdayWishesDTO birthdayWishesDTO) {
        return ResultVO.success(sysBirthdayWishesService.updateWishes(birthdayWishesDTO));
    }

    /**
     * 启用本条生日祝福.
     */
    @PostMapping("/updateStatus")
    @ApiOperation(value = "启用本条生日祝福", notes = "传入id")
    @OperationLog(dBOperation = DBOperation.ADD, message = "启用")
    public ResultVO updateStatus(@RequestBody SysBirthdayStatusDTO sysBirthdayStatusDTO) {
        return ResultVO.success(sysBirthdayWishesService.updateStatus(sysBirthdayStatusDTO));
    }


    /**
     * 删除存在数据.
     */
    @DeleteMapping("/delete")
    @OperationLog(dBOperation = DBOperation.DELETE, message = "删除生日祝福语")
    @ApiOperation(value = "删除生日祝福语", notes = "传入参数")
    // @PreAuthorize("@permissionService.hasPermi('cscp.bizCloudDisk.delete')")
    public ResultVO delete(@RequestBody List<Long> listId) {
        Integer flag = sysBirthdayWishesService.delete(listId);
        if (flag != 0) {
            return ResultVO.success(flag);
        } else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }

    /**
     * 翻页查询生日祝福语
     */
    @GetMapping("/queryBirthdayWishesPage")
    @ApiOperation(value = "翻页查询生日祝福语", notes = "传入参数")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
    public ResultVO<PageResult<SysBirthdayWishesDTO>> queryBirthdayWishesPage(SysBirthdayWishesDTO birthdayWishesDTO, BasePageForm basePageForm) {
        return ResultVO.success(sysBirthdayWishesService.queryBirthdayWishesPage(birthdayWishesDTO, basePageForm));
    }

    /**
     * 查询当前用户今天是否生日
     */
    @GetMapping("/queryBirthdayDate/{userId}")
    @ApiOperation(value = "查询当前用户今天是否生日", notes = "传入userId")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
    public ResultVO<PageResult<SysBirthdayWishesDTO>> queryBirthdayDate(@PathVariable Long userId) throws ParseException {
        return ResultVO.success(sysBirthdayWishesService.queryBirthdayDate(userId));
    }

}
