package com.ctsi.hndx.addrbook.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ctsi.hndx.addrbook.entity.TAddressBook;
import com.ctsi.hndx.addrbook.entity.dto.*;
import com.ctsi.hndx.common.MybatisBaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 通讯录 通讯录表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-11
 */
public interface TAddressBookMapper extends MybatisBaseMapper<TAddressBook> {


    /**
     * 模糊查询通讯录人员
     *
     * @param queryTAddressBookConditionDTO
     * @return
     */
    List<QueryAddressBookUserDTO> querytaddressbookuser(@Param("parm") QueryTAddressBookConditionDTO queryTAddressBookConditionDTO);

    /**
     * 查询某个单位的通讯录成员
     *
     * @param mPlusPageByBasePage
     * @param entityDTO
     * @return
     */
    @InterceptorIgnore(tenantLine = "true")
    IPage<QueryListPageDTO> queryListPage(IPage mPlusPageByBasePage, @Param("condition") QueryTAddressBookDTO entityDTO);

    /**
     * 模糊查询通讯录人员
     *
     * @param queryTAddressBookConditionDTO
     * @return
     */
    @InterceptorIgnore(tenantLine = "true")
    List<QueryAddressBookUserDTO> querytaddressbookuserPc(@Param("parm") QueryTAddressBookConditionDTO queryTAddressBookConditionDTO);

    /**
     * 模糊查询通讯录人员APP
     *
     * @param queryTAddressBookConditionDTO
     * @return
     */
    @InterceptorIgnore(tenantLine = "true")
    List<QueryAddressBookUserDTO> querytaddressbookuserapp(@Param("parm") QueryTAddressBookConditionDTO queryTAddressBookConditionDTO);


    @InterceptorIgnore(tenantLine = "true")
    Integer selectAdressCount(@Param("orgId") Long orgId, @Param("encrypt") String encrypt);

    @InterceptorIgnore(tenantLine = "true")
    List<TAddressBook> selectAdressInfo(@Param("orgId") Long orgId, @Param("encrypt")String encrypt, @Param("start")Integer start, @Param("pageSize")Integer pageSize);

    /**
     * 查询当前机构下挂在的用户
     * */
    @InterceptorIgnore(tenantLine = "true")
    List<TAddressBookUserDTO> selectCurrentOrgTAddressBookList(@Param("orgId") Long orgId);

    @InterceptorIgnore(tenantLine = "true")
    List<TAddressBookAuthorizeDTO> selectUserLabelIdList(@Param("ogrIdList") List<Long> ogrIdList);

    @InterceptorIgnore(tenantLine = "true")
    List<QueryAddressBookUserDTO> selectAddressBookUserApp(@Param("searchPhone")  String searchPhone,
                                                                 @Param("searchName")  String searchName,
                                                                 @Param("orgName")  String orgName,
                                                                @Param("limit")  Integer limit);
    /**
     * 【有全文搜索，外网使用】通讯录用户查询 (APP)
     *
     * @param searchPhone    搜索的电话（加密后）
     * @param searchName     搜索的姓名（加密后）
     * @param orgName        搜索的机构名
     * @param currentOrgCode 当前单位的机构编码路径
     * @param highestOrgCode 最高权限的机构编码路径
     * @return 用户列表
     */
    @InterceptorIgnore(tenantLine = "true")
    IPage<QueryAddressBookUserDTO> selectAddressBookUserBasic(IPage mPlusPageByBasePage, @Param("searchPhone")  String searchPhone,
                                                              @Param("searchName")  String searchName,
                                                              @Param("orgName")  String orgName,
                                                              @Param("currentOrgCode")  String currentOrgCode,
                                                              @Param("highestOrgCode")  String highestOrgCode);
    /**
     * 【有全文搜索，外网使用】通讯录用户查询 (APP)
     *
     * @param searchPhone    搜索的电话（加密后）
     * @param searchName     搜索的姓名（加密后）
     * @param orgName        搜索的机构名
     * @param currentOrgCode 当前单位的机构编码路径
     * @param highestOrgCode 最高权限的机构编码路径
     * @return 用户列表
     */
    @InterceptorIgnore(tenantLine = "true")
    IPage<QueryAddressBookUserDTO> selectAddressBookUserFullTextSearch(IPage mPlusPageByBasePage, @Param("searchPhone") String searchPhone,
                                                                       @Param("searchName") String searchName,
                                                                       @Param("orgName") String orgName,
                                                                       @Param("currentOrgCode") String currentOrgCode,
                                                                       @Param("highestOrgCode") String highestOrgCode);
}
