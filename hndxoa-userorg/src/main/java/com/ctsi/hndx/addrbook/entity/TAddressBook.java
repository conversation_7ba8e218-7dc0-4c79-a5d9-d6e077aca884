package com.ctsi.hndx.addrbook.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.ctsi.hndx.mybatisplus.typerhander.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 通讯录 通讯录表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-11
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName(autoResultMap = true)
@ApiModel(value = "TAddressBook对象", description = "通讯录 通讯录表")
public class TAddressBook implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 用户ID
     */
    @ApiModelProperty(value = "用户ID")
    @TableField("USER_ID")
    private Long userId;

    /**
     * 真实姓名
     */
    @ApiModelProperty(value = "真实姓名")
    @TableField(typeHandler = WestoneStrFullFromBase64ValueDesHandler.class)
    private String realName;

    /**
     * 真实姓名首字母拼音
     */
    @ApiModelProperty(value = "真实姓名首字母拼音")
    private String realPinYin;

    /**
     * 姓名分割加密,逗号隔开
     */
    @ApiModelProperty(value = "姓名分割加密,逗号隔开")
    @TableField(value = "LAST_NAME_ENCRYPT", typeHandler = WestoneStrDivisionFromBase64ValueDesHandler.class)
    private String lastNameEncrypt;

    /**
     * 名加密
     */
    @ApiModelProperty(value = "名加密")
    @TableField(value = "FIRST_NAME_ENCRYPT", typeHandler = WestoneStrDivisionFromBase64ValueDesHandler.class)
    private String firstNameEncrypt;

    /**
     * 默认手机号
     */
    @ApiModelProperty(value = "默认手机号")
    @TableField(value = "DEFAULT_PHONE", typeHandler = WestoneNumberFullValueFromBase64DesHandler.class)
    private String defaultPhone;

    /**
     * 默认手机加密1段
     */
    @ApiModelProperty(value = "默认手机加密1段")
    @TableField(value = "DEFAULT_PHONE_ENCRYPT_1", typeHandler = WestoneNumberDivisionFromBase64DesHandler.class)
    private String defaultPhoneEncrypt1;

//    /**
//     * 默认手机加密2段
//     */
//    @ApiModelProperty(value = "默认手机加密2段")
//    @TableField(value = "DEFAULT_PHONE_ENCRYPT_2", typeHandler = WestoneNumberDivisionDesHandler.class)
//    private String defaultPhoneEncrypt2;
//
//    /**
//     * 默认手机加密3段
//     */
//    @ApiModelProperty(value = "默认手机加密3段")
//    @TableField(value = "DEFAULT_PHONE_ENCRYPT_3", typeHandler = WestoneNumberDivisionDesHandler.class)
//    private String defaultPhoneEncrypt3;

    /**
     * 备用手机号
     */
    @ApiModelProperty(value = "备用手机号")
    @TableField(value = "RESERVE_PHONE", typeHandler = WestoneNumberFullValueFromBase64DesHandler.class)
    private String reservePhone;

    /**
     * 备用手机号加密1段
     */
    @ApiModelProperty(value = "备用手机号加密1段")
    @TableField(value = "RESERVE_PHONE_ENCRYPT_1", typeHandler = WestoneNumberDivisionFromBase64DesHandler.class)
    private String reservePhoneEncrypt1;

    /**
     * 备用手机号加密2段
     */
    @ApiModelProperty(value = "备用手机号加密2段")
    @TableField(value = "RESERVE_PHONE_ENCRYPT_2", typeHandler = WestoneNumberDivisionFromBase64DesHandler.class)
    private String reservePhoneEncrypt2;

    /**
     * 备用手机号加密3段
     */
    @ApiModelProperty(value = "备用手机号加密3段")
    @TableField(value = "RESERVE_PHONE_ENCRYPT_3", typeHandler = WestoneNumberDivisionFromBase64DesHandler.class)
    private String reservePhoneEncrypt3;

    /**
     * 座机电话
     */
    @ApiModelProperty(value = "座机电话")
    @TableField(value = "TELEPHONE", typeHandler = FullValueDesHandler.class)
    private String telephone;

    /**
     * 座机电话加密1段
     */
    @ApiModelProperty(value = "座机电话加密1段")
    @TableField(value = "TELEPHONE_ENCRYPT_1", typeHandler = FullValueDesHandler.class)
    private String telephoneEncrypt1;

//    /**
//     * 座机电话加密2段
//     */
//    @ApiModelProperty(value = "座机电话加密2段")
//    @TableField(value = "TELEPHONE_ENCRYPT_2", typeHandler = FullValueDesHandler.class)
//    private String telephoneEncrypt2;

    @ApiModelProperty(value = "职务")
    @TableField(value = "JOB_TITLE")
    private String jobTitle;

    /**
     * 秘书名称
     */
    @ApiModelProperty(value = "秘书名称")
    @TableField(value = "SECRETARY_NAME")
    private String secretaryName;

    /**
     * 秘书手机号
     */
    @ApiModelProperty(value = "秘书手机号")
    @TableField(value = "SECRETARY_PHONE", typeHandler = WestoneNumberFullValueFromBase64DesHandler.class)
    private String secretaryPhone;

    /**
     * 秘书手机号加密1段
     */
    @ApiModelProperty(value = "秘书手机号加密1段")
    @TableField(value = "SECRETARY_PHONE_ENCRYPT_1", typeHandler = WestoneNumberDivisionFromBase64DesHandler.class)
    private String secretaryPhoneEncrypt1;

//    /**
//     * 秘书手机号加密2段
//     */
//    @ApiModelProperty(value = "秘书手机号加密2段")
//    @TableField(value = "SECRETARY_PHONE_ENCRYPT_2", typeHandler = WestoneNumberDivisionDesHandler.class)
//    private String secretaryPhoneEncrypt2;

//    /**
//     * 秘书手机号加密3段
//     */
//    @ApiModelProperty(value = "秘书手机号加密3段")
//    @TableField(value = "SECRETARY_PHONE_ENCRYPT_3", typeHandler = WestoneNumberDivisionDesHandler.class)
//    private String secretaryPhoneEncrypt3;

    /**
     * 可见范围
     */
    @ApiModelProperty(value = "可见范围")
    @TableField("ADDRESS_BOOK_LABEL_ID")
    private String addressBookLableId;


    /**
     * 手机号是否显示 0：不显示 1：显示
     */
    @ApiModelProperty(value = "手机号是否显示 0：不显示 1：显示 ")
    @TableField("IS_SHOW")
    private Integer whetherShow;

    @ApiModelProperty(value = "通讯录是否显示  0-不是 1-是")
    @TableField("display")
    private Integer display;

    /**
     * 性别 0：男， 1：女
     */
    @ApiModelProperty(value = "性别 0：男， 1：女")
    @TableField("sex")
    private Integer sex;

    /**
     * 排序
     */
    @ApiModelProperty(value = "排序")
    @TableField("SORT")
    private Integer sort;

    @TableField(fill = FieldFill.INSERT)
    private Long createBy;

    @TableField(fill = FieldFill.INSERT)
    private String createName;

    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @TableField(fill = FieldFill.UPDATE, select = false)
    private LocalDateTime updateTime;

    @TableField(fill = FieldFill.UPDATE, select = false)
    private Long updateBy;

    @TableField(fill = FieldFill.UPDATE, select = false)
    private String updateName;

    @ApiModelProperty("通讯录手机号码HMAC值，完整性校验")
    private String hmacDefaultPhone;

    /**
     * 新增类型
     */
    @ApiModelProperty(value = "新增类型 1:系统人员新增 2:通讯录新增")
    private Integer AddType;

    @TableLogic
    @TableField(select = false,fill = FieldFill.INSERT)
    private Integer deleted;

    @ApiModelProperty(value = "用户状态：1表示激活，0表示锁定，默认激活")
    @TableField(fill = FieldFill.INSERT)
    private Integer status;

}
