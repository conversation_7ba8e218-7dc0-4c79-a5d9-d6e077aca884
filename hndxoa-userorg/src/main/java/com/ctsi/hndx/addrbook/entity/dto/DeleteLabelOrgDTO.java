package com.ctsi.hndx.addrbook.entity.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DeleteLabelOrgDTO {

    @ApiModelProperty(value = "机构id", required = true)
    @NotNull(message = "机构id不能为空")
    private List<Long> orgId;

    @ApiModelProperty(value = "标签id", required = true)
    @NotNull(message = "标签id不能为空")
    private Long labelId;
}
