package com.ctsi.hndx.addrbook.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ctsi.hndx.addrbook.entity.TAddressBookAuthorize;
import com.ctsi.hndx.addrbook.entity.dto.TAddressBookAuthorizeDTO;
import com.ctsi.hndx.addrbook.mapper.TAddressBookAuthorizeMapper;
import com.ctsi.hndx.addrbook.service.ITAddressBookAuthorizeService;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.common.SysBaseServiceImpl;
import com.ctsi.hndx.utils.BeanConvertUtils;
import com.ctsi.hndx.utils.ListCopyUtil;
import com.ctsi.hndx.utils.PageHelperUtil;
import com.ctsi.ssdc.model.PageResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <p>
 * 通讯录授权表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-21
 */
@Slf4j
@Service
public class TAddressBookAuthorizeServiceImpl extends SysBaseServiceImpl<TAddressBookAuthorizeMapper, TAddressBookAuthorize> implements ITAddressBookAuthorizeService {

    @Autowired
    private TAddressBookAuthorizeMapper tAddressBookAuthorizeMapper;

    /**
     * 翻页
     *
     * @param entityDTO
     * @param basePageForm
     * @return
     */
    @Override
    public PageResult<TAddressBookAuthorizeDTO> queryListPage(TAddressBookAuthorizeDTO entityDTO, BasePageForm basePageForm) {
        //设置条件
        LambdaQueryWrapper<TAddressBookAuthorize> queryWrapper = new LambdaQueryWrapper();

        IPage<TAddressBookAuthorize> pageData = tAddressBookAuthorizeMapper.selectPage(
             PageHelperUtil.getMPlusPageByBasePage(basePageForm), queryWrapper);
        //返回
        IPage<TAddressBookAuthorizeDTO> data  = pageData.convert(entity -> BeanConvertUtils.copyProperties(entity,TAddressBookAuthorizeDTO.class));

        return new PageResult<TAddressBookAuthorizeDTO>(data.getRecords(),
            data.getTotal(), data.getCurrent());
    }

    /**
     * 列表查询
     *
     * @param entityDTO
     * @return
     */
    @Override
    public List<TAddressBookAuthorizeDTO> queryList(TAddressBookAuthorizeDTO entityDTO) {
        LambdaQueryWrapper<TAddressBookAuthorize> queryWrapper = new LambdaQueryWrapper();
            List<TAddressBookAuthorize> listData = tAddressBookAuthorizeMapper.selectList(queryWrapper);
            List<TAddressBookAuthorizeDTO> TAddressBookAuthorizeDTOList = ListCopyUtil.copy(listData, TAddressBookAuthorizeDTO.class);
        return TAddressBookAuthorizeDTOList;
    }

    /**
     * 单个查询
     *
     * @param id the id of the entity
     * @return
     */
    @Override
    public TAddressBookAuthorizeDTO findOne(Long id) {
        TAddressBookAuthorize  tAddressBookAuthorize =  tAddressBookAuthorizeMapper.selectById(id);
        return  BeanConvertUtils.copyProperties(tAddressBookAuthorize,TAddressBookAuthorizeDTO.class);
    }


    /**
     * 新增
     *
     * @param entityDTO the entity to create
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public TAddressBookAuthorizeDTO create(TAddressBookAuthorizeDTO entityDTO) {
       TAddressBookAuthorize tAddressBookAuthorize =  BeanConvertUtils.copyProperties(entityDTO,TAddressBookAuthorize.class);
        save(tAddressBookAuthorize);
        return  BeanConvertUtils.copyProperties(tAddressBookAuthorize,TAddressBookAuthorizeDTO.class);
    }

    /**
     * 修改
     *
     * @param entity the entity to update
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int update(TAddressBookAuthorizeDTO entity) {
        TAddressBookAuthorize tAddressBookAuthorize = BeanConvertUtils.copyProperties(entity,TAddressBookAuthorize.class);
        return tAddressBookAuthorizeMapper.updateById(tAddressBookAuthorize);
    }

    /**
     * 删除
     *
     * @param id the id of the entity
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int delete(Long id) {
        return tAddressBookAuthorizeMapper.deleteById(id);
    }


    /**
     * 验证是否存在
     *
     * @param TAddressBookAuthorizeId
     * @return
     */
    @Override
    public boolean existByTAddressBookAuthorizeId(Long TAddressBookAuthorizeId) {
        if (TAddressBookAuthorizeId != null) {
            LambdaQueryWrapper<TAddressBookAuthorize> queryWrapper = new LambdaQueryWrapper();
            queryWrapper.eq(TAddressBookAuthorize::getId, TAddressBookAuthorizeId);
            List<TAddressBookAuthorize> result = tAddressBookAuthorizeMapper.selectList(queryWrapper);
            return result.size() > 0;
        }
        return true;
    }

    /**
    * 批量新增
    *
    */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertBatch(List<TAddressBookAuthorizeDTO> dataList) {
        List<TAddressBookAuthorize> result = ListCopyUtil.copy(dataList, TAddressBookAuthorize.class);
        return saveBatch(result);
    }


}
