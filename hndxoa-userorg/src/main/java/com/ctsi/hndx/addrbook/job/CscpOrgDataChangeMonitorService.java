package com.ctsi.hndx.addrbook.job;


import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ctsi.hndx.addrbook.entity.CscpOrgLog;
import com.ctsi.hndx.addrbook.entity.CscpUserLog;
import com.ctsi.hndx.addrbook.service.ITAddressBookOrgService;
import com.ctsi.hndx.addrbook.service.impl.CscpOrgLogServiceImpl;
import com.ctsi.hndx.enums.OrgTypeEnum;
import com.ctsi.hndx.utils.Convert;
import com.ctsi.hndx.utils.DateUtils;
import com.ctsi.hndx.utils.JobTaskLock;
import com.ctsi.ssdc.admin.domain.dto.CscpOrgDTO;
import com.ctsi.ssdc.admin.service.CscpOrgService;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.net.UnknownHostException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.concurrent.CompletableFuture;

@Slf4j
@Component
@EnableScheduling
@SuppressWarnings("all")
public class CscpOrgDataChangeMonitorService {

    @Autowired
    private CscpOrgLogServiceImpl changeLogService;

    @Autowired
    private ITAddressBookOrgService addressBookOrgService;

    @Autowired
    private CscpOrgService cscpOrgService;

    @Autowired
    private JobTaskLock jobTaskLock;

    private static final DateTimeFormatter pattern = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");


    //@Scheduled(cron = "0 0/1 * * * *")
    @Scheduled(fixedDelay = 60000)
    public void processChanges() throws UnknownHostException {
        log.info("CscpOrgDataChangeMonitorService processChanges start");
        String jobKey = this.getClass().getSimpleName() + "_" + new Exception().getStackTrace()[0].getMethodName();
        if (jobTaskLock.isJobTaskLock(jobKey, 30)) {
            log.info("CscpOrgDataChangeMonitorService processChanges log true");
            LambdaQueryWrapper<CscpOrgLog> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(CscpOrgLog::getSfwProcessed, 0);
            queryWrapper.last("LIMIT 10000"); // 添加LIMIT 10000限制
            List<CscpOrgLog> changes = changeLogService.selectListNoAdd(queryWrapper);

            if (CollUtil.isNotEmpty(changes)) {
                List<List<CscpOrgLog>> partitionedChanges = Lists.partition(changes, 2000);
                partitionedChanges.forEach(batch -> {
                    CompletableFuture.runAsync(() -> {
                        this.task(batch); // 处理当前批次的数据，而不是整个列表
                    });
                });
            }
        }
        log.info("CscpOrgDataChangeMonitorService processChanges end");
    }

    private void task(List<CscpOrgLog> changes) {
        for (CscpOrgLog change : changes) {
            try {
                // 根据操作类型进行处理
                switch (change.getChangeType()) {
                    case "INSERT":
                        this.handleFullTextInsert(change);
                        break;
                    case "UPDATE":
                        this.handleFullTextUpdate(change);
                        break;
                    case "DELETE":
                        this.handleFullTextDelete(change);
                        break;
                    default:
                        throw new IllegalArgumentException("未知的操作类型：" + change.getChangeType());
                }
                // 标记已处理
                change.setSfwProcessed(1);
                changeLogService.updateById(change);
            } catch (Exception e) {
                e.printStackTrace();
                change.setErrorMessage(e.getMessage());
                changeLogService.updateById(change);
            }
        }
    }

//    @Scheduled(cron = "0 0 1 * * ?")
    public void removeChanges() {
        LambdaQueryWrapper<CscpOrgLog> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CscpOrgLog::getSfwProcessed, 1);
        LocalDateTime time = DateUtils.getLocalDateTime().minusDays(1);
        queryWrapper.le(CscpOrgLog::getChangeTime, time.format(pattern));
        List<CscpOrgLog> changeLogs = changeLogService.selectListNoAdd(queryWrapper);
        if (CollectionUtils.isNotEmpty(changeLogs)) {
            changeLogService.deleteBatch(changeLogs);
        }
    }

    private void handleFullTextInsert(CscpOrgLog change){
        log.info("CscpOrgDataChangeMonitorService handleFullTextInsert start");
        try {
            if ("cscp_org".equals(change.getTableName())) {
                CscpOrgDTO cscpOrgDTO = cscpOrgService.findOne(Convert.toLong(change.getPrimaryKey()));
                if (null == cscpOrgDTO) {
                    return;
                }
                //生成本单位默认标签
                if (OrgTypeEnum.ORG_TYPE_2.getCode().equals(cscpOrgDTO.getType())) {
                    //租户管理员新增标签生成本单位标签，允许该单位下的所有用户查看这个标签
                    addressBookOrgService.uniformAgencySaveOrUpdateDefaultLabel(cscpOrgDTO);
                } else if (OrgTypeEnum.ORG_TYPE_3.getCode().equals(cscpOrgDTO.getType())) {
                    //部门和本单位标签绑定
                    addressBookOrgService.uniformAgencySaveOrUpdateGenerateOrgLabel(cscpOrgDTO);            }
            }
        } catch (Exception e) {
            log.error("CscpOrgDataChangeMonitorService handleFullTextInsert error : {}",e.getMessage());
        }
    }

    private void handleFullTextUpdate(CscpOrgLog change){
        log.info("CscpOrgDataChangeMonitorService handleFullTextUpdate start");
        if ("cscp_org".equals(change.getTableName())) {
            try {
                // 逻辑删除
                CscpOrgDTO cscpOrgDTO = cscpOrgService.findOne(Convert.toLong(change.getPrimaryKey()));
                if (null == cscpOrgDTO) {
                   //暂时不处理
                } else {
                    if (OrgTypeEnum.ORG_TYPE_2.getCode().equals(cscpOrgDTO.getType())) {
                        //租户管理员新增标签生成本单位标签，允许该单位下的所有用户查看这个标签
                        addressBookOrgService.uniformAgencySaveOrUpdateDefaultLabel(cscpOrgDTO);
                    } else if (OrgTypeEnum.ORG_TYPE_3.getCode().equals(cscpOrgDTO.getType())) {
                        //部门和本单位标签绑定
                        addressBookOrgService.uniformAgencySaveOrUpdateGenerateOrgLabel(cscpOrgDTO);

                    }
                }
            } catch (Exception e) {
                log.info("CscpOrgDataChangeMonitorService handleFullTextUpdate error : {}",e.getMessage());
            }
        }


    }

    private void handleFullTextDelete(CscpOrgLog change){
        //暂时不处理

    }


}
