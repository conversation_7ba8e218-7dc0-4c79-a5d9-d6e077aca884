package com.ctsi.hndx.addrbook.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ctsi.hndx.addrbook.entity.dto.*;
import com.ctsi.hndx.addrbook.service.ITAddressBookService;
import com.ctsi.hndx.annotations.ResponseResultVo;
import com.ctsi.hndx.common.BaseController;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.enums.DBOperation;
import com.ctsi.hndx.result.ResultCode;
import com.ctsi.hndx.result.ResultVO;
import com.ctsi.hndx.utils.PageHelperUtil;
import com.ctsi.ssdc.annotation.OperationLog;
import com.ctsi.ssdc.model.PageResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;


/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-11
 */

@Slf4j
@RestController
@ResponseResultVo
@RequestMapping("/api/tAddressBook")
@Api(value = "通讯录人员 通讯录表", tags = "通讯录人员管理接口")
public class TAddressBookController extends BaseController {

    @Autowired
    private ITAddressBookService tAddressBookService;

    /**
     * 新增通讯录.
     */
    @PostMapping("/create")
    @ApiOperation(value = "新增通讯录", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.ADD, message = "新增通讯录")
    public ResultVO<CreateTAddressBookDTO> createTAddressBook(@RequestBody CreateTAddressBookDTO createTAddressBookDTO) {
        CreateTAddressBookDTO result = tAddressBookService.create(createTAddressBookDTO);
        return ResultVO.success(result);
    }

    /**
     * 更新存在数据.
     */
    @PostMapping("/update")
    @ApiOperation(value = "更新存在数据", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.UPDATE, message = "更新通讯录数据")
    public ResultVO add(@RequestBody CreateTAddressBookDTO updateTAddressBook) {
        Assert.notNull(updateTAddressBook.getId(), "general.IdNotNull");
        int count = tAddressBookService.update(updateTAddressBook,2);
        if (count > 0) {
            return ResultVO.success();
        } else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }

    /**
     * 人员管理中翻页查询通讯录信息.
     */
    @GetMapping("/queryTAddressBookPage")
    @ApiOperation(value = "人员管理中翻页查询通讯录信息", notes = "传入参数")
    public ResultVO<IPage<QueryListPageDTO>> queryTAddressBookPage(QueryTAddressBookDTO queryTAddressBookDTO, BasePageForm basePageForm) {
        IPage<QueryListPageDTO> tAddressBookList = tAddressBookService.queryListPage(queryTAddressBookDTO, basePageForm);
        return ResultVO.success(tAddressBookList);
    }

    /**
     * 查询单个通讯录信息(app)
     */
    @GetMapping("/findOne")
    @ApiOperation(value = "查询单个通讯录信息", notes = "传入参数")
    public ResultVO<FindOneDTO> findOne(@RequestParam(value = "addressId") Long addressId, @RequestParam(value = "orgId") Long orgId) {
        return ResultVO.success(tAddressBookService.findOne(addressId, orgId));
    }

    /**
     * 【PC端】通讯录导出
     */
    @GetMapping("/fileExport")
    @ApiOperation(value = "通讯录导出", notes = "传入参数")
    public ResultVO fileExport(HttpServletResponse response,
                               @RequestParam(value = "orgId", required = false) Long orgId) throws Exception {
        return ResultVO.success(tAddressBookService.fileExport(response, orgId));
    }

    /**
     * 条件查询标签下面所有的通讯录，分页
     */
    @GetMapping("/queryTAddressBookAllPage")
    @ApiOperation(value = "条件查询标签下面所有的通讯录,分页", notes = "传入参数")
    public ResultVO<PageResult<QueryListPageDTO>> queryTAddressBookAllPage(QueryTAddressBookAllDTO queryTAddressBookDTO, BasePageForm basePageForm) {
        return ResultVO.success(tAddressBookService.queryTAddressBookAllPage(queryTAddressBookDTO, basePageForm));
    }

    /**
     * 【APP模糊搜索】根据用户名称，手机号模糊，机构，拼音搜索（app）
     */
    @GetMapping("/queryTAddressBookUser")
    @ApiOperation(value = "根据用户名称，手机号模糊，机构，拼音搜索（app）", notes = "传入参数")
    public ResultVO<List<QueryAddressBookUserDTO>> queryTAddressBookUser(String nameOrPhone) {
        return ResultVO.success(tAddressBookService.queryTAddressBookUser(nameOrPhone));
    }

    /**
     * 【APP 通讯录分页搜索】根据用户名称，手机号模糊，机构，拼音搜索（app）
     */
    @GetMapping("/queryTAddressBookUserPage")
    @ApiOperation(value = " 通讯录分页搜索，根据用户名称，手机号模糊，机构，拼音搜索（app）", notes = "传入参数")
    public ResultVO<PageResult<QueryAddressBookUserDTO>> queryTAddressBookUserPage(String nameOrPhone, BasePageForm basePageForm) {
        return ResultVO.success(tAddressBookService.queryTAddressBookUserPage(nameOrPhone, basePageForm));
    }
    /**
     * NEW【APP模糊搜索】根据用户名称，手机号模糊，机构，拼音搜索（app）
     */
    @GetMapping("/queryTAddressBookUserNew")
    @ApiOperation(value = "NEW根据用户名称，手机号模糊，机构，拼音搜索（app）", notes = "传入参数")
    public ResultVO<PageResult<QueryAddressBookUserDTO>> queryTAddressBookUserNew(String nameOrPhone, BasePageForm basePageForm) {
        IPage iPage = PageHelperUtil.getMPlusPageByBasePage(basePageForm);
        IPage<QueryAddressBookUserDTO> page = tAddressBookService.queryTAddressBookUserNew(nameOrPhone, iPage);
        return ResultVO.success(new PageResult<>(page.getRecords(), page.getTotal(), basePageForm.getCurrentPage()));
    }
    // "token": "",


    // /**
    //  * 更新电话,手机
    //  */
    // @GetMapping("/updateEncrypt")
    // @ApiOperation(value = "更新电话,手机", notes = "传入参数")
    // public ResultVO<List<QueryAddressBookUserDTO>> updateEncrypt(String nameOrPhone) {
    //     //tAddressBookService.updateLastNameEncrypt();
    //     return ResultVO.success();
    // }


}
