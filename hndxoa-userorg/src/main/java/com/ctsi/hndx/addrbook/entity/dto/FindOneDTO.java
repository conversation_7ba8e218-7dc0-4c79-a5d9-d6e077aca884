package com.ctsi.hndx.addrbook.entity.dto;

import com.ctsi.hndx.common.BaseDtoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class FindOneDTO extends BaseDtoEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 真实姓名
     */
    @ApiModelProperty(value = "真实姓名")
    private String realName;

    /**
     * 单位名称
     */
    @ApiModelProperty(value = "单位名称")
    private String companyName;

    /**
     * 单位id
     */
    @ApiModelProperty(value = "单位id")
    private Long companyId;

    /**
     * 部门名称
     */
    @ApiModelProperty(value = "部门名称")
    private String orgName;

    /**
     * 部门id
     */
    @ApiModelProperty(value = "部门id")
    private Long orgId;

    /**
     * 默认手机号-
     */
    @ApiModelProperty(value = "手机号号码")
    private String defaultPhone;

    /**
     * 座机电话
     */
    @ApiModelProperty(value = "座机电话")
    private String telephone;

    /**
     * 秘书名称
     */
    @ApiModelProperty(value = "秘书名称")
    private String secretaryName;

    /**
     * 秘书手机号
     */
    @ApiModelProperty(value = "秘书手机号")
    private String secretaryPhone;

    /**
     * 职务
     */
    @ApiModelProperty(value = "职务")
    private String jobTitle;

    /**
     * 排序
     */
    @ApiModelProperty(value = "排序")
    private Integer sort;

    /**
     * 可见范围
     */
    @ApiModelProperty(value = "可见范围")
    private String addressBookLableId;

    /**
     * 是否显示 0：不显示 1：显示
     */
    @ApiModelProperty(value = "是否显示 0：不显示 1：显示 ")
    private Integer whetherShow;


    /**
     * 用户id
     */
    @ApiModelProperty(value = "用户id")
    private Long userId;
}
