package com.ctsi.hndx.addrbook.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ctsi.hndx.addrbook.entity.TAddressBook;
import com.ctsi.hndx.addrbook.entity.dto.*;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.common.SysBaseServiceI;
import com.ctsi.ssdc.model.PageResult;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * <p>
 * 通讯录 通讯录表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-11
 */
public interface ITAddressBookService extends SysBaseServiceI<TAddressBook> {


    /**
     * 分页查询（脱敏）
     *
     * @param entityDTO
     * @param page
     * @return
     */
    IPage<QueryListPageDTO> queryListPage(QueryTAddressBookDTO entityDTO, BasePageForm page);

    /**
     * 根据主键id获取单个对象
     *
     * @param addressId
     * @param orgId
     * @return
     */
    FindOneDTO findOne(Long addressId, Long orgId);

    /**
     * 新增
     *
     * @param createTAddressBookDTO
     * @return
     */
    CreateTAddressBookDTO create(CreateTAddressBookDTO createTAddressBookDTO);


    /**
     * 更新
     *
     * @param updateTAddressBook
     * @return
     */
    int update(CreateTAddressBookDTO updateTAddressBook,Integer type);


    /**
     * 通讯录机构需要的分页查询(Pc and app)
     *
     * @param entityDTO
     * @param page
     * @return
     */
    PageResult<ResOrgAddressBookDTO> queryListPage(ReqOrgAddressBookDTO entityDTO, BasePageForm page);

    PageResult<QueryAddressBookUserDTO> queryListPagePC(ReqOrgAddressBookDTO entityDTO, BasePageForm page);

    /**
     * 导出
     *
     * @param response
     * @throws IOException
     */
    Boolean fileExport(HttpServletResponse response,Long orgId);

    /**
     * 条件查询标签下面所有的通讯录
     *
     * @param queryTAddressBookDTO
     * @param basePageForm
     * @return
     */
    PageResult<QueryListPageDTO> queryTAddressBookAllPage(QueryTAddressBookAllDTO queryTAddressBookDTO, BasePageForm basePageForm);

    /**
     * 根据用户名称，手机号模糊搜索（app）
     *
     * @param nameOrPhone
     * @return
     */
    List<QueryAddressBookUserDTO> queryTAddressBookUser(String nameOrPhone);
    PageResult<QueryAddressBookUserDTO> queryTAddressBookUserPage(String nameOrPhone, BasePageForm basePageForm);
    IPage<QueryAddressBookUserDTO> queryTAddressBookUserNew(String nameOrPhone, IPage iPage);

    void updateLastNameEncrypt();

    void deleteAddressBookUser(Long userId);

    /**
     * 更新用户状态
     */
    void updateUserStatus(List<Long> userIds,Integer status);
}
