package com.ctsi.hndx.addrbook.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.ctsi.hndx.addrbook.entity.TAddressBookAuthorize;
import com.ctsi.hndx.common.MybatisBaseMapper;
import com.ctsi.hndx.mybatisplus.sort.SortEnum;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 通讯录授权表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-21
 */
public interface TAddressBookAuthorizeMapper extends MybatisBaseMapper<TAddressBookAuthorize> {

    /**
     * 删除机构和标签的对应关系
     *
     * @param displayRangeId
     * @return
     */
    @InterceptorIgnore(tenantLine = "true")
    Integer deleteRelationData(@Param("labelId") Long displayRangeId);

    /**
     * 排序
     *
     * @param sort
     * @return
     */
    @InterceptorIgnore(others = "tenantId@true")
    int updataSort(SortEnum sort);

    /**
     * 删除不加上租户id和单位id
     *
     * @param queryWrapper
     * @return
     */
    @Override
    @InterceptorIgnore(tenantLine = "true")
    int delete(@Param(Constants.WRAPPER) Wrapper<TAddressBookAuthorize> queryWrapper);
}
