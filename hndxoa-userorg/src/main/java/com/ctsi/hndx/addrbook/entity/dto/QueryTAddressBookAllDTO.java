package com.ctsi.hndx.addrbook.entity.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class QueryTAddressBookAllDTO {

    @ApiModelProperty(value = "机构名称")
    private String orgName;

    @ApiModelProperty(value = "真实姓名")
    private String realName;

    @ApiModelProperty(value = "手机号")
    private String defaultPhone;

    @ApiModelProperty(value = "标签id")
    private Long labelId;

}
