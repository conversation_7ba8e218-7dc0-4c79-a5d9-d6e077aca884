package com.ctsi.hndx.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ctsi.hndx.utils.BeanConvertUtils;
import com.ctsi.hndx.utils.ListCopyUtil;
import com.ctsi.ssdc.admin.domain.CscpOrg;
import com.ctsi.ssdc.admin.service.CscpOrgService;
import com.ctsi.ssdc.model.PageResult;
import com.ctsi.hndx.entity.TTopOrgRelation;
import com.ctsi.hndx.entity.dto.TTopOrgRelationDTO;
import com.ctsi.hndx.mapper.TTopOrgRelationMapper;
import com.ctsi.hndx.service.ITTopOrgRelationService;
import com.ctsi.hndx.common.SysBaseServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.utils.PageHelperUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import java.util.List;
import java.util.stream.Collectors;

import org.springframework.transaction.annotation.Transactional;

/**
 * <p>
 * 机构管理 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-06
 */
@Slf4j
@Service
public class TTopOrgRelationServiceImpl extends SysBaseServiceImpl<TTopOrgRelationMapper, TTopOrgRelation> implements ITTopOrgRelationService {

    @Autowired
    private TTopOrgRelationMapper tTopOrgRelationMapper;
    @Autowired
    private CscpOrgService orgService;

    @Autowired
    private JdbcTemplate jdbcTemplate;

    /**
     * 翻页
     *
     * @param entityDTO
     * @param basePageForm
     * @return
     */
    @Override
    public PageResult<TTopOrgRelationDTO> queryListPage(TTopOrgRelationDTO entityDTO, BasePageForm basePageForm) {
        //设置条件
        LambdaQueryWrapper<TTopOrgRelation> queryWrapper = new LambdaQueryWrapper();

        IPage<TTopOrgRelation> pageData = tTopOrgRelationMapper.selectPage(
             PageHelperUtil.getMPlusPageByBasePage(basePageForm), queryWrapper);
        //返回
        IPage<TTopOrgRelationDTO> data  = pageData.convert(entity -> BeanConvertUtils.copyProperties(entity,TTopOrgRelationDTO.class));

        return new PageResult<TTopOrgRelationDTO>(data.getRecords(),
            data.getTotal(), data.getCurrent());
    }

    /**
     * 列表查询
     *
     * @param entityDTO
     * @return
     */
    @Override
    public List<TTopOrgRelationDTO> queryList(TTopOrgRelationDTO entityDTO) {
        LambdaQueryWrapper<TTopOrgRelation> queryWrapper = new LambdaQueryWrapper();
            List<TTopOrgRelation> listData = tTopOrgRelationMapper.selectList(queryWrapper);
            List<TTopOrgRelationDTO> TTopOrgRelationDTOList = ListCopyUtil.copy(listData, TTopOrgRelationDTO.class);
        return TTopOrgRelationDTOList;
    }

    /**
     * 单个查询
     *
     * @param id the id of the entity
     * @return
     */
    @Override
    public TTopOrgRelationDTO findOne(Long id) {
        TTopOrgRelation  tTopOrgRelation =  tTopOrgRelationMapper.selectById(id);
        return  BeanConvertUtils.copyProperties(tTopOrgRelation,TTopOrgRelationDTO.class);
    }


    /**
     * 新增
     *
     * @param entityDTO the entity to create
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public TTopOrgRelationDTO create(TTopOrgRelationDTO entityDTO) {
       TTopOrgRelation tTopOrgRelation =  BeanConvertUtils.copyProperties(entityDTO,TTopOrgRelation.class);
        save(tTopOrgRelation);
        return  BeanConvertUtils.copyProperties(tTopOrgRelation,TTopOrgRelationDTO.class);
    }

    /**
     * 修改
     *
     * @param entity the entity to update
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int update(TTopOrgRelationDTO entity) {
        TTopOrgRelation tTopOrgRelation = BeanConvertUtils.copyProperties(entity,TTopOrgRelation.class);
        return tTopOrgRelationMapper.updateById(tTopOrgRelation);
    }

    /**
     * 删除
     *
     * @param id the id of the entity
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int delete(Long id) {
        return tTopOrgRelationMapper.deleteById(id);
    }


    /**
     * 验证是否存在
     *
     * @param TTopOrgRelationId
     * @return
     */
    @Override
    public boolean existByTTopOrgRelationId(Long TTopOrgRelationId) {
        if (TTopOrgRelationId != null) {
            LambdaQueryWrapper<TTopOrgRelation> queryWrapper = new LambdaQueryWrapper();
            queryWrapper.eq(TTopOrgRelation::getId, TTopOrgRelationId);
            List<TTopOrgRelation> result = tTopOrgRelationMapper.selectList(queryWrapper);
            return result.size() > 0;
        }
        return true;
    }

    /**
    * 批量新增
    *
    */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertBatch(List<TTopOrgRelationDTO> dataList) {
        List<TTopOrgRelation> result = ListCopyUtil.copy(dataList, TTopOrgRelation.class);
        return saveBatch(result);
    }

    // 需配合初始化脚本
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void initData(Long id) {

        jdbcTemplate.update(" TRUNCATE TABLE t_top_org_relation " );
        jdbcTemplate.update(" INSERT INTO t_top_org_relation (id,org_name,parent_id,level,type ,company_id,deleted," +
                " path_code,sorti )" +
                " SELECT id,org_name,parent_id,level,type ,company_id,deleted,path_code,order_by from cscp_org  where" +
                " deleted=0  " );

        // 检查
        LambdaQueryWrapper<TTopOrgRelation> queryWrapper = new LambdaQueryWrapper();
        List<TTopOrgRelation> listData = tTopOrgRelationMapper.selectListNoAdd(queryWrapper);
        // all data
        List<TTopOrgRelation> tTopOrgRelations = tTopOrgRelationMapper.selectListNoAdd(new LambdaQueryWrapper<TTopOrgRelation>().eq(TTopOrgRelation::getType ,
                2));
        for (TTopOrgRelation orgRelation : tTopOrgRelations) {
            List<TTopOrgRelation> modifyData =
                    listData.stream().filter(i -> i.getCompanyId().longValue() == orgRelation.getCompanyId().longValue()).collect(Collectors.toList());
            // 修改数据
            String topPathCode = orgRelation.getPathCode();
            int topLength = topPathCode.length();

            List<TTopOrgRelation> topOrgList =
                    modifyData.stream().filter(i -> i.getPathCode().length() == topLength + 4).collect(Collectors.toList());
            if(!topOrgList.isEmpty()){
                topOrgList.forEach(i -> {
                    i.setTopOrgId(i.getId());
                    i.setTopOrgName(i.getOrgName());
                });
                this.saveOrUpdateBatch(topOrgList);
            }

            List<TTopOrgRelation> otherOrgList =
                    modifyData.stream().filter(i -> i.getPathCode().length() > topLength + 4).collect(Collectors.toList());

            if(!otherOrgList.isEmpty() && !topOrgList.isEmpty()){
                otherOrgList.forEach(i -> {
                    String topPath = StrUtil.subWithLength(i.getPathCode() , 0 , topLength + 4);
                    TTopOrgRelation topOrg =
                            topOrgList.stream().filter(j -> j.getPathCode().equals(topPath)).collect(Collectors.toList()).get(0);

                    i.setTopOrgId(topOrg.getId());
                    i.setTopOrgName(topOrg.getOrgName());
                });
                this.saveOrUpdateBatch(otherOrgList);
            }

        }

    }


}
