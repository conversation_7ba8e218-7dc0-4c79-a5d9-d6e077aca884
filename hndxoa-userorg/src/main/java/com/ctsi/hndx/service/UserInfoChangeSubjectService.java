package com.ctsi.hndx.service;

import com.ctsi.ssdc.admin.domain.dto.CscpUserDTO;

/**
 * <AUTHOR>
 * @Classname UserInfoChangeSubjectService
 * @Description
 * @Date 2022/3/4/0004 15:41
 */
public interface UserInfoChangeSubjectService {

    /**
     * 注册观察者
     * @param observer
     */
    void registerObserver(UserInfoChangeObserverService observer);

    /**
     * 移除观察者
     * @param observer
     */
    void removeObserver(UserInfoChangeObserverService observer);

    /**
     * 通知注册的观察者
     * @param userDTO
     */
    void notifyObservers(CscpUserDTO userDTO);

    /**
     * 被观察者更新方法
     * @param userDTO
     */
    void update(CscpUserDTO userDTO) throws Exception;
}
