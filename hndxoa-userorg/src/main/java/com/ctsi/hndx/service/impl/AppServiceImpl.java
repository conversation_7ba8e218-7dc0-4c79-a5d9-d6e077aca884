package com.ctsi.hndx.service.impl;

import com.ctsi.hndx.service.AppService;
import com.ctsi.ssdc.openapi.utils.KeyUtils;
import com.ctsi.ssdc.util.RedisUtil;
import com.google.common.collect.Maps;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.Map;

/**
 * @Description:
 * @author: tr
 * @date: 2025年02月26日 17:38
 */
@Service
public class AppServiceImpl implements AppService {

    @Autowired
    private RedisUtil redisUtil;

    @PostConstruct
    public void init()
    {
        loadingAppCache();
    }

    private void loadingAppCache()
    {
        //TODO 测试临时使用，需新增应用管理功能，新增clientId，clientSecret，secretKey等信息
        //TODO clientId可由页面输入
        //TODO clientSecret和secretKey由系统生成
        //存储客户端生成签名算法的公钥到redis缓存中
        HashMap<String, String> codePublicKeycacheMap = Maps.newHashMap();
        codePublicKeycacheMap.put("sfw", "r7uux1gcoha6h9d6k1ucsu9qoq6ey5es");
        redisUtil.set("app:client_secret", codePublicKeycacheMap);

        //存储客户端加密的秘钥到redis缓存中
        HashMap<String, String> secretKeycacheMap = Maps.newHashMap();
        secretKeycacheMap.put("sfw", "8tlareb8a7e78gra");

        redisUtil.set("app:secret_key", secretKeycacheMap);
    }

    private static void createClilent(){
        Map<String, String> keyMap = KeyUtils.generateSmKey();

        String clientSecret = keyMap.get(KeyUtils.CLIENT_SECRET);
        String secretKey = keyMap.get(KeyUtils.SECRET_KEY);

        System.out.println(clientSecret);
        System.out.println(secretKey);
    }

    public static void main(String[] args) {
        createClilent();
    }

}
