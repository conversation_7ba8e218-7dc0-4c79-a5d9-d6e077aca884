package com.ctsi.hndx.datarule.enums;

import com.ctsi.hndx.utils.ConvertUtils;
import com.google.common.collect.Lists;
import lombok.Getter;

import java.util.*;

@Getter
public enum QueryRuleEnum {
    GT(">", "大于"),
    GE(">=", "大于等于"),
    LT("<", "小于"),
    LE("<=", "小于等于"),
    EQ("=", "等于"),
    NE("!=", "不等于"),
    IN("IN", "包含"),
    LIKE("LIKE", "全模糊"),
    LEFT_LIKE("LEFT_LIKE", "左模糊"),
    RIGHT_LIKE("RIGHT_LIKE", "右模糊"),
    SQL_RULES("EXTEND_SQL", "自定义SQL片段");

    private String value;

    private String msg;

    QueryRuleEnum(String value, String msg) {
        this.value = value;
        this.msg = msg;
    }

    public static QueryRuleEnum getByValue(String value) {
        if (ConvertUtils.isEmpty(value)) {
            return null;
        }
        for (QueryRuleEnum val : values()) {
            if (val.getValue().equals(value)) {
                return val;
            }
        }
        return null;
    }

    /**
     * 获取全部枚举
     */
    public static List<QueryRuleEnum> getAllEnum() {
        List<QueryRuleEnum> list = Lists.newArrayList();
        list.addAll(Arrays.asList(values()));
        return list;
    }

    /**
     * 获取全部枚举值
     *
     * @return List<String>
     */
    public static List<String> getAllEnumCode() {
        List<String> list = Lists.newArrayList();
        for (QueryRuleEnum each : values()) {
            list.add(each.getMsg());
        }
        return list;
    }


    /**
     * 通过key获取枚举
     */
    public static QueryRuleEnum getEnumByvalue(String key) {
        QueryRuleEnum result = null;
        for (QueryRuleEnum statusEnum : getAllEnum()) {
            if (statusEnum.value.equals(key)) {
                result = statusEnum;
                break;
            }
        }
        return result;
    }


    public static List<Map<String, String>> getAdPlatformMapList() {
        List<Map<String, String>> listMap = new LinkedList<>();
        for (QueryRuleEnum AdPlatformEnum : EnumSet.allOf(QueryRuleEnum.class)) {
            Map<String, String> map = new HashMap<>();
            map.put(AdPlatformEnum.getValue(), AdPlatformEnum.getMsg());
            listMap.add(map);
        }
        return listMap;
    }

    /**
     * 得到枚举值码。
     *
     * @return 枚举值码。
     */
    public String code() {
        return value;
    }

    /**
     * 得到枚举描述。
     *
     * @return 枚举描述。
     */
    public String message() {
        return msg;
    }

}
