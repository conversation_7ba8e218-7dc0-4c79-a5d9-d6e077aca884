package com.ctsi.hndx.datarule.entity;

import com.ctsi.hndx.common.BaseEntity;

import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 数据权限规则表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-11
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "CscpDataRule对象", description = "数据权限规则表")
public class CscpDataRule extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    @TableField("CREATE_BY")
    private Long createdBy;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @TableField("CREATE_TIME")
    private LocalDateTime createdTime;

    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人")
    @TableField("UPDATE_BY")
    private Long updatedBy;

    /**
     * 创建人部门
     */
    @ApiModelProperty(value = "创建人部门")
    private Long departmentId;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    @TableField("UPDATE_TIME")
    private LocalDateTime updatedTime;

    /**
     * 创建人单位id
     */
    @ApiModelProperty(value = "创建人单位id")
    private Long companyId;

    /**
     * 规则名称
     */
    @ApiModelProperty(value = "规则名称")
    private String ruleName;

    /**
     * 菜单id
     */
    @ApiModelProperty(value = "菜单id")
    private Long menuId;

    /**
     * 规则字段
     */
    @ApiModelProperty(value = "规则字段")
    private String ruleColumn;

    /**
     * 规则值
     */
    @ApiModelProperty(value = "规则值")
    private String ruleValue;

    /**
     * 权限有效状态1有0否
     */
    @ApiModelProperty(value = "权限有效状态1有0否")
    private Integer status;

    /**
     * 规则条件
     */
    @ApiModelProperty(value = "规则条件")
    private String ruleConditions;

    /**
     * 规则条件
     */
    @ApiModelProperty(value = "是否是系统默认的字段(1:是 2:否)")
    private Integer isSys;
}
