package com.ctsi.hndx.cadre.service;

import com.ctsi.hndx.cadre.entity.BizCadreInformation;
import com.ctsi.hndx.cadre.entity.dto.BizCadreInformationDTO;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.common.SysBaseServiceI;
import com.ctsi.ssdc.model.PageResult;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <p>
 * 干部信息 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-01-25
 */
public interface IBizCadreInformationService extends SysBaseServiceI<BizCadreInformation> {


    /**
     * 分页查询
     */
    PageResult<BizCadreInformationDTO> queryListPage(BizCadreInformationDTO entityDTO, BasePageForm page);

    /**
     * 获取所有不分页
     */
    List<BizCadreInformationDTO> queryList(BizCadreInformationDTO entity);

    /**
     * 根据主键id获取单个对象
     */
    BizCadreInformationDTO findOne(Long id);

    /**
     * 新增
     */
    BizCadreInformationDTO create(BizCadreInformationDTO entity);


    /**
     * 更新
     */
    int update(BizCadreInformationDTO entity);

    /**
     * 删除
     */
    int delete(Long id);

    /**
     * 是否存在
     * existByBizCadreInformationId
     */
    boolean existByBizCadreInformationId(Long code);

    /**
     * 批量新增
     * create batch
     */
    Boolean insertBatch(List<BizCadreInformationDTO> dataList);

    /**
     * 干部信息导出
     *
     * @param response
     * @return
     */
    Boolean exportUserToExcel(HttpServletResponse response);
}
