package com.ctsi.hndx.systenant.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.common.SysBaseServiceImpl;
import com.ctsi.hndx.systenant.entity.TCompanyManagement;
import com.ctsi.hndx.systenant.entity.dto.TCompanyManagementDTO;
import com.ctsi.hndx.systenant.mapper.TCompanyManagementMapper;
import com.ctsi.hndx.systenant.service.ITCompanyManagementService;
import com.ctsi.hndx.utils.BeanConvertUtils;
import com.ctsi.hndx.utils.ListCopyUtil;
import com.ctsi.hndx.utils.PageHelperUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-03
 */

@Slf4j
@Service
public class TCompanyManagementServiceImpl extends SysBaseServiceImpl<TCompanyManagementMapper, TCompanyManagement> implements ITCompanyManagementService {

    @Autowired
    private TCompanyManagementMapper tCompanyManagementMapper;

    /**
     * 翻页
     *
     * @param entity
     * @param pageable
     * @return
     */
    @Override
    public IPage<TCompanyManagementDTO> queryListPage(TCompanyManagementDTO entityDTO, BasePageForm basePageForm) {
        //设置条件
        LambdaQueryWrapper<TCompanyManagement> queryWrapper = new LambdaQueryWrapper();

        IPage<TCompanyManagement> pageData = tCompanyManagementMapper.selectPage(
             PageHelperUtil.getMPlusPageByBasePage(basePageForm), queryWrapper);
        //返回
        IPage<TCompanyManagementDTO> data  = pageData.convert(entity -> BeanConvertUtils.copyProperties(entity,TCompanyManagementDTO.class));
        return data;
    }

    /**
     * 列表查询
     *
     * @param entity
     * @return
     */
    @Override
    public List<TCompanyManagementDTO> queryList(TCompanyManagementDTO entityDTO) {
        LambdaQueryWrapper<TCompanyManagement> queryWrapper = new LambdaQueryWrapper();
            List<TCompanyManagement> listData = tCompanyManagementMapper.selectList(queryWrapper);
            List<TCompanyManagementDTO> TCompanyManagementDTOList = ListCopyUtil.copy(listData, TCompanyManagementDTO.class);
        return TCompanyManagementDTOList;
    }

    /**
     * 单个查询
     *
     * @param id the id of the entity
     * @return
     */
    @Override
    public TCompanyManagementDTO findOne(Long id) {
        TCompanyManagement  tCompanyManagement =  tCompanyManagementMapper.selectById(id);
        return  BeanConvertUtils.copyProperties(tCompanyManagement,TCompanyManagementDTO.class);
    }


    /**
     * 新增
     *
     * @param entity the entity to create
     * @return
     */
    @Override
    @Transactional
    public TCompanyManagementDTO create(TCompanyManagementDTO entityDTO) {
       TCompanyManagement tCompanyManagement =  BeanConvertUtils.copyProperties(entityDTO,TCompanyManagement.class);
        save(tCompanyManagement);
        return  BeanConvertUtils.copyProperties(tCompanyManagement,TCompanyManagementDTO.class);
    }

    /**
     * 修改
     *
     * @param entity the entity to update
     * @return
     */
    @Override
    @Transactional
    public int update(TCompanyManagementDTO entity) {
        TCompanyManagement tCompanyManagement = BeanConvertUtils.copyProperties(entity,TCompanyManagement.class);
        return tCompanyManagementMapper.updateById(tCompanyManagement);
    }

    /**
     * 删除
     *
     * @param id the id of the entity
     */
    @Override
    @Transactional
    public int delete(Long id) {
        return tCompanyManagementMapper.deleteById(id);
    }


    /**
     * 验证是否存在
     *
     * @param TCompanyManagementId
     * @return
     */
    @Override
    public boolean existByTCompanyManagementId(Long TCompanyManagementId) {
        if (TCompanyManagementId != null) {
            LambdaQueryWrapper<TCompanyManagement> queryWrapper = new LambdaQueryWrapper();
            queryWrapper.eq(TCompanyManagement::getId, TCompanyManagementId);
            List<TCompanyManagement> result = tCompanyManagementMapper.selectList(queryWrapper);
            return result.size() > 0;
        }
        return true;
    }

    /**
    * 批量新增
    *
    */
    @Override
    @Transactional
    public Boolean insertBatch(List<TCompanyManagementDTO> dataList) {
        List<TCompanyManagement> result = ListCopyUtil.copy(dataList, TCompanyManagement.class);
        return saveBatch(result);
    }


}
