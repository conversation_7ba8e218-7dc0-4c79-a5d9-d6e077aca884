package com.ctsi.hndx.systenant.entity.dto;

import com.ctsi.hndx.common.BaseDtoEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <p>
 * 权限管理表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-02
 */
@Data
@ApiModel(value="TTenantMenuDTO对象", description="租户菜单DTO")
public class TTenantMenuDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    private Long id;

    /**
     * 菜单ID
     */
    @ApiModelProperty(value = "菜单ID")
    private Long menuId;

    @ApiModelProperty(value = "租户ID")
    private Long tenantId;

}
