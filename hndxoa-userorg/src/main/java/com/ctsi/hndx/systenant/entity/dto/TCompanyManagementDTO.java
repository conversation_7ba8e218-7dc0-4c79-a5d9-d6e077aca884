package com.ctsi.hndx.systenant.entity.dto;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.ctsi.hndx.common.BaseDtoEntity;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-03
 */
@Data
@ApiModel(value="TCompanyManagementDTO对象", description="单位授权管理DTO对象")
public class TCompanyManagementDTO implements Serializable {

    private static final long serialVersionUID = -8371618907519974460L;

    @ApiModelProperty(value = "主键")
    @TableId(value = "ID",type = IdType.ASSIGN_ID)
    @JsonSerialize(using= ToStringSerializer.class)
    private Long id;

    @ApiModelProperty(value = "单位ID")
    @TableField(fill = FieldFill.INSERT)
    private Long companyId;

    @ApiModelProperty(value = "可管理的单位ID")
    private Long managedCompanyId;

}
