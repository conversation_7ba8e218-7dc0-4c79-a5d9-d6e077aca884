package com.ctsi.hndx.wst.entity.dto;

import com.ctsi.hndx.common.BaseDtoEntity;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 卫士通机构表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-13
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value="WstOrgDTO对象", description="卫士通机构表")
public class WstOrgDTO extends BaseDtoEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 上级组织编码
     */
    @ApiModelProperty(value = "上级组织编码")
    private String parentNo;

    /**
     * 行政级别
     */
    @ApiModelProperty(value = "行政级别")
    private Integer level;

    /**
     * 负责人
     */
    @ApiModelProperty(value = "负责人")
    private String functionary;

    /**
     * 联系人
     */
    @ApiModelProperty(value = "联系人")
    private String contact;

    /**
     * 联系电话
     */
    @ApiModelProperty(value = "联系电话")
    private String telephoneNumber;

    /**
     * 组织编办编号
     */
    @ApiModelProperty(value = "组织编办编号")
    private String editingOfficeCode;

    /**
     * 组织是否需要全网同步
     */
    @ApiModelProperty(value = "组织是否需要全网同步")
    private Integer worldWide;

    private String groupNo;

    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    private String no;

    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    private String name;

    /**
     * 资源所属资源系统编码
     */
    @ApiModelProperty(value = "资源所属资源系统编码")
    private String institutionNo;

    /**
     * 0在用，1停用，2销毁
     */
    @ApiModelProperty(value = "0在用，1停用，2销毁")
    private Integer status;

    /**
     * 排序值
     */
    @ApiModelProperty(value = "排序值")
    private Integer showOrder;

    private Integer version;

    /**
     * 资源二级类型
     */
    @ApiModelProperty(value = "资源二级类型")
    private String type;

    /**
     * 扩展属性列表
     */
    @ApiModelProperty(value = "扩展属性列表")
    private String extendPropertyList;

    /**
     * 资源简述
     */
    @ApiModelProperty(value = "资源简述")
    private String briefList;


}
