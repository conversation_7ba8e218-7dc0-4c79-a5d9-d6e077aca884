package com.ctsi.hndx.wst.controller;
import com.ctsi.ssdc.model.PageResult;
import java.util.List;
import java.util.Optional;
import com.ctsi.hndx.wst.entity.WstOrg;
import com.ctsi.hndx.wst.entity.dto.WstOrgDTO;
import com.ctsi.hndx.wst.service.IWstOrgService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.ctsi.ssdc.model.ResResult;
import org.springframework.security.access.prepost.PreAuthorize;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import com.ctsi.hndx.common.BasePageForm;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.InitBinder;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.util.Assert;
import com.ctsi.hndx.common.BaseController;
import com.ctsi.hndx.annotations.ResponseResultVo;
import com.ctsi.hndx.result.ResultCode;
import com.ctsi.hndx.result.ResultVO;
import com.ctsi.ssdc.annotation.OperationLog;
import com.ctsi.hndx.enums.DBOperation;


/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-13
 *
 */

@Slf4j
@RestController
@ResponseResultVo
@RequestMapping("/api/wstOrg")
@Api(value = "卫士通机构表", tags = "卫士通机构表接口")
public class WstOrgController extends BaseController {

    private static final String ENTITY_NAME = "wstOrg";

    @Autowired
    private IWstOrgService wstOrgService;



    /**
     *  新增卫士通机构表批量数据.
     */
    @PostMapping("/createBatch")
    @ApiOperation(value = "新增批量(权限code码为：cscp.wstOrg.add)", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.ADD,message = "新增卫士通机构表批量数据")
    @PreAuthorize("@permissionService.hasPermi('cscp.wstOrg.add')")
    public ResultVO createBatch(@RequestBody List<WstOrgDTO> wstOrgList) {
       Boolean  result = wstOrgService.insertBatch(wstOrgList);
       if(result){
           return ResultVO.success();
       }else {
           return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
       }
    }

     /**
     *  新增数据.
     */
    @PostMapping("/create")
    @ApiOperation(value = "新增(权限code码为：cscp.wstOrg.add)", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.ADD,message = "新增卫士通机构表数据")
    @PreAuthorize("@permissionService.hasPermi('cscp.wstOrg.add')")
    public ResultVO<WstOrgDTO> create(@RequestBody WstOrgDTO wstOrgDTO)  {
        WstOrgDTO result = wstOrgService.create(wstOrgDTO);
        return ResultVO.success(result);
    }

    /**
     *  更新存在数据.
     */
    @PostMapping("/update")
    @ApiOperation(value = "更新存在数据(权限code码为：cscp.wstOrg.update)", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.UPDATE,message = "更新卫士通机构表数据")
    @PreAuthorize("@permissionService.hasPermi('cscp.wstOrg.update')")
    public ResultVO update(@RequestBody WstOrgDTO wstOrgDTO) {
	    Assert.notNull(wstOrgDTO.getId(), "general.IdNotNull");
        int count = wstOrgService.update(wstOrgDTO);
        if(count > 0 ){
            return ResultVO.success();
        }else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }

     /**
     *  删除存在数据.
     */
    @DeleteMapping("/delete/{id}")
    @OperationLog(dBOperation = DBOperation.DELETE,message = "删除卫士通机构表数据")
    @ApiOperation(value = "删除存在数据(权限code码为：cscp.wstOrg.delete)", notes = "传入参数")
    @PreAuthorize("@permissionService.hasPermi('cscp.wstOrg.delete')")
    public ResultVO delete(@PathVariable Long id) {
        int count = wstOrgService.delete(id);
        if(count > 0 ){
            return ResultVO.success();
        }else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }

    /**
     * 查询单条数据.
     */
    @GetMapping("/get/{id}")
    @ApiOperation(value = "查询单条数据", notes = "传入参数")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
    public ResultVO get(@PathVariable Long id) {
        WstOrgDTO wstOrgDTO = wstOrgService.findOne(id);
        return ResultVO.success(wstOrgDTO);
    }

    /**
    *  分页查询多条数据.
    */
    @GetMapping("/queryWstOrgPage")
    @ApiOperation(value = "翻页查询多条数据", notes = "传入参数")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
    public ResultVO<PageResult<WstOrgDTO>> queryWstOrgPage(WstOrgDTO wstOrgDTO, BasePageForm basePageForm) {
        return ResultVO.success(wstOrgService.queryListPage(wstOrgDTO, basePageForm));
    }

   /**
    * 查询多条数据.不分页
    */
   @GetMapping("/queryWstOrg")
   @ApiOperation(value = "查询多条数据", notes = "传入参数")
   //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
   public ResultVO<ResResult<WstOrgDTO>> queryWstOrg(WstOrgDTO wstOrgDTO) {
       List<WstOrgDTO> list = wstOrgService.queryList(wstOrgDTO);
       return ResultVO.success(new ResResult<WstOrgDTO>(list));
   }

}
