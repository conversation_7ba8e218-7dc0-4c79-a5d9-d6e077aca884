package com.ctsi.hndx.mapper;

import com.ctsi.hndx.entity.TTopOrgRelation;
import com.ctsi.hndx.common.MybatisBaseMapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 机构管理 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-06
 */
public interface TTopOrgRelationMapper extends MybatisBaseMapper<TTopOrgRelation> {

	/**
	 *
	 * @param startTime
	 * @param endTime
	 * @return
	 */
	List<Map<String,Object>> selectOrgMeetingCountByTime(@Param("startTime") String startTime ,
												@Param("endTime") String endTime);
}
