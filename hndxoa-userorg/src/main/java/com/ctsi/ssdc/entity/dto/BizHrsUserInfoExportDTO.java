package com.ctsi.ssdc.entity.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <p>
 * 人社厅人员表导出DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-29
 */
@Data
@ApiModel(value="BizHrsUserInfoExportDTO对象", description="人社厅人员表导出")
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BizHrsUserInfoExportDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 姓名
     */
    @ApiModelProperty(value = "姓名")
    @ExcelProperty("姓名")
    private String strUserId;

    /**
     * 身份证号
     */
    @ApiModelProperty(value = "身份证号")
    @ExcelProperty("身份证号")
    private String idCardNo;

    /**
     * 手机号
     */
    @ApiModelProperty(value = "手机号")
    @ExcelProperty("手机号")
    private String strMobile;

    /**
     * 单位名称
     */
    @ApiModelProperty(value = "单位名称")
    @ExcelProperty("单位名称")
    private String strUnitName;

    /**
     * 单位统一社会信用码
     */
    @ApiModelProperty(value = "单位统一社会信用码")
    @ExcelProperty("单位统一社会信用码")
    private String creditCode;

}
