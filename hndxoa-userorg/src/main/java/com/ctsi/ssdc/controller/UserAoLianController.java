package com.ctsi.ssdc.controller;

import com.ctsi.hndx.constant.HeaderConstants;
import com.ctsi.hndx.enums.DBOperation;
import com.ctsi.hndx.exception.BusinessException;
import com.ctsi.hndx.result.ResultCode;
import com.ctsi.hndx.utils.DesensitizeUtil;
import com.ctsi.ssdc.admin.domain.dto.CscpUserDTO;
import com.ctsi.ssdc.admin.service.CscpMenusService;
import com.ctsi.ssdc.admin.service.CscpUserService;
import com.ctsi.ssdc.annotation.OperationLog;
import com.ctsi.ssdc.security.SecurityUtils;
import com.ctsi.ssdc.security.UserLoginValidator;
import com.ctsi.ssdc.security.UserNameAndSamePasswordAuthenticationToken;
import com.ctsi.ssdc.security.jwt.TokenProvider;
import com.ctsi.ssdc.util.RedisUtil;
import com.nisc.SecurityEngine;
import com.nisc.SecurityEngineException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.concurrent.TimeUnit;

/**
 * @Description 奥联单点登录
 * @Date 2023/04/13 10:53
 */
@Slf4j
@Controller
@RequestMapping("/api/system")
@Api(value = "奥连vpn单点登录相关接口，采用jsp重定向到首页", tags = "奥连vpn单点登录相关接口")
public class UserAoLianController {


    @Autowired(required = false)
    UserLoginValidator userLoginValidator;

    @Autowired
    CscpUserService cscpUserService;

    private final CscpMenusService cscpMenusService;

    @Value("${nisc.commonse-path: }")
    private String commonsePath = "";

    @Value("${nisc.paramasn-path:}")
    private String paramasnPath = "";

    @Value("${nisc.web-home:}")
    private String webHome = "";


    private final TokenProvider tokenProvider;

    private final AuthenticationManager authenticationManager;

    @Autowired
    private RedisUtil redisUtil;


    @Autowired
    PasswordEncoder passwordEncoder;

    public UserAoLianController(TokenProvider tokenProvider, AuthenticationManager authenticationManager,
                                CscpMenusService cscpMenusService, CscpUserService cscpUserService) {
        this.tokenProvider = tokenProvider;
        this.authenticationManager = authenticationManager;
        this.cscpMenusService = cscpMenusService;
        this.cscpUserService = cscpUserService;
    }


    @ApiOperation(value = "奥连PC端单点登录", notes = "传入参数")
    @GetMapping(value = "/aoLianlogin")
    @OperationLog(dBOperation = DBOperation.UPDATE, message = "奥连PC单点登录")
    public String aoLianlogin(HttpServletRequest request, HttpServletResponse response) {
        log.info("奥连PC端单点登录入口：" + request.getRequestURI());
        String operation = request.getParameter("operation");
        SecurityEngine se = null;
        try {
            se = SecurityEngine.getInstance();
        } catch (SecurityEngineException e) {
            throw new BusinessException("无法加载奥联安全的库文件");
        }
        if (se == null) {
            throw new BusinessException("无法加载奥联安全的库文件");
        }
        if ("getchallenge".equals(operation)) {
            //得到challenge
            try {
                String sChallenge = se.GenChallenge();
                if (StringUtils.isNotEmpty(sChallenge)) {
                    redisUtil.set(sChallenge, 1, 10, TimeUnit.SECONDS);
                    request.setAttribute("sChallenge", sChallenge);
                }
            } catch (SecurityEngineException e) {
                throw new BusinessException("第一次请求无法获取挑战值");
            }

        } else if ("loginchallenge".equals(operation)) {
            String sChallengestr = request.getParameter("sChallenge");
            boolean b = false;
            if (StringUtils.isNotEmpty(sChallengestr)) {
                b = redisUtil.hasKey(sChallengestr);
            } else {
                throw new BusinessException("第二次请求无法获取挑战值");
            }
            if (!b) {
                throw new BusinessException("请求的{}redis对应的不存在", sChallengestr);
            }

            String sResponsestr = request.getParameter("sResponse");
            String mobilePhone = "";
            try {
                //原来老的奥联安全单点登录参数文件
                mobilePhone = se.VerifyChapEx(sResponsestr, sChallengestr, commonsePath).toLowerCase();
                log.info("老的奥联安全单点登录参数文件解析手机号码：" +
                        DesensitizeUtil.desensitizedPhoneNumber(mobilePhone));
            } catch (SecurityEngineException e) {
                //新奥联安全单点登录参数文件
                try {
                    mobilePhone = se.VerifyChapEx(sResponsestr, sChallengestr, paramasnPath).toLowerCase();
                    log.info("新的奥联安全单点登录参数文件解析手机号码：" +
                            DesensitizeUtil.desensitizedPhoneNumber(mobilePhone));
                } catch (SecurityEngineException e1) {
                    throw new BusinessException("无法解析到手机号码，原因{}", e1);
                }
            }
            String jwt;
            CscpUserDTO cscpUserDTO = cscpUserService.getUserByUsernameOrMobile(mobilePhone);
            if (cscpUserDTO == null) {
                throw new BusinessException(ResultCode.USER_LOGIN_ERROR);
            }
            // security
            UserNameAndSamePasswordAuthenticationToken authenticationToken =
                    new UserNameAndSamePasswordAuthenticationToken(cscpUserDTO.getLoginName(),
                            cscpUserDTO.getPassword());
            Authentication authentication = null;
            try {
                authentication = this.authenticationManager.authenticate(authenticationToken);
            } catch (AuthenticationException e) {
                throw e;
            }

            SecurityContextHolder.getContext().setAuthentication(authentication);
            jwt = tokenProvider.createToken(authentication, false,
                    request.getHeader(HeaderConstants.CALL_SOURCE));
            SecurityUtils.getOptionalCurrentUserId().map(userId -> {
                cscpUserService.updateUserDetailForLogin(userId);
                return null;
            });
            String token = jwt;
            request.setAttribute("webHome", webHome);
            request.setAttribute("token", token);
            log.info("奥连PC端单点登录完成跳转首页：" + webHome);
        } else {
            throw new BusinessException("参数值不对，参数值只能为operation");
        }
        return "index";
    }
}