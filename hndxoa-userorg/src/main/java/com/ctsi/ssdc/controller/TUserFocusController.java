package com.ctsi.ssdc.controller;

import com.ctsi.hndx.annotations.ResponseResultVo;
import com.ctsi.hndx.common.BaseController;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.enums.DBOperation;
import com.ctsi.hndx.result.ResultCode;
import com.ctsi.hndx.result.ResultVO;
import com.ctsi.ssdc.admin.domain.dto.TUserFocusDTO;
import com.ctsi.ssdc.admin.domain.dto.TUserFocusDataDTO;
import com.ctsi.ssdc.admin.service.ITUserFocusService;
import com.ctsi.ssdc.annotation.OperationLog;
import com.ctsi.ssdc.entity.dto.BizCollectDTO;
import com.ctsi.ssdc.model.PageResult;
import com.ctsi.ssdc.model.ResResult;
import com.ctsi.ssdc.security.SecurityUtils;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.stream.Collectors;


/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-19
 *
 */

@Slf4j
@RestController
@ResponseResultVo
@RequestMapping("/api/tUserFocus")
@Api(value = "用户关注", tags = "用户关注接口")
public class TUserFocusController extends BaseController {

    private static final String ENTITY_NAME = "tUserFocus";

    @Autowired
    private ITUserFocusService tUserFocusService;
//    @Autowired
//    private BizService bizService;



    /**
     *  新增用户关注批量数据.
     */
    @PostMapping("/createBatch")
    @ApiOperation(value = "新增批量(权限code码为：cscp.tUserFocus.add)", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.ADD,message = "新增用户关注批量数据")
    //@PreAuthorize ("@permissionService.hasPermi('cscp.tUserFocus.add')")
    public ResultVO createBatch(@RequestBody List<TUserFocusDTO> tUserFocusList) {
       Boolean  result = tUserFocusService.insertBatch(tUserFocusList);
       if(result){
           return ResultVO.success();
       }else {
           return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
       }
    }

     /**
     *  新增数据. 
     */
    @PostMapping("/create")
    @ApiOperation(value = "新增(权限code码为：cscp.tUserFocus.add)", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.ADD,message = "新增用户关注数据")
    //@PreAuthorize ("@permissionService.hasPermi('cscp.tUserFocus.add')")
    public ResultVO<TUserFocusDTO> create(@RequestBody TUserFocusDTO tUserFocusDTO)  {
        TUserFocusDTO result = tUserFocusService.create(tUserFocusDTO);
        return ResultVO.success(result);
    }

    /**
     *  更新存在数据.
     */
    @PostMapping("/update")
    @ApiOperation(value = "更新存在数据(权限code码为：cscp.tUserFocus.update)", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.UPDATE,message = "更新用户关注数据")
    //@PreAuthorize ("@permissionService.hasPermi('cscp.tUserFocus.update')")
    public ResultVO update(@RequestBody TUserFocusDTO tUserFocusDTO) {
	    Assert.notNull(tUserFocusDTO.getId(), "general.IdNotNull");
        int count = tUserFocusService.update(tUserFocusDTO);
        if(count > 0 ){
            return ResultVO.success();
        }else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }

     /**
     *  删除存在数据.
     */
    @DeleteMapping("/delete/{id}")
    @OperationLog(dBOperation = DBOperation.DELETE,message = "删除用户关注数据")
    @ApiOperation(value = "删除存在数据(权限code码为：cscp.tUserFocus.delete)", notes = "传入参数")
    //@PreAuthorize ("@permissionService.hasPermi('cscp.tUserFocus.delete')")
    public ResultVO delete(@PathVariable Long id) {
        int count = tUserFocusService.delete(id);
        if(count > 0 ){
            return ResultVO.success();
        }else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }

    /**
     * 查询单条数据.
     */
    @GetMapping("/get/{id}")
    @ApiOperation(value = "查询单条数据", notes = "传入参数")
    ////@PreAuthorize ("@permissionService.hasPermi('cscp.tenant.edit')")
    public ResultVO get(@PathVariable Long id) {
        TUserFocusDTO tUserFocusDTO = tUserFocusService.findOne(id);
        return ResultVO.success(tUserFocusDTO);
    }

    /**
    *  分页查询多条数据.
    */
    @GetMapping("/queryTUserFocusPage")
    @ApiOperation(value = "翻页查询多条数据", notes = "传入参数")
    ////@PreAuthorize ("@permissionService.hasPermi('cscp.tenant.edit')")
    public ResultVO<PageResult<TUserFocusDTO>> queryTUserFocusPage(TUserFocusDTO tUserFocusDTO, BasePageForm basePageForm) {
        return ResultVO.success(tUserFocusService.queryListPage(tUserFocusDTO, basePageForm));
    }

   /**
    * 查询多条数据.不分页
    */
   @GetMapping("/queryTUserFocus")
   @ApiOperation(value = "查询多条数据", notes = "传入参数")
   ////@PreAuthorize ("@permissionService.hasPermi('cscp.tenant.edit')")
   public ResultVO<ResResult<TUserFocusDTO>> queryTUserFocus(TUserFocusDTO tUserFocusDTO) {
       List<TUserFocusDTO> list = tUserFocusService.queryList(tUserFocusDTO);
       return ResultVO.success(new ResResult<TUserFocusDTO>(list));
   }


    /**
     *  新增数据.  关注 
     */
//    @PostMapping("/focus")
//    @OperationLog(dBOperation = DBOperation.ADD,message = "新增用户关注数据")
//    //@PreAuthorize ("@permissionService.hasPermi('cscp.tUserFocus.add')")
//    public ResultVO<TUserFocusDTO> focus(@RequestBody BizCollectDTO bizCollectDTO)  {
//        bizService.collectFocus(bizCollectDTO);
//        return ResultVO.success();
//    }

    /**
     *  取消关注  id 关注的数据id  (业务id)
     */
    @PostMapping("/cancelFocus/{id}")
    @ApiOperation(value = "", notes = "id")
    @OperationLog(dBOperation = DBOperation.ADD,message = "取消关注")
    //@PreAuthorize ("@permissionService.hasPermi('cscp.tUserFocus.add')")
    public ResultVO cancelFocus(@PathVariable Long id) {
        long currentUserId = SecurityUtils.getCurrentUserId();
        TUserFocusDTO one = tUserFocusService.findOne(id , currentUserId);
        if(null != one){
            int count = tUserFocusService.delete(one.getId());
            if(count > 0 ){
                return ResultVO.success();
            }else {
                return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
            }
        }
        return ResultVO.success();
    }

    /**
     * 判断业务数据是否被关注.
     */
    @GetMapping("/checkByDataId/{dataId}")
    @ApiOperation(value = "判断业务数据是否被关注", notes = "业务数据id")
    ////@PreAuthorize ("@permissionService.hasPermi('cscp.tenant.edit')")
    public ResultVO<Boolean> checkByDataId(@PathVariable Long dataId) {
        TUserFocusDTO tUserFocusDTO = tUserFocusService.findOne(dataId, SecurityUtils.getCurrentUserId());
        return ResultVO.success(tUserFocusDTO == null ? false: true);
    }


	/**
	 * 根据业务id获取关注的数据
     * @param tUserFocusList
	 * @return
	 */
    @PostMapping("/queryListByDataIds")
    @OperationLog(dBOperation = DBOperation.ADD,message = "根据业务id获取关注的数据")
    public ResultVO<ResResult<TUserFocusDTO>> queryListByDataIds(@RequestBody List<TUserFocusDTO> tUserFocusList) {
        List<TUserFocusDTO> data = tUserFocusService.queryListByDataIds(tUserFocusList.stream().map(i ->i.getDataId()).collect(Collectors.toList()));
        return ResultVO.success(new ResResult<TUserFocusDTO>(data));
    }

}
