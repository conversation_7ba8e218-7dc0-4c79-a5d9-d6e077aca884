package com.ctsi.ssdc.config;

import com.ctsi.hndx.constant.HeaderConstants;
import com.ctsi.hndx.utils.IpUtil;
import com.ctsi.hndx.utils.UserAESUtil;
import com.ctsi.hndx.westone.WestoneEncryptService;
import com.ctsi.ssdc.admin.domain.CscpLogLogin;
import com.ctsi.ssdc.admin.domain.dto.CscpLogOperationDTO;
import com.ctsi.ssdc.admin.service.CscpLogLoginService;
import com.ctsi.ssdc.model.UserForm;
import com.ctsi.ssdc.security.SecurityUtils;
import com.ctsi.ssdc.util.RequestUtil;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.AfterThrowing;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.env.Environment;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * Aspect for logging execution of service and repository Spring components.
 *
 * By default, it only runs with the "dev" profile.
 *
 * <AUTHOR> biyi generator
 *
 */
@Aspect
public class LoginLoggingAspect {

    private static final String SPRING_PROFILE_DEVELOPMENT = "dev";

	private final Logger log = LoggerFactory.getLogger(this.getClass());

	@Autowired
	private CscpLogLoginService loginService;

	@Autowired
	private WestoneEncryptService westoneEncryptService;

	@Value("${ctsi.log.login-log.enable:false}")
	private boolean loginEnable;

	private final Environment env;

    public LoginLoggingAspect(Environment env) {
        this.env = env;
    }

	 /**
	 * login pointcut
	 */
	@Pointcut(" (execution(* com.ctsi.ssdc.controller.UserJwtController.*(..)) "
			+ "&& !execution(* com.ctsi.ssdc.controller.UserJwtController.sendSmsVerificationCodeWw(..)) "
			+ "&& !execution(* com.ctsi.ssdc.controller.UserJwtController.sendSmsVerificationCode(..)) "
			+ "&& !execution(* com.ctsi.ssdc.controller.UserJwtController.checkVerificationCode(..)) "
			+ "&& !execution(* com.ctsi.ssdc.controller.UserJwtController.existMobileByMobileList(..)) "
			+ "&& !execution(* com.ctsi.ssdc.controller.UserJwtController.refreshToken(..)) "
			+ "&& !execution(* com.ctsi.ssdc.controller.UserJwtController.statisticsSignInCompanyPeople(..))"
			+ "&& !execution(* com.ctsi.ssdc.controller.UserJwtController.statisticsSignInTenantPeople(..))"
			+ "&& !execution(* com.ctsi.ssdc.controller.UserJwtController.numberOfPeopleOnline(..)))")
	private void loginPointCut() {}
	
	@AfterReturning(pointcut="loginPointCut()")
	public void doAfterLogin(JoinPoint joinPoint) {
		if(loginEnable) {
			loginLog(joinPoint, null);
		}
	}
	
	@AfterThrowing(pointcut="loginPointCut()", throwing="ex")
	public void doAfterLogin(JoinPoint joinPoint, Exception ex) {
		if(loginEnable) {
			loginLog(joinPoint, ex);
		}
	}
	
	/**
	 * insert login information to database
	 * @param joinPoint
	 * @param ex
	 */
	private void loginLog(JoinPoint joinPoint, Exception ex) {
		CscpLogLogin logdto=new CscpLogLogin();
		
		String username=getUserName(joinPoint);
		if (null == username || "".equals(username)) {
			username = SecurityUtils.getCurrentUserName();
		}
		logdto.setUserName(username);

		logdto.setIp((IpUtil.getRealIp(RequestUtil.getRequest())));
		String callSource = RequestUtil.getRequest().getHeader(HeaderConstants.CALL_SOURCE);
		String appVersion = RequestUtil.getRequest().getHeader(HeaderConstants.APP_VERSION.toLowerCase());
		if (org.apache.commons.lang.StringUtils.isBlank(appVersion)) {
			appVersion = RequestUtil.getRequest().getHeader(HeaderConstants.APP_VERSION_V.toLowerCase());
		}
		if (org.apache.commons.lang3.StringUtils.isBlank(appVersion)) {
			appVersion = RequestUtil.getRequest().getHeader(HeaderConstants.MY_APP_VERSION.toLowerCase());
		}
		String appVersionName = RequestUtil.getRequest().getHeader(HeaderConstants.APP_VERSION_NAME.toLowerCase());
		if (null == callSource || "".equals(callSource)) {
			callSource = "pc";
		}
		if (null == appVersionName || "".equals(appVersionName)) {
			appVersionName = "无";
		}
		String loginType = getLoginType();
		if (ex == null) {
			logdto.setStatus(CscpLogOperationDTO.SUCCESS);
			if (StringUtils.isNotBlank(appVersion)) {
				logdto.setMessage(callSource + "登录成功，登录方式：" + loginType + "，版本号为：" + appVersion + "，版本名称：" + appVersionName);
			} else {
				logdto.setMessage(callSource + "登录成功，登录方式：" + loginType);
			}
		} else {
			logdto.setStatus(CscpLogOperationDTO.ERROR);
			if (StringUtils.isNotBlank(appVersion)) {
				logdto.setMessage(callSource + "登录失败，登录方式：" + loginType + "，版本号为：" + appVersion + "，版本名称：" + appVersionName);
			} else {
				logdto.setMessage(callSource + "登录失败," + ex.getMessage() + "，登录方式：" + loginType);
			}
		}

		if (westoneEncryptService.isCipherMachine()) {
			String userName = logdto.getUserName();
			String optResult = logdto.getStatus();
			String message = logdto.getMessage();
			logdto.setCreateTime(LocalDateTime.now());
			String optTime = logdto.getCreateTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSSSSS"));
			String hmacContentSrc = UserAESUtil.md5WithSalt(userName + optResult + message + optTime);
			String hmacContent = westoneEncryptService.calculateSM3HMAC(hmacContentSrc);
			logdto.setHmacOperateContent(hmacContent);
		}

		loginService.save(logdto);
	}

	private String getLoginType() {
		String loginType = "";
		String requestURI = RequestUtil.getRequest().getRequestURI();
		String callSource = RequestUtil.getRequest().getHeader(HeaderConstants.CALL_SOURCE);
		if ("/api/system/login".equalsIgnoreCase(requestURI)) {
			loginType = "账号/密码登录";
		}
		if ("/api/system/appLoginWwSec".equalsIgnoreCase(requestURI)) {
			loginType = "短信验证码登录";
		}
		return loginType;
	}

	/**
	 * 获取登录用户名
	 * @param joinPoint
	 * @return
	 */
	private String getUserName(JoinPoint joinPoint) {
		Object[] args=joinPoint.getArgs();
		if(args!=null && args.length>0) {
			for(int index=0;index<args.length;index++) {
				if(args[index]!=null && (args[index] instanceof UserForm) ) {
					UserForm userform = (UserForm)args[index];
					return userform.getUserName();
				}
			}
		}
		return null;
	}
}
