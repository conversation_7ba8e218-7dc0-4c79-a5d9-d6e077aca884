package com.ctsi.ssdc.login;

import com.ctsi.ssdc.model.SystemLogOperation;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR> 15433
 * @date : 2025/05/19/10:09
 * description:
 */

@Data
public class LoginEvent {

    private Long userId;
    private String username;
    private String ip;
    private boolean success;
    public int badPasswordAttempts;
    public int lockoutTime;
    public SystemLogOperation systemLogOperation;
}
