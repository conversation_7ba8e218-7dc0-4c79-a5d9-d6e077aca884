package com.ctsi.ssdc.admin.service;

import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.common.SysBaseServiceI;
import com.ctsi.ssdc.admin.domain.TUserFocus;
import com.ctsi.ssdc.admin.domain.dto.TUserFocusDTO;
import com.ctsi.ssdc.admin.domain.dto.TUserFocusDataDTO;
import com.ctsi.ssdc.model.PageResult;


import java.util.List;

/**
 * <p>
 * 用户关注 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-19
 */
public interface ITUserFocusService extends SysBaseServiceI<TUserFocus> {


    /**
     * 分页查询
     *
     * @param entityDTO
     * @param page
     * @return
     */
    PageResult<TUserFocusDTO> queryListPage(TUserFocusDTO entityDTO, BasePageForm page);

    /**
     * 获取所有不分页
     *
     * @param entity
     * @return
     */
    List<TUserFocusDTO> queryList(TUserFocusDTO entity);

    /**
     * 根据主键id获取单个对象
     *
     * @param id
     * @return
     */
    TUserFocusDTO findOne(Long id);

    /**
     * 新增
     *
     * @param entity
     * @return
     */
    TUserFocusDTO create(TUserFocusDTO entity);


    /**
     * 更新
     *
     * @param entity
     * @return
     */
    int update(TUserFocusDTO entity);

    /**
     * 删除
     *
     * @param id
     * @return
     */
    int delete(Long id);

     /**
     * 是否存在
     *
     * existByTUserFocusId
     * @param code
     * @return
     */
    boolean existByTUserFocusId(Long code);

    /**
    * 批量新增
    *
    * create batch
    * @param dataList
    * @return
    */
    Boolean insertBatch(List<TUserFocusDTO> dataList);


    TUserFocusDTO findOne(Long dataId , long currentUserId);

    List<TUserFocusDTO> queryListByDataIds(List<Long> dataIds);

    /**
     *
     * @param busyId 业务id
     * @return
     */
    TUserFocusDTO getFocusInfoByBusyId(Long busyId);

    void deleteByDataId(Long bizid);

	void updateFocusStatusByBizId(Long bizid , Integer status);
}
