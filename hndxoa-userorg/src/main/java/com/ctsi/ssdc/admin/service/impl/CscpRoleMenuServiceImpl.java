package com.ctsi.ssdc.admin.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.enums.DataFilterMetaData;
import com.ctsi.hndx.utils.ListCopyUtil;
import com.ctsi.hndx.utils.PageHelperUtil;
import com.ctsi.ssdc.admin.domain.CscpMenus;
import com.ctsi.ssdc.admin.domain.CscpRoleMenu;
import com.ctsi.ssdc.admin.domain.CscpUserRole;
import com.ctsi.ssdc.admin.domain.dto.CscpDataScopeDTO;
import com.ctsi.ssdc.admin.domain.dto.CscpMenusDTO;
import com.ctsi.ssdc.admin.domain.dto.CscpRoleMenuDTO;
import com.ctsi.ssdc.admin.repository.CscpMenusRepository;
import com.ctsi.ssdc.admin.repository.CscpRoleMenuRepository;
import com.ctsi.ssdc.admin.service.CscpRoleMenuService;
import com.ctsi.ssdc.model.PageResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
@Service
public class CscpRoleMenuServiceImpl extends ServiceImpl<CscpRoleMenuRepository,CscpRoleMenu> implements CscpRoleMenuService {

    @Autowired
    private CscpRoleMenuRepository cscpRoleMenuRepository;

    @Autowired
    private CscpMenusRepository cscpMenusRepository;



    @Override
    public boolean insertCscpRoleMenus(Long id ,  List<CscpDataScopeDTO> menuIdDataScopeList) {
        List<CscpRoleMenu> collect =menuIdDataScopeList.stream().map(x -> {
            CscpRoleMenu cscpRoleMenu = new CscpRoleMenu();
            cscpRoleMenu.setRoleId(id);
            cscpRoleMenu.setMenuId(x.getMenuId());
            return cscpRoleMenu;
        }).collect(Collectors.toList());
        super.saveBatch(collect);
        return true;
    }

    @Override
    public boolean deleteCscpRoleMenus(CscpRoleMenuDTO cscpRoleMenuDTO) {
        Long roleId = cscpRoleMenuDTO.getRoleId();
        List<Long> menuIdList = cscpRoleMenuDTO.getMenuIdList();
        LambdaQueryWrapper<CscpRoleMenu> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.eq(CscpRoleMenu::getRoleId, roleId);
        queryWrapper.in(CscpRoleMenu::getMenuId, menuIdList);
        cscpRoleMenuRepository.delete(queryWrapper);
        return true;
    }

    @Override
    public PageResult<CscpMenusDTO> queryCscpMenuByRoleId(Long roleId, BasePageForm basePageForm) {
        LambdaQueryWrapper<CscpRoleMenu> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.eq(CscpRoleMenu::getRoleId, roleId);
        List<Long> menuIdList = cscpRoleMenuRepository.selectList(queryWrapper).stream()
                .map(x -> x.getMenuId()).distinct().collect(Collectors.toList());

        LambdaQueryWrapper<CscpMenus> lambdaQueryWrapper = new LambdaQueryWrapper();
        lambdaQueryWrapper.in(CscpMenus::getId, menuIdList);
        IPage<CscpMenus> menusIPage = cscpMenusRepository.selectPage(
                PageHelperUtil.getMPlusPageByBasePage(basePageForm), lambdaQueryWrapper);
        List<CscpMenusDTO> data = ListCopyUtil.copy(menusIPage.getRecords(), CscpMenusDTO.class);
        return new PageResult<CscpMenusDTO>(data, menusIPage.getTotal(), menusIPage.getTotal());
    }


}
