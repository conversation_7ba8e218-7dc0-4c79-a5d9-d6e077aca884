package com.ctsi.ssdc.admin.domain.dto;

import com.ctsi.hndx.common.BaseDtoEntity;

import java.math.BigInteger;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 部门管理权限表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-04
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value="TDeptManagementAuthorityDTO对象", description="部门管理权限表")
public class TDeptManagementAuthorityDTO extends BaseDtoEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 管理员用户ID
     */
    @ApiModelProperty(value = "管理员用户ID")
    private Long userId;

    /**
     * 被管理的部门ID
     */
    @ApiModelProperty(value = "被管理的部门ID")
    private Long deptId;

    /**
     * 被管理的部门ID
     */
    @ApiModelProperty(value = "被管理的部门名称")
    private String deptName;


}
