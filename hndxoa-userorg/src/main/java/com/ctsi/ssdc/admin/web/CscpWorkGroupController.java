package com.ctsi.ssdc.admin.web;

import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.enums.DBOperation;
import com.ctsi.hndx.exception.BusinessException;
import com.ctsi.hndx.result.ResultVO;
import com.ctsi.ssdc.admin.domain.CscpUserWorkGroup;
import com.ctsi.ssdc.admin.domain.dto.CscpOtherListRealName;
import com.ctsi.ssdc.admin.domain.dto.CscpUserDTO;
import com.ctsi.ssdc.admin.domain.dto.CscpWorkGroupDTO;
import com.ctsi.ssdc.admin.service.CscpUserWorkGroupService;
import com.ctsi.ssdc.admin.service.CscpWorkGroupService;
import com.ctsi.ssdc.annotation.OperationLog;
import com.ctsi.ssdc.model.PageResult;
import com.ctsi.ssdc.security.SecurityUtils;
import com.zdww.biyi.component.sdk.aop.BeanExposeMethodAble;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.propertyeditors.CustomDateEditor;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.*;

import java.net.URISyntaxException;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

import static com.ctsi.ssdc.admin.consts.ComponentConstant.ADMIN;
import static com.ctsi.ssdc.admin.consts.ComponentConstant.METHOD;


@RestController
@RequestMapping("/api/system")
@Api(value = "工作组管理接口", tags = "工作组管理接口")
public class CscpWorkGroupController {

    private final Logger log = LoggerFactory.getLogger(CscpWorkGroupController.class);

    private static final String ENTITY_NAME = "cscpWorkGroup";

    private final CscpWorkGroupService cscpWorkGroupService;

    @Autowired
    private CscpUserWorkGroupService cscpUserWorkGroupService;
    
    public CscpWorkGroupController(CscpWorkGroupService cscpWorkGroupService) {
        this.cscpWorkGroupService = cscpWorkGroupService;
    }

    @InitBinder
    public void initBinder(WebDataBinder binder) {   
        DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");   
        dateFormat.setLenient(true);   
        binder.registerCustomEditor(Date.class, new CustomDateEditor(dateFormat, true));   
    }

    @ApiOperation(value = "新增工作组")
    @PostMapping("/createCscpWorkGroup")
    @OperationLog(dBOperation = DBOperation.ADD,message = "新增工作组")
    @BeanExposeMethodAble(component = ADMIN,method = METHOD)
    public ResultVO<CscpWorkGroupDTO> createCscpWorkGroup(@RequestBody CscpWorkGroupDTO cscpWorkGroupDTO) throws URISyntaxException {
        log.debug("REST request to save CscpWorkGroup : {}", cscpWorkGroupDTO);
        if (cscpWorkGroupDTO.getId() != null) {
            throw new BusinessException("A new cscpWorkGroup cannot already have an ID", ENTITY_NAME, "idexists");
        }
        CscpWorkGroupDTO result = cscpWorkGroupService.insert(cscpWorkGroupDTO);
        return ResultVO.success(result);
    }

    @ApiOperation(value = "修改工作组")
    @PutMapping("/updateCscpWorkGroup")
    @BeanExposeMethodAble(component = ADMIN,method = METHOD)
    @OperationLog(dBOperation = DBOperation.UPDATE,message = "修改工作组")
    public ResultVO<CscpWorkGroupDTO> updateCscpWorkGroup(@RequestBody CscpWorkGroupDTO cscpWorkGroupDTO) throws URISyntaxException {
        log.debug("REST request to update CscpWorkGroup : {}", cscpWorkGroupDTO);
        if (cscpWorkGroupDTO.getId() == null) {
            return createCscpWorkGroup(cscpWorkGroupDTO);
        }
        CscpWorkGroupDTO result = cscpWorkGroupService.update(cscpWorkGroupDTO);
        return ResultVO.success(result);
    }

    @ApiOperation(value = "分页查询工作组信息")
    @GetMapping("/getCscpWorkGroups")
    @BeanExposeMethodAble(component = ADMIN,method = METHOD)
    public ResultVO<PageResult<CscpWorkGroupDTO>> getCscpWorkGroups(CscpWorkGroupDTO cscpWorkGroupDTO, BasePageForm basePageForm) {
        log.debug("REST request to get CscpWorkGroups");
        PageResult<CscpWorkGroupDTO> result = cscpWorkGroupService.findByCscpWorkGroupDTO(cscpWorkGroupDTO, basePageForm);
        return ResultVO.success(result);
    }


    @ApiOperation(value = "通过ID查询工作组信息")
    @GetMapping("/getCscpWorkGroup/{id}")
    @BeanExposeMethodAble(component = ADMIN,method = METHOD)
    public ResultVO<CscpWorkGroupDTO> getCscpWorkGroup(@PathVariable Long id) {
        log.debug("REST request to get CscpWorkGroup : {}", id);
        CscpWorkGroupDTO cscpWorkGroupDTO = cscpWorkGroupService.findOne(id);
        return ResultVO.success(cscpWorkGroupDTO);
    }


    @ApiOperation(value = "通过ID删除工作组信息")
    @DeleteMapping("/deleteCscpWorkGroup/{id}")
    @BeanExposeMethodAble(component = ADMIN,method = METHOD)
    @OperationLog(dBOperation = DBOperation.DELETE,message = "通过ID删除工作组信息")
    public ResultVO deleteCscpWorkGroup(@PathVariable Long id) {
        log.debug("REST request to delete CscpWorkGroup : {}", id);
        cscpWorkGroupService.delete(id);
        return ResultVO.success();
    }


    @ApiOperation(value = "删除工作组下的用户信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "工作组ID", required = true),
            @ApiImplicitParam(name = "userIdList", value = "需要删除工作组的员工ID集合", allowMultiple = true,
                    required = true, dataType = "Long")
    })
    @DeleteMapping("/deleteUserInWorkGroup/{id}")
    @BeanExposeMethodAble(component = ADMIN,method = METHOD)
    @OperationLog(dBOperation = DBOperation.DELETE,message = "删除工作组下的用户信息")
    public ResultVO deleteUserInWorkGroup(@PathVariable Long id, @RequestBody List<Long> userIdList) {
        log.debug("REST request to delete CscpWorkGroup : {}", id);
        cscpUserWorkGroupService.deleteUserInWorkGroup(id, userIdList);
        return ResultVO.success();
    }


    @ApiOperation(value = "给工作组增加员工")
    @PostMapping("/insertCscpUserGroups")
    @OperationLog(dBOperation = DBOperation.ADD,message = "给工作组增加用户信息")
    public ResultVO<Boolean> insertCscpUserGroups(@RequestBody List<CscpUserWorkGroup> list) {
        Boolean result = cscpUserWorkGroupService.insertCscpUserGroups(list);
        return ResultVO.success(result);
    }

    @ApiOperation(value = "查询该工作组下所有用户信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "groupId", value = "工作组ID", required = true)
    })
    @GetMapping("/queryCscpUserByGroupId/{groupId}")
    public ResultVO<PageResult<CscpUserDTO>> queryCscpUserByGroupId(@PathVariable Long groupId,
                                                                    String realName,
                                                                    BasePageForm basePageForm) {
        CscpOtherListRealName cscpOtherListRealName = new CscpOtherListRealName();
        cscpOtherListRealName.setGroupId(groupId);
        cscpOtherListRealName.setRealName(realName);
        PageResult<CscpUserDTO> userDTOList = cscpUserWorkGroupService.queryCscpUserByGroupListIds(cscpOtherListRealName, basePageForm);

        return ResultVO.success(userDTOList);
    }


    @ApiOperation(value = "查询工作组id集成下面的的所有用户信息，根据用户名模糊查询")
    @PostMapping("/queryCscpUserByGroupListIds")
    @BeanExposeMethodAble(component = ADMIN, method = METHOD)
    public ResultVO<PageResult<CscpUserDTO>> queryCscpUserByGroupListIds(@RequestBody CscpOtherListRealName cscpOtherListRealName,
                                                                        BasePageForm basePageForm) {
        cscpOtherListRealName.setGroupId(cscpOtherListRealName.getIds().get(0));
        PageResult<CscpUserDTO> cscpUserDTOList = cscpUserWorkGroupService.queryCscpUserByGroupListIds(cscpOtherListRealName, basePageForm);
        return ResultVO.success(cscpUserDTOList);
    }


    @ApiOperation(value = "查询不在该工作组下所有用户信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "groupId", value = "工作组ID", required = true)
    })
    @GetMapping("/queryUsersNotInGroup/{groupId}")
    public ResultVO<PageResult<CscpUserDTO>> queryUsersNotInGroup(@PathVariable Long groupId,
                                                                  String realName,
                                                                  BasePageForm basePageForm) {
        PageResult<CscpUserDTO> userDTOList = cscpUserWorkGroupService.queryUsersNotInGroup(groupId, realName, basePageForm);
        return ResultVO.success(userDTOList);
    }


    @ApiOperation(value = "获取当前登录用户的工作组信息")
    @GetMapping("/queryCscpUserByGroupId")
    public ResultVO<List<Long>> queryCscpUserByGroupId() {
        List<Long> longs = cscpUserWorkGroupService.queryUserGroupsId(SecurityUtils.getCurrentUserId());
        return ResultVO.success(longs);
    }

    
}
