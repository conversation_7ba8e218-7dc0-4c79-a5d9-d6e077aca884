package com.ctsi.ssdc.admin.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.ctsi.hndx.config.DbConst;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR> 15433
 * @date : 2025/06/12/14:51
 * description: 报批传阅系统数据源操作服务实现
 */
@Service
@DS(DbConst.OA)
public class DsOptBpcyServiceImpl extends DsOptServiceImpl {


    @Override
    public String target() {
        return DbConst.OA;
    }

    @Override
    public String selectUserLoginCountMessage(Long id) {
        return "报批传阅系统有数据，无法删除，用户id：" + id;
    }
}
