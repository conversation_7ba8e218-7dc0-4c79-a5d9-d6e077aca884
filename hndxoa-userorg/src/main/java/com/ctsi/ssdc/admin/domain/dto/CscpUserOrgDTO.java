package com.ctsi.ssdc.admin.domain.dto;

import com.ctsi.hndx.common.BaseDtoEntity;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR> Generator
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class CscpUserOrgDTO extends BaseDtoEntity {

    private static final long serialVersionUID = 1L;


    @JsonSerialize(using = ToStringSerializer.class)
    private Long userId;

    /**
     * 部门id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long orgId;

    /**
     * 单位id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long companyId;

    /**
     * 职务
     */
    @ApiModelProperty("职务")
    private String post;

    @ApiModelProperty("部门排序号")
    private Integer orderBy;

    /**
     * 是否默认登录账号，同一个账号属于多个单位或者部门时，1表示默认登录账号
     */
    private Integer defaultDepartment;

    /**
     * 是否为部门领导(1个部门可以设置多个)
     * 1表示为部门领导
     */
    @ApiModelProperty("是否为部门领导")
    private Integer departmentHead;

    @ApiModelProperty("用户名")
    private String userName;

    /**
     * 部门名称
     */
    private String orgName;

    /**
     * 单位名称
     */
    private String companyName;

    /**
     * 机构完整编码
     */
    private String pathCode;

    /**
     * 机构简称
     */
    private String orgAbbreviation;

    /**
     * 职级
     */
    @ApiModelProperty("职级")
    private String rank;

    @ApiModelProperty("进入单位时间")
    private LocalDateTime entryTime;
}