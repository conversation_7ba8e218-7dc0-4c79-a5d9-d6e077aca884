package com.ctsi.ssdc.admin.cache;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ctsi.hndx.constant.SysConfigConstant;
import com.ctsi.hndx.tsysconfig.service.ISysConfigService;
import com.ctsi.ssdc.util.RedisUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

/**
 * 基于google guava的实现，缓存验证码信息
 * <AUTHOR>
 *
 */

@Component
public class RedisLoginCache implements LoginCache {

    @Autowired
    private ISysConfigService sysConfigService;

    @Autowired(
            required = false
    )
    private RedisUtil redisUtil;





    @Override
    public boolean put(String key, Integer value) {
        try {
            JSONObject jsonObject = JSON.parseObject(sysConfigService.getSysConfigValueByCode(SysConfigConstant.SECURITY_AUTHENTICATION));
            // 密码登录失败锁定时间 单位为s
            redisUtil.set(key, value.toString(), jsonObject.getIntValue("loginLockTime") * 60, TimeUnit.SECONDS);
        } catch (Exception ex) {
            return false;
        }
		return true;
	}

    @Override
	public Integer get(String key) {

        Object str = redisUtil.get(key);

        Integer result = null;
        if(str!=null && str instanceof Integer){
            result = (Integer) str;
        }
        if(str!=null && str instanceof String){
            result = Integer.valueOf((String)str) ;
        }

        return result;
	}

    @Override
    public boolean delete(String key) {
        redisUtil.del(key);
        return true;
    }

    @Override
	public Long getExpire(String key){
        long expire = redisUtil.getExpire(key);
        return expire;
    }

}
