package com.ctsi.ssdc.admin.domain.dto;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.ctsi.hndx.excel.ExcelCustomStringTrimConverter;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Classname UserDTO
 * @Description
 * @Date 2021/12/29/0029 15:25
 */
@Data
@ApiModel(value="用户导入模板", description="用户导入模板")
public class UserImportDTO implements Serializable {

    private static final long serialVersionUID = 6834936645058754443L;

    @ApiModelProperty(value = "用户名")
    @ExcelIgnore
    private String userName;

    @ApiModelProperty(value = "用户密码")
    @ExcelIgnore
    private String password;

    @ApiModelProperty(value = "登录名")
    @ExcelProperty(value = "登录名",converter = ExcelCustomStringTrimConverter.class)
    private String importLoginName;

    @ApiModelProperty(value = "真实姓名*")
    @ExcelProperty(value = "姓名*",converter = ExcelCustomStringTrimConverter.class)
    private String realName;

    @ApiModelProperty(value = "手机号码*")
    @ExcelProperty(value = "手机号码*")
    private String mobile;

    @ApiModelProperty(value = "身份证号")
    @ExcelProperty(value = "身份证号", converter = ExcelCustomStringTrimConverter.class)
    private String idCardNo;

    @ApiModelProperty(value = "单位名称")
    @ExcelIgnore
    private String companyName;

    @ApiModelProperty(value = "部门名称")
    @ExcelProperty(value = "人员所在内设机构名称(多部门使用,隔开)*", converter = ExcelCustomStringTrimConverter.class)
    private String departmentName;

    @ApiModelProperty(value = "职务")
    @ExcelProperty(value = "职务", converter = ExcelCustomStringTrimConverter.class)
    private String post;

    @ApiModelProperty(value = "职级")
    @ExcelProperty(value = "职级")
    private String rank;

    @ApiModelProperty(value = "用户部门中间表排序号")
    @ExcelProperty(value = "人员所在内设机构排序(多部门使用,隔开)*")
    private String sort;

    @ApiModelProperty(value = "备用手机号")
    @ExcelProperty(value = "备用手机号")
    private String backupMobile;

    @ApiModelProperty(value = "办公电话")
    @ExcelProperty(value = "办公电话",converter = ExcelCustomStringTrimConverter.class)
    private String officePhone;

    @ApiModelProperty(value = "邮箱")
    @ExcelProperty(value = "邮箱", converter = ExcelCustomStringTrimConverter.class)
    private String email;

    @ApiModelProperty(value = "唯一信任号")
    @ExcelProperty(value = "唯一信任号")
    private String strId;

    @ApiModelProperty(value = "失败原因")
    @ExcelProperty("失败原因")
    private String failedReason;

    @ApiModelProperty(value = "用户表排序号")
    @ExcelIgnore
    private String orderBy;

    @ApiModelProperty("是否为部门领导")
    @ExcelIgnore
    private String departmentHead;

    @ApiModelProperty(value = "部门ID")
    @ExcelIgnore
    private Long departmentId;

    @ApiModelProperty(value = "单位ID")
    @ExcelIgnore
    private Long companyId;

    @ApiModelProperty(value = "租户ID")
    @ExcelIgnore
    private Long tenantId;

    @ApiModelProperty(value = "通讯录是否显示")
    @ExcelIgnore
    private String display;

    @ApiModelProperty(value = "通讯录手机号是否显示")
    @ExcelIgnore
    private String whetherShow;

    @ApiModelProperty(value = "是否统计")
    @ExcelIgnore
    private String statistics;

    @ApiModelProperty(value = "秘书手机号")
    @ExcelIgnore
    private String secretaryPhone;

    @ApiModelProperty(value = "秘书姓名")
    @ExcelIgnore
    private String secretaryName;

    @ExcelIgnore
    private String flag;
}
