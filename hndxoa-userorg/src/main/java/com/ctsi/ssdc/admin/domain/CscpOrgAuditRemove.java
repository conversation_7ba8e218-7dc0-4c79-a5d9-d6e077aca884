package com.ctsi.ssdc.admin.domain;

import com.ctsi.hndx.common.BaseEntity;
import lombok.Data;

import java.io.Serializable;

/**
    * 机构删除审核表
    */
@Data
public class CscpOrgAuditRemove extends BaseEntity implements Serializable {

    /**
    * 机构id
    */
    private Long orgId;

    /**
    * 机构名称
    */
    private String orgName;

    /**
    * 机构编码
    */
    private String orgCode;

    /**
    * 审核申请人
    */
    private String auditCreateName;

    /**
    * 审核说明
    */
    private String auditExplain;

    /**
    * 审核类型[0:未审核 1:已审核]
    */
    private Integer auditType;

    /**
     * 删除方式[0:直接删除;1:申请删除]
     */
    private Integer removeType;


    private static final long serialVersionUID = 1L;
}