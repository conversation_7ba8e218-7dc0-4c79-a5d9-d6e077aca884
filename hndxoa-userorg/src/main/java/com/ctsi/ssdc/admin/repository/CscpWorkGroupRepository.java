package com.ctsi.ssdc.admin.repository;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ctsi.hndx.common.MybatisBaseMapper;
import com.ctsi.ssdc.admin.domain.CscpWorkGroup;
import com.ctsi.ssdc.database.annotation.InjectByDataBaseType;
import com.ctsi.ssdc.database.enums.EnumDatabaseName;




/**
 * <AUTHOR> Generator
*/
@InjectByDataBaseType(includes= {EnumDatabaseName.ORACLE})
public interface CscpWorkGroupRepository extends MybatisBaseMapper<CscpWorkGroup> {

}