package com.ctsi.ssdc.admin.domain.dto;


import com.ctsi.ssdc.security.CscpUserNumberDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @Classname StatisticsSignInPeopleDTO
 * @Description
 * @Date 2022/1/7 17:02
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("租户在线人数和不在线人数")
public class CscpStatisticsSignInTenantDTO {


    /**
     * 在线的单位和租户
     */
    @ApiModelProperty(value = "在线的单位和租户")
    private List<CscpTenantNumberDTO> onLineList;

    /**
     * 在线单位和租户数量
     */
    @ApiModelProperty(value = "在线用户数量")
    private Integer onLineCount;

    /**
     * 不在线的用户
     */
    @ApiModelProperty(value = "不在线的用户")
    private List<CscpTenantNumberDTO> notOnLineList;

    /**
     * 不在线用户数量
     */
    @ApiModelProperty(value = "不在线用户数量")
    private Integer notOnLineCount;


}
