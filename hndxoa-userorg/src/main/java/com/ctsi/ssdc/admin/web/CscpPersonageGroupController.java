package com.ctsi.ssdc.admin.web;


import com.ctsi.hndx.annotations.ResponseResultVo;
import com.ctsi.hndx.common.BaseController;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.enums.DBOperation;
import com.ctsi.hndx.result.ResultCode;
import com.ctsi.hndx.result.ResultVO;
import com.ctsi.ssdc.admin.domain.dto.CscpPersonageGroupDTO;
import com.ctsi.ssdc.admin.service.ICscpPersonageGroupService;
import com.ctsi.ssdc.annotation.OperationLog;
import com.ctsi.ssdc.model.PageResult;
import com.ctsi.ssdc.model.ResResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>
 * 个人自定义组表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-13
 */
@Slf4j
@RestController
@ResponseResultVo
@RequestMapping("/api/cscpPersonageGroup")
@Api(value = "个人自定义组表", tags = "个人自定义组表接口")
public class CscpPersonageGroupController extends BaseController {
    private static final String ENTITY_NAME = "cscpPersonageGroup";

    @Autowired
    private ICscpPersonageGroupService cscpPersonageGroupService;



    /**
     *  新增个人自定义组表批量数据.
     */
    @PostMapping("/createBatch")
    @ApiOperation(value = "新增批量(权限code码为：cscp.cscpPersonageGroup.add)", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.ADD,message = "新增个人自定义组表批量数据")
    public ResultVO createBatch(@RequestBody List<CscpPersonageGroupDTO> cscpPersonageGroupList) {
        Boolean  result = cscpPersonageGroupService.insertBatch(cscpPersonageGroupList);
        if(result){
            return ResultVO.success();
        }else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }

    /**
     *  新增数据.
     */
    @PostMapping("/create")
    @ApiOperation(value = "新增(权限code码为：cscp.cscpPersonageGroup.add)", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.ADD,message = "新增个人自定义组表数据")
    public ResultVO<CscpPersonageGroupDTO> create(@RequestBody CscpPersonageGroupDTO dto)  {
        CscpPersonageGroupDTO result = cscpPersonageGroupService.create(dto);
        return ResultVO.success(result);
    }

    /**
     *  更新存在数据.
     */
    @PostMapping("/update")
    @ApiOperation(value = "更新存在数据(权限code码为：cscp.cscpPersonageGroup.update)", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.UPDATE,message = "更新个人自定义组表数据")
    public ResultVO update(@RequestBody CscpPersonageGroupDTO dto) {
        Assert.notNull(dto.getId(), "general.IdNotNull");
        int count = cscpPersonageGroupService.update(dto);
        if(count > 0 ){
            return ResultVO.success();
        }else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }

    /**
     *  删除存在数据.
     */
    @DeleteMapping("/delete/{id}")
    @OperationLog(dBOperation = DBOperation.DELETE,message = "删除个人自定义组表数据")
    @ApiOperation(value = "删除存在数据(权限code码为：cscp.cscpPersonageGroup.delete)", notes = "传入参数")
    public ResultVO delete(@PathVariable Long id) {
        int count = cscpPersonageGroupService.delete(id);
        if(count > 0 ){
            return ResultVO.success();
        }else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }

    /**
     * 查询单条数据.
     */
    @GetMapping("/get/{id}")
    @ApiOperation(value = "查询单条数据", notes = "传入参数")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
    public ResultVO get(@PathVariable Long id) {
        CscpPersonageGroupDTO dto = cscpPersonageGroupService.findOne(id);
        return ResultVO.success(dto);
    }

    /**
     *  分页查询多条数据.
     */
    @GetMapping("/queryCscpPersonageGroupPage")
    @ApiOperation(value = "翻页查询多条数据", notes = "传入参数")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
    public ResultVO<PageResult<CscpPersonageGroupDTO>> queryCscpPersonageGroupPage(CscpPersonageGroupDTO dto, BasePageForm basePageForm) {
        return ResultVO.success(cscpPersonageGroupService.queryListPage(dto, basePageForm));
    }

    /**
     * 查询多条数据.不分页
     */
    @GetMapping("/queryCscpPersonageGroup")
    @ApiOperation(value = "查询多条数据", notes = "传入参数")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
    public ResultVO<ResResult<CscpPersonageGroupDTO>> queryCscpPersonageGroup(CscpPersonageGroupDTO dto) {
        List<CscpPersonageGroupDTO> list = cscpPersonageGroupService.queryList(dto);
        return ResultVO.success(new ResResult<CscpPersonageGroupDTO>(list));
    }

    @GetMapping("/existByPersonageGroupName")
    @ApiOperation(value = "名称是否存在", notes = "传入参数")
    public ResultVO<Boolean> existByPersonageGroupName(@RequestParam String name) {
        Boolean flag = cscpPersonageGroupService.existByPersonageGroupName(name);
        return ResultVO.success(flag);
    }
}
