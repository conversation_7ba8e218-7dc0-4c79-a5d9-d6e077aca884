package com.ctsi.ssdc.admin.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.common.SysBaseServiceImpl;
import com.ctsi.hndx.utils.BeanConvertUtils;
import com.ctsi.hndx.utils.ListCopyUtil;
import com.ctsi.hndx.utils.PageHelperUtil;
import com.ctsi.hndx.utils.SnowflakeIdUtil;
import com.ctsi.ssdc.admin.domain.CscpPersonageGroup;
import com.ctsi.ssdc.admin.domain.dto.CscpPersonageGroupDTO;
import com.ctsi.ssdc.admin.domain.dto.CscpPersonageUserGroupDTO;
import com.ctsi.ssdc.admin.repository.CscpPersonageGroupMapper;
import com.ctsi.ssdc.admin.repository.CscpPersonageUserGroupMapper;
import com.ctsi.ssdc.admin.service.ICscpPersonageGroupService;
import com.ctsi.ssdc.admin.service.ICscpPersonageUserGroupService;
import com.ctsi.ssdc.model.PageResult;
import com.ctsi.ssdc.security.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <p>
 * 个人自定义组表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-13
 */
@Slf4j
@Service
public class CscpPersonageGroupServiceImpl extends SysBaseServiceImpl<CscpPersonageGroupMapper, CscpPersonageGroup> implements ICscpPersonageGroupService {

    @Autowired
    private CscpPersonageGroupMapper cscpPersonageGroupMapper;
    @Autowired
    private ICscpPersonageUserGroupService cscpPersonageUserGroupService;
    @Autowired
    private CscpPersonageUserGroupMapper cscpPersonageUserGroupMapper;

    /**
     * 翻页
     *
     * @param entityDTO
     * @param basePageForm
     * @return
     */
    @Override
    public PageResult<CscpPersonageGroupDTO> queryListPage(CscpPersonageGroupDTO entityDTO, BasePageForm basePageForm) {
        //设置条件
        LambdaQueryWrapper<CscpPersonageGroup> queryWrapper = new LambdaQueryWrapper();

        if(StringUtils.isNotBlank(entityDTO.getPersonageName())) {
            queryWrapper.like(CscpPersonageGroup::getPersonageName, entityDTO.getPersonageName());
        }
        queryWrapper.eq(CscpPersonageGroup::getCreateBy, SecurityUtils.getCurrentUserId());
        queryWrapper.orderByAsc(CscpPersonageGroup::getOrderBy);
        IPage<CscpPersonageGroup> pageData = cscpPersonageGroupMapper.selectPageNoAdd(
                PageHelperUtil.getMPlusPageByBasePage(basePageForm), queryWrapper);
        //返回
        IPage<CscpPersonageGroupDTO> data  = pageData.convert(entity -> BeanConvertUtils.copyProperties(entity,CscpPersonageGroupDTO.class));

        for (CscpPersonageGroupDTO record : data.getRecords()) {
            Long id = record.getId();
            CscpPersonageUserGroupDTO dto = BeanConvertUtils.copyProperties(record , CscpPersonageUserGroupDTO.class);
            dto.setGroupId(id);
            PageResult<CscpPersonageUserGroupDTO> list = cscpPersonageUserGroupService.queryListPage(dto ,
                    new BasePageForm(1,10000));
            record.setCscpPersonageUserGroupDTOList(list.getData());
        }

        return new PageResult<CscpPersonageGroupDTO>(data.getRecords(),
                data.getTotal(), data.getCurrent());
    }

    /**
     * 列表查询
     *
     * @param entityDTO
     * @return
     */
    @Override
    public List<CscpPersonageGroupDTO> queryList(CscpPersonageGroupDTO entityDTO) {
        LambdaQueryWrapper<CscpPersonageGroup> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.orderByAsc(CscpPersonageGroup::getOrderBy);
        List<CscpPersonageGroup> listData = cscpPersonageGroupMapper.selectListNoAdd(queryWrapper);
        List<CscpPersonageGroupDTO> CscpFormHaoqianVersionDTOList = ListCopyUtil.copy(listData, CscpPersonageGroupDTO.class);
        return CscpFormHaoqianVersionDTOList;
    }

    /**
     * 单个查询
     *
     * @param id the id of the entity
     * @return
     */
    @Override
    public CscpPersonageGroupDTO findOne(Long id) {
        CscpPersonageGroup cscpPersonageGroup =  cscpPersonageGroupMapper.selectById(id);
        return  BeanConvertUtils.copyProperties(cscpPersonageGroup,CscpPersonageGroupDTO.class);
    }


    /**
     * 新增
     *
     * @param entityDTO the entity to create
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public CscpPersonageGroupDTO create(CscpPersonageGroupDTO entityDTO) {
        LambdaQueryWrapper<CscpPersonageGroup> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.orderByDesc(CscpPersonageGroup::getOrderBy);
        List<CscpPersonageGroup> groupList = cscpPersonageGroupMapper.selectList(queryWrapper);
        // 设置排序
        if (CollectionUtils.isNotEmpty(groupList) && groupList.get(0).getOrderBy()!=null){
            entityDTO.setOrderBy(groupList.get(0).getOrderBy()+1);
        }else {
            entityDTO.setOrderBy(groupList.size()+1);
        }
        entityDTO.setId(SnowflakeIdUtil.getSnowFlakeLongId());
        CscpPersonageGroup cscpFormHaoqianVersion =  BeanConvertUtils.copyProperties(entityDTO,CscpPersonageGroup.class);
        save(cscpFormHaoqianVersion);
        // 保存关联的用户
        if (CollectionUtils.isNotEmpty(entityDTO.getCscpPersonageUserGroupDTOList())) {
            entityDTO.getCscpPersonageUserGroupDTOList().forEach(i -> i.setGroupId(entityDTO.getId()));
            cscpPersonageUserGroupService.insertBatch(entityDTO.getCscpPersonageUserGroupDTOList());
        }
        return  BeanConvertUtils.copyProperties(cscpFormHaoqianVersion,CscpPersonageGroupDTO.class);
    }

    /**
     * 修改
     *
     * @param entity the entity to update
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int update(CscpPersonageGroupDTO entity) {
        // 更新关联的用户
        if (CollectionUtils.isNotEmpty(entity.getCscpPersonageUserGroupDTOList())) {
            cscpPersonageUserGroupMapper.deleteGroupUserByUserId(entity.getId());

            if (CollectionUtils.isNotEmpty(entity.getCscpPersonageUserGroupDTOList())) {
                entity.getCscpPersonageUserGroupDTOList().forEach(i -> i.setGroupId(entity.getId()));
                cscpPersonageUserGroupService.insertBatch(entity.getCscpPersonageUserGroupDTOList());
            }
        }


        CscpPersonageGroup cscpFormHaoqianVersion = BeanConvertUtils.copyProperties(entity,CscpPersonageGroup.class);
        return cscpPersonageGroupMapper.updateById(cscpFormHaoqianVersion);
    }

    /**
     * 删除
     *
     * @param id the id of the entity
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int delete(Long id) {
        return cscpPersonageGroupMapper.deleteById(id);
    }


    /**
     * 批量新增
     *
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertBatch(List<CscpPersonageGroupDTO> dataList) {
        List<CscpPersonageGroup> result = ListCopyUtil.copy(dataList, CscpPersonageGroup.class);
        return saveBatch(result);
    }

    @Override
    public boolean existByPersonageGroupName(String name) {
        if (StringUtils.isNotEmpty(name)) {
            LambdaQueryWrapper<CscpPersonageGroup> queryWrapper = new LambdaQueryWrapper();
            queryWrapper.eq(CscpPersonageGroup::getPersonageName, name);
            queryWrapper.eq(CscpPersonageGroup::getCreateBy, SecurityUtils.getCurrentUserId());
            List<CscpPersonageGroup> result = cscpPersonageGroupMapper.selectListNoAdd(queryWrapper);
            return result.size() > 0;
        }
        return true;
    }
}
