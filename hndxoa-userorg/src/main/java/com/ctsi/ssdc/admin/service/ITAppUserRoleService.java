package com.ctsi.ssdc.admin.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.common.SysBaseServiceI;
import com.ctsi.ssdc.admin.domain.TAppUserRole;
import com.ctsi.ssdc.admin.domain.dto.CscpUserDTO;
import com.ctsi.ssdc.admin.domain.dto.TAppRoleDTO;
import com.ctsi.ssdc.admin.domain.dto.TAppUserRoleDTO;
import com.ctsi.ssdc.model.PageResult;

import java.util.List;

/**
 * <p>
 * 用户权限表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-19
 */
public interface ITAppUserRoleService extends SysBaseServiceI<TAppUserRole> {


    /**
     * 分页查询
     */
    IPage<TAppUserRoleDTO> queryListPage(TAppUserRoleDTO entityDTO, BasePageForm page);

    /**
     * 获取所有不分页
     */
    List<TAppUserRoleDTO> queryList(TAppUserRoleDTO entity);

    /**
     * 根据主键id获取单个对象
     */
    TAppUserRoleDTO findOne(Long id);

    /**
     * 新增
     */
    TAppUserRoleDTO create(TAppUserRoleDTO entity);


    /**
     * 更新
     */
    int update(TAppUserRoleDTO entity);

    /**
     * 删除
     */
    int delete(Long id);

     /**
     * 是否存在
     * existByTAppUserRoleId
     */
    boolean existByTAppUserRoleId(Long code);

    /**
    * 批量新增
    * create batch
    */
    Boolean insertBatch(List<TAppUserRoleDTO> dataList);


    /**
     * 新增App角色用户表(一个角色给多个用户)
     *
     * @return
     */
    boolean insertRoleUsers(TAppUserRoleDTO tAppUserRoleDTO);

    /**
     * 删除同一App角色的多个用户
     *
     * @return
     */
    boolean deleteTAppRoleUsers(TAppUserRoleDTO tAppUserRoleDTO);


    /**
     * 查询该App角色下的所有用户信息
     *
     * @return
     */
    PageResult<CscpUserDTO> queryTAppUsersByRoleId(Long appRoleId, String realName, BasePageForm basePageForm);

    /**
     * 查询该App角色下的未管理的用户信息
     *
     * @return
     */
    PageResult<CscpUserDTO> queryCscpUserNotInRole(Long appRoleId, String realName, BasePageForm basePageForm);

    /**
     * 新增App角色用户表(多个角色给一个用户)
     *
     * @return
     */
    boolean insertRolesToUser(List<Long> tAppRoleIdList, Long userId);

    /**
     * 根据用户ID查询用户App角色中间表
     * @param userId
     * @return
     */
    List<TAppUserRoleDTO> queryTAppUserRoleByUserId(Long userId);


    /**
     * 根据用户ID查询其关联的角色信息(全表查询没有设置数据隔离)
     * @param userId
     * @return
     */
    List<TAppRoleDTO> queryTAppRolesByUserId(Long userId);

    /**
     * 根据用户ID删除移动端用户角色中间表信息(无单位ID隔离)
     * @param userId
     * @return
     */
    boolean deleteAppUserRoleByUserId(Long userId);
}
