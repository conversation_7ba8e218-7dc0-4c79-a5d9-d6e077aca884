package com.ctsi.ssdc.admin.domain.dto;

import com.ctsi.ssdc.admin.domain.CscpOrgRole;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
@ApiModel(value="CscpOrgParamDTO", description="机构DTO")
public class CscpOrgParamDTO implements Serializable {

    private static final long serialVersionUID = 1895342466201734891L;

    private List<CscpOrgDTO> cscpOrgDTOs;

    private List<CscpOrgRole> scpOrgRoles;

}
