package com.ctsi.ssdc.admin.web;

import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.ssdc.admin.domain.dto.CscpLogLoginDTO;
import com.ctsi.ssdc.admin.service.CscpLogLoginService;
import com.ctsi.ssdc.model.PageResult;
import com.ctsi.ssdc.util.ResponseUtil;
import com.zdww.biyi.component.sdk.aop.BeanExposeMethodAble;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.propertyeditors.CustomDateEditor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.InitBinder;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Optional;

import static com.ctsi.ssdc.admin.consts.ComponentConstant.ADMIN;
import static com.ctsi.ssdc.admin.consts.ComponentConstant.METHOD;


/**
 * REST controller for managing CscpLogLogin.
 *
 * <AUTHOR> biyi generator
 *
 */
@RestController
@Api(tags = "系统登录日志的详情接口")
@RequestMapping("/api/system")
public class CscpLogLoginController {

    private final Logger log = LoggerFactory.getLogger(CscpLogLoginController.class);

    private final CscpLogLoginService cscpLogLoginService;

    public CscpLogLoginController(CscpLogLoginService cscpLogLoginService) {
        this.cscpLogLoginService = cscpLogLoginService;
    }

    @InitBinder
    public void initBinder(WebDataBinder binder) {   
        DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");   
        dateFormat.setLenient(true);   
        binder.registerCustomEditor(Date.class, new CustomDateEditor(dateFormat, true));   
    } 
	
    /**
     * GET  /cscpLogLogins : get the cscpLogLogins.
     *
     * @return the ResponseEntity with status 200 (OK) and the list of cscpLogLogins in body
     */
    @GetMapping("/cscpLogLogins")
    @BeanExposeMethodAble(component = ADMIN,method = METHOD)
    @ApiOperation(value = "分页查询所有登录日志")
    public PageResult<CscpLogLoginDTO> getCscpLogLogins(CscpLogLoginDTO cscpLogLoginDTO, BasePageForm basePageForm) {
        log.debug("REST request to get CscpLogLogins");
        return cscpLogLoginService.findByCscpLogLoginDTO(cscpLogLoginDTO, basePageForm);
    }
    


    /**
     * GET  /cscpLogLogins/:id : get the "id" cscpLogLogin.
     *
     * @param id the id of the cscpLogLoginDTO to retrieve
     * @return the ResponseEntity with status 200 (OK) and with body the cscpLogLoginDTO, or with status 404 (Not Found)
     */
    @GetMapping("/cscpLogLogins/{id}")
    @BeanExposeMethodAble(component = ADMIN,method = METHOD)
    @ApiOperation(value = "查看某个日志的信息")
    public ResponseEntity<CscpLogLoginDTO> getCscpLogLogin(@PathVariable Long id) {
        log.debug("REST request to get CscpLogLogin : {}", id);
        CscpLogLoginDTO cscpLogLoginDTO = cscpLogLoginService.findOne(id);
        return ResponseUtil.wrapOrNotFound(Optional.ofNullable(cscpLogLoginDTO));
    }
}
