package com.ctsi.ssdc.admin.web;
import java.net.URISyntaxException;

import com.ctsi.hndx.enums.DBOperation;
import com.ctsi.ssdc.annotation.OperationLog;
import com.ctsi.ssdc.model.PageResult;
import java.util.List;

import com.ctsi.ssdc.admin.domain.dto.TAppPermissionModuleDTO;
import com.ctsi.ssdc.admin.service.ITAppPermissionModuleService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import com.ctsi.hndx.common.BasePageForm;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.util.Assert;
import com.ctsi.hndx.common.BaseController;
import com.ctsi.hndx.annotations.ResponseResultVo;
import com.ctsi.hndx.result.ResultCode;
import com.ctsi.hndx.result.ResultVO;


/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-22
 *
 */

@Slf4j
@RestController
@ResponseResultVo
@RequestMapping("/api/tAppPermissionModule")
@Api(value = "移动端单元模块", tags = "移动端单元模块接口")
public class TAppPermissionModuleController extends BaseController {

    private static final String ENTITY_NAME = "tAppPermissionModule";

    @Autowired
    private ITAppPermissionModuleService tAppPermissionModuleService;



    /**
     *  新增权限管理表批量数据.
     */
    @PostMapping("/createBatch")
    @ApiOperation(value = "新增批量", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.ADD,message = "新增权限管理表批量数据")
    public ResultVO createBatch(@RequestBody List<TAppPermissionModuleDTO> tAppPermissionModuleList) {
       Boolean  result = tAppPermissionModuleService.insertBatch(tAppPermissionModuleList);
       if(result){
           return ResultVO.success();
       }else {
           return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
       }
    }

     /**
     *  新增数据.
     */
    @PostMapping("/create")
    @ApiOperation(value = "新增", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.ADD,message = "新增权限管理表数据")
    public ResultVO<TAppPermissionModuleDTO> create(@RequestBody TAppPermissionModuleDTO tAppPermissionModuleDTO) throws URISyntaxException {
        TAppPermissionModuleDTO result = tAppPermissionModuleService.create(tAppPermissionModuleDTO);
        return ResultVO.success(result);
    }

    /**
     *  更新存在数据.
     */
    @PostMapping("/update")
    @ApiOperation(value = "更新存在数据", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.UPDATE,message = "更新权限管理表数据")
    public ResultVO update(@RequestBody TAppPermissionModuleDTO tAppPermissionModuleDTO) {
	    Assert.notNull(tAppPermissionModuleDTO.getId(), "general.IdNotNull");
        int count = tAppPermissionModuleService.update(tAppPermissionModuleDTO);
        if(count > 0 ){
            return ResultVO.success();
        }else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }

     /**
     *  删除存在数据.
     */
    @DeleteMapping("/delete/{id}")
    @ApiOperation(value = "删除存在数据", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.DELETE,message = "删除权限管理表数据")
    public ResultVO delete(@PathVariable Long id) {
        int count = tAppPermissionModuleService.delete(id);
        if(count > 0 ){
            return ResultVO.success();
        }else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }

    /**
     * 查询单条数据.
     */
    @GetMapping("/get/{id}")
    @ApiOperation(value = "查询单条数据", notes = "传入参数")
    public ResultVO get(@PathVariable Long id) {
        TAppPermissionModuleDTO tAppPermissionModuleDTO = tAppPermissionModuleService.findOne(id);
        return ResultVO.success(tAppPermissionModuleDTO);
    }

    /**
    *  分页查询多条数据.
    */
    @GetMapping("/queryTAppPermissionModulePage")
    @ApiOperation(value = "翻页查询多条数据", notes = "传入参数")
    public ResultVO<PageResult<TAppPermissionModuleDTO>> queryTAppPermissionModulePage(TAppPermissionModuleDTO tAppPermissionModuleDTO, BasePageForm basePageForm) {
        return ResultVO.success(tAppPermissionModuleService.queryListPage(tAppPermissionModuleDTO, basePageForm));
    }

   /**
    * 查询所有移动端功能模块
    */
   @GetMapping("/queryTAppPermissionModule")
   @ApiOperation(value = "查询多条数据", notes = "传入参数")
   public ResultVO<PageResult<TAppPermissionModuleDTO>> queryTAppPermissionModule(TAppPermissionModuleDTO tAppPermissionModuleDTO, BasePageForm basePageForm) {
       PageResult<TAppPermissionModuleDTO> pageResult = tAppPermissionModuleService.queryList(tAppPermissionModuleDTO, basePageForm);
       return ResultVO.success(pageResult);
   }

    /**
     * 查询所有移动端功能模块(仅限列表查询，模糊查询)
     */
    @GetMapping("/queryList")
    @ApiOperation(value = "查询多条数据",notes = "传入参数")
    public ResultVO<PageResult<TAppPermissionModuleDTO>> queryList(TAppPermissionModuleDTO tAppPermissionModuleDTO, BasePageForm basePageForm) {
        PageResult<TAppPermissionModuleDTO> pageResult = tAppPermissionModuleService.queryListLike(tAppPermissionModuleDTO, basePageForm);
        return ResultVO.success(pageResult);
    }
}
