package com.ctsi.ssdc.admin.domain.dto;

import com.ctsi.hndx.common.BaseDtoEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 个人自定义组用户关联表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-13
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value="CscpPersonageUserGroupDTO对象", description="个人自定义组用户关联表")
public class CscpPersonageUserGroupDTO extends BaseDtoEntity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("用户id")
    private Long userId;

    @ApiModelProperty(value = "用户真实姓名")
    private String userName;

    @ApiModelProperty("单位ID")
    private Long unitId;

    @ApiModelProperty(value = "单位名称")
    private String unitName;

    @ApiModelProperty("部门ID")
    private Long branchId;

    @ApiModelProperty(value = "部门名称")
    private String branchName;

    @ApiModelProperty(value = "用户手机号码")
    private String mobile;

    @ApiModelProperty(value = "用户登录名称")
    private String loginName;

    @ApiModelProperty("groupid")
    private Long groupId;

    //@ApiModelProperty(value = "机构名称")
    //private String orgName;

    //@ApiModelProperty(value = "用户真实姓名")
    //private String realName;

    //@ApiModelProperty("机构ID")
    //private Long orgId;
}
