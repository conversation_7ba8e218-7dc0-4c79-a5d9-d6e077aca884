
package com.ctsi.ssdc.admin.domain.dto;

import com.ctsi.hndx.common.BaseDtoEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import lombok.EqualsAndHashCode;

@Data
@ApiModel("同步导出记录数据传输对象")
@EqualsAndHashCode(callSuper = true)
public class TSyncExportRecordDTO extends BaseDtoEntity {


    @ApiModelProperty("是否导出")
    private String isExport;

    @ApiModelProperty("类型")
    private String type;

    @ApiModelProperty("机构编码")
    private String orgCode;

    @ApiModelProperty("同步信息")
    private String syncMessage;
    @ApiModelProperty("文件路径")
    private String filePath;

    @ApiModelProperty("导出机构名")
    private String exportOrgName;

    @ApiModelProperty("导出机构ID")
    private String exportOrgId;

    @ApiModelProperty("创建人名称")
    private String createName;

    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;
}
