package com.ctsi.ssdc.admin.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.ctsi.hndx.common.BaseEntity;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 个人自定义组用户关联表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-13
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("cscp_personage_user_group")
@ApiModel(value="CscpPersonageUserGroup对象", description="个人自定义组用户关联表")
public class CscpPersonageUserGroup extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("用户id")
    private Long userId;

    @ApiModelProperty("groupid")
    private Long groupId;

    @ApiModelProperty("机构ID")
    private Long orgId;


}
