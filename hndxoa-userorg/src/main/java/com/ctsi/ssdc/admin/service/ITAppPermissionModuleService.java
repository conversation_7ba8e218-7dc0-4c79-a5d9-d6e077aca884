package com.ctsi.ssdc.admin.service;

import com.ctsi.ssdc.admin.domain.dto.TAppPermissionModuleDTO;
import com.ctsi.ssdc.admin.domain.TAppPermissionModule;
import com.ctsi.hndx.common.SysBaseServiceI;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.ssdc.model.PageResult;
import java.util.List;

/**
 * <p>
 * 权限管理表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-22
 */
public interface ITAppPermissionModuleService extends SysBaseServiceI<TAppPermissionModule> {


    /**
     * 分页查询
     */
    PageResult<TAppPermissionModuleDTO> queryListPage(TAppPermissionModuleDTO entityDTO, BasePageForm page);

    /**
     * 获取所有移动端功能模块
     */
    PageResult<TAppPermissionModuleDTO> queryList(TAppPermissionModuleDTO entity, BasePageForm page);

    /**
     * 根据主键id获取单个对象
     */
    TAppPermissionModuleDTO findOne(Long id);

    /**
     * 新增
     */
    TAppPermissionModuleDTO create(TAppPermissionModuleDTO entity);


    /**
     * 更新
     */
    int update(TAppPermissionModuleDTO entity);

    /**
     * 删除
     */
    int delete(Long id);

     /**
     * 是否存在
     * existByTAppPermissionModuleId
     */
    boolean existByTAppPermissionModuleId(Long code);

    /**
    * 批量新增
    * create batch
    */
    Boolean insertBatch(List<TAppPermissionModuleDTO> dataList);

    /**
     * 根据Code获取单个对象
     */
    TAppPermissionModuleDTO queryTAppPermissionModuleByCode(String code);

    /**
     * 根据Code集合获取对象
     */
    List<TAppPermissionModuleDTO> queryTAppPermissionModuleByCodeList(List<String> codeList);

    /**
     * 查询该租户所拥有的移动端APP功能模块
     * @param tenantId
     * @return
     */
    List<TAppPermissionModuleDTO> queryAppPermissionModuleListByTenantId(Long tenantId);

    /**
     * 获取所有移动端功能模块(列表查询专用)
     */
    PageResult<TAppPermissionModuleDTO> queryListLike(TAppPermissionModuleDTO entity, BasePageForm page);

    /**
     * 排序号自增
     * @param entity
     * @return
     */
    Boolean updateSort(TAppPermissionModuleDTO entity);
}
