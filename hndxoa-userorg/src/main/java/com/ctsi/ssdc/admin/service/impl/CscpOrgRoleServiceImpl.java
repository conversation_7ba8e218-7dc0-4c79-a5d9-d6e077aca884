package com.ctsi.ssdc.admin.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.exception.BusinessException;
import com.ctsi.hndx.tree.Node;
import com.ctsi.hndx.tree.impl.BaseTreeCurdServiceImpl;
import com.ctsi.hndx.utils.BeanConvertUtils;
import com.ctsi.hndx.utils.ListCopyUtil;
import com.ctsi.hndx.utils.PageHelperUtil;
import com.ctsi.ssdc.admin.domain.CscpOrg;
import com.ctsi.ssdc.admin.domain.CscpOrgRole;
import com.ctsi.ssdc.admin.domain.dto.CscpOrgDTO;
import com.ctsi.ssdc.admin.domain.dto.CscpOrgRoleDTO;
import com.ctsi.ssdc.admin.repository.CscpOrgRepository;
import com.ctsi.ssdc.admin.repository.CscpOrgRoleRepository;
import com.ctsi.ssdc.admin.service.CscpOrgRoleService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
public class CscpOrgRoleServiceImpl extends BaseTreeCurdServiceImpl<CscpOrgDTO,CscpOrg,CscpOrgRepository>
implements CscpOrgRoleService {

    @Autowired
    private CscpOrgRoleRepository cscpOrgRoleRepository;

    @Autowired
    private CscpOrgRepository cscpOrgRepository;

    private static final Long ROOT_ORG_ID = 0L; //最顶层机构默认父级ID

    @Override
    public CscpOrgRoleDTO insert(CscpOrgRoleDTO cscpOrgRoleDTO) {
        CscpOrgRole cscpOrgRole = new CscpOrgRole();
        BeanUtils.copyProperties(cscpOrgRoleDTO, cscpOrgRole);

        cscpOrgRoleRepository.insert(cscpOrgRole);
        BeanUtils.copyProperties(cscpOrgRole, cscpOrgRoleDTO);
        return cscpOrgRoleDTO;
    }

    @Override
    public CscpOrgRoleDTO update(CscpOrgRoleDTO cscpOrgRoleDTO) {
        CscpOrgRole cscpOrgRole = new CscpOrgRole();
        BeanUtils.copyProperties(cscpOrgRoleDTO, cscpOrgRole);
        cscpOrgRoleRepository.updateById(cscpOrgRole);
        BeanUtils.copyProperties(cscpOrgRole, cscpOrgRoleDTO);
        return cscpOrgRoleDTO;
    }

    @Override
    public IPage<CscpOrgRoleDTO> findAll(CscpOrgRoleDTO cscpOrgRoleDTO, BasePageForm basePageForm) {

        LambdaQueryWrapper<CscpOrgRole> queryWrapper = new LambdaQueryWrapper();

        IPage<CscpOrgRole> pageData = cscpOrgRoleRepository.selectPage(
                PageHelperUtil.getMPlusPageByBasePage(basePageForm), queryWrapper);

        IPage<CscpOrgRoleDTO> data  = pageData.convert(entity ->
                BeanConvertUtils.copyProperties(entity, CscpOrgRoleDTO.class));
        return data;
    }

    @Override
    public CscpOrgRoleDTO findOne(Long id) {
        CscpOrgRole cscpOrgRole = cscpOrgRoleRepository.selectById(id);
        CscpOrgRoleDTO cscpOrgRoleDTO = new CscpOrgRoleDTO();
        BeanUtils.copyProperties(cscpOrgRole, cscpOrgRoleDTO);
        return cscpOrgRoleDTO;
    }

    @Override
    public void delete(Long id) {
        cscpOrgRoleRepository.deleteById(id);
    }

    @Override
    @Transactional
    public boolean saveOrgRoles(Long orgId, List<Long> roleList) {
        try {
            LambdaQueryWrapper<CscpOrgRole> queryWrapper = new LambdaQueryWrapper();
            queryWrapper.eq(CscpOrgRole::getOrgId, orgId);
            cscpOrgRoleRepository.delete(queryWrapper);

            for (Long roleId : roleList) {
                CscpOrgRole record = new CscpOrgRole();
                record.setOrgId(orgId);
                record.setRoleId(roleId);
                cscpOrgRoleRepository.insert(record);
            }
        } catch (Exception e) {
            log.error("{}", e.getMessage());
            throw new BusinessException("插入部门角色表数据失败！");
        }
        return true;
    }

    @Override
    @Transactional
    public boolean insertCscpOrgRoles(Long roleId, List<Long> orgIdList) {
//        List<CscpOrgRole> collect = orgIdList.stream().map(orgId -> {
//            CscpOrgRole cscpOrgRole = new CscpOrgRole();
//            cscpOrgRole.setRoleId(roleId);
//            cscpOrgRole.setOrgId(orgId);
//            return cscpOrgRole;
//        }).collect(Collectors.toList());
//        return saveBatch(collect);

        LambdaQueryWrapper<CscpOrgRole> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.eq(CscpOrgRole::getRoleId, roleId);
        cscpOrgRoleRepository.delete(queryWrapper);

        try {
            for (Long orgId : orgIdList) {
                CscpOrgRole record = new CscpOrgRole();
                record.setOrgId(orgId);
                record.setRoleId(roleId);
                cscpOrgRoleRepository.insert(record);
            }
        } catch (Exception e) {
            log.error("{}", e.getMessage());
            throw new BusinessException("插入部门角色表数据失败！");
        }
        return true;
    }

    @Override
    public boolean deleteCscpOrgRoles(Long roleId, List<Long> orgIdList) {
        LambdaQueryWrapper<CscpOrgRole> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.eq(CscpOrgRole::getRoleId, roleId);
        queryWrapper.in(CscpOrgRole::getOrgId, orgIdList);
        cscpOrgRoleRepository.delete(queryWrapper);
        return true;
    }

    @Override
    public List<Node<CscpOrgDTO>> queryCscpOrgByRoleId(Long roleId) {
        LambdaQueryWrapper<CscpOrgRole> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.eq(CscpOrgRole::getRoleId, roleId);
        List<Long> orgIdList = cscpOrgRoleRepository.selectList(queryWrapper).stream()
                .map(x -> x.getOrgId()).distinct().collect(Collectors.toList());

        Long parentId = ROOT_ORG_ID;
        List<Node<CscpOrgDTO>> cscpTreeDataNode = this.selectChildrenListNodeByParentId(parentId, orgIdList);
        return cscpTreeDataNode;
    }

    @Override
    public List<CscpOrgDTO> getDataDtOFromDomin(List<CscpOrg> list) {
        return ListCopyUtil.copy(list, CscpOrgDTO.class);
    }

    @Override
    public  CscpOrgDTO copyDto(CscpOrg cscpOrg, CscpOrgDTO treeData){
        treeData = new CscpOrgDTO();
        BeanUtils.copyProperties(cscpOrg, treeData);
        return treeData;
    }
}
