package com.ctsi.ssdc.admin.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.common.SysBaseServiceImpl;
import com.ctsi.hndx.exception.BusinessException;
import com.ctsi.hndx.utils.BeanConvertUtils;
import com.ctsi.hndx.utils.ListCopyUtil;
import com.ctsi.hndx.utils.PageHelperUtil;
import com.ctsi.ssdc.admin.domain.CscpUser;
import com.ctsi.ssdc.admin.domain.TAppPermissionModule;
import com.ctsi.ssdc.admin.domain.TAppRole;
import com.ctsi.ssdc.admin.domain.TAppRolePermission;
import com.ctsi.ssdc.admin.domain.dto.CscpMenusDTO;
import com.ctsi.ssdc.admin.domain.dto.TAppPermissionModuleDTO;
import com.ctsi.ssdc.admin.domain.dto.TAppRolePermissionDTO;
import com.ctsi.ssdc.admin.domain.dto.TAppUserRoleDTO;
import com.ctsi.ssdc.admin.repository.TAppRolePermissionRepository;
import com.ctsi.ssdc.admin.service.ITAppPermissionModuleService;
import com.ctsi.ssdc.admin.service.ITAppRolePermissionService;
import com.ctsi.ssdc.admin.service.ITAppRoleService;
import com.ctsi.ssdc.admin.service.ITAppUserRoleService;
import com.ctsi.ssdc.model.PageResult;
import com.ctsi.ssdc.security.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <p>
 * 用户权限表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-20
 */

@Slf4j
@Service
public class TAppRolePermissionServiceImpl extends SysBaseServiceImpl<TAppRolePermissionRepository, TAppRolePermission> implements ITAppRolePermissionService {

    @Autowired
    private TAppRolePermissionRepository tAppRolePermissionMapper;

    @Autowired
    private ITAppUserRoleService itAppUserRoleService;

    @Autowired
    private ITAppRoleService tAppRoleMapper;

    @Autowired
    private ITAppPermissionModuleService itAppPermissionModuleService;

    /**
     * 翻页
     *
     * @param entityDTO
     * @param basePageForm
     * @return
     */
    @Override
    public PageResult<TAppRolePermissionDTO> queryListPage(TAppRolePermissionDTO entityDTO, BasePageForm basePageForm) {
        //设置条件
        LambdaQueryWrapper<TAppRolePermission> queryWrapper = new LambdaQueryWrapper();

        IPage<TAppRolePermission> pageData = tAppRolePermissionMapper.selectPage(
             PageHelperUtil.getMPlusPageByBasePage(basePageForm), queryWrapper);
        //返回
        IPage<TAppRolePermissionDTO> data  = pageData.convert(entity -> BeanConvertUtils.copyProperties(entity,TAppRolePermissionDTO.class));

        return new PageResult<TAppRolePermissionDTO>(data.getRecords(),
            data.getTotal(), data.getCurrent());
    }

    /**
     * 列表查询
     *
     * @param entityDTO
     * @return
     */
    @Override
    public List<TAppRolePermissionDTO> queryList(TAppRolePermissionDTO entityDTO) {
        LambdaQueryWrapper<TAppRolePermission> queryWrapper = new LambdaQueryWrapper();
            List<TAppRolePermission> listData = tAppRolePermissionMapper.selectList(queryWrapper);
            List<TAppRolePermissionDTO> TAppRolePermissionDTOList = ListCopyUtil.copy(listData, TAppRolePermissionDTO.class);
        return TAppRolePermissionDTOList;
    }

    /**
     * 单个查询
     *
     * @param id the id of the entity
     * @return
     */
    @Override
    public TAppRolePermissionDTO findOne(Long id) {
        TAppRolePermission  tAppRolePermission =  tAppRolePermissionMapper.selectById(id);
        return  BeanConvertUtils.copyProperties(tAppRolePermission,TAppRolePermissionDTO.class);
    }


    /**
     * 新增
     *
     * @param entityDTO the entity to create
     * @return
     */
    @Override
    @Transactional
    public TAppRolePermissionDTO create(TAppRolePermissionDTO entityDTO) {
       TAppRolePermission tAppRolePermission =  BeanConvertUtils.copyProperties(entityDTO,TAppRolePermission.class);
        save(tAppRolePermission);
        return  BeanConvertUtils.copyProperties(tAppRolePermission,TAppRolePermissionDTO.class);
    }

    /**
     * 修改
     *
     * @param entity the entity to update
     * @return
     */
    @Override
    @Transactional
    public int update(TAppRolePermissionDTO entity) {
        TAppRolePermission tAppRolePermission = BeanConvertUtils.copyProperties(entity,TAppRolePermission.class);
        return tAppRolePermissionMapper.updateById(tAppRolePermission);
    }

    /**
     * 删除
     *
     * @param id the id of the entity
     */
    @Override
    @Transactional
    public int delete(Long id) {
        return tAppRolePermissionMapper.deleteById(id);
    }


    /**
     * 验证是否存在
     *
     * @param TAppRolePermissionId
     * @return
     */
    @Override
    public boolean existByTAppRolePermissionId(Long TAppRolePermissionId) {
        if (TAppRolePermissionId != null) {
            LambdaQueryWrapper<TAppRolePermission> queryWrapper = new LambdaQueryWrapper();
            queryWrapper.eq(TAppRolePermission::getId, TAppRolePermissionId);
            List<TAppRolePermission> result = tAppRolePermissionMapper.selectList(queryWrapper);
            return result.size() > 0;
        }
        return true;
    }

    /**
    * 批量新增
    *
    */
    @Override
    @Transactional
    public Boolean insertBatch(List<TAppRolePermissionDTO> dataList) {
        List<TAppRolePermission> result = ListCopyUtil.copy(dataList, TAppRolePermission.class);
        return saveBatch(result);
    }

    @Override
    public boolean insertAppRolePermissions(List<TAppRolePermissionDTO> tAppRolePermissionDTOList) {
        LambdaQueryWrapper<TAppRolePermission> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.eq(TAppRolePermission::getAppRoleId, tAppRolePermissionDTOList.get(0).getAppRoleId());
        tAppRolePermissionMapper.delete(queryWrapper);

        List<TAppRolePermission> result = ListCopyUtil.copy(tAppRolePermissionDTOList, TAppRolePermission.class);
        return saveBatch(result);
    }

    @Override
    public boolean deleteAppRolePermissions(TAppRolePermissionDTO tAppRolePermissionDTO) {
        Long appRoleId = tAppRolePermissionDTO.getAppRoleId();
        List<String> appPermissionCodeList = tAppRolePermissionDTO.getAppPermissionCodeList();
        LambdaQueryWrapper<TAppRolePermission> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.eq(TAppRolePermission::getAppRoleId, appRoleId);
        queryWrapper.in(TAppRolePermission::getAppPermissionCode, appPermissionCodeList);
        tAppRolePermissionMapper.delete(queryWrapper);
        return true;
    }

    @Override
    public PageResult<TAppPermissionModuleDTO> queryTAppPermissionModuleByRoleId(Long appRoleId, BasePageForm basePageForm) {
        LambdaQueryWrapper<TAppRolePermission> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TAppRolePermission::getAppRoleId, appRoleId);
        List<String> appPermissionCodeList = tAppRolePermissionMapper.selectList(queryWrapper).stream()
                .map(x -> x.getAppPermissionCode()).distinct().collect(Collectors.toList());


        if (CollectionUtils.isNotEmpty(appPermissionCodeList)) {
            LambdaQueryWrapper<TAppPermissionModule> lambdaQueryWrapper = new LambdaQueryWrapper<>();
            lambdaQueryWrapper.in(TAppPermissionModule::getCode, appPermissionCodeList);
            IPage<CscpUser> tAppPermissionModuleIPage = itAppPermissionModuleService.page(
                    PageHelperUtil.getMPlusPageByBasePage(basePageForm), lambdaQueryWrapper);

            List<TAppPermissionModuleDTO> data = ListCopyUtil.copy(tAppPermissionModuleIPage.getRecords(), TAppPermissionModuleDTO.class);
            return new PageResult<TAppPermissionModuleDTO>(data, tAppPermissionModuleIPage.getTotal(), tAppPermissionModuleIPage.getTotal());
        }
        return null;
    }

    @Override
    public List<TAppPermissionModuleDTO> getTAppPermissionByUserId(Long uid) {
//
//        List<TAppPermissionModuleDTO> tAppPermissionModuleDTOList = new ArrayList<>();
//
//        // 查询用户关联的移动端角色
//        List<TAppUserRoleDTO> tAppUserRoleDTOList = itAppUserRoleService.queryTAppUserRoleByUserId(uid);
//        if (CollectionUtils.isEmpty(tAppUserRoleDTOList)) {
//            return tAppPermissionModuleDTOList;
//        }
//        List<Long> appRoleIdList = tAppUserRoleDTOList.stream().map(TAppUserRoleDTO :: getAppRoleId).collect(Collectors.toList());
//
//        // 通过角色获取关联移动端功能模块的code
//        LambdaQueryWrapper<TAppRolePermission> queryWrapper = new LambdaQueryWrapper<>();
//        queryWrapper.in(TAppRolePermission::getAppRoleId, appRoleIdList);
//        List<String> appPermissionCodeList = tAppRolePermissionMapper.selectList(queryWrapper).stream()
//                .map(x -> x.getAppPermissionCode()).distinct().collect(Collectors.toList());
//        if (CollectionUtils.isEmpty(appPermissionCodeList)) {
//           return tAppPermissionModuleDTOList;
//        }
//
//        // 查询租户所拥有的功能模块
//        Long tenantId = SecurityUtils.getCurrentCscpUserDetail().getTenantId();
//        if (Objects.isNull(tenantId)) {
//            throw new BusinessException("该用户没有找到其租户ID");
//        }
//        List<TAppPermissionModuleDTO> appPermissionModuleDTOList = itAppPermissionModuleService.queryAppPermissionModuleListByTenantId(tenantId);
//
//        // 通过功能模块code获取功能模块信息
//        tAppPermissionModuleDTOList = itAppPermissionModuleService.queryTAppPermissionModuleByCodeList(appPermissionCodeList);
//
//        // 两者取交集
//        List<TAppPermissionModuleDTO> moduleDTOList = tAppPermissionModuleDTOList.stream()
//                .filter(item -> appPermissionModuleDTOList.stream().map(e -> e.getId())
//                        .collect(Collectors.toList()).contains(item.getId()))
//                .collect(Collectors.toList());
//        moduleDTOList = moduleDTOList.stream().filter(q->q.getParentId().longValue() == 0L).collect(Collectors.toList());
//        return moduleDTOList;
        List<TAppPermissionModuleDTO> tAppPermissionModuleDTOList = new ArrayList<>();

        // 查询用户关联的移动端角色
        List<TAppUserRoleDTO> tAppUserRoleDTOList = itAppUserRoleService.queryTAppUserRoleByUserId(uid);
        List<Long> appRoleIdList = new ArrayList<>();
        if (CollectionUtils.isEmpty(tAppUserRoleDTOList)) {
            //如果该用户没有角色授权，获取角色list集合
            LambdaQueryWrapper<TAppRole> queryWrapper = new LambdaQueryWrapper();
            queryWrapper.eq(TAppRole::getTenantId,SecurityUtils.getCurrentCscpUserDetail().getTenantId());
            queryWrapper.isNull(TAppRole::getCompanyId);
            queryWrapper.eq(TAppRole::getName,"普通用户");
            List<TAppRole> listData = tAppRoleMapper.selectListNoAdd(queryWrapper);
            if(CollectionUtils.isNotEmpty(listData)){
                appRoleIdList = listData.stream().map(TAppRole :: getId).collect(Collectors.toList());
            }else {
                return tAppPermissionModuleDTOList;
            }
        }else {
            appRoleIdList = tAppUserRoleDTOList.stream().map(TAppUserRoleDTO :: getAppRoleId).collect(Collectors.toList());
        }
//        List<Long> appRoleIdList = tAppUserRoleDTOList.stream().map(TAppUserRoleDTO :: getAppRoleId).collect(Collectors.toList());
        // 通过角色获取关联移动端功能模块的code
        LambdaQueryWrapper<TAppRolePermission> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(TAppRolePermission::getAppRoleId, appRoleIdList);
        List<String> appPermissionCodeList = tAppRolePermissionMapper.selectList(queryWrapper).stream()
                .map(x -> x.getAppPermissionCode()).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(appPermissionCodeList)) {
            return tAppPermissionModuleDTOList;
        }

        // 查询租户所拥有的功能模块
        Long tenantId = SecurityUtils.getCurrentCscpUserDetail().getTenantId();
        if (Objects.isNull(tenantId)) {
            throw new BusinessException("该用户没有找到其租户ID");
        }
        List<TAppPermissionModuleDTO> appPermissionModuleDTOList = itAppPermissionModuleService.queryAppPermissionModuleListByTenantId(tenantId);

        // 通过功能模块code获取功能模块信息
        tAppPermissionModuleDTOList = itAppPermissionModuleService.queryTAppPermissionModuleByCodeList(appPermissionCodeList);

        // 两者取交集
        List<TAppPermissionModuleDTO> moduleDTOList = tAppPermissionModuleDTOList.stream()
                .filter(item -> appPermissionModuleDTOList.stream().map(e -> e.getId())
                        .collect(Collectors.toList()).contains(item.getId()))
                .collect(Collectors.toList());
        moduleDTOList = moduleDTOList.stream().filter(q->q.getParentId().longValue() == 0L).collect(Collectors.toList());
        return moduleDTOList;
    }

    @Override
    public List<TAppPermissionModuleDTO> getChildTAppPermissionByUserId(Long id,Long uid) {

        List<TAppPermissionModuleDTO> tAppPermissionModuleDTOList = new ArrayList<>();

        // 查询用户关联的移动端角色
        List<TAppUserRoleDTO> tAppUserRoleDTOList = itAppUserRoleService.queryTAppUserRoleByUserId(uid);
        if (CollectionUtils.isEmpty(tAppUserRoleDTOList)) {
            return tAppPermissionModuleDTOList;
        }
        List<Long> appRoleIdList = tAppUserRoleDTOList.stream().map(TAppUserRoleDTO :: getAppRoleId).collect(Collectors.toList());

        // 通过角色获取关联移动端功能模块的code
        LambdaQueryWrapper<TAppRolePermission> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(TAppRolePermission::getAppRoleId, appRoleIdList);
        List<String> appPermissionCodeList = tAppRolePermissionMapper.selectList(queryWrapper).stream()
                .map(x -> x.getAppPermissionCode()).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(appPermissionCodeList)) {
            return tAppPermissionModuleDTOList;
        }

        // 查询租户所拥有的功能模块
        Long tenantId = SecurityUtils.getCurrentCscpUserDetail().getTenantId();
        if (Objects.isNull(tenantId)) {
            throw new BusinessException("该用户没有找到其租户ID");
        }
        List<TAppPermissionModuleDTO> appPermissionModuleDTOList = itAppPermissionModuleService.queryAppPermissionModuleListByTenantId(tenantId);

        // 通过功能模块code获取功能模块信息
        tAppPermissionModuleDTOList = itAppPermissionModuleService.queryTAppPermissionModuleByCodeList(appPermissionCodeList);

        // 两者取交集
        List<TAppPermissionModuleDTO> moduleDTOList = tAppPermissionModuleDTOList.stream()
                .filter(item -> appPermissionModuleDTOList.stream().map(e -> e.getId())
                        .collect(Collectors.toList()).contains(item.getId()))
                .collect(Collectors.toList());

        moduleDTOList = moduleDTOList.stream().filter(q->q.getParentId().longValue() == id.longValue()).collect(Collectors.toList());
        return moduleDTOList;
    }

    @Override
    public List<TAppRolePermissionDTO> getTAppPermissionByRoleId(Long appRoleId) {
        LambdaQueryWrapper<TAppRolePermission> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.eq(TAppRolePermission::getAppRoleId, appRoleId);
        List<TAppRolePermission> TAppRolePermissionList = tAppRolePermissionMapper.selectList(queryWrapper);
        return ListCopyUtil.copy(TAppRolePermissionList, TAppRolePermissionDTO.class);
    }

    @Override
    public List<TAppRolePermissionDTO> getTAppPermissionByRoleIdList(List<Long> appRoleIdList) {
        LambdaQueryWrapper<TAppRolePermission> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.in(TAppRolePermission::getAppRoleId, appRoleIdList);
        List<TAppRolePermission> TAppRolePermissionList = tAppRolePermissionMapper.selectList(queryWrapper);
        return ListCopyUtil.copy(TAppRolePermissionList, TAppRolePermissionDTO.class);
    }
}
