package com.ctsi.ssdc.admin.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ctsi.hndx.utils.BeanConvertUtils;
import com.ctsi.hndx.utils.ListCopyUtil;
import com.ctsi.ssdc.admin.domain.CformLogOpration;
import com.ctsi.ssdc.admin.domain.dto.CformLogOprationDTO;
import com.ctsi.ssdc.admin.repository.CformLogOprationMapper;
import com.ctsi.ssdc.admin.service.ICformLogOprationService;
import com.ctsi.ssdc.model.PageResult;
import com.ctsi.hndx.common.SysBaseServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.utils.PageHelperUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.List;
import org.springframework.transaction.annotation.Transactional;

/**
 * <p>
 * 表单查看表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-04
 */
@Slf4j
@Service
public class CformLogOprationServiceImpl extends SysBaseServiceImpl<CformLogOprationMapper, CformLogOpration> implements ICformLogOprationService {

    @Autowired
    private CformLogOprationMapper cformLogOprationMapper;

    /**
     * 翻页
     *
     * @param entityDTO
     * @param basePageForm
     * @return
     */
    @Override
    public PageResult<CformLogOprationDTO> queryListPage(CformLogOprationDTO entityDTO, BasePageForm basePageForm) {
        //设置条件
        LambdaQueryWrapper<CformLogOpration> queryWrapper = new LambdaQueryWrapper();
        if(StringUtils.isNotBlank(entityDTO.getCreateName())) {
            queryWrapper.like(CformLogOpration::getCreateName, entityDTO.getCreateName());
        }
        if(StringUtils.isNotBlank(entityDTO.getUri())) {
            queryWrapper.like(CformLogOpration::getUri, entityDTO.getUri());
        }
        if(StringUtils.isNotBlank(entityDTO.getMessage())) {
            queryWrapper.like(CformLogOpration::getMessage, entityDTO.getMessage());
        }
        if(entityDTO.getOperationType() != null) {
            queryWrapper.eq(CformLogOpration::getOperationType, entityDTO.getOperationType());
        }
        queryWrapper.orderByDesc(CformLogOpration::getCreateTime);
        IPage<CformLogOpration> pageData = cformLogOprationMapper.selectPageNoAdd(
             PageHelperUtil.getMPlusPageByBasePage(basePageForm), queryWrapper);
        //返回
        IPage<CformLogOprationDTO> data  = pageData.convert(entity -> BeanConvertUtils.copyProperties(entity,CformLogOprationDTO.class));

        return new PageResult<CformLogOprationDTO>(data.getRecords(),
            data.getTotal(), data.getCurrent());
    }

    /**
     * 列表查询
     *
     * @param entityDTO
     * @return
     */
    @Override
    public List<CformLogOprationDTO> queryList(CformLogOprationDTO entityDTO) {
        LambdaQueryWrapper<CformLogOpration> queryWrapper = new LambdaQueryWrapper();
            List<CformLogOpration> listData = cformLogOprationMapper.selectList(queryWrapper);
            List<CformLogOprationDTO> CformLogOprationDTOList = ListCopyUtil.copy(listData, CformLogOprationDTO.class);
        return CformLogOprationDTOList;
    }

    /**
     * 单个查询
     *
     * @param id the id of the entity
     * @return
     */
    @Override
    public CformLogOprationDTO findOne(Long id) {
        CformLogOpration  cformLogOpration =  cformLogOprationMapper.selectById(id);
        return  BeanConvertUtils.copyProperties(cformLogOpration,CformLogOprationDTO.class);
    }


    /**
     * 新增
     *
     * @param entityDTO the entity to create
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public CformLogOprationDTO create(CformLogOprationDTO entityDTO) {
       CformLogOpration cformLogOpration =  BeanConvertUtils.copyProperties(entityDTO,CformLogOpration.class);
        save(cformLogOpration);
        return  BeanConvertUtils.copyProperties(cformLogOpration,CformLogOprationDTO.class);
    }

    /**
     * 修改
     *
     * @param entity the entity to update
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int update(CformLogOprationDTO entity) {
        CformLogOpration cformLogOpration = BeanConvertUtils.copyProperties(entity,CformLogOpration.class);
        return cformLogOprationMapper.updateById(cformLogOpration);
    }

    /**
     * 删除
     *
     * @param id the id of the entity
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int delete(Long id) {
        return cformLogOprationMapper.deleteById(id);
    }


    /**
     * 验证是否存在
     *
     * @param CformLogOprationId
     * @return
     */
    @Override
    public boolean existByCformLogOprationId(Long CformLogOprationId) {
        if (CformLogOprationId != null) {
            LambdaQueryWrapper<CformLogOpration> queryWrapper = new LambdaQueryWrapper();
            queryWrapper.eq(CformLogOpration::getId, CformLogOprationId);
            List<CformLogOpration> result = cformLogOprationMapper.selectList(queryWrapper);
            return result.size() > 0;
        }
        return true;
    }

    /**
    * 批量新增
    *
    */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertBatch(List<CformLogOprationDTO> dataList) {
        List<CformLogOpration> result = ListCopyUtil.copy(dataList, CformLogOpration.class);
        return saveBatch(result);
    }


}
