package com.ctsi.ssdc.admin.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.common.SysBaseServiceImpl;
import com.ctsi.hndx.exception.BusinessException;
import com.ctsi.hndx.utils.ListCopyUtil;
import com.ctsi.hndx.utils.PageHelperUtil;
import com.ctsi.ssdc.admin.domain.CscpUserOrg;
import com.ctsi.ssdc.admin.domain.CscpUserWorkGroup;
import com.ctsi.ssdc.admin.domain.CscpWorkGroup;
import com.ctsi.ssdc.admin.domain.dto.CscpWorkGroupDTO;
import com.ctsi.ssdc.admin.repository.CscpUserWorkGroupRepository;
import com.ctsi.ssdc.admin.repository.CscpWorkGroupRepository;
import com.ctsi.ssdc.admin.service.CscpUserService;
import com.ctsi.ssdc.admin.service.CscpWorkGroupService;
import com.ctsi.ssdc.model.PageResult;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
public class CscpWorkGroupServiceImpl extends SysBaseServiceImpl<CscpWorkGroupRepository,CscpWorkGroup> implements CscpWorkGroupService {

    private final Logger log = LoggerFactory.getLogger(CscpWorkGroupServiceImpl.class);

    @Autowired
    private CscpWorkGroupRepository cscpWorkGroupRepository;

    @Autowired
    private CscpUserWorkGroupRepository cscpUserWorkGroupRepository;

    @Autowired
    private CscpUserService cscpUserService;


    /**
     * insert a cscpWorkGroup.
     *
     * @param cscpWorkGroupDTO the entity to insert
     * @return the persisted entity
     */
    @Override
    @Transactional
    public CscpWorkGroupDTO insert(CscpWorkGroupDTO cscpWorkGroupDTO) {
        log.debug("Request to insert CscpWorkGroup : {}", cscpWorkGroupDTO);
        
        CscpWorkGroup cscpWorkGroup = new CscpWorkGroup();
        BeanUtils.copyProperties(cscpWorkGroupDTO, cscpWorkGroup);
        cscpWorkGroupRepository.insert(cscpWorkGroup);

        // 保存用户工作组关系
//        for(int i = 0; i < cscpWorkGroupDTO.getUserIdList().size(); i++) {
//            CscpUserWorkGroup cscpUserWorkGroup = new CscpUserWorkGroup();
//            cscpUserWorkGroup.setUserId(cscpWorkGroupDTO.getUserIdList().get(i));
//            cscpUserWorkGroup.setGroupId(cscpWorkGroup.getId());;
//            cscpUserWorkGroupRepository.insert(cscpUserWorkGroup);
//        }
        BeanUtils.copyProperties(cscpWorkGroup, cscpWorkGroupDTO);
        return cscpWorkGroupDTO;
    }
    
     /**
     * update a cscpWorkGroup.
     *
     * @param cscpWorkGroupDTO the entity to update
     * @return the persisted entity
     */
    @Override
    @Transactional
    public CscpWorkGroupDTO update(CscpWorkGroupDTO cscpWorkGroupDTO) {
        log.debug("Request to update CscpWorkGroup : {}", cscpWorkGroupDTO);
        
        CscpWorkGroup cscpWorkGroup = new CscpWorkGroup();
        BeanUtils.copyProperties(cscpWorkGroupDTO, cscpWorkGroup);
        cscpWorkGroupRepository.updateById(cscpWorkGroup);

        // 先删除之前的用户工作组关系
//        LambdaQueryWrapper<CscpUserWorkGroup> queryWrapper = new LambdaQueryWrapper();
//        queryWrapper.eq(CscpUserWorkGroup::getGroupId, cscpWorkGroup.getId());
//        cscpUserWorkGroupRepository.delete(queryWrapper);
//
//        for(int i = 0; i < cscpWorkGroupDTO.getUserIdList().size(); i++){
//            CscpUserWorkGroup cscpUserWorkGroup = new CscpUserWorkGroup();
//            cscpUserWorkGroup.setUserId(cscpWorkGroupDTO.getUserIdList().get(i));
//            cscpUserWorkGroup.setGroupId(cscpWorkGroupDTO.getId());
//            cscpUserWorkGroupRepository.insert(cscpUserWorkGroup);
//        }

        BeanUtils.copyProperties(cscpWorkGroup, cscpWorkGroupDTO);
        return cscpWorkGroupDTO;
    }
    


    /**
     * Get one cscpWorkGroup.
     *
     * @param id the id of the entity
     * @return the entity
     */
    @Override
    @Transactional(readOnly = true)
    public CscpWorkGroupDTO findOne(Long id) {
        CscpWorkGroup cscpWorkGroup = cscpWorkGroupRepository.selectById(id);
        
        CscpWorkGroupDTO dto = new CscpWorkGroupDTO();
        BeanUtils.copyProperties(cscpWorkGroup, dto);
        return dto;
    }

    /**
     * Delete the cscpWorkGroup .
     *
     * @param id the id of the entity
     */
    @Override
    @Transactional
    public void delete(Long id) {
        log.debug("Request to delete CscpWorkGroup : {} ", id);

        //判断该工作组是否存在用户
        LambdaQueryWrapper<CscpUserWorkGroup> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(CscpUserWorkGroup::getGroupId, id);
        List<CscpUserWorkGroup> cscpUserWorkGroupList = cscpUserWorkGroupRepository.selectList(queryWrapper);
        if (CollectionUtils.isNotEmpty(cscpUserWorkGroupList)) {
            throw new BusinessException("删除失败：该工作组存在关联的用户！");
        }
        
        cscpWorkGroupRepository.deleteById(id);

    }
    

	
	/**
     * Get the cscpWorkGroups.
     *
     * @return the list of entities
     */
    @Override
    @Transactional(readOnly = true)
    public PageResult<CscpWorkGroupDTO> findByCscpWorkGroupDTO(CscpWorkGroupDTO cscpWorkGroupDTO, BasePageForm basePageForm) {
    
        log.debug("Request to find CscpWorkGroups");

        LambdaQueryWrapper<CscpWorkGroup> queryWrapper = new LambdaQueryWrapper();
		if(StringUtils.isNotBlank(cscpWorkGroupDTO.getGroupName())) {
            queryWrapper.like(CscpWorkGroup::getGroupName, cscpWorkGroupDTO.getGroupName());
		}
        IPage<CscpWorkGroup> userIPage = cscpWorkGroupRepository.selectPage(
                PageHelperUtil.getMPlusPageByBasePage(basePageForm), queryWrapper);

		List<CscpWorkGroupDTO> data = ListCopyUtil.copy(userIPage.getRecords(), CscpWorkGroupDTO.class);

		return new PageResult<CscpWorkGroupDTO>(data,userIPage.getTotal(),userIPage.getTotal());
		
    }

	
}
