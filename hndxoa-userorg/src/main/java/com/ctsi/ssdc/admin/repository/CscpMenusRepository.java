package com.ctsi.ssdc.admin.repository;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.ctsi.hndx.common.MybatisBaseMapper;
import com.ctsi.hndx.mybatisplus.sort.SortEnum;
import com.ctsi.hndx.mybatisplus.sort.SortMapper;
import com.ctsi.ssdc.admin.domain.CscpMenus;
import com.ctsi.ssdc.admin.domain.dto.CscpMenusDTO;
import com.ctsi.ssdc.database.annotation.InjectByDataBaseType;
import com.ctsi.ssdc.database.enums.EnumDatabaseName;
import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.type.JdbcType;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> Generator
 */
@InjectByDataBaseType(includes = {EnumDatabaseName.ORACLE})
public interface CscpMenusRepository extends MybatisBaseMapper<CscpMenus>, SortMapper {


    @Select({
            "select DISTINCT cm.*  ",
            "FROM cscp_menus cm  ",
            "left join cscp_role_menu crm on cm.id = crm.menu_id  ",
            "left join cscp_user_role cur on crm.role_id = cur.role_id  ",
            "where cur.user_id=#{userId,jdbcType=BIGINT}  ",
            "order by order_by"
    })
    @Results({
            @Result(column = "id", property = "id", jdbcType = JdbcType.BIGINT, id = true),
            @Result(column = "name", property = "name", jdbcType = JdbcType.VARCHAR),
            @Result(column = "icon", property = "icon", jdbcType = JdbcType.VARCHAR),
            @Result(column = "title", property = "title", jdbcType = JdbcType.VARCHAR),
            @Result(column = "url", property = "url", jdbcType = JdbcType.VARCHAR),
            @Result(column = "http_method", property = "httpMethod", jdbcType = JdbcType.VARCHAR),
            @Result(column = "component", property = "component", jdbcType = JdbcType.VARCHAR),
            @Result(column = "parent_id", property = "parentId", jdbcType = JdbcType.BIGINT),
            @Result(column = "type", property = "type", jdbcType = JdbcType.VARCHAR),
            @Result(column = "permission_code", property = "permissionCode", jdbcType = JdbcType.VARCHAR),
            @Result(column = "order_by", property = "orderBy", jdbcType = JdbcType.BIGINT)
    })
    @InterceptorIgnore(tenantLine = "true")
    List<CscpMenus> selectByUserId(Long userId);

    @Results({
            @Result(column = "id", property = "id", jdbcType = JdbcType.BIGINT, id = true),
            @Result(column = "icon", property = "icon", jdbcType = JdbcType.VARCHAR),
            @Result(column = "title", property = "title", jdbcType = JdbcType.VARCHAR),
            @Result(column = "url", property = "url", jdbcType = JdbcType.VARCHAR),
            @Result(column = "parent_id", property = "parentId", jdbcType = JdbcType.BIGINT),
            @Result(column = "type", property = "type", jdbcType = JdbcType.VARCHAR),
            @Result(column = "order_by", property = "orderBy", jdbcType = JdbcType.BIGINT),
            @Result(column = "component", property = "component", jdbcType = JdbcType.VARCHAR)
    })
    @Select({"select distinct c.id, c.title, c.icon, c.url, c.parent_id, c.type, c.order_by, "
            + "c.component  FROM cscp_role_menu b, cscp_menus c "
            + "where b.menu_id = c.id and b.role_id = #{roleId,jdbcType=BIGINT} order by c.order_by"})
    @MapKey("id")
    Map<Long, CscpMenusDTO> menusByRole(Long roleId);

    /**
     * 查询租户关联的所有菜单
     * @param tenantId
     * @return
     */
    @Results({
            @Result(column = "id", property = "id", jdbcType = JdbcType.BIGINT, id = true),
            @Result(column = "icon", property = "icon", jdbcType = JdbcType.VARCHAR),
            @Result(column = "title", property = "title", jdbcType = JdbcType.VARCHAR),
            @Result(column = "url", property = "url", jdbcType = JdbcType.VARCHAR),
            @Result(column = "parent_id", property = "parentId", jdbcType = JdbcType.BIGINT),
            @Result(column = "type", property = "type", jdbcType = JdbcType.VARCHAR),
            @Result(column = "order_by", property = "orderBy", jdbcType = JdbcType.BIGINT),
            @Result(column = "component", property = "component", jdbcType = JdbcType.VARCHAR)
    })
    @Select({"select distinct c.id, c.title, c.icon, c.url, c.parent_id, c.type, c.order_by, "
            + "c.component  FROM t_tenant_menu b, cscp_menus c "
            + "where b.menu_id = c.id and b.tenant_id = #{tenantId,jdbcType=BIGINT} and b.deleted = 0 " +
            "and c.deleted = 0 order by c.order_by"})
    List<CscpMenusDTO> qryMenusByTenantId(Long tenantId);

    //按照父id搜索
    @Select({"select * from  cscp_menus where  parent_id = #{parentId,jdbcType=BIGINT} order by order_by"})
    @Results({
            @Result(column = "id", property = "id", jdbcType = JdbcType.BIGINT, id = true),
            @Result(column = "icon", property = "icon", jdbcType = JdbcType.VARCHAR),
            @Result(column = "title", property = "title", jdbcType = JdbcType.VARCHAR),
            @Result(column = "url", property = "url", jdbcType = JdbcType.VARCHAR),
            @Result(column = "http_method", property = "httpMethod", jdbcType = JdbcType.VARCHAR),
            @Result(column = "component", property = "component", jdbcType = JdbcType.VARCHAR),
            @Result(column = "parent_id", property = "parentId", jdbcType = JdbcType.BIGINT),
            @Result(column = "type", property = "type", jdbcType = JdbcType.VARCHAR),
            @Result(column = "permission_code", property = "permissionCode", jdbcType = JdbcType.VARCHAR),
            @Result(column = "order_by", property = "orderBy", jdbcType = JdbcType.BIGINT)
    })
    List<CscpMenus> selectByParentId(Long parentId);
}