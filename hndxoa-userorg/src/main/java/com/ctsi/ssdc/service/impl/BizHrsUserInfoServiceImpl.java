package com.ctsi.ssdc.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ctsi.hndx.encryption.KeyCenterUtils;
import com.ctsi.hndx.utils.BeanConvertUtils;
import com.ctsi.hndx.utils.ListCopyUtil;
import com.ctsi.hndx.westone.WestoneEncryptService;
import com.ctsi.ssdc.admin.domain.CscpOrg;
import com.ctsi.ssdc.admin.domain.CscpUser;
import com.ctsi.ssdc.admin.domain.CscpUserOrg;
import com.ctsi.ssdc.admin.service.CscpOrgService;
import com.ctsi.ssdc.admin.service.CscpUserOrgService;
import com.ctsi.ssdc.admin.service.CscpUserService;
import com.ctsi.ssdc.entity.dto.BizHrsUserInfoDetailDTO;
import com.ctsi.ssdc.entity.dto.BizHrsUserInfoExportDTO;
import com.ctsi.ssdc.model.PageResult;
import com.ctsi.ssdc.entity.BizHrsUserInfo;
import com.ctsi.ssdc.entity.dto.BizHrsUserInfoDTO;
import com.ctsi.ssdc.mapper.BizHrsUserInfoMapper;
import com.ctsi.ssdc.service.IBizHrsUserInfoService;
import com.ctsi.hndx.common.SysBaseServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.utils.PageHelperUtil;
import com.ctsi.hndx.utils.ZipFileUtil;
import com.ctsi.hndx.utils.FileNameUtil;
import com.alibaba.excel.EasyExcel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.springframework.transaction.annotation.Transactional;

/**
 * <p>
 * 人社厅人员表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-07
 */
@Slf4j
@Service
public class BizHrsUserInfoServiceImpl extends SysBaseServiceImpl<BizHrsUserInfoMapper, BizHrsUserInfo> implements IBizHrsUserInfoService {

    @Autowired
    private BizHrsUserInfoMapper bizHrsUserInfoMapper;
    @Autowired
    private CscpUserService cscpUserService;
    @Autowired
    private CscpOrgService cscpOrgService;
    @Autowired
    private CscpUserOrgService cscpUserOrgService;
    @Autowired
    private WestoneEncryptService westoneEncryptService;

    /**
     * 翻页
     *
     * @param entityDTO
     * @param basePageForm
     * @return
     */
    @Override
    public PageResult<BizHrsUserInfoDTO> queryListPage(BizHrsUserInfoDTO entityDTO, BasePageForm basePageForm) {
        //设置条件
        LambdaQueryWrapper<BizHrsUserInfo> queryWrapper = new LambdaQueryWrapper();

        IPage<BizHrsUserInfo> pageData = bizHrsUserInfoMapper.selectPage(
             PageHelperUtil.getMPlusPageByBasePage(basePageForm), queryWrapper);
        //返回
        IPage<BizHrsUserInfoDTO> data  = pageData.convert(entity -> BeanConvertUtils.copyProperties(entity,BizHrsUserInfoDTO.class));

        return new PageResult<BizHrsUserInfoDTO>(data.getRecords(),
            data.getTotal(), data.getCurrent());
    }

    /**
     * 列表查询
     *
     * @param entityDTO
     * @return
     */
    @Override
    public PageResult<BizHrsUserInfoDTO> queryHrsUnitPage(BizHrsUserInfoDTO entityDTO, BasePageForm basePageForm) {
        LambdaQueryWrapper<BizHrsUserInfo> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.select( BizHrsUserInfo::getStrUnitName);
        queryWrapper.groupBy(BizHrsUserInfo::getStrUnitName);
        queryWrapper.like(StringUtils.isNotBlank(entityDTO.getStrUnitName()), BizHrsUserInfo::getStrUnitName, entityDTO.getStrUnitName());
        queryWrapper.like(StringUtils.isNotBlank(entityDTO.getStrUserId()), BizHrsUserInfo::getStrUserId, entityDTO.getStrUserId());

        IPage<BizHrsUserInfoDTO> pageData = bizHrsUserInfoMapper.selectPageNoAdd(
                PageHelperUtil.getMPlusPageByBasePage(basePageForm), queryWrapper
        );
        return new PageResult<>(pageData.getRecords(), pageData.getTotal(), pageData.getCurrent());
    }

    /**
     * 单个查询
     *
     * @param id the id of the entity
     * @return
     */
    @Override
    public BizHrsUserInfoDTO findOne(Long id) {
        BizHrsUserInfo  bizHrsUserInfo =  bizHrsUserInfoMapper.selectById(id);
        return  BeanConvertUtils.copyProperties(bizHrsUserInfo,BizHrsUserInfoDTO.class);
    }


    /**
     * 新增
     *
     * @param entityDTO the entity to create
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public BizHrsUserInfoDTO create(BizHrsUserInfoDTO entityDTO) {
       BizHrsUserInfo bizHrsUserInfo =  BeanConvertUtils.copyProperties(entityDTO,BizHrsUserInfo.class);
        save(bizHrsUserInfo);
        return  BeanConvertUtils.copyProperties(bizHrsUserInfo,BizHrsUserInfoDTO.class);
    }

    /**
     * 修改
     *
     * @param entity the entity to update
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int update(BizHrsUserInfoDTO entity) {
        BizHrsUserInfo bizHrsUserInfo = BeanConvertUtils.copyProperties(entity,BizHrsUserInfo.class);
        return bizHrsUserInfoMapper.updateById(bizHrsUserInfo);
    }

    /**
     * 删除
     *
     * @param id the id of the entity
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int delete(Long id) {
        return bizHrsUserInfoMapper.deleteById(id);
    }


    /**
     * 验证是否存在
     *
     * @param BizHrsUserInfoId
     * @return
     */
    @Override
    public boolean existByBizHrsUserInfoId(Long BizHrsUserInfoId) {
        if (BizHrsUserInfoId != null) {
            LambdaQueryWrapper<BizHrsUserInfo> queryWrapper = new LambdaQueryWrapper();
            queryWrapper.eq(BizHrsUserInfo::getId, BizHrsUserInfoId);
            List<BizHrsUserInfo> result = bizHrsUserInfoMapper.selectList(queryWrapper);
            return result.size() > 0;
        }
        return true;
    }

    /**
    * 批量新增
    *
    */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertBatch(List<BizHrsUserInfoDTO> dataList) {
        List<BizHrsUserInfo> result = ListCopyUtil.copy(dataList, BizHrsUserInfo.class);
        return saveBatch(result);
    }

    @Override
    public PageResult<BizHrsUserInfoDetailDTO> queryBizHrsUserInfoPage(BizHrsUserInfoDTO entityDTO, BasePageForm basePageForm) {
        if(basePageForm.getCurrentPage() == null || basePageForm.getCurrentPage() <= 0){
            basePageForm.setCurrentPage(1);
            basePageForm.setPageSize(1000);
        }
        LambdaQueryWrapper<BizHrsUserInfo> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.select(BizHrsUserInfo::getId,
                BizHrsUserInfo::getStrUnitName,
                BizHrsUserInfo::getStrUserId,
                BizHrsUserInfo::getIdCardNo,
                BizHrsUserInfo::getStrMobile
        );
        queryWrapper.like(StringUtils.isNotBlank(entityDTO.getStrUnitName()), BizHrsUserInfo::getStrUnitName, entityDTO.getStrUnitName());
        queryWrapper.like(StringUtils.isNotBlank(entityDTO.getStrUserId()), BizHrsUserInfo::getStrUserId, entityDTO.getStrUserId());
        queryWrapper.like(StringUtils.isNotBlank(entityDTO.getIdCardNo()), BizHrsUserInfo::getIdCardNo, entityDTO.getIdCardNo());
        queryWrapper.like(StringUtils.isNotBlank(entityDTO.getStrMobile()), BizHrsUserInfo::getStrMobile, entityDTO.getStrMobile());
        IPage<BizHrsUserInfo> pageData = bizHrsUserInfoMapper.selectPageNoAdd(PageHelperUtil.getMPlusPageByBasePage(basePageForm), queryWrapper);
        List<BizHrsUserInfo> listData = pageData.getRecords();

        return new PageResult<>(ListCopyUtil.copy(listData, BizHrsUserInfoDetailDTO.class), pageData.getTotal(), pageData.getCurrent());

    }

    /**
     * 同步人社厅用户信息到系统用户表
     *
     * @param hrsUnitName 人社厅选中的单位名称
     * @param deptName 系统已存在的部门名称
     * @return 更新成功的记录数
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public List<BizHrsUserInfoDTO> syncUserInfo(String hrsUnitName, String deptName) {
        // 根据条件查询人社厅用户
        LambdaQueryWrapper<BizHrsUserInfo> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.eq(StringUtils.isNotBlank(hrsUnitName), BizHrsUserInfo::getStrUnitName, hrsUnitName);
        List<BizHrsUserInfo> listData = bizHrsUserInfoMapper.selectList(queryWrapper);
        List<BizHrsUserInfoDTO> hrsUsers = ListCopyUtil.copy(listData, BizHrsUserInfoDTO.class);
        if (hrsUsers == null || hrsUsers.isEmpty()) {
            return new ArrayList<>();
        }

        // 2. 过滤掉同名的人社厅用户
        Map<String, Long> userNameCount = hrsUsers.stream()
                .collect(Collectors.groupingBy(BizHrsUserInfoDTO::getStrUserId, Collectors.counting()));
        List<BizHrsUserInfoDTO> distinctHrsUsers  = hrsUsers.stream()
                .filter(user -> userNameCount.get(user.getStrUserId()) == 1)
                .collect(Collectors.toList());

        if (CollUtil.isEmpty(distinctHrsUsers)) {
            return new ArrayList<>();
        }

        // 3. 根据 deptName 查询 cscp_org 表获取机构 ID
        LambdaQueryWrapper<CscpOrg> orgQueryWrapper = new LambdaQueryWrapper<>();
        orgQueryWrapper.eq(CscpOrg::getOrgName, deptName);
        CscpOrg cscpOrg = cscpOrgService.getOne(orgQueryWrapper);

        if (cscpOrg == null) {
            // 如果根据 deptName 没有找到机构，则无法继续同步
            return new ArrayList<>();
        }

        // 4. 根据机构 ID 查询 cscp_user_org 表获取用户 ID
        LambdaQueryWrapper<CscpUserOrg> userOrgQueryWrapper = new LambdaQueryWrapper<>();
        userOrgQueryWrapper.eq(CscpUserOrg::getOrgId, cscpOrg.getId());
        List<CscpUserOrg> cscpUserOrgs = cscpUserOrgService.list(userOrgQueryWrapper);

        if (CollUtil.isEmpty(cscpUserOrgs)) {
            // 如果该机构下没有用户，则无需同步
            return new ArrayList<>();
        }

        List<Long> userIds = cscpUserOrgs.stream()
                .map(CscpUserOrg::getUserId)
                .collect(Collectors.toList());

        // 5. 根据用户 ID 列表查询 cscp_user 表获取所有用户
        List<CscpUser> cscpUsers = Optional.ofNullable(cscpUserService.listByIds(userIds))
                .orElse(Collections.emptyList())
                .stream()
                .collect(Collectors.groupingBy(CscpUser::getRealName))
                .values()
                .stream()
                .map(duplicates -> duplicates.get(0))
                .collect(Collectors.toList());
        if (CollUtil.isEmpty(cscpUsers)) {
            return new ArrayList<>();
        }

        Map<String, CscpUser> cscpUserMap = cscpUsers.stream()
                .collect(Collectors.toMap(CscpUser::getRealName, Function.identity()));

        List<CscpUser> usersToUpdate = new ArrayList<>();
        for (BizHrsUserInfoDTO hrsUser : distinctHrsUsers) {
            CscpUser cscpUser = cscpUserMap.get(hrsUser.getStrUserId());
            if (cscpUser != null) {
                if (StrUtil.isBlank(cscpUser.getIdCardNo())) {
                    cscpUser.setIdCardNo(hrsUser.getIdCardNo());
                }
                if (StrUtil.isBlank(cscpUser.getStrIdCardNo())) {
                    cscpUser.setStrIdCardNo(hrsUser.getIdCardNo());
                }
                if (StrUtil.isBlank(cscpUser.getMobile())) {
                    cscpUser.setMobile(hrsUser.getStrMobile());
                }
                if (StrUtil.isBlank(cscpUser.getMobileStart())) {
                    cscpUser.setMobileStart(hrsUser.getStrMobile());
                }
                if (StrUtil.isBlank(cscpUser.getMobileMiddle())) {
                    cscpUser.setMobileMiddle(hrsUser.getStrMobile());
                }
                if (StrUtil.isBlank(cscpUser.getMobileEnd())) {
                    cscpUser.setMobileEnd(hrsUser.getStrMobile());
                }
                cscpUser.setUpdateTime(LocalDateTime.now());
                usersToUpdate.add(cscpUser);
            }
        }

        // 更新单位统一社会信用码
        if (StrUtil.isNotBlank(hrsUsers.get(0).getCreditCode())) {
            cscpOrg.setCreditCode(hrsUsers.get(0).getCreditCode());
            cscpOrg.setUpdateTime(LocalDateTime.now());
            cscpOrgService.updateById(cscpOrg);
        }

        if (CollUtil.isNotEmpty(usersToUpdate)) {
            // 批量更新用户信息
            boolean updateResult = cscpUserService.updateBatchById(usersToUpdate);
            if (updateResult) {
                // 转换更新后的用户信息为DTO对象返回
                return distinctHrsUsers.stream()
                        .filter(hrs -> cscpUserMap.containsKey(hrs.getStrUserId()))
                        .collect(Collectors.toList());
            }
        }
        return new ArrayList<>();
    }

    /**
     * 导出所有人社厅人员数据，按单位分组生成Excel文件并打包下载
     *
     * @param response HTTP响应对象
     * @return 是否导出成功
     */
    @Override
    public Boolean exportAllUsersByUnit(HttpServletResponse response) {
        List<File> tempFiles = new ArrayList<>();
        try {
            // 1. 查询所有人社厅人员数据
            List<BizHrsUserInfo> allUsers = bizHrsUserInfoMapper.selectList(null);
            if (allUsers == null || allUsers.isEmpty()) {
                log.warn("没有找到人社厅人员数据");
                return false;
            }

            // 2. 按单位名称分组
            Map<String, List<BizHrsUserInfo>> usersByUnit = allUsers.stream()
                    .filter(user -> StringUtils.isNotBlank(user.getStrUnitName()))
                    .collect(Collectors.groupingBy(BizHrsUserInfo::getStrUnitName));

            if (usersByUnit.isEmpty()) {
                log.warn("没有找到有效的单位数据");
                return false;
            }

            // 3. 为每个单位生成Excel文件
            String tempDir = System.getProperty("java.io.tmpdir");
            for (Map.Entry<String, List<BizHrsUserInfo>> entry : usersByUnit.entrySet()) {
                String unitName = entry.getKey();
                List<BizHrsUserInfo> unitUsers = entry.getValue();

                // 转换为导出DTO
                List<BizHrsUserInfoExportDTO> exportData = unitUsers.stream()
                        .map(this::convertToExportDTO)
                        .collect(Collectors.toList());

                // 生成安全的文件名（去除特殊字符）
                String safeFileName = unitName.replaceAll("[\\\\/:*?\"<>|]", "_");
                String excelFilePath = tempDir + File.separator + safeFileName + "_" + System.currentTimeMillis() + ".xlsx";
                File excelFile = new File(excelFilePath);

                // 写入Excel文件
                EasyExcel.write(excelFilePath, BizHrsUserInfoExportDTO.class)
                        .sheet(unitName)
                        .doWrite(exportData);

                tempFiles.add(excelFile);
                log.info("已生成Excel文件: {} ({}条记录)", safeFileName, exportData.size());
            }

            // 4. 打包成ZIP文件并返回
            String zipFileName = "人社厅人员数据_" + System.currentTimeMillis() + ".zip";
            FileNameUtil.setDownloadResponseHeader(response, zipFileName);

            try (OutputStream outputStream = response.getOutputStream()) {
                ZipFileUtil.toZip(tempFiles, outputStream);
                outputStream.flush();
            }

            log.info("成功导出{}个单位的人员数据", usersByUnit.size());
            return true;

        } catch (Exception e) {
            log.error("导出人社厅人员数据失败", e);
            return false;
        } finally {
            // 5. 清理临时文件
            for (File tempFile : tempFiles) {
                if (tempFile.exists()) {
                    tempFile.delete();
                }
            }
        }
    }

    /**
     * 转换为导出DTO
     */
    private BizHrsUserInfoExportDTO convertToExportDTO(BizHrsUserInfo user) {
        return BizHrsUserInfoExportDTO.builder()
                .strUserId(user.getStrUserId())
                .idCardNo(user.getIdCardNo())
                .strMobile(user.getStrMobile())
                .strUnitName(user.getStrUnitName())
                .creditCode(user.getCreditCode())
                .build();
    }



}
