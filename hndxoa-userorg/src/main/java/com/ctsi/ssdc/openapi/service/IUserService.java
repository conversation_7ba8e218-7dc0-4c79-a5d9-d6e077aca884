package com.ctsi.ssdc.openapi.service;

import javax.servlet.http.HttpServletRequest;

/**
 * @Description: 用户信息服务接口
 * @author: tr
 * @date: 2025年02月27日 11:25
 */
public interface IUserService {

    /**
     * @Description: 获取当前用户信息，返回加密字符串
     * @author: tr
     * @Date: 2025/2/27 11:28
     * @param: [request]
     * @returnValue: java.lang.String
     */
    String getCurrUserInfo(HttpServletRequest request);
}
