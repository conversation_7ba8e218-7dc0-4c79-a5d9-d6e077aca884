package com.ctsi.ssdc.openapi.service.impl;

import cn.hutool.json.JSONUtil;
import com.ctsi.ssdc.openapi.domain.response.OpenApiUserDetail;
import com.ctsi.ssdc.openapi.service.IUserService;
import com.ctsi.ssdc.openapi.utils.SM4Utils;
import com.ctsi.ssdc.security.CscpUserDetail;
import com.ctsi.ssdc.security.jwt.TokenProvider;
import com.ctsi.ssdc.util.RedisUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.util.Map;

/**
 * @Description: 用户信息服务实现类
 * @author: tr
 * @date: 2025年02月27日 11:25
 */
@Service
public class UserServiceImpl implements IUserService {

    @Autowired
    private TokenProvider tokenProvider;

    @Autowired
    private RedisUtil redisUtil;

    @Override
    public String getCurrUserInfo(HttpServletRequest request) {
        String xbtToken = request.getHeader("xbtToken");
        String clientId = request.getHeader("clientId");
        CscpUserDetail cscpUserDetail = tokenProvider.getLoginUser(xbtToken);
        if (cscpUserDetail != null){
            OpenApiUserDetail openApiUserDetail = new OpenApiUserDetail();
            BeanUtils.copyProperties(cscpUserDetail, openApiUserDetail);

            Map<String, String> secretKeyPair = (Map<String, String>)redisUtil.get("app:secret_key");
            String secretKey = secretKeyPair.get(clientId);
            String result = SM4Utils.encryptSm4(JSONUtil.toJsonStr(openApiUserDetail), secretKey);
            return result;
        }
        return null;
    }
}
