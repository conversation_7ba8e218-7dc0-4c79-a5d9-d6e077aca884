package com.ctsi.ssdc.openapi.controller;

import com.ctsi.hndx.result.ResultVO;
import com.ctsi.ssdc.openapi.domain.request.TokenReq;
import com.ctsi.ssdc.openapi.service.ITokenService;
import com.ctsi.ssdc.openapi.utils.SM4Utils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

/**
 * @Description: openapi-Token相关接口
 * @author: tr
 * @date: 2025年02月18日 18:55
 */
@RestController
@RequestMapping("/api/openapi/system/v1")
public class TokenController {

    @Resource
    private ITokenService tokenService;

    @PostMapping(value = "/createToken")
    public ResultVO createToken(HttpServletRequest request, @RequestBody TokenReq tokenReq) {
        String result = tokenService.createToken(request, tokenReq);
        return ResultVO.success(result);
    }

    @PostMapping(value = "/validateToken")
    public ResultVO validateToken(HttpServletRequest request) {
        String result = tokenService.validateToken(request);
        return ResultVO.success(result);
    }

    @PostMapping(value = "/logout")
    public ResultVO logout(HttpServletRequest request) {
        return tokenService.logout(request);
    }


    @PostMapping(value = "/refreshToken")
    public ResultVO refreshToken(HttpServletRequest request) {
        String result = tokenService.refreshToken(request);
        return ResultVO.success(result);
    }

    /**
     * 获取当前登录人的token并返回SM4加密后的token
     * @param request HTTP请求
     * @return 加密后的token
     */
    @PostMapping(value = "/getCurrentUserEncryptedToken")
    public ResultVO getCurrentUserEncryptedToken(HttpServletRequest request) {
        String result = tokenService.getCurrentUserEncryptedToken(request);
        return ResultVO.success(result);
    }

}
