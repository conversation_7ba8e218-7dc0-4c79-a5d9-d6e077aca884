//package com.ctsi.middleware.mq;
//
//import com.ctsi.hndx.annotations.ResponseResultVo;
//import com.ctsi.hndx.common.BaseController;
//import com.ctsi.hndx.result.ResultVO;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.rocketmq.spring.core.RocketMQTemplate;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.web.bind.annotation.GetMapping;
//import org.springframework.web.bind.annotation.PathVariable;
//import org.springframework.web.bind.annotation.RequestMapping;
//import org.springframework.web.bind.annotation.RestController;
//
//@Slf4j
//@RestController
//@ResponseResultVo
//@RequestMapping("/api/mq")
//public class ProducerController extends BaseController {
//
//    @Autowired
//    private RocketMQTemplate rocketMQTemplate;
//
//    @GetMapping("/send/{message}")
//    public ResultVO<String> send(@PathVariable("message") String message) {
//        rocketMQTemplate.convertAndSend("swoa-test-topic", message);
//        return ResultVO.success("Message: '" + message + "' sent.");
//    }
//}