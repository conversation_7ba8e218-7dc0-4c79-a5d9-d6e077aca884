package com.ctsi.business.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/12/28
 * @apiNote
 */
@Data
public class QueryAllProcessMonitorDTO {

    @ApiModelProperty("流程定义的key")
    String processDefinitionKey;

    @ApiModelProperty("流程实例id")
    String processInstanceId;

    @ApiModelProperty("跟节点流程实例id,不是子流程的时候，和上面的流程实例id相等")
    String rootProcessInstanceId;

    @ApiModelProperty("流程定义id")
    String processDefinitionId;

    @ApiModelProperty("当前流程任务id")
    String taskId;

    @ApiModelProperty("baseId")
    String baseId;
    @ApiModelProperty("当前流程任务定义的key")
    String taskDefinitionKey;

    @ApiModelProperty("表单id")
    String formId;

    @ApiModelProperty("附件")
    String annex;

    @ApiModelProperty("流程标题")
    String title;

    @ApiModelProperty("流程分类的名称用来表示业务名称")
    String procTypeName;

    @ApiModelProperty("当前环节名称")
    String name;

    @ApiModelProperty("流程办理类别 0: 本人办理  1:代人办理")
    int assignType;

    @ApiModelProperty("任务创建时间")
    LocalDateTime taskCreateTime;

    @ApiModelProperty("流程创建时间")
    LocalDateTime processCreatTime;

    @ApiModelProperty("流程结束时间")
    String processEndTime;

    @ApiModelProperty("业务主键id")
    String formDataId;

    @ApiModelProperty("流程拟稿人")
    String createName;

    @ApiModelProperty("流程拟稿ID")
    Long createBy;

    @ApiModelProperty("流程拟稿人部门名称")
    String departmentName;

    @ApiModelProperty("流程拟稿人单位名称")
    String companyName;

    @ApiModelProperty("流程状态：0业务没有与流程关联，1启动流程，2办理中，3完成，4暂停，5作废。其他参考具体文档，见常量BpmStatusConstants")
    Integer bpmStatus;

    @ApiModelProperty("当前流程办理人")
    Long assingee;

    @ApiModelProperty("当前流程办理人")
    List<String> assingeeList;

    @ApiModelProperty("当前步骤的流程办理人的名称")
    String assigneeName;
    @ApiModelProperty("是否正文 1表示有正文 0表示无正文")
    Integer document;

    @ApiModelProperty("处理状态")
    Integer readStatus;

    @ApiModelProperty("处理状态（在用字段）")
    String readStatusStr;

    @ApiModelProperty("流程处理时间")
    LocalDateTime handleTime;

    @ApiModelProperty("是否转传阅卡  0-否 1-是")
    Integer isCircularize;

    @ApiModelProperty("批阅意见")
    String reviewComments;

    @ApiModelProperty("在办处理按钮是否显示 0-不显示 1-显示")
    Integer isHandleBtn;

    @ApiModelProperty("领导批示集合")
    List<String> leaderList;

    @ApiModelProperty("紧急程度字段，通过数据字典获取")
    private String urgency;

    @ApiModelProperty(value = "发送时间")
    private LocalDateTime sendTime;

    @ApiModelProperty(value = "催办次数")
    private Integer remindCount;

    /**
     * 上一步流程处理人
     */
    private String userNamePre;

}
