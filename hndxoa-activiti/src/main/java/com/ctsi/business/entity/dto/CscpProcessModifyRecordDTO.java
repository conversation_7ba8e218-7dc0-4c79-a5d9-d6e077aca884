package com.ctsi.business.entity.dto;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.annotation.EnumValue;
import com.ctsi.hndx.common.BaseDtoEntity;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

/**
 * <p>
 * 表单修改留痕记录
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-11
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "CscpProcessModifyRecordDTO对象", description = "表单修改留痕记录")
public class CscpProcessModifyRecordDTO extends BaseDtoEntity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("表单字段信息id")
    private String formId;

    @ApiModelProperty("业务主键id")
    private String formDataId;

    @ApiModelProperty("流程实例id")
    private String processInstanceId;

    @ApiModelProperty("修改字段")
    private String modifyField;

    @ApiModelProperty("修改字段名称")
    private String modifyFieldName;

    @ApiModelProperty("修改之前字段值")
    private String oldValue;

    @ApiModelProperty("修改之后字段值")
    private String newValue;

    @ApiModelProperty("修改时间")
    private LocalDateTime modifyTime;

    @ApiModelProperty("创建人")
    private String createName;

    @ApiModelProperty(value = "当前流程节点名称")
    private String nodeName;
    @ApiModelProperty(value = "当前流程节点key")
    private String nodeKey;

     public static enum FileStatus{
        FILE_DOC("doc","正文"),
         FILE_ANNEX("annex","附件")


         ;
        private String code;
        private String name;

         FileStatus(String code,String name) {
            this.code = code;
            this.name = name;
        }

        public static FileStatus  getFileStatus(String code){
            for (FileStatus value : values()) {
                if(StrUtil.equals(value.code,code)){
                    return value;
                }
            }
             return null;
        }
        public String getCode() {
            return code;
        }
        public String getName() {
            return name;
        }
    }

}
