package com.ctsi.business.entity.dto;

import com.ctsi.hndx.common.BaseDtoEntity;
import java.io.Serializable;
import java.math.BigInteger;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

/**
 * <p>
 * 表单修改留痕记录
 * 每次存数据把最新的一条流程数据id找到 赋值给插入数据的pid
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-26
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@ApiModel(value="CscpProcessHistoryRecordDTO对象", description="表单修改留痕记录")
public class CscpProcessHistoryRecordDTO extends BaseDtoEntity {

    private static final long serialVersionUID = 1L;


    /**
     * 拟稿初次提交的pid=0,再次提交时 pid为上次提交的id
     */
    private Long pid;


    /**
     * 表单字段信息id
     */
    @ApiModelProperty(value = "表单字段信息id")
    private String formId;

    /**
     * 业务主键id
     */
    @ApiModelProperty(value = "业务主键id")
    private String formDataId;

    /**
     * 流程实例id
     */
    @ApiModelProperty(value = "流程实例id")
    private String processInstanceId;

    /**
     * 业务提交历史记录
     */
    @ApiModelProperty(value = "业务提交历史记录")
    private String formDataJson;

    /**
     * 下一步流程节点key
     */
    @ApiModelProperty(value = "下一步流程节点key")
    private String toNodeKey;

    /**
     * 当前流程节点key
     */
    @ApiModelProperty(value = "当前流程节点key")
    private String nodeKey;

    /**
     * 当前流程节点名称
     */
    @ApiModelProperty(value = "当前流程节点名称")
    private String nodeName;

    /**
     * 当前流程定义id
     */
    @ApiModelProperty(value = "当前流程定义id")
    private String processDefinitionId;


}
