package com.ctsi.business.controller;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ctsi.business.domain.CscpProcBase;
import com.ctsi.business.entity.dto.CscpProcessModifyRecordDTO;
import com.ctsi.business.entity.dto.CscpProcessModifyRecordDTO;
import com.ctsi.business.repository.CscpProcBaseRepository;
import com.ctsi.business.service.ICscpProcessModifyRecordService;
import com.ctsi.hndx.annotations.ResponseResultVo;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.enums.DBOperation;
import com.ctsi.hndx.result.ResultCode;
import com.ctsi.hndx.result.ResultVO;
import com.ctsi.ssdc.annotation.OperationLog;
import com.ctsi.ssdc.model.PageResult;
import com.ctsi.ssdc.model.ResResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>
 * 表单修改留痕记录 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-11
 */
@Slf4j
@RestController
@ResponseResultVo
@RequestMapping("/api/cscpProcessModifyRecordController")
@Api(value = "表单修改留痕记录", tags = "表单修改留痕记录接口")
public class CscpProcessModifyRecordController {

	private static final String ENTITY_NAME = "cscpProcessHistoryRecord";

	@Autowired
	private ICscpProcessModifyRecordService cscpProcessHistoryRecordService;

	@Autowired
	private CscpProcBaseRepository cscpProcBaseRepository;

	/**
	 *  新增表单修改留痕记录批量数据.
	 */
	@PostMapping("/createBatch")
	@ApiOperation(value = "新增批量(权限code码为：cscp.cscpProcessHistoryRecord.add)", notes = "传入参数")
	@OperationLog(dBOperation = DBOperation.ADD,message = "新增表单修改留痕记录批量数据")
	// @PreAuthorize("@permissionService.hasPermi('cscp.cscpProcessHistoryRecord.add')")
	public ResultVO createBatch(@RequestBody List<CscpProcessModifyRecordDTO> cscpProcessHistoryRecordList) {
		Boolean  result = cscpProcessHistoryRecordService.insertBatch(cscpProcessHistoryRecordList);
		if(result){
			return ResultVO.success();
		}else {
			return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
		}
	}
	

	@DeleteMapping("/delete/{id}")
	@OperationLog(dBOperation = DBOperation.DELETE,message = "删除表单修改留痕记录数据")
	@ApiOperation(value = "删除存在数据(权限code码为：cscp.cscpProcessHistoryRecord.delete)", notes = "传入参数")
	// @PreAuthorize("@permissionService.hasPermi('cscp.cscpProcessHistoryRecord.delete')")
	public ResultVO delete(@PathVariable Long id) {
		int count = cscpProcessHistoryRecordService.delete(id);
		if(count > 0 ){
			return ResultVO.success();
		}else {
			return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
		}
	}

	/**
	 * 查询单条数据.
	 */
	@GetMapping("/get/{id}")
	@ApiOperation(value = "查询单条数据", notes = "传入参数")
	//// @PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
	public ResultVO get(@PathVariable Long id) {
		CscpProcessModifyRecordDTO cscpProcessHistoryRecordDTO = cscpProcessHistoryRecordService.findOne(id);
		return ResultVO.success(cscpProcessHistoryRecordDTO);
	}

	/**
	 *  分页查询多条数据.
	 */
	@GetMapping("/queryCscpProcessHistoryRecordPage")
	@ApiOperation(value = "翻页查询多条数据", notes = "传入参数")
	//// @PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
	public ResultVO<PageResult<CscpProcessModifyRecordDTO>> queryCscpProcessHistoryRecordPage(CscpProcessModifyRecordDTO cscpProcessHistoryRecordDTO, BasePageForm basePageForm) {
		return ResultVO.success(cscpProcessHistoryRecordService.queryListPage(cscpProcessHistoryRecordDTO, basePageForm));
	}

	/**
	 * 查询多条数据.不分页
	 */
	@GetMapping("/queryCscpProcessHistoryRecord")
	@ApiOperation(value = "查询多条数据", notes = "传入参数")
	//// @PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
	public ResultVO<ResResult<CscpProcessModifyRecordDTO>> queryCscpProcessHistoryRecord(CscpProcessModifyRecordDTO cscpProcessHistoryRecordDTO) {
		List<CscpProcessModifyRecordDTO> list = cscpProcessHistoryRecordService.queryList(cscpProcessHistoryRecordDTO);
		return ResultVO.success(new ResResult<CscpProcessModifyRecordDTO>(list));
	}

	@GetMapping("/getModifyFields/{processInstanceId}")
	@ApiOperation(value = "查询多条数据", notes = "传入参数")
	//// @PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
	public ResultVO<List<String>> getModifyFields(@PathVariable String processInstanceId) {

		// processInstanceId
		List<String> list = cscpProcessHistoryRecordService.getModifyFields(processInstanceId);
		return ResultVO.success(list);
	}



	@GetMapping("/getDeptIdByFormDataId")
	@ApiOperation(value = "查询创建人的部门id", notes = "传入参数")
	//// @PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
	public ResultVO<Long> getDeptIdByFormDataId(String formDataId) {

		LambdaQueryWrapper<CscpProcBase> queryWrapper = new LambdaQueryWrapper();
		queryWrapper.eq(CscpProcBase::getFormDataId,formDataId).last(" limit 1")
				;
		List<CscpProcBase> cscpProcBases = cscpProcBaseRepository.selectListNoAdd(queryWrapper);
		if(cscpProcBases != null && cscpProcBases.size() > 0 ){
			return ResultVO.success(cscpProcBases.get(0).getDepartmentId());
		}
		return ResultVO.success();
	}


	
}
