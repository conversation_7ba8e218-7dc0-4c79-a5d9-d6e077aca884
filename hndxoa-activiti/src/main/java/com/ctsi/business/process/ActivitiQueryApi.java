package com.ctsi.business.process;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ctsi.activiti.bpmn.entity.CscpProc;
import com.ctsi.activiti.candidate.loader.CandidateDetail;
import com.ctsi.activiti.candidate.loader.CandidateLoader;
import com.ctsi.activiti.core.model.ExecutionLine;
import com.ctsi.activiti.core.model.TaskQueryParam;
import com.ctsi.activiti.core.service.ActivitiQueryService;
import com.ctsi.activiti.core.vo.*;
import com.ctsi.activiti.multi.loader.MultiStrategyDetail;
import com.ctsi.activiti.multi.loader.MultiStrategyLoader;
import com.ctsi.business.domain.Cscp;
import com.ctsi.business.domain.CscpAuditContent;
import com.ctsi.business.domain.CscpOutAuthorization;
import com.ctsi.business.domain.CscpProcBase;
import com.ctsi.business.domain.dto.CscpBtnDto;
import com.ctsi.business.domain.dto.ProcessHandlerDTO;
import com.ctsi.business.dto.QueryApprovalDTO;
import com.ctsi.business.dto.QueryProcessSignDTO;
import com.ctsi.business.service.CscpActivitiService;
import com.ctsi.business.service.CscpAuditContentService;
import com.ctsi.business.service.CscpOutAuthorizationService;
import com.ctsi.business.service.CscpProcBaseService;
import com.ctsi.business.vo.QueryApproveManagementCodeVO;
import com.ctsi.common.bo.PageActiviti;
import com.ctsi.common.bo.PageForm;
import com.ctsi.common.bo.ResponseBO;
import com.ctsi.common.utils.DateTimeUtil;
import com.ctsi.common.utils.PageData;
import com.ctsi.common.utils.SysErrEnum;
import com.ctsi.hndx.annotations.LimitSubmit;
import com.ctsi.hndx.annotations.PermissionData;
import com.ctsi.hndx.annotations.ResponseResultVo;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.common.datapermission.DataFilterThreadLocal;
import com.ctsi.hndx.constant.BpmStatusConstants;
import com.ctsi.hndx.enums.DBOperation;
import com.ctsi.hndx.enums.DataFilterMetaData;
import com.ctsi.hndx.enums.FileBasePathName;
import com.ctsi.hndx.filestore.FileStoreTemplateService;
import com.ctsi.hndx.haoqian.entity.dto.CscpHaoqianVersionDTO;
import com.ctsi.hndx.haoqian.service.ICscpHaoqianVersionService;
import com.ctsi.hndx.leadershipEntrustment.entity.BizLeadershipEntrustment;
import com.ctsi.hndx.leadershipEntrustment.service.IBizLeadershipEntrustmentService;
import com.ctsi.hndx.result.ResultCode;
import com.ctsi.hndx.result.ResultVO;
import com.ctsi.hndx.tsysconfig.service.ISysConfigService;
import com.ctsi.hndx.utils.HaoQianUtils;
import com.ctsi.hndx.utils.ListCopyUtil;
import com.ctsi.hndx.wps.WpsUtil;
import com.ctsi.operation.service.CscpDocumentFileService;
import com.ctsi.service.CFormDataService;
import com.ctsi.ssdc.admin.domain.CscpUser;
import com.ctsi.ssdc.admin.domain.dto.CscpDistributeBaseUserDTO;
import com.ctsi.ssdc.admin.service.CscpUserRoleService;
import com.ctsi.ssdc.admin.service.CscpUserService;
import com.ctsi.ssdc.annotation.OperationLog;
import com.ctsi.ssdc.entity.dto.BizApprovalGiveAdviceRecordDTO;
import com.ctsi.ssdc.model.PageResult;
import com.ctsi.ssdc.security.SecurityUtils;
import com.ctsi.ssdc.service.BizService;
import com.ctsi.ssdc.service.ExportToExcelService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 工作流查询操作API，待办、已办、流程图、流转日志等等
 *
 * @author: PANJL
 * @date: 2019-05-07 20:04
 */
@Slf4j
@Api(tags = "工作流查询操作接口，作API，待办、已办、流程图、流转日志等等", value = "工作流查询操作接口")
@RestController
@RequestMapping("/api/activiti/query")
public class ActivitiQueryApi {

    @Autowired
    private ActivitiQueryService activitiQueryService;

    @Autowired
    private MultiStrategyLoader multiStrategyLoader;

    @Autowired
    private CandidateLoader candidateLoader;

    @Autowired
    private CscpActivitiService cscpActivitiService;

    @Autowired
    private CscpOutAuthorizationService cscpOutAuthorizationService;

    @Autowired
    private CscpProcBaseService cscpProcBaseService;

    @Autowired
    private IBizLeadershipEntrustmentService bigLeadershipEntrustmentService;

    @Autowired
    private CscpAuditContentService cscpAuditContentService;

    @Autowired
    private CFormDataService cFormDataService;

    @Autowired
    private ICscpHaoqianVersionService cscpHaoqianVersionService;

    @Autowired
    private CscpUserRoleService cscpUserRoleService;

    @Autowired
    private HaoQianUtils haoQianUtils;

    @Autowired
    private FileStoreTemplateService fileStoreTemplateService;

    @Autowired
    private CscpDocumentFileService cscpDocumentFileService;

    @Autowired
    private ISysConfigService sysConfigService;

    @Autowired
    private  CscpUserService cscpUserService;
    @Autowired
    private  BizService bizService;

    @Autowired
    private ExportToExcelService exportToExcelService;

    /**
     * 获取可用的候选人策略列表
     *
     * @return
     */
    @ApiOperation(value = "获取可用的候选人策略列表")
    @GetMapping("/getCandidateStrategyList")
    public List<CandidateDetail> getCandidateStrategyList() {
        return candidateLoader.getCandidateStrategyList();
    }

    /**
     * 获取可用的多实例策略
     *
     * @return
     * <AUTHOR>
     */
    @ApiOperation(value = "获取可用的多实例策略")
    @GetMapping("/getMultiInstanceStrategyList")
    public List<MultiStrategyDetail> getMultiInstanceStrategyList() {
        return multiStrategyLoader.getMultiInstanceStrategyList();
    }

    /**
     * 获取默认的多实例策略
     *
     * @return
     */
    @ApiOperation(value = "获取默认的多实例策略")
    @GetMapping("/getDefaultMultiInstanceStrategy")
    public MultiStrategyDetail getDefaultMultiInstanceStrategy() {
        return multiStrategyLoader.getDefaultMultiInstanceStrategy();
    }

    /**
     * 获取任务可用的出口路线
     *
     * @param taskId 任务ID
     * @return
     */
    @ApiOperation(value = "根据任务id，获取下一步的列表")
    @GetMapping("/getLineList/{taskId}")
    @ResponseResultVo
    public List<ExecutionLine> getLineList(@ApiParam(required = true, value = "任务ID")
                                           @PathVariable String taskId) {
        return activitiQueryService.getOutLineList(taskId);
    }


    /**
     * 起草的时候选择下一步流程
     *
     * @return
     */
    @ApiOperation(value = "根据processDefinitionKey，获取下一步的列表")
    @GetMapping("/getOutLineListByProcessDefinitionKey/{processDefinitionKey}")
    @ResponseResultVo
    public List<ExecutionLine> getOutLineListByProcessDefinitionKey(@ApiParam(required = true, value = "根据processDefinitionKey")
                                                                    @PathVariable String processDefinitionKey) {
        return activitiQueryService.getOutLineListByProcessDefinitionKey(processDefinitionKey);
    }

    /**
     * 获取当前任务的可用按钮
     *
     * @param taskId 任务ID
     * @return
     */
    @ApiOperation(value = "获取当前任务的可用按钮")
    @GetMapping("/getBtnList/{taskId}")
    public ResponseBO<List<CscpBtnDto>> getBtnList(@ApiParam(required = true, value = "任务ID") @PathVariable String taskId) {
        try {
            List<CscpBtnDto> listRes = activitiQueryService.getCscpBtnByTaskId(taskId);
            return new ResponseBO<>(listRes, SysErrEnum.SUCCESS);
        } catch (Exception ex) {
            ex.printStackTrace();
            return new ResponseBO<>(SysErrEnum.ERROR);
        }
    }


    /**
     * 获取任务信息
     *
     * @param taskId 任务ID
     * @return
     */
    @ApiOperation(value = "获取任务信息")
    @GetMapping("/getTaskById/{taskId}")
    public DetailTaskData getTaskById(@ApiParam(required = true, value = "任务ID") @PathVariable String taskId) {
        return activitiQueryService.getTaskById(taskId);
    }

    /**
     * 获取用户待办任务列表
     *
     * @return
     * @param/
     */
    @ApiOperation(value = "获取用户待办任务列表", hidden = true)
    @GetMapping("/getActivelyTaskList")
    @Deprecated
    public PageResult<DetailTaskData> getActivelyTaskList(
            @ApiParam(required = true, value = "当前页码") @RequestParam int pageNumber,
            @ApiParam(required = true, value = "每页长度") @RequestParam int pageSize,
            @ApiParam(value = "类型ID") @RequestParam(required = false) String typeId,
            @ApiParam(value = "查询条件，流程定义KEY") @RequestParam(required = false) String processDefinitionKey,
            @ApiParam(value = "查询条件，流程名称") @RequestParam(required = false) String processName) {
        String uid = SecurityUtils.getCurrentUserId() + "";
        String assignee = uid;

        return activitiQueryService.getActivelyTaskList(pageNumber, pageSize, assignee, processDefinitionKey, processName, typeId);
    }

    /**
     * 获取用户待办任务列表以及授权的列表
     *
     * @param
     * @return
     */
    @ApiOperation(value = "获取用户待办任务列表")
    @GetMapping("/getActivelyTaskListUnionOutAuth")
    @ResponseResultVo
    public PageResult<TaskVO> getActivelyTaskListUnionOutAuth(
            BasePageForm basePageForm,
            TaskQueryParam taskQueryParam) {

        String uid = SecurityUtils.getCurrentUserId() + "";

        String dateStrNow = DateTimeUtil.getDateTimeString();

        LambdaQueryWrapper<CscpOutAuthorization> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.gt(CscpOutAuthorization::getStarttime, dateStrNow).le(CscpOutAuthorization::getEndtime, dateStrNow);

        List<CscpOutAuthorization> listOutAuth = cscpOutAuthorizationService.list(lambdaQueryWrapper);
        Set<String> setAssign = new HashSet<>(1 + listOutAuth.size());
        setAssign.add(uid);
        Optional.ofNullable(listOutAuth).ifPresent(loa -> loa.forEach(oa -> setAssign.add(oa.getUserid())));
        if (taskQueryParam == null) {
            taskQueryParam = new TaskQueryParam();
        }
        taskQueryParam.setTaskCandidateOrAssignedIn(setAssign.stream().collect(Collectors.toList()));
        String secretary = sysConfigService.getSecretaryConfig();
        if(uid.equals(secretary)){
            PageResult<TaskVO> prs = activitiQueryService.getSecretaryActivelyTaskListUnionOutAuth(basePageForm.getCurrentPage(), basePageForm.getPageSize(), taskQueryParam);
            return prs;
        }else{
            PageResult<TaskVO> prs = activitiQueryService.getActivelyTaskListWithMultiAssignee(basePageForm.getCurrentPage(), basePageForm.getPageSize(), taskQueryParam, false);
            return prs;
        }

        //暂时全部为本人任务
        /* if(prs != null && prs.getData() != null){
            for(TaskVO dt : prs.getData()){
                if(dt.getAssignee()!=null && dt.getAssignee().equals(uid)){
                    dt.setAssignType("0");
                }else{
                    dt.setAssignType("1");
                }
            }
        }*/
    }

    /**
     * 获取领导待办任务列表
     *
     * @param
     * @return
     */
    @ApiOperation(value = "获取领导待办任务列表")
    @GetMapping("/getLiaisonActivelyTaskListUnionOutAuth")
    @ResponseResultVo
    public PageResult<TaskVO> getLiaisonActivelyTaskListUnionOutAuth(
            BasePageForm basePageForm,
            TaskQueryParam taskQueryParam) {
        //查询领导用户ID
        LambdaQueryWrapper<BizLeadershipEntrustment> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.eq(BizLeadershipEntrustment::getLiaisonManId, SecurityUtils.getCurrentUserId());
        List<BizLeadershipEntrustment> bizLeadershipEntrustment = bigLeadershipEntrustmentService.selectListNoAdd(queryWrapper);

        if (bizLeadershipEntrustment != null && bizLeadershipEntrustment.size() > 0) {
            List<String> listStr = new ArrayList<>();
            List<String> directListStr = new ArrayList<>();
            bizLeadershipEntrustment.forEach(item -> {
                String assignee = item.getLeaderId() + "";
                listStr.add(assignee);
                if(item.getMainLiaisonMan() == 1){
                    directListStr.add(assignee);
                }
            });

            taskQueryParam.setTaskCandidateOrAssignedIn(listStr);
            taskQueryParam.setDirectTaskCandidateOrAssignedIn(directListStr);
            PageResult<TaskVO> prs = activitiQueryService.getLiaisonActivelyTaskListUnionOutAuth(basePageForm.getCurrentPage(), basePageForm.getPageSize(), taskQueryParam, false);
            return prs;
        }else {
            PageResult<TaskVO> vo = new PageResult<TaskVO>();
            List<TaskVO> list = new ArrayList<>();
            vo.setData(list);
            vo.setRecordsFiltered(0);
            vo.setRecordsTotal(0);
            return vo;
        }
    }

    @ApiOperation(value = "获取领导待办任务角标")
    @GetMapping("/getLiaisonActivelyTaskListUnionOutAuthSubscript")
    @ResponseResultVo
    public ResultVO<Integer> getLiaisonActivelyTaskListUnionOutAuthSubscript() {
        //查询领导用户ID
        LambdaQueryWrapper<BizLeadershipEntrustment> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.eq(BizLeadershipEntrustment::getLiaisonManId, SecurityUtils.getCurrentUserId());
        List<BizLeadershipEntrustment> bizLeadershipEntrustment = bigLeadershipEntrustmentService.selectListNoAdd(queryWrapper);
        if (bizLeadershipEntrustment != null && bizLeadershipEntrustment.size() > 0) {
            List<String> listStr = new ArrayList<>();
            bizLeadershipEntrustment.forEach(item -> {
                String assignee = item.getLeaderId() + "";
                listStr.add(assignee);
            });
            TaskQueryParam taskQueryParam = new TaskQueryParam();
            taskQueryParam.setTaskCandidateOrAssignedIn(listStr);
            Integer count = activitiQueryService.getLiaisonActivelyTaskListUnionOutAuthSubscript(taskQueryParam, false);
            return ResultVO.success(count);
        }else {
            return ResultVO.success(0);
        }
    }

    @ApiOperation(value = "新增或者修改参谋意见")
    @PostMapping("/createOrUpdateGiveAdvice")
    @ResponseResultVo
    @OperationLog(dBOperation = DBOperation.ADD, message = "新增或者修改参谋意见")
    @LimitSubmit(key = "ActivitiQueryApi.createOrUpdateGiveAdvice:%s",limit = 5,needAllWait = true)
    public ResultVO<Boolean> createOrUpdateGiveAdvice(@Valid @RequestBody BizApprovalGiveAdviceRecordDTO dto) {
        return ResultVO.success(activitiQueryService.createOrUpdateGiveAdvice(dto));
    }

    @ApiOperation(value = "获取参谋意见")
    @PostMapping("/queryGiveAdvice")
    @ResponseResultVo
    public ResultVO<BizApprovalGiveAdviceRecordDTO> queryGiveAdvice(@RequestBody BizApprovalGiveAdviceRecordDTO dto) {
        return ResultVO.success(activitiQueryService.queryGiveAdvice(dto));
    }

    @ApiOperation(value = "获取呈批件领导下参谋意见列表")
    @GetMapping("/queryGiveAdviceListByLeaderIdAndDataId/{formDataId}")
    @ResponseResultVo
    public ResultVO<List<BizApprovalGiveAdviceRecordDTO>> queryGiveAdviceListByLeaderIdAndDataId(@NotNull(message = "呈批件不能为空") @PathVariable("formDataId") String formDataId) {
        return ResultVO.success(activitiQueryService.queryGiveAdviceListByLeaderIdAndDataId(formDataId));
    }



    @ApiOperation(value = "删除参谋意见")
    @PostMapping("/deleteGiveAdvice")
    @ResponseResultVo
    @OperationLog(dBOperation = DBOperation.DELETE, message = "删除参谋意见")
    @LimitSubmit(key = "ActivitiQueryApi.deleteGiveAdvice:%s",limit = 5,needAllWait = true)
    public ResultVO<Boolean> deleteGiveAdvice(@RequestBody BizApprovalGiveAdviceRecordDTO dto) {
        return ResultVO.success(activitiQueryService.deleteGiveAdvice(dto));
    }


    public static PageResult<TaskVO> getUserTasks(BasePageForm basePageForm, TaskQueryParam taskQueryParam,
                                                  ActivitiQueryService activitiQueryService,
                                                  CscpOutAuthorizationService cscpOutAuthorizationService) {

        String uid = SecurityUtils.getCurrentUserId() + "";

        String dateStrNow = DateTimeUtil.getDateTimeString();

        LambdaQueryWrapper<CscpOutAuthorization> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.gt(CscpOutAuthorization::getStarttime, dateStrNow).le(CscpOutAuthorization::getEndtime, dateStrNow);

        List<CscpOutAuthorization> listOutAuth = cscpOutAuthorizationService.list(lambdaQueryWrapper);
        Set<String> setAssign = new HashSet<>(1 + listOutAuth.size());
        setAssign.add(uid);
        Optional.ofNullable(listOutAuth).ifPresent(loa -> loa.forEach(oa -> setAssign.add(oa.getUserid())));
        if (taskQueryParam == null) {
            taskQueryParam = new TaskQueryParam();
        }
        taskQueryParam.setTaskCandidateOrAssignedIn(setAssign.stream().collect(Collectors.toList()));
        PageResult<TaskVO> prs = activitiQueryService.getActivelyTaskListWithMultiAssignee(basePageForm.getCurrentPage(), basePageForm.getPageSize(), taskQueryParam, false);
        return prs;

    }

    /**
     * 获取用户待办任务的角标
     *
     * @param
     * @return
     */
    @ApiOperation(value = "获取用户待办任务的角标")
    @GetMapping("/getCountActivelyTaskListUnionOutAuth")
    @ResponseResultVo
    public Integer getCountActivelyTaskListUnionOutAuth(
            BasePageForm basePageForm,
            TaskQueryParam taskQueryParam) {
        String uid = SecurityUtils.getCurrentUserId() + "";

        String dateStrNow = DateTimeUtil.getDateTimeString();

        LambdaQueryWrapper<CscpOutAuthorization> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.gt(CscpOutAuthorization::getStarttime, dateStrNow).le(CscpOutAuthorization::getEndtime, dateStrNow);

        List<CscpOutAuthorization> listOutAuth = cscpOutAuthorizationService.list(lambdaQueryWrapper);
        Set<String> setAssign = new HashSet<>(1 + listOutAuth.size());
        setAssign.add(uid);
        Optional.ofNullable(listOutAuth).ifPresent(loa -> loa.forEach(oa -> setAssign.add(oa.getUserid())));
        if (taskQueryParam == null) {
            taskQueryParam = new TaskQueryParam();
        }
        taskQueryParam.setTaskCandidateOrAssignedIn(setAssign.stream().collect(Collectors.toList()));

        taskQueryParam.setTaskCandidateOrAssignedIn(setAssign.stream().collect(Collectors.toList()));
        String secretary = sysConfigService.getSecretaryConfig();
        if(uid.equals(secretary)){
            PageResult<TaskVO> prs = activitiQueryService.getSecretaryActivelyTaskListUnionOutAuth(basePageForm.getCurrentPage(), basePageForm.getPageSize(), taskQueryParam);
            return Math.toIntExact(prs.getRecordsTotal());
        }else{
            PageResult<TaskVO> prs = activitiQueryService.getActivelyTaskListWithMultiAssignee(basePageForm.getCurrentPage(), basePageForm.getPageSize(), taskQueryParam, true);
            return Math.toIntExact(prs.getRecordsTotal());
        }


    }

    @ApiOperation(value = "获取用户待办任务的角标,根据 商信ID")
    @GetMapping("/getCountActivelyTaskListByStrId")
    @ResponseResultVo
    public Integer getCountActivelyTaskListByStrId(String strId) {
        if(StringUtils.isEmpty(strId)){
            return 0;
        }
        BasePageForm basePageForm = new BasePageForm();
        TaskQueryParam taskQueryParam = new TaskQueryParam();
        // String uid = SecurityUtils.getCurrentUserId() + "";
        CscpUser user = cscpUserService.selectUserByStrId(strId);
        if( null == user){
            return 0;
        }
        taskQueryParam.setTaskCandidateOrAssignedIn(Arrays.asList(user.getId().toString()));
        PageResult<TaskVO> prs =
                activitiQueryService.getActivelyTaskListWithMultiAssignee(basePageForm.getCurrentPage(),
                        basePageForm.getPageSize(), taskQueryParam, false);
        return Math.toIntExact(prs.getRecordsTotal());
    }

    @ApiOperation(value = "校验呈批件编号，并返回10个最近已用文号")
    @PostMapping("/checkOfficialDocumentNumber")
    @ResponseResultVo
    public ResultVO<String> checkOfficialDocumentNumber(@RequestBody QueryApproveManagementCodeVO vo) {
        List<String> recentCodeList = activitiQueryService.checkOfficialDocumentNumber(vo);
        if (CollectionUtils.isEmpty(recentCodeList)) {
            return ResultVO.success("该编号尚未使用，可使用！");
        }else{
            return new ResultVO(ResultCode.ERROR,recentCodeList);
        }

    }

    @ApiOperation(value = "获取各部门呈批件编号")
    @GetMapping("/getApproveManagementCode")
    @ResponseResultVo
    public ResultVO<String> getApproveManagementCode(String receiveType) {
        return ResultVO.success(activitiQueryService.getApproveManagementCode(receiveType));
    }

    @ApiOperation(value = "获取各部门最大内部编号")
    @PostMapping("/getApproveInnerCode")
    @ResponseResultVo
    public ResultVO<QueryApprovalDTO> getApproveInnerCode(@RequestBody QueryApproveManagementCodeVO vo){
        return ResultVO.success(activitiQueryService.getApproveInnerCode(vo));
    }


    @ApiOperation(value = "获取各部门呈批件最大编号")
    @PostMapping("/getApproveManagementMaxCode")
    @ResponseResultVo
    public ResultVO<QueryApprovalDTO> getApproveManagementMaxCode(@RequestBody QueryApproveManagementCodeVO vo) {
        return ResultVO.success(activitiQueryService.getApproveManagementMaxCode(vo));
    }

    @ApiOperation(value = "修复各部门呈批件编号不连续")
    @GetMapping("/syncApproveManagementCode")
    @ResponseResultVo
    public ResultVO<Void> syncApproveManagementCode() {
        activitiQueryService.syncApproveManagementCode();
        return ResultVO.success();
    }

    /**
     * 获取用户经办过的结流程列表
     *
     * @return
     */
    @ApiOperation(value = "获取用户经办过流程列表")
    @GetMapping("/getUserApproveProcessList")
    @ResponseResultVo
    public PageResult<TaskVO> getUserApproveProcessList(
            BasePageForm basePageForm,
            @ApiParam(value = "查询参数") TaskQueryParam taskQueryParam) {
        String assignee = SecurityUtils.getCurrentUserId() + "";
        if (taskQueryParam == null) {
            taskQueryParam = new TaskQueryParam();
        }
        taskQueryParam.setTaskCandidateOrAssignedInStr(assignee);
        taskQueryParam.setBpmStatus(BpmStatusConstants.PROCESS_RUNNING); // 只查询办理中流程
        return activitiQueryService.getHandleUserProcessList(basePageForm.getCurrentPage(), basePageForm.getPageSize(), taskQueryParam);
    }

    /**
     * 获取待办在办已办三种状态列表的数据
     *
     * @return
     */
    @ApiOperation(value = "获取待办在办已办三种状态列表的数据")
    @GetMapping("/getUserProcessList")
    @ResponseResultVo
    public PageResult<TaskVO> getUserProcessList(
            BasePageForm basePageForm,
            @ApiParam(value = "查询参数") TaskQueryParam taskQueryParam) {
        String assignee = SecurityUtils.getCurrentUserId() + "";
        if (taskQueryParam == null) {
            taskQueryParam = new TaskQueryParam();
        }
        taskQueryParam.setTaskCandidateOrAssignedInStr(assignee);
        return activitiQueryService.getUserProcessList(basePageForm.getCurrentPage(), basePageForm.getPageSize(), taskQueryParam);
    }
    /**
     * 获取用户已办结过的流程列表，按照单位过滤
     *
     * @return
     */
    @ApiOperation(value = "获取用户已办结过的流程列表，按照单位过滤")
    @GetMapping("/getUserEndProcessList")
    @ResponseResultVo
    @PermissionData(isWebComponent = true, alias = "content")
    public PageResult<TaskVO> getUserEndProcessList(
            BasePageForm basePageForm,
            @ApiParam(value = "查询参数") TaskQueryParam taskQueryParam) {
        DataFilterMetaData dataFilterMetaData = DataFilterThreadLocal.get();
        if (dataFilterMetaData == null){
            String assignee = SecurityUtils.getCurrentUserId() + "";
            taskQueryParam.setTaskCandidateOrAssignedInStr(assignee);
        }

        taskQueryParam.setBpmStatus(BpmStatusConstants.PROCESS_END);
        return activitiQueryService.getUserApproveProcessList(basePageForm.getCurrentPage(), basePageForm.getPageSize(), taskQueryParam);
    }


    @ApiOperation(value = "已办件动态")
    @PostMapping("/queryDoneApprovalList")
    @ResponseResultVo
    public PageResult<TaskContentVO> queryDoneApprovalList(@ApiParam(value = "查询参数") TaskQueryParam taskQueryParam, BasePageForm basePageForm) {
        return activitiQueryService.queryDoneApprovalList(taskQueryParam, basePageForm.getCurrentPage(), basePageForm.getPageSize());
    }


    /**
     * 联络员获取领导经办过的结流程列表
     *
     * @return
     */
    @ApiOperation(value = "联络员获取领导经办过的结流程列表")
    @GetMapping("/getLiaisonUserApproveProcessList")
    @ResponseResultVo
    @PermissionData(isWebComponent = true, alias = "content")
    public PageResult<TaskVO> getLiaisonUserApproveProcessList(
            BasePageForm basePageForm,
            @ApiParam(value = "查询参数") TaskQueryParam taskQueryParam) {
        //查询当前登录人的领导ID
        LambdaQueryWrapper<BizLeadershipEntrustment> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.eq(BizLeadershipEntrustment::getLiaisonManId, SecurityUtils.getCurrentUserId());
        List<BizLeadershipEntrustment> bizLeadershipEntrustment = bigLeadershipEntrustmentService.selectListNoAdd(queryWrapper);
        if (bizLeadershipEntrustment != null && bizLeadershipEntrustment.size() > 0) {
            List<String> listStr = new ArrayList<>();
            bizLeadershipEntrustment.forEach(item -> {
                String assignee = item.getLeaderId() + "";
                listStr.add(assignee);
            });
            taskQueryParam.setTaskCandidateOrAssignedIn(listStr);
            taskQueryParam.setBpmStatus(BpmStatusConstants.PROCESS_RUNNING);
            PageResult<TaskVO> list = activitiQueryService.getUserApproveProcessList(basePageForm.getCurrentPage(), basePageForm.getPageSize(), taskQueryParam);

            List<String> processInstanceIdList = list.getData().stream().map(TaskVO::getProcessInstanceId).collect(Collectors.toList());
            List<CscpAuditContent> cscpAuditContentList = new ArrayList<>();
            if(processInstanceIdList!=null && processInstanceIdList.size()>0){
                cscpAuditContentList = cscpAuditContentService.getAuditContentListByInstanceIdList(processInstanceIdList);
            }
            List<CscpAuditContent> finalCscpAuditContentList = cscpAuditContentList;

            list.getData().forEach(item->{
                List<CscpAuditContent> auditList = finalCscpAuditContentList.stream().filter(q->q.getProcInstId().equals(item.getProcessInstanceId()))
                        .sorted(Comparator.comparing(CscpAuditContent::getCreateTime).reversed()).collect(Collectors.toList());
                List<String> leaderList = new ArrayList<>();
                listStr.forEach(str->{
                    List<CscpAuditContent> strList = auditList.stream().filter(q->q.getAuditorId().equals(str)).collect(Collectors.toList());
                    if(strList!=null && strList.size()>0){
                        if (com.ctsi.hndx.utils.StringUtils.isNotEmpty(strList.get(0).getAuditContent())){
                            leaderList.add(strList.get(0).getAuditorName()+"批示："+strList.get(0).getAuditContent()+"。");
                        }else {
                            leaderList.add(strList.get(0).getAuditorName()+"已办理完成。");
                        }

                    }
                });
                if(leaderList.size()>0){
                    item.setLeaderList(leaderList);
                }
            });
            return list;
        } else {
            PageResult<TaskVO> vo = new PageResult<TaskVO>();
            List<TaskVO> list = new ArrayList<>();
            vo.setData(list);
            vo.setRecordsFiltered(0);
            vo.setRecordsTotal(0);
            return vo;
        }
    }

    /**
     *联络员获取领导用户已办结过的流程列表
     *
     * @return
     */
    @ApiOperation(value = "联络员获取领导用户已办结过的流程列表")
    @GetMapping("/getLiaisonUserEndProcessList")
    @ResponseResultVo
    @PermissionData(isWebComponent = true, alias = "content")
    public PageResult<TaskVO> getLiaisonUserEndProcessList(
            BasePageForm basePageForm,
            @ApiParam(value = "查询参数") TaskQueryParam taskQueryParam) {
        //查询当前登录人的领导ID
        LambdaQueryWrapper<BizLeadershipEntrustment> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.eq(BizLeadershipEntrustment:: getLiaisonManId, SecurityUtils.getCurrentUserId());
        List<BizLeadershipEntrustment> bizLeadershipEntrustment = bigLeadershipEntrustmentService.selectListNoAdd(queryWrapper);
        if(bizLeadershipEntrustment!=null && bizLeadershipEntrustment.size()>0){
            List<String> listStr = new ArrayList<>();
            List<String> directListStr = new ArrayList<>();
            bizLeadershipEntrustment.forEach(item->{
                String assignee = item.getLeaderId() + "";
                listStr.add(assignee);
                if(item.getMainLiaisonMan() == 1){
                    directListStr.add(assignee);
                }
            });
            taskQueryParam.setTaskCandidateOrAssignedIn(listStr);
            taskQueryParam.setDirectTaskCandidateOrAssignedIn(directListStr);
            //taskQueryParam.setBpmStatus(BpmStatusConstants.PROCESS_END);
            taskQueryParam.setBpmStatusList(Arrays.asList(BpmStatusConstants.PROCESS_RUNNING, BpmStatusConstants.PROCESS_END));
//            taskQueryParam.setCompanyId(SecurityUtils.getCurrentCscpUserDetail().getCompanyId());
            PageResult<TaskVO> list = activitiQueryService.getLiaisonUserEndProcessList(basePageForm.getCurrentPage(), basePageForm.getPageSize(), taskQueryParam);

            List<String> processInstanceIdList = list.getData().stream().map(TaskVO::getProcessInstanceId).collect(Collectors.toList());
            List<CscpAuditContent> cscpAuditContentList = new ArrayList<>();
            if(processInstanceIdList!=null && processInstanceIdList.size()>0){
                cscpAuditContentList = cscpAuditContentService.getAuditContentListByInstanceIdList(processInstanceIdList);
            }
            List<CscpAuditContent> finalCscpAuditContentList = cscpAuditContentList;

            list.getData().forEach(item->{
                List<CscpAuditContent> auditList = finalCscpAuditContentList.stream().filter(q->q.getProcInstId().equals(item.getProcessInstanceId()))
                        .sorted(Comparator.comparing(CscpAuditContent::getCreateTime).reversed()).collect(Collectors.toList());
                List<String> leaderList = new ArrayList<>();
                listStr.forEach(str->{
                    List<CscpAuditContent> strList = auditList.stream().filter(q->q.getAuditorId().equals(str)).collect(Collectors.toList());
                    if(strList!=null && strList.size()>0) {
                        if (com.ctsi.hndx.utils.StringUtils.isNotEmpty(strList.get(0).getAuditContent())) {
                            leaderList.add(strList.get(0).getAuditContent());
                        } else {
                            leaderList.add("已办理完成。");
                        }
                       // item.setHandleTime(strList.get(0).getCreateTime());
                    }
                });
                if(leaderList.size()>0){
                    item.setLeaderList(leaderList);
                }
                // ## 增加章子信息
                if( listStr.size() > 0){
                    String stampInfo = bizService.findStampInfo(item.getFormDataId(),Long.valueOf(listStr.get(0)),item.getHandleTime());
                    item.setLeaderStampInfo(stampInfo);
                }
            });


            return list;
        }else {
            PageResult<TaskVO> vo = new PageResult<TaskVO>();
            List<TaskVO>  list = new ArrayList<>();
            vo.setData(list);
            vo.setRecordsFiltered(0);
            vo.setRecordsTotal(0);
            return vo;
        }
    }

    /**
     *一键办结的流程列表
     *
     * @return
     */
    @ApiOperation(value = "一键办结的流程列表")
    @GetMapping("/getOneClickFinishProcessList")
    @ResponseResultVo
    public PageResult<TaskVO> getOneClickFinishProcessList(
            BasePageForm basePageForm,
            @ApiParam(value = "查询参数") TaskQueryParam taskQueryParam) {
        taskQueryParam.setBpmStatus(BpmStatusConstants.PROCESS_END);
        taskQueryParam.setCompanyId(SecurityUtils.getCurrentCscpUserDetail().getCompanyId());

        PageResult<TaskVO> list = activitiQueryService.getOneClickFinishProcessList(basePageForm.getCurrentPage(), basePageForm.getPageSize(), taskQueryParam);
        if (list.getData()!=null && list.getData().size()>0){
            //查询当前登录人的领导ID
            LambdaQueryWrapper<BizLeadershipEntrustment> queryWrapper = new LambdaQueryWrapper();
            queryWrapper.eq(BizLeadershipEntrustment::getLiaisonManId, SecurityUtils.getCurrentUserId());
            List<BizLeadershipEntrustment> bizLeadershipEntrustment = bigLeadershipEntrustmentService.selectListNoAdd(queryWrapper);
            List<String> listStr = new ArrayList<>();
            if (bizLeadershipEntrustment != null && bizLeadershipEntrustment.size() > 0) {
                bizLeadershipEntrustment.forEach(item -> {
                    String assignee = item.getLeaderId() + "";
                    listStr.add(assignee);
                });
            }

            List<String> processInstanceIdList = list.getData().stream().map(TaskVO::getProcessInstanceId).collect(Collectors.toList());
            List<CscpAuditContent> cscpAuditContentList = new ArrayList<>();
            if(processInstanceIdList!=null && processInstanceIdList.size()>0){
                cscpAuditContentList = cscpAuditContentService.getAuditContentListByInstanceIdList(processInstanceIdList);
            }
            List<CscpAuditContent> finalCscpAuditContentList = cscpAuditContentList;
            list.getData().forEach(item -> {
                List<CscpAuditContent> auditList = finalCscpAuditContentList.stream().filter(q->q.getProcInstId().equals(item.getProcessInstanceId()))
                        .sorted(Comparator.comparing(CscpAuditContent::getCreateTime).reversed()).collect(Collectors.toList());
                List<String> leaderList = new ArrayList<>();
                listStr.forEach(str -> {
                    List<CscpAuditContent> strList = auditList.stream().filter(q -> q.getAuditorId().equals(str)).collect(Collectors.toList());
                    if (strList != null && strList.size() > 0) {
                        leaderList.add(strList.get(0).getAuditorName() + "已批示");
                    }
                });
                if (leaderList.size() > 0) {
                    item.setLeaderList(leaderList);
                }
            });
            return list;
        }else {
            PageResult<TaskVO> vo = new PageResult<TaskVO>();
            List<TaskVO>  listTask = new ArrayList<>();
            vo.setData(listTask);
            vo.setRecordsFiltered(0);
            vo.setRecordsTotal(0);
            return vo;
        }

    }

    /**
     * 获取单位经办过的结流程列表
     *
     * @return
     */
    @ApiOperation(value = "获取单位经办过的结流程列表")
    @GetMapping("/getCompanyIdApproveProcessList")
    @ResponseResultVo
    @PermissionData(isWebComponent = true, alias = "content")
    public PageResult<TaskVO> getCompanyIdApproveProcessList(
            BasePageForm basePageForm,
            @ApiParam(value = "查询参数") TaskQueryParam taskQueryParam) {
        taskQueryParam.setBpmStatus(BpmStatusConstants.PROCESS_RUNNING);

        List<Long> companyIdList = new ArrayList<>();
        List<CscpDistributeBaseUserDTO> cscpDistributeBaseUserList = cscpUserRoleService.queryCscpUserAllByRoleName("流程历史单位查询管理员", new ArrayList<Long>(), companyIdList);
        if (cscpDistributeBaseUserList != null && cscpDistributeBaseUserList.size() > 0) {
            List<String> userIdArry = new ArrayList<>();
            cscpDistributeBaseUserList.forEach(item -> {
                userIdArry.add(item.getUserId().toString());
            });
            if (userIdArry.contains(String.valueOf(SecurityUtils.getCurrentUserId()))) {
                taskQueryParam.setCompanyId(SecurityUtils.getCurrentCscpUserDetail().getCompanyId());
            } else {
                String assignee = SecurityUtils.getCurrentUserId() + "";
                taskQueryParam.setCreateId(assignee);
            }
        } else {
            String assignee = SecurityUtils.getCurrentUserId() + "";
            taskQueryParam.setCreateId(assignee);
        }

        PageResult<TaskVO> list = activitiQueryService.getUserApproveProcessList(basePageForm.getCurrentPage(), basePageForm.getPageSize(), taskQueryParam);
        return list;
    }

    /**
     *获取单位已办结过的流程列表
     *
     * @return
     */
    @ApiOperation(value = "获取单位已办结过的流程列表")
    @GetMapping("/getCompanyIdEndProcessList")
    @ResponseResultVo
    @PermissionData(isWebComponent = true, alias = "content")
    public PageResult<TaskVO> getCompanyIdEndProcessList(
            BasePageForm basePageForm,
            @ApiParam(value = "查询参数") TaskQueryParam taskQueryParam) {
        taskQueryParam.setBpmStatus(BpmStatusConstants.PROCESS_END);

        List<Long> companyIdList = new ArrayList<>();
        List<CscpDistributeBaseUserDTO> cscpDistributeBaseUserList = cscpUserRoleService.queryCscpUserAllByRoleName("流程历史单位查询管理员", new ArrayList<Long>(), companyIdList);
        if (cscpDistributeBaseUserList != null && cscpDistributeBaseUserList.size() > 0) {
            List<String> userIdArry = new ArrayList<>();
            cscpDistributeBaseUserList.forEach(item -> {
                userIdArry.add(item.getUserId().toString());
            });
            if (userIdArry.contains(String.valueOf(SecurityUtils.getCurrentUserId()))) {
                taskQueryParam.setCompanyId(SecurityUtils.getCurrentCscpUserDetail().getCompanyId());
            } else {
                String assignee = SecurityUtils.getCurrentUserId() + "";
                taskQueryParam.setCreateId(assignee);
            }
        } else {
            String assignee = SecurityUtils.getCurrentUserId() + "";
            taskQueryParam.setCreateId(assignee);
        }

        PageResult<TaskVO> list = activitiQueryService.getUserApproveProcessList(basePageForm.getCurrentPage(), basePageForm.getPageSize(), taskQueryParam);
        return list;
    }

    /**
     * 获取当前用户起草的或者本单位的流程监控列表
     *
     * @return
     */
    @ApiOperation(value = "获取当前用户起草的或者本单位的流程监控列表")
    @GetMapping("/getUserMonitorProcessList")
    @ResponseResultVo
    @PermissionData(isWebComponent = true, alias = "base")
    public PageResult<TaskVO> getUserMonitorProcessList(
            BasePageForm basePageForm,
            @ApiParam(value = "查询参数") TaskQueryParam taskQueryParam) {
        if (DataFilterThreadLocal.get() == null) {
            String assignee = SecurityUtils.getCurrentUserId() + "";
            taskQueryParam.setTaskCandidateOrAssignedInStr(assignee);
        }
        return cscpProcBaseService.getProcessMonitorList(basePageForm, taskQueryParam);
    }

    /**
     * 查询本单位的流程监控
     *
     * @return
     */
    @ApiOperation(value = "查询本单位的流程监控")
    @GetMapping("/getCompanyMonitorProcessList")
    @ResponseResultVo
    @PermissionData(isWebComponent = true, alias = "base")
    public PageResult<TaskVO> getCompanyMonitorProcessList(
            BasePageForm basePageForm,
            @ApiParam(value = "查询参数") TaskQueryParam taskQueryParam) {
        DataFilterMetaData dataFilterMetaData = DataFilterThreadLocal.get();
        if (dataFilterMetaData == null){
            Long companyId = SecurityUtils.getCurrentCompanyId();
            taskQueryParam.setCompanyId(companyId);
        }
        return cscpProcBaseService.getCompanyMonitorProcessList(basePageForm, taskQueryParam);
    }


    /**
     * 获取本单位的在办流程列表
     *
     * @return
     */
    @ApiOperation(value = "获取本单位的在办流程列表")
    @GetMapping("/getCompanyApproveProcessList")
    @ResponseResultVo
    @PermissionData(isWebComponent = true, alias = "content")
    public PageResult<TaskVO> getCompanyApproveProcessList(
            BasePageForm basePageForm,
            @ApiParam(value = "查询参数") TaskQueryParam taskQueryParam) {
        if (taskQueryParam == null) {
            taskQueryParam = new TaskQueryParam();
        }
        DataFilterMetaData dataFilterMetaData = DataFilterThreadLocal.get();
        if (dataFilterMetaData == null){
            Long companyId = SecurityUtils.getCurrentCompanyId();
            taskQueryParam.setCompanyId(companyId);
        }
        taskQueryParam.setBpmStatus(BpmStatusConstants.PROCESS_RUNNING);
        return activitiQueryService.getUserApproveProcessList(basePageForm.getCurrentPage(), basePageForm.getPageSize(), taskQueryParam);
    }

    /**
     *联获取本单位已办结过的流程列表
     *
     * @return
     */
    @ApiOperation(value = "联获取本单位已办结过的流程列表")
    @GetMapping("/getCompanyEndProcessList")
    @ResponseResultVo
    @PermissionData(isWebComponent = true, alias = "content")
    public PageResult<TaskVO> getCompanyEndProcessList(
            BasePageForm basePageForm,
            @ApiParam(value = "查询参数") TaskQueryParam taskQueryParam) {
        DataFilterMetaData dataFilterMetaData = DataFilterThreadLocal.get();
        if (dataFilterMetaData == null) {
            Long companyId = SecurityUtils.getCurrentCompanyId();
            taskQueryParam.setCompanyId(companyId);
        }
        taskQueryParam.setBpmStatus(BpmStatusConstants.PROCESS_END);
        return activitiQueryService.getUserApproveProcessList(basePageForm.getCurrentPage(), basePageForm.getPageSize(), taskQueryParam);
    }

    /**
     * 根据流程实例ID，获取流程对象
     *
     * @param processInstanceId 流程实例ID
     * @return
     */
    @ApiOperation(value = "根据流程实例ID，获取流程对象")
    @GetMapping("/getByProcessInstanceId/{processInstanceId}")
    public CscpProc getByProcessInstanceId(@ApiParam(required = true, value = "流程实例ID") @PathVariable String processInstanceId) {
        return activitiQueryService.getByProcessInstanceId(processInstanceId);
    }

    /**
     * 根据流程实例ID，获取流程实例的流程运行图
     *
     * @param processInstanceId 流程实例ID
     * @return
     */
    @ApiOperation(value = "根据流程实例ID，获取流程实例的流程运行图")
    @GetMapping("/getRouteNodes/{processInstanceId}")
    public List<TaskData> getRouteNodes(@ApiParam(required = true, value = "流程实例ID") @PathVariable String processInstanceId) {
        return activitiQueryService.getProcessInstanceDiagram(processInstanceId);
    }


    /**
     * 作废任务列表
     *
     * @return
     */
    @ApiOperation(value = "获取作废任务列表")
    @PostMapping("/getCancellationProcessList")
    public ResponseBO<PageActiviti<PageData>> getCancellationProcessList(@RequestBody @ApiParam(required = true) PageForm<Cscp> form) {
        try {
            String uid = SecurityUtils.getCurrentUserId() + "";

            form.getT().setAssignee(uid);
            PageActiviti<PageData> cancellationProcessList = cscpActivitiService.getCancellationProcessList(form);
            return new ResponseBO<PageActiviti<PageData>>(cancellationProcessList, SysErrEnum.SUCCESS);

        } catch (Exception ex) {
            ex.printStackTrace();
            return new ResponseBO<>(SysErrEnum.ERROR);
        }
    }


    /**
     * 传阅任务列表
     *
     * @return
     */
    @ApiOperation(value = "获取传阅任务列表")
    @PostMapping("/getViewTaskList/{flag}")
    public ResponseBO<PageActiviti<PageData>> getViewTaskList(@RequestBody @ApiParam(required = true) PageForm<Cscp> form,
                                                              @ApiParam(required = true, value = "状态") @PathVariable String flag) {
        try {
            String uid = SecurityUtils.getCurrentUserId() + "";

            form.getT().setAssignee(uid);
            form.getT().setFlag(flag);
            PageActiviti<PageData> pageActiviti = cscpActivitiService.getViewTaskList(form);
            return new ResponseBO<PageActiviti<PageData>>(pageActiviti, SysErrEnum.SUCCESS);

        } catch (Exception ex) {
            ex.printStackTrace();
            return new ResponseBO<>(SysErrEnum.ERROR);
        }
    }

    /**
     * 获取运行中的所有任务列表（任务调度）
     *
     * @return
     * @ApiParam(required = true, value = "当前页码") @RequestParam int pageNumber,
     * @ApiParam(required = true, value = "每页长度") @RequestParam int pageSize,
     * @ApiParam(required = true, value = "用户ID") @PathVariable String assignee,
     * @ApiParam(value = "查询条件，流程定义KEY") @RequestParam(required = false) String processDefinitionKey,
     * @ApiParam(value = "查询条件，流程名称") @RequestParam(required = false) String processName
     */
    @ApiOperation(value = "获取运行中的所有任务列表（任务调度）", hidden = true)
    @PostMapping("/getAllTaskList")
    @Deprecated
    public ResponseBO<PageActiviti<PageData>> getAllTaskList(PageForm<Cscp> form) {
        try {
            PageActiviti<PageData> pageActiviti = cscpActivitiService.getAllTaskList(form);
            return new ResponseBO<PageActiviti<PageData>>(pageActiviti, SysErrEnum.SUCCESS);
        } catch (Exception ex) {
            ex.printStackTrace();
            return new ResponseBO<>(SysErrEnum.ERROR);
        }
    }


    @ApiOperation(value = "获取超时任务信息")
    @PostMapping("/getOverTimeTask")
    public ResponseBO<PageActiviti<PageData>> getOverTimeTask(@RequestBody @ApiParam(required = true) PageForm<Cscp> form) {
        try {
            PageActiviti<PageData> pageActiviti = cscpActivitiService.getOverTimeTask(form);
            return new ResponseBO<PageActiviti<PageData>>(pageActiviti, SysErrEnum.SUCCESS);
        } catch (Exception ex) {
            ex.printStackTrace();
            return new ResponseBO<>(SysErrEnum.ERROR);
        }
    }

    @ApiOperation(value = "首页统计数量")
    @PostMapping("/homePageNum")
    public ResponseBO<PageData> homePageNum() {
        try {
            String assignee = SecurityUtils.getCurrentUserId() + "";

            // PageResult<ProcessData> his = activitiQueryService.getEndedProcessList(0, 1, assignee, null, null);
            PageResult<DetailTaskData> todo = activitiQueryService.getActivelyTaskList(0, 10, assignee, null, null, null);

            PageForm<Cscp> form = new PageForm<Cscp>();
            Cscp cscp = new Cscp();
            form.setT(cscp);
            form.getT().setAssignee(assignee);
            form.getT().setFlag("0");
            PageActiviti<PageData> readed = cscpActivitiService.getViewTaskList(form);

            form.getT().setFlag("1");
            PageActiviti<PageData> unread = cscpActivitiService.getViewTaskList(form);

            PageData pd = new PageData();
            // pd.put("his", his.getRecordsTotal());
            pd.put("todo", todo.getRecordsTotal());
            pd.put("readed", readed.getTotal());
            pd.put("unread", unread.getTotal());

            return new ResponseBO<PageData>(pd, SysErrEnum.SUCCESS);

        } catch (Exception ex) {
            ex.printStackTrace();
            return new ResponseBO<>(SysErrEnum.ERROR);
        }
    }

    @ApiOperation(value = "流程下任务列表")
    @GetMapping("/getTaskByProcess")
    public ResponseBO<List<PageData>> getTaskByProcess(String processInstanceId, String taskId, String nodeKey) {
        try {
            List<PageData> list = activitiQueryService.getTaskByProcess(processInstanceId, taskId, nodeKey);
            return new ResponseBO<>(list, SysErrEnum.SUCCESS);
        } catch (Exception ex) {
            ex.printStackTrace();
            return new ResponseBO<>(SysErrEnum.ERROR);
        }
    }

    @ApiOperation(value = "流程下任务列表，减签人员查询接口")
    @GetMapping("/getProcessHandler")
    public ResponseBO<List<ProcessHandlerDTO>> getProcessHandler(String processInstanceId, String nodeKey, Integer type) {
        try {
            List<ProcessHandlerDTO> list = activitiQueryService.getProcessHandler(processInstanceId, nodeKey,type);
            return new ResponseBO<>(list, SysErrEnum.SUCCESS);
        } catch (Exception ex) {
            ex.printStackTrace();
            return new ResponseBO<>(SysErrEnum.ERROR);
        }
    }

    @ApiOperation(value = "查询流程节点是否显示减签按钮接口")
    @GetMapping("/getVerifySign")
    public ResultVO<QueryProcessSignDTO> getVerifySign(String processDefinitionId, String processInstanceId) {
        return ResultVO.success(activitiQueryService.getVerifySign(processDefinitionId, processInstanceId));
    }

    //    @GetMapping("/getTaskNameById")
//    public ResponseBO<Map<String, Object>> getMainFormData(String formDataId, String formId) {
    @ApiOperation(value = "通过业务id获取任务节点名称")
    @GetMapping("/getTaskNameById")
    public ResponseBO getTaskNameById(@ApiParam(value = "业务id") @RequestParam(required = false) String id) {
        String taskName = "";
        try {
            CscpProcBase cscpProcBase= cscpProcBaseService.getProcBaseByFormDataId(id);
            if(null!=cscpProcBase.getProcInstId()&& StringUtils.isNotEmpty(cscpProcBase.getProcInstId())) {
                //通过流程实例获取任务节点名称
                taskName = activitiQueryService.getTaskNameByProcessInstanceId(cscpProcBase.getProcInstId());
            }
            return new ResponseBO<>(taskName, SysErrEnum.SUCCESS);
        } catch (Exception ex) {
            ex.printStackTrace();
            return new ResponseBO<>(SysErrEnum.ERROR);
        }
    }

    @ApiOperation(value = "通过业务id更新或者添加好签版本文件")
    @GetMapping("/addOrUpdateHaoqianByFormDataId")
    public ResponseBO addOrUpdateHaoqianByFormDataId(@ApiParam(value = "业务id") @RequestParam(required = false) String id) {
        try {
            CscpHaoqianVersionDTO cscpHaoqianVersionDTO = new CscpHaoqianVersionDTO();
            cscpHaoqianVersionDTO.setFormDataId(Long.parseLong(id));
            //判断本次提交有没有好签版本
            cscpHaoqianVersionDTO = cscpHaoqianVersionService.queryOneNewsInfo(cscpHaoqianVersionDTO);
            CscpProcBase cscpProcBase = cscpProcBaseService.getProcBaseByFormDataId(id);
            if(null!=cscpProcBase){
                if(null==cscpHaoqianVersionDTO) {
                    cscpHaoqianVersionDTO = new CscpHaoqianVersionDTO();
                    cscpHaoqianVersionDTO.setFormDataId(Long.parseLong(id));
                    cscpHaoqianVersionDTO.setVersionNumber(1);
                    cscpHaoqianVersionDTO.setFormDataId(Long.parseLong(id));
                    cscpHaoqianVersionDTO.setTaskLink("版本1");
                    if(null!=cscpProcBase){
                        cscpHaoqianVersionDTO.setProcTypeName(cscpProcBase.getProcTypeName());
                    }
                }
                cFormDataService.copyAddPdfToHaoqianVersion(cscpHaoqianVersionDTO);
                return new ResponseBO<>(SysErrEnum.SUCCESS);
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            return new ResponseBO<>(SysErrEnum.ERROR);
        }
        return new ResponseBO<>(SysErrEnum.ERROR);
    }

    @ApiOperation(value = "通过业务id更新或者添加好签版本文件，处理单合并到正文")
    @GetMapping("/addOrUpdateHaoqianByFormDataIdByMerge")
    public ResponseBO addOrUpdateHaoqianByFormDataIdByMerge(@ApiParam(value = "业务id") @RequestParam(required = false) String id) {
        try {
            CscpHaoqianVersionDTO cscpHaoqianVersionDTO = new CscpHaoqianVersionDTO();
            cscpHaoqianVersionDTO.setFormDataId(Long.parseLong(id));
            //判断本次提交有没有好签版本
            cscpHaoqianVersionDTO = cscpHaoqianVersionService.queryOneNewsInfo(cscpHaoqianVersionDTO);
            CscpProcBase cscpProcBase = cscpProcBaseService.getProcBaseByFormDataId(id);
            if(null!=cscpProcBase){
                if(null==cscpHaoqianVersionDTO) {
                    cscpHaoqianVersionDTO = new CscpHaoqianVersionDTO();
                    cscpHaoqianVersionDTO.setFormDataId(Long.parseLong(id));
                    cscpHaoqianVersionDTO.setVersionNumber(1);
                    cscpHaoqianVersionDTO.setFormDataId(Long.parseLong(id));
                    cscpHaoqianVersionDTO.setTaskLink("版本1");
                    if(null!=cscpProcBase){
                        cscpHaoqianVersionDTO.setProcTypeName(cscpProcBase.getProcTypeName());
                    }
                }
                cFormDataService.copyAddPdfToHaoqianVersion(cscpHaoqianVersionDTO, "4");
                return new ResponseBO<>(SysErrEnum.SUCCESS);
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            return new ResponseBO<>(SysErrEnum.ERROR);
        }
        return new ResponseBO<>(SysErrEnum.ERROR);
    }

    @ApiOperation(value = "通过业务id下载最新的好签版本文件")
    @GetMapping("/downLoadLatestHaoqianFile")
    public ResultVO<String> downLoadLatestHaoqianFile(@ApiParam(value = "业务id") @RequestParam(required = false) String id) {
        try {
            CscpHaoqianVersionDTO cscpHaoqianVersionDTO = new CscpHaoqianVersionDTO();
            cscpHaoqianVersionDTO.setFormDataId(Long.parseLong(id));
            // 判断本次提交有没有好签版本
            cscpHaoqianVersionDTO = cscpHaoqianVersionService.queryOneNewsInfo(cscpHaoqianVersionDTO);
            if (null == cscpHaoqianVersionDTO) {
                return ResultVO.error("通过业务id下载最新的好签版本文件失败，没有好签版本");
            }
            String downloadFileUrl = "";
            if("0".equals(cscpHaoqianVersionDTO.getIsUpdate())){
                log.info("好签版本SignId=" + cscpHaoqianVersionDTO.getSignId());
                downloadFileUrl = haoQianUtils.downloadFileUrl(cscpHaoqianVersionDTO.getSignId());
            }else {
                MultipartFile file = null;
                try {
                    //同步转换获取pdf文件字节流
                    file = cscpDocumentFileService.getDocumentMultipartFileSync(Long.parseLong(id), "2");
                }catch (Exception e){
                    log.info("更新好签pdf调用wps转换失败，业务id为:"+id+";异常为："+e.toString());
                }
                String signid = null;
                //上传pdf文件到好签服务器
                signid = haoQianUtils.uploadFileToHaoQian(file, null, null, null);
                downloadFileUrl = haoQianUtils.downloadFileUrl(signid);
            }

            log.info("好签版本downloadFileUrl=" + downloadFileUrl);
            byte[] data = WpsUtil.httpDownLoad(downloadFileUrl, null);
            if (null == data || data.length == 0) {
                return ResultVO.error("通过业务id下载最新的好签版本文件失败，下载好签版本失败");
            }
            String ossFileUrl = fileStoreTemplateService.createFileUrl(FileBasePathName.OFFICE, ".pdf");
            if (fileStoreTemplateService.uploadFile(ossFileUrl, data)) {
                log.info("好签版本上传服务器路径=" + downloadFileUrl);
                return ResultVO.success(ossFileUrl);
            } else {
                return ResultVO.error("通过业务id下载最新的好签版本文件失败，好签版本文件上传失败");
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            return ResultVO.error("通过业务id下载最新的好签版本文件失败，接口异常。");
        }
    }



    /**
     * 领导批示导出
     */
    @GetMapping("/fileExport")
    @ApiOperation(value = "导出", notes = "传入参数")
    public ResultVO fileExport(HttpServletResponse response, @ApiParam(value = "查询参数") TaskQueryParam taskQueryParam) {
        BasePageForm basePageForm = new BasePageForm(1,10000);

        PageResult<TaskVO> liaisonUserEndProcessList = this.getLiaisonUserEndProcessList(basePageForm,
                taskQueryParam);
        List<TaskVO> data = liaisonUserEndProcessList.getData();
        data.forEach(i -> i.setContent(i.getLeaderList() ==  null || i.getLeaderList().isEmpty() ? null  :
                i.getLeaderList().get(0)));
        List<TaskExportVO> copy = ListCopyUtil.copy(data , TaskExportVO.class);
        Boolean bool = null;
        try {
            bool = exportToExcelService.exportToExcel(copy, TaskExportVO.class, response);
        } catch (IOException e) {
            e.printStackTrace();
        }
        return ResultVO.success();
    }


}
