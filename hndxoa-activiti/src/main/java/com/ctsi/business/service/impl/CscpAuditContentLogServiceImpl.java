package com.ctsi.business.service.impl;

import com.ctsi.business.domain.CscpAuditContentLog;
import com.ctsi.business.repository.CscpAuditContentLogMapper;
import com.ctsi.business.service.ICscpAuditContentLogService;
import com.ctsi.hndx.common.SysBaseServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 审核意见日志表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-13
 */
@Service
public class CscpAuditContentLogServiceImpl extends SysBaseServiceImpl<CscpAuditContentLogMapper, CscpAuditContentLog> implements ICscpAuditContentLogService {
    @Autowired
    private CscpAuditContentLogMapper cscpAuditContentLogMapper;
}
