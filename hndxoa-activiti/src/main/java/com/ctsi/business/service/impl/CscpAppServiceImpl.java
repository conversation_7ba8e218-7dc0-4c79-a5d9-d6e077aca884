package com.ctsi.business.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ctsi.common.bo.PageActiviti;
import com.ctsi.common.bo.PageActivitiUtil;
import com.ctsi.common.bo.PageForm;
import com.ctsi.business.domain.CscpProcType;
import com.ctsi.common.exception.CustomException;
import com.ctsi.business.repository.CscpProcTypeRepository;
import com.ctsi.hndx.utils.MybatisQueryUtil;
import com.ctsi.ssdc.model.PageResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.lang.String;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.ctsi.business.domain.CscpApp;
import com.ctsi.business.service.CscpAppService;
import com.ctsi.business.repository.CscpAppRepository;

/**
 * Service Implementation for managing CscpApp.
 *
 * <AUTHOR>
 *
 */
@Service
public class CscpAppServiceImpl extends ServiceImpl<CscpAppRepository, CscpApp> implements CscpAppService   {

    @Autowired
    private CscpProcTypeRepository cscpProcTypeRepository;

    @Autowired
	private CscpAppRepository cscpAppRepository;

	@Override
	public IPage<CscpApp> all(PageForm<CscpApp> form) {
		Page<CscpApp> page = new Page<>(form.getCurrentPage(),form.getPageSize());
		CscpApp cscpProcType = form.getT();
		QueryWrapper<CscpApp> cscpAppQueryWrapper = MybatisQueryUtil.paddingDefaultConditionQuery(CscpApp.class, cscpProcType);


//		if(null != listCscpApp) {
//			pageActiviti = PageActiviti.toPage(pq, listCscpApp);
//		}

		return cscpAppRepository.selectPage(page,cscpAppQueryWrapper);
	}

	@Override
	public CscpApp findOneByAppCode(CscpApp cscpApp) {
		LambdaQueryWrapper<CscpApp> lambdaQueryWrapper = new LambdaQueryWrapper<>();
		lambdaQueryWrapper.eq(CscpApp::getAppCode,cscpApp.getAppCode());
		return cscpAppRepository.selectOne(lambdaQueryWrapper);
	}

    @Override
    public void deleteCscpApp(String id) {
		CscpApp cscpApp = cscpAppRepository.selectById(id);
		Map<String,Object> params=new HashMap<>();
	    params.put("appCode",cscpApp.getAppCode());
        params.put("parentId","0");
		LambdaQueryWrapper<CscpProcType> lambdaQueryWrapper = new LambdaQueryWrapper<>();
		lambdaQueryWrapper.ne(CscpProcType::getAppCode,cscpApp.getAppCode())
				.eq(CscpProcType::getParentTypeId, "0");
        List<CscpProcType> typeList= cscpProcTypeRepository.selectList(lambdaQueryWrapper);
        if(typeList!=null && typeList.size()>0){
            throw  new CustomException("该应用下分类不为空");
        }
        this.cscpAppRepository.deleteById(id);
    }



}
