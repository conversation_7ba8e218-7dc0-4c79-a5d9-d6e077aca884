package com.ctsi.business.service.impl;


import cn.hutool.core.codec.Base64Encoder;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ctsi.activiti.core.model.CompleteParam;
import com.ctsi.activiti.enums.ActStatusEnum;
import com.ctsi.activiti.enums.ProcUtilsEnum;
import com.ctsi.business.domain.CscpAuditContent;
import com.ctsi.business.domain.CscpAuditContentLog;
import com.ctsi.business.domain.CscpProcBase;
import com.ctsi.business.repository.CscpAuditContentRepository;
import com.ctsi.business.service.CscpAuditContentService;
import com.ctsi.business.service.CscpProcBaseService;
import com.ctsi.business.service.ICscpAuditContentLogService;
import com.ctsi.hndx.aop.RestControllerAspectLog;
import com.ctsi.hndx.common.SysBaseServiceImpl;
import com.ctsi.hndx.constant.BpmStatusConstants;
import com.ctsi.hndx.enums.CallSourceEnum;
import com.ctsi.hndx.filestore.FileStoreTemplateService;
import com.ctsi.hndx.utils.SnowflakeIdUtil;
import com.ctsi.ssdc.admin.domain.CscpOrg;
import com.ctsi.ssdc.admin.domain.CscpUserOrg;
import com.ctsi.ssdc.admin.repository.CscpUserOrgRepository;
import com.ctsi.ssdc.admin.service.CscpOrgService;
import com.ctsi.ssdc.admin.service.CscpUserService;
import com.ctsi.ssdc.security.CscpUserDetail;
import com.ctsi.ssdc.security.SecurityUtils;
import com.ctsi.ssdc.util.DateUtil;
import org.activiti.engine.delegate.DelegateExecution;
import org.activiti.engine.impl.persistence.entity.TaskEntity;
import org.activiti.engine.task.Task;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;


/**
 * Service Implementation for managing CscpAuditContent.
 *
 * <AUTHOR>
 */
@Service
public class CscpAuditContentServiceImpl extends SysBaseServiceImpl<CscpAuditContentRepository, CscpAuditContent> implements CscpAuditContentService {
    private final Logger log = LoggerFactory.getLogger(CscpAuditContentServiceImpl.class);
    @Autowired
    private CscpAuditContentRepository cscpAuditContentRepository;

    @Autowired
    private CscpProcBaseService cscpProcBaseService;

    @Autowired
    private FileStoreTemplateService fileStoreTemplateService;

    @Autowired
    private CscpOrgService cscpOrgService;

    @Autowired
    private CscpUserOrgRepository cscpUserOrgRepository;

    @Autowired
    private CscpUserService cscpUserService;

    @Autowired
    private ICscpAuditContentLogService cscpAuditContentLogService;

    @Override
    public List<CscpAuditContent> getAuditContentList(String procInstId) {
        LambdaQueryWrapper<CscpAuditContent> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(CscpAuditContent::getProcInstId, procInstId)
                .ne(CscpAuditContent::getAuditStatus,String.valueOf(ActStatusEnum.STORAGE.getCode())).orderByDesc(CscpAuditContent::getCreateTime);
        List<CscpAuditContent> cscpAuditContentList = cscpAuditContentRepository.selectListNoAdd(lambdaQueryWrapper);
        // 办结时未添加意见，则手动添加
        LambdaQueryWrapper<CscpProcBase> queryWrapper = new LambdaQueryWrapper<CscpProcBase>()
                .eq(CscpProcBase::getProcInstId, procInstId)
                .select(CscpProcBase::getBpmStatus);
        List<CscpProcBase> cscpProcBaseList = cscpProcBaseService.selectListNoAdd(queryWrapper);
        if (ObjectUtil.equal(cscpProcBaseList.get(0).getBpmStatus(),BpmStatusConstants.PROCESS_END)){
            CscpAuditContent cscpAuditContent = cscpAuditContentList.get(0);
            // 正常办结和一键办结，不添加办结意见.
            if(StringUtils.isBlank(cscpAuditContent.getProcessContent())
                    && !StringUtils.equals(cscpAuditContent.getActName().trim(),"手动办结")
                    && StringUtils.isBlank(cscpAuditContent.getAuditContent())){
                cscpAuditContent.setAuditContent("已办结，无办结意见。");
            }
        }
        for (CscpAuditContent cscpAuditContent : cscpAuditContentList) {
            cscpAuditContent.setBpmStatus(cscpProcBaseList.get(0).getBpmStatus());
        }
        return changeBase64(cscpAuditContentList);
    }


    private List<CscpAuditContent> changeBase64(List<CscpAuditContent> cscpAuditContentList) {
        if (CollectionUtil.isNotEmpty(cscpAuditContentList)) {
            cscpAuditContentList.forEach(cscpAuditContent -> {
                if (CscpAuditContent.SXQP.equalsIgnoreCase(cscpAuditContent.getAuditContent())) {
                    //  下载文件转为base64
                    if (StringUtils.isNotEmpty(cscpAuditContent.getSignImage())) {
                        byte[] bytes = fileStoreTemplateService.downloadFile(cscpAuditContent.getSignImage());
                        if (bytes != null) {
                            String encode = Base64Encoder.encode(bytes);
                            cscpAuditContent.setSignImage(encode);
                        }
                    }
                }
                if (CscpAuditContent.WYQP.equalsIgnoreCase(cscpAuditContent.getAuditContent())) {
                    cscpAuditContent.setAuditContent(CscpAuditContent.WYQP_ZC);
                }
            });
        }
        return cscpAuditContentList;
    }

    @Override
    public void deleteByprocessInstanceId(String processInstanceId) {
        cscpAuditContentRepository.deleteByprocessInstanceId(processInstanceId);
    }
    @Override
    public void delete(String id) {
        cscpAuditContentRepository.deleteById(id);
    }

    /**
     * 插入流程审批意见
     *
     * @param taskEntity
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void insertTakListenerLogContent(TaskEntity taskEntity, String comment, String newFilePath) {
        String commentsFormId = (String) taskEntity.getVariableLocal(ProcUtilsEnum.COMMENTS_FORM_ID.getCode());
        String processDefinitionId = taskEntity.getProcessDefinitionId();
        String nodeKey = taskEntity.getTaskDefinitionKey();
        String procInstId = taskEntity.getProcessInstanceId();
        taskEntity.getParentTaskId();
        //流程历史中保存业务表的主键id
        String businessId = taskEntity.getExecution().getParent().getBusinessKey();
        DelegateExecution execution = taskEntity.getExecution();
        String rootProcessInstanceId = execution.getRootProcessInstanceId();
        if (!procInstId.equals(rootProcessInstanceId)) {
            LambdaQueryWrapper<CscpProcBase> lambdaQueryWrapper = new LambdaQueryWrapper<>();
            lambdaQueryWrapper.eq(CscpProcBase::getProcInstId, rootProcessInstanceId)
                    .select(CscpProcBase::getFormDataId);
            CscpProcBase cscpProcBase = cscpProcBaseService.selectOneNoAdd(lambdaQueryWrapper);
            businessId = cscpProcBase.getFormDataId();
        }

        CscpAuditContent content = new CscpAuditContent();
        content.setActId(nodeKey);
        content.setCommentsFormId(commentsFormId);
        content.setRootProcInstId(rootProcessInstanceId);
        content.setBusinessId(businessId);
        //保存不同调用来源 PC，安卓，ios等等
        if (RestControllerAspectLog.getCallSourceEnum() != null) {
            content.setClientType(RestControllerAspectLog.getCallSourceEnum().getMessageName());
        } else {
            content.setClientType(CallSourceEnum.PC.getMessageName());
        }
        String name = taskEntity.getName();
        content.setActName(name);
        CscpUserDetail currentCscpUserDetail = SecurityUtils.getCurrentCscpUserDetail();
        content.setAuditorName(currentCscpUserDetail.getRealName());
        content.setAuditorId(String.valueOf(currentCscpUserDetail.getId()));
        content.setProcInstId(procInstId);
        content.setProcDefId(processDefinitionId);
        content.setAuditTime(DateUtil.formatDateObject3(new Date()));
        //记录意见
        if (StringUtils.isNotEmpty(comment)) {
            content.setAuditContent(comment);
        }
        content.setId(SnowflakeIdUtil.getSnowFlakeLongId());
        content.setAuditStatus("0");
        content.setSignImage(newFilePath);
        cscpAuditContentRepository.insert(content);

    }

    /**
     * 插入流程审批意见
     *
     * @param taskEntity
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void insertTakListenerLogContent(TaskEntity taskEntity, String comment,String processComment ,String newFilePath,int signType, CompleteParam completeParam) {
        String commentsFormId = (String) taskEntity.getVariableLocal(ProcUtilsEnum.COMMENTS_FORM_ID.getCode());
        String processDefinitionId = taskEntity.getProcessDefinitionId();
        String nodeKey = taskEntity.getTaskDefinitionKey();
        String procInstId = taskEntity.getProcessInstanceId();
        //流程历史中保存业务表的主键id
        String businessId = taskEntity.getExecution().getParent().getBusinessKey();
        DelegateExecution execution = taskEntity.getExecution();
        String rootProcessInstanceId = execution.getRootProcessInstanceId();
        if (!procInstId.equals(rootProcessInstanceId)) {
            LambdaQueryWrapper<CscpProcBase> lambdaQueryWrapper = new LambdaQueryWrapper<>();
            lambdaQueryWrapper.eq(CscpProcBase::getProcInstId, rootProcessInstanceId)
                    .select(CscpProcBase::getFormDataId);
            CscpProcBase cscpProcBase = cscpProcBaseService.selectOneNoAdd(lambdaQueryWrapper);
            businessId = cscpProcBase.getFormDataId();
        }
        String signatureImageURL = completeParam.getSignatureImageURL();
        Integer xCoordinate = completeParam.getCoordinatex();
        Integer yCoordinate = completeParam.getCoordinatey();
        String width = completeParam.getWidth();
        String high = completeParam.getHigh();
        if (signType == 0) {
            LambdaQueryWrapper<CscpAuditContent> lambdaQueryWrapper2 = new LambdaQueryWrapper<>();
            lambdaQueryWrapper2.eq(CscpAuditContent::getTaskId, taskEntity.getId());
            lambdaQueryWrapper2.eq(CscpAuditContent::getAuditorId, SecurityUtils.getCurrentUserId());
            CscpAuditContent auditContent = cscpAuditContentRepository.selectOneNoAdd(lambdaQueryWrapper2);

            if (auditContent != null) {
                auditContent.setAuditContent(comment);
                auditContent.setProcessContent(processComment);
                auditContent.setActId(nodeKey);
                auditContent.setBusinessId(businessId);
                auditContent.setActName(taskEntity.getName());
                auditContent.setAuditTime(DateUtil.formatDateObject3(new Date()));
                auditContent.setAuditStatus("0");
                auditContent.setCommentsFormId(commentsFormId);
                if (StrUtil.isNotBlank(signatureImageURL)){
                    auditContent.setSignatureImageURL(signatureImageURL);
                }
                Optional.ofNullable(xCoordinate).ifPresent(value -> auditContent.setXCoordinate(value));
                Optional.ofNullable(yCoordinate).ifPresent(value -> auditContent.setYCoordinate(value));
                Optional.ofNullable(width).ifPresent(value -> auditContent.setWidth(value));
                Optional.ofNullable(high).ifPresent(value -> auditContent.setHigh(value));
                //保存不同调用来源 PC，安卓，ios等等
                if (RestControllerAspectLog.getCallSourceEnum() != null) {
                    auditContent.setClientType(RestControllerAspectLog.getCallSourceEnum().getMessageName());
                } else {
                    auditContent.setClientType(CallSourceEnum.PC.getMessageName());
                }
                auditContent.setSignType(signType);
                cscpAuditContentRepository.updateById(auditContent);
            } else {
                CscpAuditContent content = new CscpAuditContent();
                content.setActId(nodeKey);
                content.setCommentsFormId(commentsFormId);
                content.setRootProcInstId(rootProcessInstanceId);
                content.setBusinessId(businessId);
                //保存不同调用来源 PC，安卓，ios等等
                if (RestControllerAspectLog.getCallSourceEnum() != null) {
                    content.setClientType(RestControllerAspectLog.getCallSourceEnum().getMessageName());
                } else {
                    content.setClientType(CallSourceEnum.PC.getMessageName());
                }
                String name = taskEntity.getName();
                content.setActName(name);
                CscpUserDetail currentCscpUserDetail = SecurityUtils.getCurrentCscpUserDetail();
                content.setAuditorName(currentCscpUserDetail.getRealName());
                content.setAuditorId(String.valueOf(currentCscpUserDetail.getId()));
                content.setProcInstId(procInstId);
                content.setProcDefId(processDefinitionId);
                content.setAuditTime(DateUtil.formatDateObject3(new Date()));
                //记录意见
                if (StringUtils.isNotEmpty(comment)) {
                    content.setAuditContent(comment);
                }
                if (StringUtils.isNotEmpty(processComment)) {
                    content.setProcessContent(processComment);
                }

                content.setId(SnowflakeIdUtil.getSnowFlakeLongId());
                content.setAuditStatus("0");
                content.setSignImage(newFilePath);
                content.setSignatureImageURL(newFilePath);
                content.setSignType(signType);
                Optional.ofNullable(xCoordinate).ifPresent(value -> content.setXCoordinate(value));
                Optional.ofNullable(yCoordinate).ifPresent(value -> content.setYCoordinate(value));
                Optional.ofNullable(width).ifPresent(value -> content.setWidth(value));
                Optional.ofNullable(high).ifPresent(value -> content.setHigh(value));
                if (StrUtil.isNotBlank(signatureImageURL)){
                    content.setSignatureImageURL(signatureImageURL);
                }

                //组装个人排序号
                //获取用户全局排序号

                String qj = "9999";
                if (currentCscpUserDetail.getOrderBy() != null) {
                    qj = com.ctsi.hndx.utils.StringUtils.addZeroForNum(String.valueOf(currentCscpUserDetail.getOrderBy().toString()), 4);
                }
                //获取部门编号
                Long orgId = currentCscpUserDetail.getDepartmentId();
                CscpOrg cscpOrg = cscpOrgService.getById(orgId);
                String orgCode = "";
                if (cscpOrg != null) {
                    orgCode = cscpOrg.getPathCode();
                }
                //获取个人排序
                LambdaQueryWrapper<CscpUserOrg> queryWrapper = new LambdaQueryWrapper();
                queryWrapper.eq(CscpUserOrg::getUserId, SecurityUtils.getCurrentUserId());
                queryWrapper.eq(CscpUserOrg::getOrgId, orgId);
                CscpUserOrg cscpUserOrg = cscpUserOrgRepository.selectOneNoAdd(queryWrapper);
                String gr = "9999";
                if (cscpUserOrg != null) {
                    if (cscpUserOrg.getOrderBy() != null) {
                        gr = com.ctsi.hndx.utils.StringUtils.addZeroForNum(String.valueOf(cscpUserOrg.getOrderBy().toString()), 4);
                    }
                }

                String pathCode = qj + orgCode + gr;
                content.setPathCode(pathCode);
                content.setMobile(currentCscpUserDetail.getMobile());
                content.setTaskId(taskEntity.getId());
                cscpAuditContentRepository.insert(content);
            }
        } else {
            CscpAuditContent content = new CscpAuditContent();
            content.setActId(nodeKey);
            content.setCommentsFormId(commentsFormId);
            content.setRootProcInstId(rootProcessInstanceId);
            content.setBusinessId(businessId);
            //保存不同调用来源 PC，安卓，ios等等
            if (RestControllerAspectLog.getCallSourceEnum() != null) {
                content.setClientType(RestControllerAspectLog.getCallSourceEnum().getMessageName());
            } else {
                content.setClientType(CallSourceEnum.PC.getMessageName());
            }
            String name = taskEntity.getName();
            content.setActName(name);
            CscpUserDetail currentCscpUserDetail = SecurityUtils.getCurrentCscpUserDetail();
            content.setAuditorName(currentCscpUserDetail.getRealName());
            content.setAuditorId(String.valueOf(currentCscpUserDetail.getId()));
            content.setProcInstId(procInstId);
            content.setProcDefId(processDefinitionId);
            content.setAuditTime(DateUtil.formatDateObject3(new Date()));
            //记录意见
            if (StringUtils.isNotEmpty(comment)) {
                content.setAuditContent(comment);
            }
            if (StringUtils.isNotEmpty(processComment)) {
                content.setProcessContent(processComment);
            }
            content.setId(SnowflakeIdUtil.getSnowFlakeLongId());
            content.setAuditStatus("0");
            content.setSignImage(newFilePath);
            content.setSignType(signType);

            //组装个人排序号
            //获取用户全局排序号

            String qj = "9999";
            if (currentCscpUserDetail.getOrderBy() != null) {
                qj = com.ctsi.hndx.utils.StringUtils.addZeroForNum(String.valueOf(currentCscpUserDetail.getOrderBy().toString()), 4);
            }
            //获取部门编号
            Long orgId = currentCscpUserDetail.getDepartmentId();
            CscpOrg cscpOrg = cscpOrgService.getById(orgId);
            String orgCode = "";
            if (cscpOrg != null) {
                orgCode = cscpOrg.getPathCode();
            }
            //获取个人排序
            LambdaQueryWrapper<CscpUserOrg> queryWrapper = new LambdaQueryWrapper();
            queryWrapper.eq(CscpUserOrg::getUserId, SecurityUtils.getCurrentUserId());
            queryWrapper.eq(CscpUserOrg::getOrgId, orgId);
            CscpUserOrg cscpUserOrg = cscpUserOrgRepository.selectOneNoAdd(queryWrapper);
            String gr = "9999";
            if (cscpUserOrg != null) {
                if (cscpUserOrg.getOrderBy() != null) {
                    gr = com.ctsi.hndx.utils.StringUtils.addZeroForNum(String.valueOf(cscpUserOrg.getOrderBy().toString()), 4);
                }
            }

            String pathCode = qj + orgCode + gr;
            content.setPathCode(pathCode);
            content.setMobile(currentCscpUserDetail.getMobile());
            content.setTaskId(taskEntity.getId());
            if (StrUtil.isNotBlank(signatureImageURL)){
                content.setSignatureImageURL(signatureImageURL);
            }
            Optional.ofNullable(xCoordinate).ifPresent(value -> content.setXCoordinate(value));
            Optional.ofNullable(yCoordinate).ifPresent(value -> content.setYCoordinate(value));
            Optional.ofNullable(width).ifPresent(value -> content.setWidth(value));
            Optional.ofNullable(high).ifPresent(value -> content.setHigh(value));
            cscpAuditContentRepository.insert(content);
        }

    }


    /**
     * 插入流程审批意见,传入为task
     *
     * @param
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void insertTakListenerLogContent(Task task, String comment, String commentsFormId, ActStatusEnum actStatusEnum,String originalRealName,String originalUserId) {
        String processDefinitionId = task.getProcessDefinitionId();
        String nodeKey = task.getTaskDefinitionKey();
        String procInstId = task.getProcessInstanceId();
        //流程历史中保存业务表的主键id

        LambdaQueryWrapper<CscpProcBase> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(CscpProcBase::getProcInstId, procInstId)
                .select(CscpProcBase::getFormDataId, CscpProcBase::getRootProcInstId);
        CscpProcBase cscpProcBase = cscpProcBaseService.selectOneNoAdd(lambdaQueryWrapper);
        String businessId = cscpProcBase.getFormDataId();
        String rootProcessInstanceId = cscpProcBase.getRootProcInstId();

        CscpAuditContent content = new CscpAuditContent();
        content.setActId(nodeKey);
        content.setCommentsFormId(commentsFormId);
        content.setRootProcInstId(rootProcessInstanceId);
        content.setBusinessId(businessId);
        //保存不同调用来源 PC，安卓，ios等等
        if (RestControllerAspectLog.getCallSourceEnum() != null) {
            content.setClientType(RestControllerAspectLog.getCallSourceEnum().getMessageName());
        } else {
            content.setClientType(CallSourceEnum.PC.getMessageName());
        }
        String name = task.getName();
        content.setActName(name);
        CscpUserDetail currentCscpUserDetail = SecurityUtils.getCurrentCscpUserDetail();
        if (StringUtils.isNotEmpty(originalRealName)){
            content.setAuditorName(originalRealName);

        }else {
            content.setAuditorName(currentCscpUserDetail.getRealName());

        }
        if (StringUtils.isNotEmpty(originalUserId)){
            content.setAuditorId(originalUserId);
        }else {
            content.setAuditorId(String.valueOf(currentCscpUserDetail.getId()));
        }
        content.setProcInstId(procInstId);
        content.setProcDefId(processDefinitionId);
        content.setAuditTime(DateUtil.formatDateObject3(new Date()));
        //记录意见
        if (StringUtils.isNotEmpty(comment)) {
            content.setAuditContent(comment);
        }
        content.setAuditStatus(String.valueOf(actStatusEnum.getCode()));
        //组装个人排序号
        //获取用户全局排序号

        String qj = "9999";
        if(currentCscpUserDetail.getOrderBy() != null){
            qj = com.ctsi.hndx.utils.StringUtils.addZeroForNum(String.valueOf(currentCscpUserDetail.getOrderBy().toString()), 4);
        }
        //获取部门编号
        Long orgId = currentCscpUserDetail.getDepartmentId();
        CscpOrg cscpOrg = cscpOrgService.getById(orgId);
        String orgCode = "";
        if(cscpOrg!=null){
            orgCode = cscpOrg.getPathCode();
        }
        //获取个人排序
        LambdaQueryWrapper<CscpUserOrg> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.eq(CscpUserOrg::getUserId, SecurityUtils.getCurrentUserId());
        queryWrapper.eq(CscpUserOrg::getOrgId, orgId);
        CscpUserOrg cscpUserOrg = cscpUserOrgRepository.selectOneNoAdd(queryWrapper);
        String gr = "9999";
        if(cscpUserOrg!=null){
            if(cscpUserOrg.getOrderBy()!=null){
                gr = com.ctsi.hndx.utils.StringUtils.addZeroForNum(String.valueOf(cscpUserOrg.getOrderBy().toString()), 4);
            }
        }

        String pathCode = qj + orgCode + gr;
        content.setPathCode(pathCode);
        content.setMobile(currentCscpUserDetail.getMobile());
        content.setTaskId(task.getId());
        cscpAuditContentRepository.insert(content);

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void insertTakListenerLogContentNew(Task task, String comment, String commentsFormId, ActStatusEnum actStatusEnum,String currentName) {
        String processDefinitionId = task.getProcessDefinitionId();
        String nodeKey = task.getTaskDefinitionKey();
        String procInstId = task.getProcessInstanceId();
        //流程历史中保存业务表的主键id

        LambdaQueryWrapper<CscpProcBase> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(CscpProcBase::getProcInstId, procInstId)
                .select(CscpProcBase::getFormDataId, CscpProcBase::getRootProcInstId);
        CscpProcBase cscpProcBase = cscpProcBaseService.selectOneNoAdd(lambdaQueryWrapper);
        String businessId = cscpProcBase.getFormDataId();
        String rootProcessInstanceId = cscpProcBase.getRootProcInstId();

        CscpAuditContent content = new CscpAuditContent();
        content.setActId(nodeKey);
        content.setCommentsFormId(commentsFormId);
        content.setRootProcInstId(rootProcessInstanceId);
        content.setBusinessId(businessId);
        //保存不同调用来源 PC，安卓，ios等等
        if (RestControllerAspectLog.getCallSourceEnum() != null) {
            content.setClientType(RestControllerAspectLog.getCallSourceEnum().getMessageName());
        } else {
            content.setClientType(CallSourceEnum.PC.getMessageName());
        }
        content.setActName(currentName);
        CscpUserDetail currentCscpUserDetail = SecurityUtils.getCurrentCscpUserDetail();
        content.setAuditorName(currentCscpUserDetail.getRealName());
        content.setAuditorId(String.valueOf(currentCscpUserDetail.getId()));
        content.setProcInstId(procInstId);
        content.setProcDefId(processDefinitionId);
        content.setAuditTime(DateUtil.formatDateObject3(new Date()));
        //记录意见
        if (StringUtils.isNotEmpty(comment)) {
            content.setAuditContent(comment);
        }
        content.setAuditStatus(String.valueOf(actStatusEnum.getCode()));
        //组装个人排序号
        //获取用户全局排序号

        String qj = "9999";
        if(currentCscpUserDetail.getOrderBy() != null){
            qj = com.ctsi.hndx.utils.StringUtils.addZeroForNum(String.valueOf(currentCscpUserDetail.getOrderBy().toString()), 4);
        }
        //获取部门编号
        Long orgId = currentCscpUserDetail.getDepartmentId();
        CscpOrg cscpOrg = cscpOrgService.getById(orgId);
        String orgCode = "";
        if(cscpOrg!=null){
            orgCode = cscpOrg.getPathCode();
        }
        //获取个人排序
        LambdaQueryWrapper<CscpUserOrg> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.eq(CscpUserOrg::getUserId, SecurityUtils.getCurrentUserId());
        queryWrapper.eq(CscpUserOrg::getOrgId, orgId);
        CscpUserOrg cscpUserOrg = cscpUserOrgRepository.selectOneNoAdd(queryWrapper);
        String gr = "9999";
        if(cscpUserOrg!=null){
            if(cscpUserOrg.getOrderBy()!=null){
                gr = com.ctsi.hndx.utils.StringUtils.addZeroForNum(String.valueOf(cscpUserOrg.getOrderBy().toString()), 4);
            }
        }

        String pathCode = qj + orgCode + gr;
        content.setPathCode(pathCode);
        content.setMobile(currentCscpUserDetail.getMobile());
        content.setTaskId(task.getId());
        cscpAuditContentRepository.insert(content);

    }


    @Override
    public List<CscpAuditContent> getAuditContentListByRootInstanceId(String rootProcessInstanceId) {
        LambdaQueryWrapper<CscpAuditContent> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(CscpAuditContent::getRootProcInstId, rootProcessInstanceId)
                .ne(CscpAuditContent::getAuditStatus,String.valueOf(ActStatusEnum.STORAGE.getCode())).orderByDesc(CscpAuditContent::getAuditTime);
        return changeBase64(cscpAuditContentRepository.selectListNoAdd(lambdaQueryWrapper));

    }

    @Override
    public List<CscpAuditContent> getAuditContentListByInstanceId(String processInstanceId) {
        LambdaQueryWrapper<CscpAuditContent> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(CscpAuditContent::getProcInstId, processInstanceId)
                .ne(CscpAuditContent::getAuditStatus,String.valueOf(ActStatusEnum.STORAGE.getCode())).orderByDesc(CscpAuditContent::getCreateTime);
        return cscpAuditContentRepository.selectListNoAdd(lambdaQueryWrapper);

    }

    @Override
    public List<CscpAuditContent> getAuditContentListByInstanceIdList(List<String> processInstanceIdList) {
        LambdaQueryWrapper<CscpAuditContent> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.in(CscpAuditContent::getProcInstId,processInstanceIdList)
                .ne(CscpAuditContent::getAuditStatus,String.valueOf(ActStatusEnum.STORAGE.getCode())).orderByDesc(CscpAuditContent::getCreateTime);
        return cscpAuditContentRepository.selectListNoAdd(lambdaQueryWrapper);

    }

    @Override
    public CscpAuditContent getAuditContentByInstanceId(String processInstanceId) {
        LambdaQueryWrapper<CscpAuditContent> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(CscpAuditContent::getProcInstId, processInstanceId)
                .ne(CscpAuditContent::getAuditStatus,String.valueOf(ActStatusEnum.STORAGE.getCode())).orderByDesc(CscpAuditContent::getCreateTime).last("limit 1");
        return cscpAuditContentRepository.selectOneNoAdd(lambdaQueryWrapper);

    }

    @Override
    public List<CscpAuditContent> getAuditContentListByStatus(String processInstanceId) {
        LambdaQueryWrapper<CscpAuditContent> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(CscpAuditContent::getProcInstId, processInstanceId)
                .eq(CscpAuditContent::getAuditorId,String.valueOf(SecurityUtils.getCurrentCscpUserDetail().getId()))
                .eq(CscpAuditContent::getAuditStatus,String.valueOf(ActStatusEnum.STORAGE.getCode()));
        return cscpAuditContentRepository.selectListNoAdd(lambdaQueryWrapper);

    }

    @Override
    public List<CscpAuditContent> getAuditContentList(String processInstanceId, String rootProcessInstanceId, boolean isComment) {
        LambdaQueryWrapper<CscpProcBase> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        List<CscpAuditContent> cscpAuditContents = new ArrayList<>();
        Integer bpmStatus = 0;
        if (StringUtils.isEmpty(rootProcessInstanceId)) {
            return this.getAuditContentList(processInstanceId);
        }
        if (processInstanceId.equals(rootProcessInstanceId)) {
            //表示父流程进入，判断是否有子流程，如果有子流程，判断是否共用一个处理单
            lambdaQueryWrapper.eq(CscpProcBase::getRootProcInstId, rootProcessInstanceId).select(CscpProcBase::getProcessingSheet, CscpProcBase::getProcInstId,CscpProcBase::getBpmStatus);
            List<CscpProcBase> cscpProcBaseList = cscpProcBaseService.selectListNoAdd(lambdaQueryWrapper);
            if (CollectionUtils.isNotEmpty(cscpProcBaseList)) {
                bpmStatus = cscpProcBaseList.get(0).getBpmStatus();
                if (cscpProcBaseList.size() > 1) {
                    if (!isComment) {
                        cscpAuditContents = this.getAuditContentListByRootInstanceId(rootProcessInstanceId);
                    } else {
                        //表示有子流程，判断子流程和父流程是否公用一个处理单，取出
                        List<Long> processList = new ArrayList<>();
                        processList.add(Long.valueOf(rootProcessInstanceId));
                        cscpProcBaseList.forEach(cscpProcBase -> {
                            if (cscpProcBase.getProcessingSheet() == 1) {
                                processList.add(Long.valueOf(cscpProcBase.getProcInstId()));
                            }
                        });
                        LambdaQueryWrapper<CscpAuditContent> lambdaQueryWrapperAudit =
                                new LambdaQueryWrapper<>();
                        lambdaQueryWrapperAudit.in(CscpAuditContent::getProcInstId, processList)
                                .ne(CscpAuditContent::getAuditStatus,String.valueOf(ActStatusEnum.STORAGE.getCode())).orderByDesc(CscpAuditContent::getCreateTime);
                        cscpAuditContents = changeBase64(cscpAuditContentRepository.selectListNoAdd(lambdaQueryWrapperAudit));
                    }

                } else {
                    //没有子流程
                    cscpAuditContents = this.getAuditContentList(processInstanceId);
                }
            }

        } else {
            //  为子流程进入，先判断子流程是否是共用处理单，如果公用的话，显示所有，不共用的话显示自己的
            lambdaQueryWrapper.eq(CscpProcBase::getProcInstId, processInstanceId).select(CscpProcBase::getProcessingSheet, CscpProcBase::getProcInstId);
            CscpProcBase cscpProcBase = cscpProcBaseService.selectOneNoAdd(lambdaQueryWrapper);
            int processingSheet = 0;
            if(cscpProcBase!=null){
                bpmStatus = cscpProcBase.getBpmStatus();
                processingSheet = cscpProcBase.getProcessingSheet();
            }
            if (processingSheet == 1) {
                //共用处理单,找出父流程及其子流程的所有
                cscpAuditContents = this.getAuditContentListByRootInstanceId(rootProcessInstanceId);
            } else {
                //不共用处理单
                cscpAuditContents = this.getAuditContentList(processInstanceId);
            }
        }
        for (CscpAuditContent cscpAuditContent : cscpAuditContents) {
            cscpAuditContent.setBpmStatus(bpmStatus);
        }
        return cscpAuditContents;
    }
    /**
     * 插入流程审批意见
     *
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void create(CscpAuditContent cscpAuditContent) {
        LambdaQueryWrapper<CscpAuditContent> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(CscpAuditContent::getTaskId,cscpAuditContent.getTaskId());
        lambdaQueryWrapper.eq(CscpAuditContent::getAuditorId,SecurityUtils.getCurrentUserId());
        CscpAuditContent auditContent = cscpAuditContentRepository.selectOneNoAdd(lambdaQueryWrapper);
        if (auditContent!=null){
            auditContent.setCommentsFormId(cscpAuditContent.getCommentsFormId());
            auditContent.setAuditContent(cscpAuditContent.getAuditContent());
            auditContent.setProcessContent(cscpAuditContent.getProcessContent());
            auditContent.setSignatureImageURL(cscpAuditContent.getSignatureImageURL());
            cscpAuditContentRepository.updateById(auditContent);
        }else{
            CscpAuditContent content = new CscpAuditContent();
            content.setSignatureImageURL(cscpAuditContent.getSignatureImageURL());
            content.setActId(cscpAuditContent.getActId());
            content.setCommentsFormId(cscpAuditContent.getCommentsFormId());
            content.setRootProcInstId(cscpAuditContent.getRootProcInstId());
            //保存不同调用来源 PC，安卓，ios等等
            if (RestControllerAspectLog.getCallSourceEnum() != null) {
                content.setClientType(RestControllerAspectLog.getCallSourceEnum().getMessageName());
            } else {
                content.setClientType(CallSourceEnum.PC.getMessageName());
            }
            content.setActName(cscpAuditContent.getActName());
            CscpUserDetail currentCscpUserDetail = SecurityUtils.getCurrentCscpUserDetail();
            content.setAuditorName(currentCscpUserDetail.getRealName());
            content.setAuditorId(String.valueOf(currentCscpUserDetail.getId()));
            content.setProcInstId(cscpAuditContent.getProcInstId());
            content.setProcDefId(cscpAuditContent.getProcDefId());
            content.setAuditTime(DateUtil.formatDateObject3(new Date()));
            content.setAuditContent(cscpAuditContent.getAuditContent());
            content.setProcessContent(cscpAuditContent.getProcessContent());
            content.setId(SnowflakeIdUtil.getSnowFlakeLongId());
            content.setAuditStatus(String.valueOf(ActStatusEnum.STORAGE.getCode()));
            content.setSignType(0);

            //组装个人排序号
            //获取用户全局排序号

            String qj = "9999";
            if(currentCscpUserDetail.getOrderBy() != null){
                qj = com.ctsi.hndx.utils.StringUtils.addZeroForNum(String.valueOf(currentCscpUserDetail.getOrderBy().toString()), 4);
            }
            //获取部门编号
            Long orgId = currentCscpUserDetail.getDepartmentId();
            CscpOrg cscpOrg = cscpOrgService.getById(orgId);
            String orgCode = "";
            if(cscpOrg!=null){
                orgCode = cscpOrg.getPathCode();
            }
            //获取个人排序
            LambdaQueryWrapper<CscpUserOrg> queryWrapper = new LambdaQueryWrapper();
            queryWrapper.eq(CscpUserOrg::getUserId, SecurityUtils.getCurrentUserId());
            queryWrapper.eq(CscpUserOrg::getOrgId, orgId);
            CscpUserOrg cscpUserOrg = cscpUserOrgRepository.selectOneNoAdd(queryWrapper);
            String gr = "9999";
            if(cscpUserOrg!=null){
                if(cscpUserOrg.getOrderBy()!=null){
                    gr = com.ctsi.hndx.utils.StringUtils.addZeroForNum(String.valueOf(cscpUserOrg.getOrderBy().toString()), 4);
                }
            }

            String pathCode = qj + orgCode + gr;
            content.setPathCode(pathCode);
            content.setMobile(currentCscpUserDetail.getMobile());
            content.setTaskId(cscpAuditContent.getTaskId());
            cscpAuditContentRepository.insert(content);
        }
    }


    @Override
    public void createAuditContent(CscpAuditContent cscpAuditContent) {
        CscpAuditContent model = this.getAuditContentByInstanceId(cscpAuditContent.getProcInstId());
        cscpAuditContent.setProcDefId(model.getProcDefId());
//        cscpAuditContent.setClientType("电脑端");
        cscpAuditContent.setBusinessId(model.getBusinessId());
        cscpAuditContent.setRootProcInstId(model.getRootProcInstId());
        cscpAuditContent.setId(SnowflakeIdUtil.getSnowFlakeLongId());
        cscpAuditContent.setAuditStatus("0");
        cscpAuditContent.setSignType(0);
        DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        cscpAuditContent.setCreateTime(LocalDateTime.parse(cscpAuditContent.getAuditTime(), df));
        cscpAuditContent.setCreateBy(Long.valueOf(cscpAuditContent.getAuditorId()));
        cscpAuditContent.setCreateName(cscpAuditContent.getAuditorName());

        LambdaQueryWrapper<CscpUserOrg> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.eq(CscpUserOrg::getUserId, cscpAuditContent.getCreateBy());
        List<CscpUserOrg> cscpUserOrg = cscpUserOrgRepository.selectListNoAdd(queryWrapper);
        if(cscpUserOrg!=null && cscpUserOrg.size()>0) {
            cscpAuditContent.setDepartmentId(cscpUserOrg.get(0).getOrgId());
            cscpAuditContent.setCompanyId(cscpUserOrg.get(0).getCompanyId());
        }

        CscpOrg cscpOrg = cscpOrgService.getById(cscpAuditContent.getDepartmentId());
        if(cscpOrg!=null){
            cscpAuditContent.setTenantId(cscpOrg.getTenantId());
        }
        cscpAuditContentRepository.insert(cscpAuditContent);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateAuditContent(CscpAuditContent cscpAuditContent) {
        CscpAuditContent model = cscpAuditContentRepository.selectById(cscpAuditContent.getId());
        String content = model.getAuditContent();//获取旧文本签批

        model.setAuditContent(cscpAuditContent.getAuditContent());
        cscpAuditContentRepository.updateById(model);
        log.info(SecurityUtils.getCurrentUserName()+" 将业务id为"+model.getBusinessId()+"数据的 审核内容: "+content+"  改成-->  " +cscpAuditContent.getAuditContent());
        //记录历史记录
        CscpAuditContentLog contentLog = new CscpAuditContentLog();
        contentLog.setId(SnowflakeIdUtil.getSnowFlakeLongId());
        contentLog.setContentId(model.getId().toString());
        contentLog.setProcInstId(model.getProcInstId());
        contentLog.setActId(model.getActId());
        contentLog.setActName(model.getActName());
        contentLog.setOldAuditContent(content);
        contentLog.setNewAuditContent(cscpAuditContent.getAuditContent());
        cscpAuditContentLogService.save(contentLog);
    }

    @Override
    public List<String> getProcInstIdByUsrId(Long userId){

        List<CscpAuditContent> cscpAuditContentList = cscpAuditContentRepository.selectListNoAdd(new QueryWrapper<CscpAuditContent>()
                .select("distinct proc_inst_id")
                .eq("auditor_id", userId));
        return cscpAuditContentList.stream().map(i -> i.getProcInstId()).collect(Collectors.toList());
    }
}
