package com.ctsi.business.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ctsi.activiti.bpmn.constant.BpmnElements;
import com.ctsi.activiti.bpmn.dto.CscpProcDTO;
import com.ctsi.activiti.bpmn.entity.BpmnNode;
import com.ctsi.activiti.bpmn.entity.CscpProc;
import com.ctsi.activiti.bpmn.entity.CscpProcExtended;
import com.ctsi.activiti.bpmn.service.ActivitiBpmnDefinitionService;
import com.ctsi.activiti.candidate.chooseassignee.AssigneeType;
import com.ctsi.activiti.candidate.service.CandidateService;
import com.ctsi.activiti.candidate.strategy.CandidateRuleParam;
import com.ctsi.activiti.core.entity.QueryDynamicEntity;
import com.ctsi.activiti.core.mapper.DynamicMapper;
import com.ctsi.activiti.core.model.TaskQueryParam;
import com.ctsi.activiti.core.service.impl.ActivitiCoreServiceImpl;
import com.ctsi.activiti.core.vo.DetailTaskData;
import com.ctsi.activiti.core.vo.PageQuery;
import com.ctsi.activiti.core.vo.TaskData;
import com.ctsi.activiti.enums.ActStatusEnum;
import com.ctsi.activiti.ext.entity.ProcessAssignee;
import com.ctsi.activiti.ext.repository.ProcessAssigneeRepository;
import com.ctsi.activiti.ext.service.ProcessAssigneeService;
import com.ctsi.business.domain.*;
import com.ctsi.business.domain.dto.BusinessCscpProcBaseDto;
import com.ctsi.business.domain.dto.CscpProcBaseDto;
import com.ctsi.business.domain.dto.CscpProcQueryDTO;
import com.ctsi.business.repository.*;
import com.ctsi.business.service.*;
import com.ctsi.common.bo.*;
import com.ctsi.common.exception.CustomException;
import com.ctsi.common.utils.ActivitiUtils;
import com.ctsi.common.utils.JsonUtils;
import com.ctsi.common.utils.PageData;
import com.ctsi.common.utils.SearchTypeToOperatorUtils;
import com.ctsi.domain.CformForm;
import com.ctsi.domain.dto.FormItems;
import com.ctsi.hndx.cformbizdatacontent.service.CformBizDataContentService;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.common.SysBaseServiceImpl;
import com.ctsi.hndx.common.datapermission.DataFilterThreadLocal;
import com.ctsi.hndx.common.datapermission.DataNoFilterThreadLocal;
import com.ctsi.hndx.common.datapermission.SysDataRule;
import com.ctsi.hndx.constant.BpmStatusConstants;
import com.ctsi.hndx.constant.SysConfigConstant;
import com.ctsi.hndx.exception.BusinessException;
import com.ctsi.hndx.fieldaccess.entity.dto.CformFieldSearchVO;
import com.ctsi.hndx.fieldaccess.service.ICformFieldAccessService;
import com.ctsi.hndx.result.ResultCode;
import com.ctsi.hndx.systenant.entity.dto.TSysTenantDTO;
import com.ctsi.hndx.systenant.service.ITSysTenantService;
import com.ctsi.hndx.tsysconfig.service.ISysConfigService;
import com.ctsi.hndx.utils.*;
import com.ctsi.service.CFormDataService;
import com.ctsi.service.CformFormService;
import com.ctsi.ssdc.admin.domain.CscpOrg;
import com.ctsi.ssdc.admin.domain.dto.CscpDistributeBaseUserDTO;
import com.ctsi.ssdc.admin.domain.dto.CscpUserDTO;
import com.ctsi.ssdc.admin.repository.CscpUserRoleRepository;
import com.ctsi.ssdc.admin.service.CscpOrgService;
import com.ctsi.ssdc.admin.service.CscpUserRoleService;
import com.ctsi.ssdc.admin.service.CscpUserService;
import com.ctsi.ssdc.model.PageResult;
import com.ctsi.ssdc.security.SecurityUtils;
import com.ctsi.ssdc.util.DateUtil;
import com.github.pagehelper.PageHelper;
import org.activiti.engine.RuntimeService;
import org.activiti.engine.TaskService;
import org.activiti.engine.history.HistoricTaskInstance;
import org.activiti.engine.impl.persistence.entity.TaskEntityImpl;
import org.activiti.engine.runtime.ProcessInstance;
import org.activiti.engine.task.Task;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class CscpActivitiServiceImpl extends SysBaseServiceImpl<CscpActivitRepository, CscpProc> implements CscpActivitiService {

    @Autowired
    private CscpActivitRepository cscpActivitRepository;

    @Autowired
    private TaskService taskService;

    @Autowired
    private CandidateService candidateService;

    @Autowired
    private CscpUserService cscpUserService;

    @Autowired
    private CscpOrgService cscpOrgService;

    @Autowired
    private ActivitiBpmnDefinitionService activitiBpmnDefinitionService;
    @Autowired
    private CformBizDataContentService cscpProcBaseDataContentService;

    @Autowired
    private CscpProcBaseService cscpProcBaseService;

    @Autowired
    private RuntimeService runtimeService;

    @Autowired
    private CscpUserRoleRepository userRoleRepository;

    @Autowired
    private CscpProcBaseRepository cscpProcBaseRepository;

    @Autowired
    private CscpProcTypeRepository cscpProcTypeRepository;

    @Autowired
    private CscpAppRepository cscpAppRepository;

    @Autowired
    private CscpInformationService cscpInformationService;

    @Autowired
    private CscpInformationRepository cscpInformationRepository;

    @Autowired
    private CscpHolidayService cscpHolidayService;

    @Autowired
    private CformFormService cformFormService;

    @Autowired
    private CFormDataService  cFormDataService;


    @Autowired
    private DynamicMapper dynamicMapper;

    @Autowired
    private ICformFieldAccessService cformFieldAccessService;

    @Autowired
    private ITSysTenantService itSysTenantService;

    @Autowired
    private ProcessAssigneeRepository processAssigneeRepository;

    @Autowired
    private CscpUserRoleService cscpUserRoleService;

    @Autowired
    private ProcessAssigneeService processAssigneeService;
    @Autowired
    private CscpAuditContentService cscpAuditContentService;
    @Autowired
    private ISysConfigService sysConfigService;

    //   流程发起人的配置
    private final String ASSINEG_START = "[\"com.ctsi.activiti.candidate.strategy.impl.BuiltinProcStarterStrategy\"]";
    //  流程发起人的配置角色
    private final String ASSINEG_START_RULE = "[{\"name\":\"流程发起人\",\"sort\":1,\"avatar\":[{\"color\":\"#19be6b\"," +
            "\"type\":\"创建人\",\"title\":\"创建人\"}]," +
            "\"value\":[],\"key\":\"com.ctsi.activiti.candidate.strategy.impl.BuiltinProcStarterStrategy\",\"url\":\"\"}]";

    @Override
    public PageActiviti<CscpProc> getModelListListForPage(PageForm<CscpProc> form) throws Exception {
        if (form.getT() == null) {
            form.setT(new CscpProc());
        }
        Page page = new Page(form.getCurrentPage(), form.getPageSize());
        CscpProc cscpProc = form.getT();
        cscpProc.setCompanyId(SecurityUtils.getCurrentCscpUserDetail().getCompanyId());
        //  租户和系统取自己建立的
        if (SecurityUtils.isTenantName() || SecurityUtils.isSystemName()){
            cscpProc.setCreateBy(SecurityUtils.getCurrentUserId());
        }
        IPage<CscpProc> list = cscpActivitRepository.queryPageModelList(page, cscpProc);
        PageActiviti pageActiviti = PageActivitiUtil.convertPageCfrm(list);
        return pageActiviti;
    }


    @Override
    public PageActiviti<CscpProcExtended> getModelListListExtendedForPage(PageForm<CscpProcExtended> form) throws Exception {
        if (form.getT() == null) {
            form.setT(new CscpProcExtended());
        }
        PageQuery pq = PageQuery.toPageQuery(form, CscpProcExtended.class);
        PageHelper.startPage(form.getCurrentPage(), form.getPageSize());
        List<CscpProcExtended> list = cscpActivitRepository.queryPageModelExtendedList(pq);
        return PageActiviti.listToPage(list);
    }

    @Override
    public PageActiviti<TaskEntityImpl> getActivitiTaskListMultiAssignForPage(PageForm<TaskQueryParam> form) throws Exception {
        PageActiviti<TaskEntityImpl> pageActiviti = new PageActiviti<>();
        if (form.getT() == null) {
            form.setT(new TaskQueryParam());
        }
        IPage iPage = new Page(form.getCurrentPage(), form.getPageSize());
        IPage<TaskEntityImpl> pageList = cscpActivitRepository.queryPageTaskListMultiAssign(iPage, form.getT());
        pageActiviti = PageActivitiUtil.convertPageCfrm(pageList);
        return pageActiviti;

    }

    @Override
    public List<CscpUserDTO> getNodeAssigneesList(String taskId, String nodeKey) throws Exception {
        // 任务ID
        if (ActivitiUtils.isEmpty(taskId)) {
            throw new IllegalArgumentException("没有设置任务ID");
        }

        // 查找任务
        Task task = taskService.createTaskQuery().taskId(taskId).singleResult();
        if (task == null) {
            throw new IllegalArgumentException("没有找到流程任务");
        }

        BpmnNode currentNode = activitiBpmnDefinitionService.getBpmnNodeByNodeKey(task.getProcessDefinitionId(), nodeKey);

        CandidateRuleParam param = new CandidateRuleParam();
        param.setProcessNode(currentNode);
        param.setProcessInstanceId(task.getProcessInstanceId());


        // 调用候选人策略接口，获取受理人列表
        List<String> assignees = candidateService.getCandidates(param);
        List<CscpUserDTO> userList = new ArrayList<>();
        if (assignees != null && assignees.size() > 0) {
            Set<String> set = new HashSet<String>();
            List<String> newList = new ArrayList<>();
            for (String id : assignees) {
                if (set.add(id)) {
                    newList.add(id);
                }
            }
            for (String userId : newList) {
                CscpUserDTO cscpUserDTO = cscpUserService.findByUserId(Long.valueOf(userId));
                userList.add(cscpUserDTO);
            }
        }
        return userList;
    }

    @Override
    public List<ProcessAssignee> getProcessAssigneeByProcessInstanceId(String processInstanceId,String taskId) {
        //查询下一阶段办理人，未读状态的所有人
        //1.已读，已处理
        LambdaQueryWrapper<ProcessAssignee> lambdaQueryAssigneeWrapper = new LambdaQueryWrapper<>();
        lambdaQueryAssigneeWrapper.eq(ProcessAssignee::getProcessInstanceId,processInstanceId);
        //lambdaQueryAssigneeWrapper.eq(ProcessAssignee::getReadStatus,0);
        lambdaQueryAssigneeWrapper.ne(StringUtils.isNotBlank(taskId),ProcessAssignee::getTaskId,taskId);
        List<ProcessAssignee> list = processAssigneeRepository.selectListNoAdd(lambdaQueryAssigneeWrapper);
        return list;
    }

    @Override
    public CscpBaseParams getFormBaseParams(Map<String, Object> params) {
        CscpBaseParams cscpBaseParams = new CscpBaseParams();
        String procId = MapUtils.getString(params, "processInstanceId");
        String modelKey = MapUtils.getString(params, "processDefinitionKey");
        String taskId = MapUtils.getString(params, "taskId");
        CscpUserDTO cscpUserDTO = null;
        if (SecurityUtils.getCurrentUser().isPresent()) {
            cscpBaseParams.setRealName(SecurityUtils.getCurrentCscpUserDetail().getRealName());
            cscpBaseParams.setUserId(String.valueOf(SecurityUtils.getCurrentCscpUserDetail().getId()));
            cscpBaseParams.setUserName(SecurityUtils.getCurrentCscpUserDetail().getUsername());
        }

        cscpBaseParams.setProcessInstanceId(procId);

        cscpBaseParams.setCurrTime(DateUtil.DATE_FORMAT_THREE.format(new Date()));
        //查询阅读状态
        LambdaQueryWrapper<ProcessAssignee> lambdaQueryAssigneeWrapper = new LambdaQueryWrapper<>();
        lambdaQueryAssigneeWrapper.eq(ProcessAssignee::getProcessInstanceId,procId);
        lambdaQueryAssigneeWrapper.eq(ProcessAssignee::getAssignee,SecurityUtils.getCurrentUserId());
        ProcessAssignee processAssignee = processAssigneeRepository.selectOneNoAdd(lambdaQueryAssigneeWrapper);
        if (processAssignee!=null){
            cscpBaseParams.setReadStatus(processAssignee.getReadStatus());
        }
        //根据modelKey获取最新的流程定义

        CscpProc cscpProc = activitiBpmnDefinitionService.getBpmnModelMaxVersionByProcessDefinitionKey(modelKey);
        CscpProcBase cscpProcBase = null;

        if (StringUtils.isNotEmpty(procId)) {
            cscpProcBase = cscpProcBaseService.getProcBaseByProcInstId(procId);
        }

        if (cscpProcBase != null) {
            cscpBaseParams.setTableName(cscpProcBase.getTableName());
            cscpBaseParams.setFormDataId(cscpProcBase.getFormDataId());
            cscpBaseParams.setCompanyId(cscpProcBase.getCompanyId());
            // 设置 Long deptId
            cscpBaseParams.setDeptId(cscpProcBase.getDepartmentId());
            Long userId = Long.valueOf(SecurityUtils.getCurrentUserId());
            if(userId == cscpProcBase.getCreateBy().longValue()){
                cscpBaseParams.setIsMakeDraft(1);
            }else{
                cscpBaseParams.setIsMakeDraft(0);
            }
           // String  currentDataContent = cscpProcBaseDataContentService.getOnlyCurrentDataContent(cscpProcBase.getId());
          //  cscpBaseParams.setCurrentDataContent(currentDataContent);
            //不是初次打开表单，取出历史模型数据
            cscpProc = activitiBpmnDefinitionService.getBpmnModelByProcessDefinitionId( cscpProcBase.getProcDefId());
            if (cscpProc == null) {
                throw new CustomException("流程模型不存在");
            }
            cscpBaseParams.setProcessDefinitionId(cscpProcBase.getProcDefId());
            cscpBaseParams.setProcessName(cscpProc.getName());
            cscpBaseParams.setFormMainName(cscpProcBase.getTitle());
            cscpBaseParams.setFormId(cscpProcBase.getFormDefId());
            cscpBaseParams.setIsSplitScreen(cscpProcBase.getIsSplitScreen());
        }

        if (cscpProc != null && cscpProcBase == null) {
            cscpBaseParams.setProcessDefinitionId(cscpProc.getProcessDefinitionId());
            cscpBaseParams.setProcessName(cscpProc.getName());
            if (SecurityUtils.getCurrentUser().isPresent()) {
                cscpBaseParams.setFormMainName(cscpProc.getName() + "(" + SecurityUtils.getCurrentCscpUserDetail().getRealName() + " " + cscpBaseParams.getCurrTime() + ")");
            }
            cscpBaseParams.setCompanyId(SecurityUtils.getCurrentCompanyId());
            // 设置 Long deptId
            cscpBaseParams.setDeptId(SecurityUtils.getCurrentCscpUserDetail().getDepartmentId());
            cscpBaseParams.setFormId(cscpProc.getFormId());
            CformForm cformForm = cformFormService.queryCfromByFormId(cscpProc.getFormId());
            String addPubContent = cformForm.getAddPubContent();
            if (StringUtils.isEmpty(addPubContent)){
                throw new BusinessException("没有设置起草表单");
            }
            cscpBaseParams.setTableName(cformForm.getModelData());
            //cscpBaseParams.setCurrentDataContent(cformForm.getAddPubContent().replace(Constants.FORM_TITLE_NAME,SecurityUtils.getCurrentCscpUserDetail().getCompanyName()));
        }

        if (StringUtils.isEmpty(procId)) {
            cscpBaseParams.setProcessStatus("draft");
            BpmnNode bpmnNode = activitiBpmnDefinitionService.getFirstNode(cscpBaseParams.getProcessDefinitionId());
            if (bpmnNode != null) {
                cscpBaseParams.setNodeKey(bpmnNode.getNodeKey());
            }
        } else {
            BpmnNode endNode = null;
            List<BpmnNode> bpmnNodeList = activitiBpmnDefinitionService.getEndNodeList(cscpBaseParams.getProcessDefinitionId());
            if (bpmnNodeList != null && bpmnNodeList.size() > 0) {
                endNode = bpmnNodeList.get(0);
            }
            if (StringUtils.isNotEmpty(taskId)) {
                Task task = taskService.createTaskQuery().taskId(taskId).singleResult();
                if (task != null) {
                    cscpBaseParams.setProcessStatus("todo");
                    cscpBaseParams.setNodeKey(task.getTaskDefinitionKey());
                } else {
                    cscpBaseParams.setProcessStatus("finished");
                    if (endNode != null) {
                        cscpBaseParams.setNodeKey(endNode.getNodeKey());
                    }
                }
            }
            ProcessInstance processInstance = runtimeService.createProcessInstanceQuery().processInstanceId(procId).singleResult();

            if (processInstance == null) {
                cscpBaseParams.setProcessStatus("end");
                if (endNode != null) {
                    cscpBaseParams.setNodeKey(endNode.getNodeKey());
                }
            }
        }

        if (cscpProc != null) {
            BpmnNode bpmnNode = activitiBpmnDefinitionService.getFirstNode(cscpProc.getProcessDefinitionId());
            cscpBaseParams.setFromUrl(bpmnNode == null ? "" : bpmnNode.getFormUrl());
        }
        return cscpBaseParams;
    }

    @Override
    public BpmnNode getActSet(QueryParams params) {
        String formId = params.getFormId();
        String processInstanceId = params.getProcessInstanceId();
        String processDefinitionKey = params.getProcessDefinitionKey();
        String type = params.getType();
        //  获取处理单的展示类型,获取初始节点
        if ("sign".equalsIgnoreCase(type)){
            CscpProc cscpProc = activitiBpmnDefinitionService.getBpmnModelMaxVersionByProcessDefinitionKey(processDefinitionKey);
            if (cscpProc == null) {
                throw new RuntimeException("流程模型不存在");
            }
           String processDefinitionId = cscpProc.getProcessDefinitionId();
            return activitiBpmnDefinitionService.getFirstNode(processDefinitionId);
        }

        if (StringUtils.isNotEmpty(formId) && StringUtils.isEmpty(processInstanceId) && StringUtils.isEmpty(processDefinitionKey) ){
            // 获取新增表单时候的类型
            BpmnNode bpmnNode = new BpmnNode();
            String addFormItem= cformFormService.selectAddFormItem(formId);
            bpmnNode.setFormItems(addFormItem);
            return bpmnNode;
        }

        String processDefinitionId ="";
        String nodeKey = params.getNodeKey();
        CscpProc cscpProc = null;
        if (StringUtils.isNotEmpty(processInstanceId)) {
            CscpProcBase cscpProcBase = cscpProcBaseService.getProcBaseByProcInstId(processInstanceId);
            processDefinitionId = cscpProcBase.getProcDefId();
            cscpProc = activitiBpmnDefinitionService.getBpmnModelByProcessDefinitionId(processDefinitionId);
        }else {
            cscpProc = activitiBpmnDefinitionService.getBpmnModelMaxVersionByProcessDefinitionKey(processDefinitionKey);
            if (cscpProc == null) {
                throw new RuntimeException("流程模型不存在");
            }
            processDefinitionId = cscpProc.getProcessDefinitionId();
        }
        if (StringUtils.isEmpty(nodeKey) || "null".equals(nodeKey)) {
            BpmnNode bpmnNode = activitiBpmnDefinitionService.getFirstNode(processDefinitionId);
            // 获取新增表单时候的类型
            if (com.ctsi.hndx.utils.StringUtils.isEmpty(processInstanceId)){
                String addFormItem=  cformFormService.selectAddFormItem(cscpProc.getFormId());
                bpmnNode.setFormItems(addFormItem);
                return bpmnNode;
            }else {
                return bpmnNode;
            }

        } else {
            return activitiBpmnDefinitionService.getBpmnNodeByNodeKey(processDefinitionId, nodeKey);
        }
    }

    @Override
    public PageActiviti<PageData> getCancellationProcessList(PageForm<Cscp> form) {
        PageActiviti<PageData> pageActiviti = new PageActiviti<>();
        if (form.getT() == null) {
            form.setT(new Cscp());
        }

        IPage<PageData> iPage = new Page<>(form.getCurrentPage(), form.getPageSize());
        IPage<PageData> pageDataIPage = cscpActivitRepository.queryPageCancellationProcessList(iPage, form.getT());
        PageActiviti.listToPage(pageDataIPage.getRecords());

//        pageActiviti.setTotal((int) pageDataIPage.getTotal());
        return pageActiviti;
    }

    @Override
    public PageActiviti<PageData> getViewTaskList(PageForm<Cscp> form) {
        if (form.getT() == null) {
            form.setT(new Cscp());
        }
        IPage<PageData> iPage = new Page(form.getCurrentPage(), form.getPageSize());
        IPage<PageData> pageDataIPage = cscpActivitRepository.queryPageViewTaskList(iPage, form.getT());
        return PageActiviti.listToPage(pageDataIPage.getRecords());
    }

    @Override
    public List<PageData> getExpressionByNode(PageData pageData) {
        return cscpActivitRepository.getExpressionByNode(pageData);
    }

    @Override
    public void viewTask(PageData pd) {
        cscpActivitRepository.viewTask(pd);
    }

    @Override
    public void setViewTaskRead(PageData pd) {
        cscpActivitRepository.setViewTaskRead(pd);
    }

    @Override
    public PageData getViewTask(PageData pd) {
        return cscpActivitRepository.getViewTask(pd);
    }

    @Override
    public List<DetailTaskData> getMultiTaskList(String processInstanceId) {
        ProcessInstance processInstance = runtimeService.createProcessInstanceQuery().processInstanceId(processInstanceId).singleResult();
        if (processInstance == null) {
            throw new IllegalArgumentException("流程已经结束");
        }

        List<Task> taskList = taskService.createTaskQuery().processInstanceId(processInstanceId).list();
        // 转换任务信息
        List<DetailTaskData> taskDataList = new ArrayList<>();
        if (taskList != null) {
            taskList.forEach(task -> {
                TaskData taskData = TaskData.newInstance(task);
                if (taskData == null) {
                    return;
                }
                if (taskData.getAssignee().equals(String.valueOf(SecurityUtils.getCurrentUserId()))) {
                    return;
                }
                DetailTaskData detailTaskData = ActivitiUtils.toBean(DetailTaskData.class, taskData);
                taskDataList.add(detailTaskData);
            });
        }
        return taskDataList;
    }

    @Override
    public PageActiviti<PageData> getAllTaskList(PageForm<Cscp> form) {
        PageActiviti<PageData> pageActiviti = new PageActiviti<>();
        if (form.getT() == null) {
            form.setT(new Cscp());
        }

        IPage iPage = new Page(form.getCurrentPage(), form.getPageSize());

        IPage<PageData> pageDataIPage = cscpActivitRepository.queryPageAllTaskList(iPage, form.getT());
        List<PageData> records = pageDataIPage.getRecords();
        // 设置当前环节
        for (PageData temp : records) {
            String procInstId = temp.getString("PROCESSINSTID");

            ProcessInstance processInstance = runtimeService.createProcessInstanceQuery().processInstanceId(procInstId).singleResult();
            if (processInstance == null) {
                temp.put("CURRENT_NODE_ID", "");
                temp.put("CURRENT_NODE_NAME", "");
            } else {
                List<Task> taskList = taskService.createTaskQuery().processInstanceId(procInstId).list();
                if (taskList == null || taskList.size() <= 0) {
                    continue;
                }
                Task taskTemp = taskList.get(0);
                temp.put("CURRENT_NODE_ID", taskTemp.getTaskDefinitionKey());
                temp.put("CURRENT_NODE_NAME", taskTemp.getName());
            }
        }

        pageActiviti = PageActiviti.listToPage(records);
//        if(null != listPageData) {
//            pageActiviti = PageActiviti.toPage(pq, listPageData);
//        }

        return pageActiviti;
    }

    @Override
    public PageActiviti<PageData> getOverTimeTask(PageForm<Cscp> form) {
        PageActiviti<PageData> pageActiviti = new PageActiviti<>();
        if (form.getT() == null) {
            form.setT(new Cscp());
        }

        IPage iPage = new Page(form.getCurrentPage(), form.getPageSize());
        List<PageData> records = cscpActivitRepository.queryPageOverTimeTask(iPage, form.getT()).getRecords();
        for (PageData pd : records) {
            String limitTime = pd.getString("LIMITTIME");
            if (limitTime == null) {
                continue;
            }
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            Date limit = null;
            Date now = new Date();
            try {
                limit = sdf.parse(limitTime);
            } catch (ParseException e) {
                e.printStackTrace();
            }
            Long otms = null;
            if (limit != null) {
                otms = cscpHolidayService.getOverTimeMilliScnd(now, limit);
            }
            if (otms == null) {
                otms = 0L;
            }
            Double othour = otms / (3600000.0);
            pd.put("OVER_TIME_HOUR_DOUBLE", othour);
            pd.put("OVER_TIME_HOUR_STR", String.format("%.1f", othour));
        }

        for (PageData pd : records) {
            if (!pd.containsKey("DEALUSERID")) {
                pd.put("DEALUSERID", "");
                pd.put("DEALUSERNAME", "");
            } else {
                String duid = pd.getString("DEALUSERID");
                if (duid != null) {
                    Long duidint = Long.parseLong(duid);
                    CscpUserDTO userDetail = cscpUserService.findByUserId(duidint);
                    StringBuilder usb = new StringBuilder("");
                    if (userDetail != null) {
                        usb.append(userDetail.getRealName());
                    }
                    pd.put("DEALUSERNAME", usb.toString());
                }

            }
        }

        pageActiviti = PageActiviti.listToPage(records);
//        if(null != listPageData) {
//            pageActiviti = PageActiviti.toPage(pq, listPageData);
//        }

        return pageActiviti;
    }

    @Override
    public  PageResult<CscpProcBaseDto> getCscpProcBaseForPage(TaskQueryParam  taskQueryParam, BasePageForm basePageForm) {
        Page<CscpProcBase> page = new Page<CscpProcBase>(basePageForm.getCurrentPage(), basePageForm.getPageSize());

        LambdaQueryWrapper<CscpProcBase> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.like(StringUtils.isNotEmpty(taskQueryParam.getTitle()),CscpProcBase::getTitle,taskQueryParam.getTitle())
                .like(StringUtils.isNotEmpty(taskQueryParam.getCreateName()),CscpProcBase::getCreateName,taskQueryParam.getCreateName())
                .like(StringUtils.isNotEmpty(taskQueryParam.getDepartmentName()),CscpProcBase::getDepartmentName,taskQueryParam.getDepartmentName())
                .eq( taskQueryParam.getBpmStatus() != null,CscpProcBase::getBpmStatus,taskQueryParam.getBpmStatus())
                .ge( StringUtils.isNotEmpty(taskQueryParam.getProcessStartCreatTime()),CscpProcBase::getCreateTime,taskQueryParam.getProcessStartCreatTime())
                .le( StringUtils.isNotEmpty(taskQueryParam.getProcessEndCreatTime()),CscpProcBase::getCreateTime,taskQueryParam.getProcessEndCreatTime())
                .eq(StringUtils.isNotEmpty(taskQueryParam.getBusinessType()),CscpProcBase::getTableName,taskQueryParam.getBusinessType());

        if (DataFilterThreadLocal.get() == null) {
            lambdaQueryWrapper.eq(CscpProcBase::getCreateBy,SecurityUtils.getCurrentUserId());
        }
        lambdaQueryWrapper.orderByDesc(CscpProcBase::getCreateTime);
        Page<CscpProcBase> cscpProcBasePage = cscpProcBaseRepository.selectPage(page, lambdaQueryWrapper);
        List<CscpProcBase> cscpProcBaseList = cscpProcBasePage.getRecords();
        List<CscpProcBaseDto> cscpProcBaseDtoList = ListCopyUtil.copy(cscpProcBaseList, CscpProcBaseDto.class);

        if (CollectionUtil.isNotEmpty(cscpProcBaseList) && StringUtils.isNotEmpty(taskQueryParam.getBusinessType())){
            String tableNameByModelDataType = cscpProcBaseService.getTableNameByModelDataType(taskQueryParam.getBusinessType());
            if (StringUtils.isNotEmpty(tableNameByModelDataType)){
                /*List<Long> idsList = new ArrayList<>();
                cscpProcBaseDtoList.forEach(cscpProcBaseDto -> idsList.add(Long.valueOf(cscpProcBaseDto.getFormDataId())));

                DynamicEntity dynamicEntity = new DynamicEntity();
                dynamicEntity.setTableName(tableNameByModelDataType);
                dynamicEntity.setIds(idsList);
                List<Map> dataList = dynamicMapper.selectDataList(dynamicEntity);
                dataList.forEach(map -> {
                    String id = MapUtils.getString(map,"id");
                    cscpProcBaseDtoList.forEach(cscpProcBaseDto -> {
                        if (id.equals(cscpProcBaseDto.getFormDataId())){
                            cscpProcBaseDto.setBusinessDataMap(map);
                        }
                    });
                });*/
            }

        }

        PageResult<CscpProcBaseDto> result = new PageResult<>(cscpProcBaseDtoList,cscpProcBasePage.getTotal(),cscpProcBasePage.getTotal());
        return result;
    }

    @Override
    public List<Map<String, Object>> getProcessGraphCount(Map<String, Object> params) {
        String years = MapUtils.getString(params, "years");
        if (StringUtils.isEmpty(years)) {
            years = DateUtil.format(new Date(), "yyyy");
        }
        List<String> yearsList = new ArrayList<>();
        for (int i = 1; i < 13; i++) {
            if (i < 10) {
                yearsList.add(years + "-0" + i);
            } else {
                yearsList.add(years + "-" + i);
            }
        }
        List<Map<String, Object>> list = new ArrayList<>();
        yearsList.forEach(month -> {
            params.put("month", month);
            Integer count = cscpActivitRepository.queryProcessGraphCount(params);
            Map<String, Object> temp = new HashMap<>();
            temp.put("value", count);
            temp.put("name", month.substring(5));
            list.add(temp);
        });

        return list;
    }

    @Override
    public List<Map<String, Object>> getProcessPieCount(Map<String, Object> params) {
        String years = MapUtils.getString(params, "years");
        String appCode = MapUtils.getString(params, "appCode");
        if (StringUtils.isEmpty(years)) {
            years = DateUtil.format(new Date(), "yyyy");
        }
        params.put("years", years);
        List<Map<String, Object>> list = new ArrayList<>();
        if (StringUtils.isEmpty(appCode)) {
            List<CscpApp> appList = cscpAppRepository.selectList(null);
            appList.forEach(app -> {
                params.put("appCode", app.getAppCode());
                Integer count = cscpActivitRepository.queryProcessPieCount(params);
                Map<String, Object> temp = new HashMap<>();
                temp.put("value", count);
                temp.put("name", app.getAppName());
                temp.put("id", app.getAppCode());
                list.add(temp);
            });
        } else {
            String parentId = MapUtils.getString(params, "parentId");
            if (StringUtils.isEmpty(parentId)) {
                params.put("parentId", "0");
            }
            //此处需要修改
            List<CscpProcType> typeList = new ArrayList<>();//cscpProcTypeRepository.queryAllType(params);
            typeList.forEach(type -> {
                params.put("typeId", type.getTypeId());
                Integer count = cscpActivitRepository.queryProcessPieCount(params);
                Map<String, Object> temp = new HashMap<>();
                temp.put("value", count);
                temp.put("name", type.getTypeName());
                temp.put("id", type.getTypeId());
                list.add(temp);
            });
        }
        return list;
    }

    @Override
    public PageData getDynamicUrl(String id) {
        PageData pageData = new PageData();
        CscpInformation cscpInformation = cscpInformationRepository.selectById(id);
        String bid = cscpInformation.getBid();
        String[] bidArr = bid.split("\\|");
        if (bidArr.length < 2) {
            throw new CustomException("数据读取异常");
        }
        String processInstanceId = bidArr[0];
        String taskId = bidArr[1];
        CscpProcBase cscpProcBase = cscpProcBaseService.getProcBaseByProcInstId(processInstanceId);
        if (cscpProcBase == null) {
            throw new CustomException("基础读取异常");
        }
        CscpProc cscpProc = activitiBpmnDefinitionService.getBpmnModelByProcessDefinitionId(cscpProcBase.getProcDefId());
        if (cscpProc == null) {
            throw new CustomException("流程模型不存在");
        }
        pageData.put("processDefinitionKey", cscpProc.getProcessDefinitionKey());
        pageData.put("processInstanceId", processInstanceId);
        pageData.put("taskId", taskId);
        pageData.put("formId", cscpProcBase.getFormDefId());
        Task task = taskService.createTaskQuery().taskId(taskId).singleResult();
        if (task != null) {
            pageData.put("status", "todo");
            pageData.put("nodekey", task.getTaskDefinitionKey());
        } else {
            pageData.put("status", "finished");
        }

        //更新已读状态
        CscpInformation cscpInformation1 = new CscpInformation();
        cscpInformation1.setReadStatus("1");
        LambdaQueryWrapper<CscpInformation> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(CscpInformation::getBid, cscpInformation.getBid());
        cscpInformationService.update(cscpInformation1, lambdaQueryWrapper);

        return pageData;
    }

    @Override
    public PageActiviti<HistoricTaskInstance> getActivitiFinishedTaskListForPage(PageForm<TaskQueryParam> form) {
        PageActiviti<HistoricTaskInstance> pageActiviti = new PageActiviti<>();
        if (form.getT() == null) {
            form.setT(new TaskQueryParam());
        }
        PageQuery pq = PageQuery.toPageQuery(form, TaskQueryParam.class);
        PageHelper.startPage(form.getCurrentPage(), form.getPageSize());
        List<HistoricTaskInstance> list = cscpActivitRepository.queryPageFinishedTaskListForPage(pq);
        pageActiviti = PageActiviti.listToPage(list);
//        if(list != null){
//            pageActiviti = PageActiviti.toPage(pq,list);
//        }
        return pageActiviti;
    }

    @Override
    public List<CscpProc> getLeatestProcess(long uid) {
        List<CscpProcType> listCscpProcType = cscpProcTypeRepository.selectList(null);
        List<String> ltid = new ArrayList<>();
        for (CscpProcType pType : listCscpProcType) {
            /*if (!cscpProcPermissionsSrvice.checkUserProcessTypePermissions("" + uid, pType.getTypeId(), Constants.PERMISSION_OBJ_TYPE_PROC_TYPE)) {
                continue;
            }*/
            ltid.add(pType.getTypeId());
        }
        if (ltid.size() <= 0) {
            ltid.add("~");
        }

        LambdaQueryWrapper<CscpProc> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.in(CscpProc::getTypeId, ltid);
        return cscpActivitRepository.selectList(lambdaQueryWrapper);
    }

    @Override
    public AssigneeType getNodeAssigneesType(String taskId, String nodeKey) {
        CandidateRuleParam param = new CandidateRuleParam();
        // 任务ID
        if (ActivitiUtils.isEmpty(taskId)) {
            throw new IllegalArgumentException("没有设置任务ID");
        }

        // 查找任务
        Task task = taskService.createTaskQuery().taskId(taskId).singleResult();

        if (task == null) {
            //throw new IllegalArgumentException("没有找到流程任务");
            throw new BusinessException("该流程任务已提交，请回到首页刷新页面");
        }
        boolean hasMulti = false;
        BpmnNode currentNode = activitiBpmnDefinitionService.getBpmnNodeByNodeKey(task.getProcessDefinitionId(), nodeKey);


        if (currentNode != null) {
            hasMulti = currentNode.getMultiInstance() == 1;
            if (BpmnElements.isCallActivity(currentNode.getType())) {
                String peocessKey = currentNode.getSubProcessDefinitionKey();
                CscpProc proc  = activitiBpmnDefinitionService.getBpmnModelMaxVersionByProcessDefinitionKey(peocessKey);
                currentNode = activitiBpmnDefinitionService.getFirstNode(proc.getProcessDefinitionId());
            }else {
                BpmnNode firstNode = activitiBpmnDefinitionService.getFirstNode(task.getProcessDefinitionId());
                if (currentNode.getId().equalsIgnoreCase(firstNode.getId())){
                    //   如果回到流程起草人
                    currentNode.setAssigneeRule(ASSINEG_START_RULE);
                    currentNode.setAssignees(ASSINEG_START);
                }

            }
        } else {
            //  可能情况是下一步环节回到了父流程
            // 1  找到父流程的实例id
            LambdaQueryWrapper<CscpProcBase> lambdaQueryWrapper = new LambdaQueryWrapper<>();
            lambdaQueryWrapper.eq(CscpProcBase::getProcInstId, task.getProcessInstanceId())
                    .select(CscpProcBase::getRootProcInstId);
            CscpProcBase cscpProcBase = cscpProcBaseRepository.selectOneOnlyAddTenantId(lambdaQueryWrapper);
            String processDefinitionId = runtimeService.createProcessInstanceQuery().processInstanceId(cscpProcBase.getRootProcInstId()).singleResult().getProcessDefinitionId();
            param.setSuperProcessInstanceId(cscpProcBase.getRootProcInstId());
            currentNode = activitiBpmnDefinitionService.getBpmnNodeByNodeKey(processDefinitionId, nodeKey);
            if (currentNode != null) {
                if (BpmnElements.isCallActivity(currentNode.getType())) {
                    currentNode = activitiBpmnDefinitionService.getFirstNode(currentNode.getSubProcessDefinitionId());
                }else {
                    BpmnNode firstNode = activitiBpmnDefinitionService.getFirstNode(processDefinitionId);
                    if (currentNode.getId().equalsIgnoreCase(firstNode.getId())){
                        //   如果回到流程起草人
                        currentNode.setAssigneeRule(ASSINEG_START_RULE);
                        currentNode.setAssignees(ASSINEG_START);
                    }

                }
            }
            hasMulti = currentNode.getMultiInstance() == 1;
        }


        //判断多实例

        param.setProcessNode(currentNode);
        param.setProcessInstanceId(task.getProcessInstanceId());

        AssigneeType assigneeType = candidateService.getAssigneeType(param);
        if (assigneeType == null || CollectionUtils.isEmpty(assigneeType.getNodePeopleTypeList())) {
            throw new BusinessException("流程中taskid为{}，任务名称为{}找不到处理人，请确实是否配置处理人", taskId, nodeKey);
        }
        assigneeType.setHasMulti(hasMulti);
        return assigneeType;
    }

    @Override
    public AssigneeType getNodeAssigneesTypeByNodeKey(String processDefinitionId,String nodeKey) {
        CandidateRuleParam param = new CandidateRuleParam();

        boolean hasMulti = false;
        BpmnNode currentNode = activitiBpmnDefinitionService.getBpmnNodeByNodeKey(processDefinitionId, nodeKey);

        hasMulti = currentNode.getMultiInstance() == 1;
        if (BpmnElements.isCallActivity(currentNode.getType())) {
            currentNode = activitiBpmnDefinitionService.getFirstNode(currentNode.getSubProcessDefinitionId());
        }

        //判断多实例

        param.setProcessNode(currentNode);
        param.setProcessInstanceId("");

        AssigneeType assigneeType = candidateService.getAssigneeType(param);
        if (assigneeType == null || CollectionUtils.isEmpty(assigneeType.getNodePeopleTypeList())) {
            throw new BusinessException("任务名称为{}找不到处理人，请确实是否配置处理人", nodeKey);
        }
        assigneeType.setHasMulti(hasMulti);
        return assigneeType;
    }


    /**
     * 给app的详情页面提供展示数据
     *
     * @param processDefinitionId
     * @param taskDefinitionKey
     * @param formDataId
     * @return
     */
    @Override
    public AppDetailParam getCurrentNodeFormItems(String processDefinitionId, String taskDefinitionKey, String formDataId) {
        LambdaQueryWrapper<CscpProcBase> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(CscpProcBase::getFormDataId, formDataId).select(CscpProcBase::getFormDefId, CscpProcBase::getId);
        CscpProcBase cscpProcBase = cscpProcBaseService.getOne(lambdaQueryWrapper);
        String formId = cscpProcBase.getFormDefId();
        //获取数据
        Map<String, Object> dataMap = ActivitiCoreServiceImpl.getMainFormMapData(formId, formDataId);

        AppDetailParam appDetailParam = new AppDetailParam();
        String  appDataContent = cscpProcBaseDataContentService.getOnlyAppDataContent(cscpProcBase.getId());
        appDetailParam.setAppDataContent(appDataContent);
        BpmnNode bpmnNode = null;
        if(StringUtils.isNotEmpty(taskDefinitionKey) && !"null".equals(taskDefinitionKey)){
            bpmnNode = activitiBpmnDefinitionService.getBpmnNodeByNodeKey(processDefinitionId, taskDefinitionKey);;
        }else {
            bpmnNode = activitiBpmnDefinitionService.getFirstNode(processDefinitionId);
        }
        String items = bpmnNode.getAppFormItems();
        if (StringUtils.isEmpty(items)) {
            //  app在流程节点的设置，取不到的话直接取第一次设置的
            bpmnNode = activitiBpmnDefinitionService.getFirstNode(processDefinitionId);
            items = bpmnNode.getAppFormItems();
            if (StringUtils.isEmpty(items)) {
                throw new BusinessException(ResultCode.PROC_NODE_NO_FORM);
            }
        }
        List<FormItems> formItemsList = JsonUtils.jsonToList(items, FormItems.class);
        List<AppDetailValue> appDetailValueList = new ArrayList<>();
        for (FormItems formItem : formItemsList) {
            String modelItemId = formItem.getModelItemId();
            boolean disabled = formItem.getDisabled();
            String openType = formItem.getOpinionType();
            if (StringUtils.isEmpty(modelItemId) || "null".equals(modelItemId)) {
                if ("sign".equals(openType) && !disabled) {
                    //当前需要填充意见了
                    appDetailParam.setCommentsFormId(formItem.getFieldId());
                }
            } else if (formItem.getVisible() && !"title".equals(modelItemId)) {
                AppDetailValue appDetailValue = BeanConvertUtils.copyProperties(formItem, AppDetailValue.class);
                String value = MapUtils.getString(dataMap, modelItemId);
                appDetailValue.setFiledValue(value);
                appDetailValueList.add(appDetailValue);
            }
        }
        appDetailParam.setTitle(MapUtils.getString(dataMap, "title"));
        appDetailParam.setAppDetailValueList(appDetailValueList);
        appDetailParam.setFormDataId(Long.valueOf(formDataId));
        return appDetailParam;

    }


    @Override
    public AppDetailParam getFirstCurrentNodeFormItems(String processDefinitionKey, String formId,String inboxId) {
        LambdaQueryWrapper<CformForm> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(CformForm::getFormId, formId).select(CformForm::getAppPubContent);
        CformForm cf = cformFormService.selectOneNoAdd(lambdaQueryWrapper);
        AppDetailParam appDetailParam = new AppDetailParam();
        appDetailParam.setAppDataContent(cf.getAppPubContent());

        CscpProc cscpProc = activitiBpmnDefinitionService.getBpmnModelMaxVersionByProcessDefinitionKey(processDefinitionKey);
        BpmnNode bpmnNode = activitiBpmnDefinitionService.getFirstNode(cscpProc.getProcessDefinitionId());
        String items = bpmnNode.getAppFormItems();
        if (StringUtils.isEmpty(items)) {
            throw new BusinessException(ResultCode.PROC_NODE_NO_FORM);
        }
        Map<String, Object> formDataByInbox = null;
        if (StringUtils.isNotEmpty(inboxId)){
            formDataByInbox = cFormDataService.getFormDataByInbox(inboxId);
        }

        List<FormItems> formItemsList = JsonUtils.jsonToList(items, FormItems.class);
        List<AppDetailValue> appDetailValueList = new ArrayList<>();
        for (FormItems formItem : formItemsList) {
            String modelItemId = formItem.getModelItemId();
            boolean disabled = formItem.getDisabled();
            String openType = formItem.getOpinionType();
            if (formItem.getVisible()) {
                AppDetailValue appDetailValue = BeanConvertUtils.copyProperties(formItem, AppDetailValue.class);
                if (StringUtils.isNotEmpty(inboxId)){
                    // 如果来自于收件湘的情况，直接过来正文附件，直接显示标题
                    if ("title".equals(modelItemId)){
                        appDetailValue.setFiledValue(MapUtils.getString(formDataByInbox,"title"));
                    }
                    if ("正文".equals(formItem.getFieldName()) ||"附件".equals(formItem.getFieldName()) ){
                        continue;
                    }
                 }
                appDetailValueList.add(appDetailValue);
            }
        }
        appDetailParam.setAppDetailValueList(appDetailValueList);
        appDetailParam.setFormDataId(SnowflakeIdUtil.getSnowFlakeLongId());

        return appDetailParam;
    }

    @Override
    public PageResult<Map<String, Object>> getBusinessCscpProcBaseForPage(Map<String, Object> mapParam, BasePageForm basePageForm) {
        Page<Map<String, Object>> page = new Page<>(basePageForm.getCurrentPage(), basePageForm.getPageSize());
        List<Map<String, Object>> dataList = new ArrayList<>();
        long totle = 0;
        long currentPage = 0;
        String businessType = MapUtils.getString(mapParam,"businessType");
        if (StringUtils.isNotEmpty(businessType)){
            //第一条sql语句不过滤
            DataNoFilterThreadLocal.set();
            String tableNameByModelDataType = cscpProcBaseService.getTableNameByModelDataType(businessType);
            if (StringUtils.isNotEmpty(tableNameByModelDataType)){
                QueryDynamicEntity dynamicEntity = new QueryDynamicEntity();
                dynamicEntity.setTableName(tableNameByModelDataType);
                // 验证表名是否sql注入;

                mapParam.remove("permissionDataWebCompontent");
                mapParam.remove("businessType");
                mapParam.remove("currentPage");
                mapParam.remove("pageSize");

                // 按照表的列名分组
                DataNoFilterThreadLocal.set();
                Map<String, List<CformFieldSearchVO>> collect = cformFieldAccessService.queryCformSearchByBusinessType(businessType)
                        .stream().collect(Collectors.groupingBy(CformFieldSearchVO::getType));

                // 拼接Where后面的sql
                StringBuilder whereSqlString = new StringBuilder();
                for (Map.Entry<String, Object> entry : mapParam.entrySet()) {
                    String value = String.valueOf(entry.getValue()) ;
                    if (StringUtils.isEmpty(value)){
                        continue;
                    }
                    List<CformFieldSearchVO> formFieldSearchVOList = collect.get(entry.getKey());
                    if (CollectionUtils.isEmpty(formFieldSearchVOList)) {
                        continue;
                        //throw new BusinessException("搜索入参不合法：没有找到该列对应的查询动态表单实体:{}" + entry.getKey());
                    }
                    if (formFieldSearchVOList.size() > 1) {
                        throw new BusinessException("搜索入参不合法：该列对应的查询动态表单实体只允许1条:{}" + entry.getKey());
                    }
                    // 转换成对应的操作符："like"、"="、"~"
                    String operateType = SearchTypeToOperatorUtils.searchTypeToOperator(formFieldSearchVOList.get(0).getSearchType());
                    //时间分割
                    if ("~".equals(operateType)) {
                        if ("~".equals(value)){
                            continue;
                        }
                        String[] timePeriod = String.valueOf(entry.getValue()).split("~");
                        String startTime = timePeriod[0];
                        String endTime = "";
                        if(timePeriod.length>1) {
                            endTime = timePeriod[1];
                        }else{
                            //只查一天
                            endTime=timePeriod[0];
                        }

                        // 防止查询的条件有sql注入，只需要验证条件和值
                        StringBuilder timeSql = new StringBuilder();
                        timeSql.append(entry.getKey()).append(" ").append(startTime).append(" ")
                                .append(entry.getKey()).append(" ").append(endTime);
                        SqlInjectionUtil.filterContent(timeSql.toString());
                        //处理只传了年月日但是数据库里包含时分秒的场景 例如2022-11-18
                        if(startTime.length()==10){

                            whereSqlString.append(" and ").append(entry.getKey()).append(" >= ").append("str_to_date('").append(startTime).append("','%Y-%m-%d')")
                                    .append(" and ").append(entry.getKey()).append(" <= ").append("str_to_date('").append(endTime).append("','%Y-%m-%d')+1");

                        }else {
                            whereSqlString.append(" and ").append(entry.getKey()).append(" >= ").append("'").append(startTime).append("'")
                                    .append(" and ").append(entry.getKey()).append(" <= ").append("'").append(endTime).append("'");
                        }
                    }
                    else if ("like".equals(operateType)) {
                        // 防止查询的条件有sql注入，只需要验证条件和值
                        StringBuilder likeSql = new StringBuilder();
                        likeSql.append(entry.getKey()).append(" ").append(entry.getValue());
                        SqlInjectionUtil.filterContent(likeSql.toString());

                        whereSqlString.append(" and ").append(entry.getKey()).append(" like ")
                                .append("'").append("%").append(entry.getValue()).append("%").append("'");
                    }
                    else {
                        // 防止查询的条件有sql注入，只需要验证条件和值
                        StringBuilder stringBuilder = new StringBuilder();
                        stringBuilder.append(entry.getKey()).append(" ").append(entry.getValue());
                        SqlInjectionUtil.filterContent(stringBuilder.toString());

                        whereSqlString.append(" and ").append(entry.getKey()).append(" = ")
                                .append("'").append(entry.getValue()).append("'");
                    }
                }

                // 拼接createBy的sql
                if (DataFilterThreadLocal.get() == null) {
                    whereSqlString.append(" and ").append("create_by").append(" = ")
                            .append("'").append(SecurityUtils.getCurrentUserId()).append("'");
                }else{
                    List<SysDataRule> list = DataFilterThreadLocal.get().getDataRuleListuleList();
                    list.forEach(item->{
                        if("company_id".equals(item.getRuleColumn())){
                            whereSqlString.append(" and ").append("company_id").append(" = ")
                                    .append("'").append(SecurityUtils.getCurrentCompanyId()).append("'");
                        }else if("department_id".equals(item.getRuleColumn())){
                            whereSqlString.append(" and ").append("department_id").append(" = ")
                                    .append("'").append(SecurityUtils.getCurrentCscpUserDetail().getDepartmentId()).append("'");
                        }
                    });
                }
                // 完整sql
                dynamicEntity.setWhereSql(whereSqlString.toString());
                IPage<Map<String, Object>> pageDataList = dynamicMapper.selectDataList(page, dynamicEntity);
                totle = pageDataList.getTotal();
                dataList = pageDataList.getRecords();
                currentPage = pageDataList.getCurrent();
                if (CollectionUtil.isNotEmpty(dataList)){
                    List<String> businessIds = dataList.stream().map(map -> {
                        return MapUtils.getString(map,"id");
                    }).collect(Collectors.toList());
                    LambdaQueryWrapper<CscpProcBase> lambdaQueryWrapper = new LambdaQueryWrapper<>();
                    lambdaQueryWrapper.in(CscpProcBase::getFormDataId,businessIds);
                    List<CscpProcBase> cscpProcBaseList = cscpProcBaseRepository.selectListNoAdd(lambdaQueryWrapper);
                    dataList.forEach(map -> {
                        String deptName = MapUtil.getStr(map, "department_name");
                        if(map.get("department_id") !=null && StringUtils.isEmpty(deptName)) {
                            CscpOrg cscpOrg = cscpOrgService.getById(map.get("department_id").toString());
                            if (cscpOrg != null) {
                                deptName = cscpOrg.getOrgName();
                            }
                        }
                        map.put("department_name",deptName);
                        String businessId =  MapUtils.getString(map,"id");
                        BusinessCscpProcBaseDto businessCscpProcBaseDto = new BusinessCscpProcBaseDto();
                        businessCscpProcBaseDto.setBusinessDataMap(map);
                        for (int i = 0; i< cscpProcBaseList.size(); i ++){
                            CscpProcBase cscpProcBase = cscpProcBaseList.get(i);
                            String formDataId = cscpProcBase.getFormDataId();
                            if (businessId.equals(formDataId)) {
                                map.put("actDefUniqueName", cscpProcBase.getActDefUniqueName());
                                map.put("processDefinitionKey", cscpProcBase.getModelkey());
                                map.put("cscpProcBaseId", cscpProcBase.getId());
                                map.put("rootProcessInstanceId", cscpProcBase.getRootProcInstId());
                                map.put("procInstId", cscpProcBase.getProcInstId());
                                map.put("create_time", cscpProcBase.getCreateTime());
                                map.put("CREATE_TIME", cscpProcBase.getCreateTime());


                                break;
                            }
                        }
                    });
                }

            }
        }
        PageResult<Map<String, Object>> result = new PageResult<>(dataList,totle,currentPage);
        return result;
    }

    @Override
    public PageResult<HashMap<String, Object>> getBusinessCscpProcPage(Map<String, Object> mapParam, BasePageForm basePageForm) {
        Page<Map<String, Object>> page = new Page<>(basePageForm.getCurrentPage(), basePageForm.getPageSize());
        List<HashMap<String, Object>> dataList = new ArrayList<>();
        long totle = 0;
        long currentPage = 0;
        String businessType = MapUtils.getString(mapParam,"businessType");
        String accessType = MapUtils.getString(mapParam,"accessType");
        if (StringUtils.isNotEmpty(businessType)) {
            //第一条sql语句不过滤
            String tableNameByModelDataType = cscpProcBaseService.getTableNameByModelDataType(businessType);
            if (StringUtils.isNotEmpty(tableNameByModelDataType)) {
                QueryDynamicEntity dynamicEntity = new QueryDynamicEntity();
                dynamicEntity.setTableName(tableNameByModelDataType);
                // 验证表名是否sql注入;
                mapParam.remove("permissionDataWebCompontent");
                mapParam.remove("businessType");
                mapParam.remove("currentPage");
                mapParam.remove("pageSize");
                mapParam.remove("accessType");
                mapParam.remove("tableName");
                // 按照表的列名分组
                Map<String, List<CformFieldSearchVO>> collect = cformFieldAccessService.queryCformSearchByBusinessTypeNew(businessType, accessType)
                        .stream().collect(Collectors.groupingBy(CformFieldSearchVO::getType));

                // 拼接Where后面的sql
                StringBuilder whereSqlString = new StringBuilder();
                List<CscpProcQueryDTO> queryList = new ArrayList<>();
                for (Map.Entry<String, Object> entry : mapParam.entrySet()) {
                    CscpProcQueryDTO dto = new CscpProcQueryDTO();
                    String value = String.valueOf(entry.getValue());
                    if (StringUtils.isEmpty(value)) {
                        continue;
                    }
                    dto.setFieldValue(value);
                    List<CformFieldSearchVO> formFieldSearchVOList = collect.get(entry.getKey());
                    if (CollectionUtils.isEmpty(formFieldSearchVOList)) {
                        if ("auditor_name".equals(entry.getKey())) {
                            dto.setFieldKey("content." + entry.getKey());
                            dto.setFieldOperateType("like");
                        } else {
                            dto.setFieldOperateType("=");
                            dto.setFieldKey("b." + entry.getKey());
                        }
                    } else {
                        String operateType = SearchTypeToOperatorUtils.searchTypeToOperator(formFieldSearchVOList.get(0).getSearchType());
                        dto.setFieldOperateType(operateType);
                        dto.setFieldKey("t." + entry.getKey());
                    }
                    // 转换成对应的操作符："like"、"="、"~"
                    queryList.add(dto);
                }
                queryList.forEach(item -> {
                    //时间分割
                    if ("~".equals(item.getFieldOperateType())) {
                        if (!"~".equals(item.getFieldValue())) {
                            String[] timePeriod = String.valueOf(item.getFieldValue()).split("~");
                            String startTime = timePeriod[0];
                            String endTime = "";
                            if (timePeriod.length > 1) {
                                endTime = timePeriod[1];
                            } else {
                                //只查一天
                                endTime = timePeriod[0];
                            }
                            // 防止查询的条件有sql注入，只需要验证条件和值
//                            StringBuilder timeSql = new StringBuilder();
//                            timeSql.append(item.getFieldKey()).append(" ").append(startTime).append(" ")
//                                    .append(item.getFieldKey()).append(" ").append(endTime);
//                            SqlInjectionUtil.filterContent(timeSql.toString());
                            //处理只传了年月日但是数据库里包含时分秒的场景 例如2022-11-18
                            if (startTime.length() == 10) {

                                whereSqlString.append(" and ").append(item.getFieldKey()).append(" >= ").append("str_to_date('").append(startTime).append("','%Y-%m-%d')")
                                        .append(" and ").append(item.getFieldKey()).append(" <= ").append("str_to_date('").append(endTime).append("','%Y-%m-%d')+1");

                            } else {
                                whereSqlString.append(" and ").append(item.getFieldKey()).append(" >= ").append("'").append(startTime).append("'")
                                        .append(" and ").append(item.getFieldKey()).append(" <= ").append("'").append(endTime).append("'");
                            }
                            ;
                        }
                    } else if ("like".equals(item.getFieldOperateType())) {
                        // 防止查询的条件有sql注入，只需要验证条件和值
//                        StringBuilder likeSql = new StringBuilder();
//                        likeSql.append(item.getFieldKey()).append(" ").append(item.getFieldValue());
//                        SqlInjectionUtil.filterContent(likeSql.toString());

                        whereSqlString.append(" and ").append(item.getFieldKey()).append(" like ")
                                .append("'").append("%").append(item.getFieldValue()).append("%").append("'");
                    } else {
                        // 防止查询的条件有sql注入，只需要验证条件和值
//                        StringBuilder stringBuilder = new StringBuilder();
//                        stringBuilder.append(item.getFieldKey()).append(" ").append(item.getFieldValue());
//                        SqlInjectionUtil.filterContent(stringBuilder.toString());

                        whereSqlString.append(" and ").append(item.getFieldKey()).append(" = ")
                                .append("'").append(item.getFieldValue()).append("'");
                    }
                });
                List<Long> companyIdList = new ArrayList<>();
                //   注释下，只获取本人的创建数据
                List<CscpDistributeBaseUserDTO> cscpDistributeBaseUserList = new ArrayList<>();
                //List<CscpDistributeBaseUserDTO> cscpDistributeBaseUserList = cscpUserRoleService.queryCscpUserAllByRoleName("单位查询管理员",new ArrayList<Long>(),companyIdList);
                if(cscpDistributeBaseUserList!= null && cscpDistributeBaseUserList.size()>0){
                    String name = cscpDistributeBaseUserList.stream().map(CscpDistributeBaseUserDTO::getUserName).collect(Collectors.joining(","));
                    List<String> userIdArry = new ArrayList<>();
                    cscpDistributeBaseUserList.forEach(item->{
                        userIdArry.add(item.getUserId().toString());
                    });
                    if (userIdArry.contains(String.valueOf(SecurityUtils.getCurrentUserId()))) {
                        whereSqlString.append(" and ").append("t.company_id").append(" = ")
                                .append("'").append(SecurityUtils.getCurrentCompanyId()).append("'");
                    } else {
                        whereSqlString.append(" and ").append("t.create_by").append(" = ")
                                .append("'").append(SecurityUtils.getCurrentUserId()).append("'");
                    }

                }else{
                    whereSqlString.append(" and ").append("t.create_by").append(" = ")
                            .append("'").append(SecurityUtils.getCurrentUserId()).append("'");
                }

                // 完整sql
                dynamicEntity.setWhereSql(whereSqlString.toString());
                IPage<HashMap<String, Object>> pageDataList = dynamicMapper.selectDataFormList(page, dynamicEntity);
                totle = pageDataList.getTotal();
                dataList = pageDataList.getRecords();
                currentPage = pageDataList.getCurrent();
            }
        }
        PageResult<HashMap<String, Object>> result = new PageResult<>(dataList,totle,currentPage);
        return result;
    }


    @Override
    public PageResult<HashMap<String, Object>> getBusinessCscpProcPageByCompanyId(Map<String, Object> mapParam, BasePageForm basePageForm) {
        Page<Map<String, Object>> page = new Page<>(basePageForm.getCurrentPage(), basePageForm.getPageSize());
        List<HashMap<String, Object>> dataList = new ArrayList<>();
        long totle = 0;
        long currentPage = 0;
        String businessType = MapUtils.getString(mapParam,"businessType");
        String accessType = MapUtils.getString(mapParam,"accessType");
        if (StringUtils.isNotEmpty(businessType)) {
            //第一条sql语句不过滤
            String tableNameByModelDataType = cscpProcBaseService.getTableNameByModelDataType(businessType);
            if (StringUtils.isNotEmpty(tableNameByModelDataType)) {
                QueryDynamicEntity dynamicEntity = new QueryDynamicEntity();
                dynamicEntity.setTableName(tableNameByModelDataType);
                // 验证表名是否sql注入;
                mapParam.remove("permissionDataWebCompontent");
                mapParam.remove("businessType");
                mapParam.remove("currentPage");
                mapParam.remove("pageSize");
                mapParam.remove("accessType");
                mapParam.remove("tableName");
                // 按照表的列名分组
                Map<String, List<CformFieldSearchVO>> collect = cformFieldAccessService.queryCformSearchByBusinessTypeNew(businessType, accessType)
                        .stream().collect(Collectors.groupingBy(CformFieldSearchVO::getType));

                // 拼接Where后面的sql
                StringBuilder whereSqlString = new StringBuilder();
                List<CscpProcQueryDTO> queryList = new ArrayList<>();
                for (Map.Entry<String, Object> entry : mapParam.entrySet()) {
                    CscpProcQueryDTO dto = new CscpProcQueryDTO();
                    String value = String.valueOf(entry.getValue());
                    if (StringUtils.isEmpty(value)) {
                        continue;
                    }
                    dto.setFieldValue(value);
                    List<CformFieldSearchVO> formFieldSearchVOList = collect.get(entry.getKey());
                    if (CollectionUtils.isEmpty(formFieldSearchVOList)) {
                        if ("auditor_name".equals(entry.getKey())) {
                            dto.setFieldKey("content." + entry.getKey());
                            dto.setFieldOperateType("like");
                        } else {
                            dto.setFieldOperateType("=");
                            dto.setFieldKey("b." + entry.getKey());
                        }
                    } else {
                        String operateType = SearchTypeToOperatorUtils.searchTypeToOperator(formFieldSearchVOList.get(0).getSearchType());
                        dto.setFieldOperateType(operateType);
                        dto.setFieldKey("t." + entry.getKey());
                    }
                    // 转换成对应的操作符："like"、"="、"~"
                    queryList.add(dto);
                }
                queryList.forEach(item -> {
                    //时间分割
                    if ("~".equals(item.getFieldOperateType())) {
                        if (!"~".equals(item.getFieldValue())) {
                            String[] timePeriod = String.valueOf(item.getFieldValue()).split("~");
                            String startTime = timePeriod[0];
                            String endTime = "";
                            if (timePeriod.length > 1) {
                                endTime = timePeriod[1];
                            } else {
                                //只查一天
                                endTime = timePeriod[0];
                            }
                            // 防止查询的条件有sql注入，只需要验证条件和值
//                            StringBuilder timeSql = new StringBuilder();
//                            timeSql.append(item.getFieldKey()).append(" ").append(startTime).append(" ")
//                                    .append(item.getFieldKey()).append(" ").append(endTime);
//                            SqlInjectionUtil.filterContent(timeSql.toString());
                            //处理只传了年月日但是数据库里包含时分秒的场景 例如2022-11-18
                            if (startTime.length() == 10) {

                                whereSqlString.append(" and ").append(item.getFieldKey()).append(" >= ").append("str_to_date('").append(startTime).append("','%Y-%m-%d')")
                                        .append(" and ").append(item.getFieldKey()).append(" <= ").append("str_to_date('").append(endTime).append("','%Y-%m-%d')+1");

                            } else {
                                whereSqlString.append(" and ").append(item.getFieldKey()).append(" >= ").append("'").append(startTime).append("'")
                                        .append(" and ").append(item.getFieldKey()).append(" <= ").append("'").append(endTime).append("'");
                            }
                            ;
                        }
                    } else if ("like".equals(item.getFieldOperateType())) {
                        // 防止查询的条件有sql注入，只需要验证条件和值
//                        StringBuilder likeSql = new StringBuilder();
//                        likeSql.append(item.getFieldKey()).append(" ").append(item.getFieldValue());
//                        SqlInjectionUtil.filterContent(likeSql.toString());

                        whereSqlString.append(" and ").append(item.getFieldKey()).append(" like ")
                                .append("'").append("%").append(item.getFieldValue()).append("%").append("'");
                    } else {
                        // 防止查询的条件有sql注入，只需要验证条件和值
//                        StringBuilder stringBuilder = new StringBuilder();
//                        stringBuilder.append(item.getFieldKey()).append(" ").append(item.getFieldValue());
//                        SqlInjectionUtil.filterContent(stringBuilder.toString());

                        whereSqlString.append(" and ").append(item.getFieldKey()).append(" = ")
                                .append("'").append(item.getFieldValue()).append("'");
                    }
                });
                whereSqlString.append(" and ").append("b.company_id").append(" = ")
                        .append("'").append(SecurityUtils.getCurrentCompanyId()).append("'");

                // 完整sql
                dynamicEntity.setWhereSql(whereSqlString.toString());
                IPage<HashMap<String, Object>> pageDataList = dynamicMapper.selectDataFormList(page, dynamicEntity);
                totle = pageDataList.getTotal();
                dataList = pageDataList.getRecords();
                currentPage = pageDataList.getCurrent();
            }
        }
        PageResult<HashMap<String, Object>> result = new PageResult<>(dataList,totle,currentPage);
        return result;
    }

    /**
     * 获取我说所有的起草数据
     *
     * @param pd
     * @param basePageForm
     * @return
     */
    @Override
    public PageResult<HashMap<String, Object>> getMyAllBusinessCscpProcPage( TaskQueryParam taskQueryParam, List<String> processDefinitionIdList ,BasePageForm basePageForm) {
        Page<HashMap<String, Object>> page = new Page<>(basePageForm.getCurrentPage(), basePageForm.getPageSize());
        List<HashMap<String, Object>> dataList = new ArrayList<>();
        long totle = 0;
        IPage<HashMap<String, Object>> pageDataList = cscpProcBaseService.getMyAllBusinessCscpProcPage(taskQueryParam,processDefinitionIdList,page);
        this.assembleAssigneeNames(pageDataList);
        totle = pageDataList.getTotal();
        dataList = pageDataList.getRecords();

        PageResult<HashMap<String, Object>> result = new PageResult<>(dataList,totle,pageDataList.getCurrent());
        return result;
    }

    @Override
    public Integer changePrintDownloadStatus(String processInstanceId, String isPrint,String isPrintName) {
        Integer i =cscpProcBaseRepository.changePrintDownloadStatus(processInstanceId, isPrint,isPrintName,SecurityUtils.getCurrentUserId());
        return i;
    }


    @Override
    public String appGetCurrentNodeFormItems(String processDefinitionId, String taskDefinitionKey) {
        BpmnNode bpmnNode = activitiBpmnDefinitionService.getBpmnNodeByNodeKey(processDefinitionId, taskDefinitionKey);
        String items = bpmnNode.getFormItems();
        if (StringUtils.isEmpty(items)) {
            throw new BusinessException(ResultCode.PROC_NODE_NO_FORM);
        }
        List<FormItems> formItemsList = JsonUtils.jsonToList(items, FormItems.class);
        for (FormItems formItem : formItemsList) {
            String modelItemId = formItem.getModelItemId();
            boolean disabled = formItem.getDisabled();
            String openType = formItem.getOpinionType();
            if (StringUtils.isEmpty(modelItemId) || "null".equals(modelItemId)) {
                if ("sign".equals(openType) && !disabled) {
                    //当前需要填充意见了
                    return formItem.getFieldId();
                }
            }
        }
        return "";
    }

    @Override
    public PageActiviti<CscpProc> getSubProcessModelListForPage(PageForm<CscpProc> form) {
        if (form.getT() == null) {
            form.setT(new CscpProc());
        }
        Page page = new Page(form.getCurrentPage(), form.getPageSize());
        CscpProc cscpProc = form.getT();
        IPage<CscpProc> list = cscpActivitRepository.queryPageModelList(page, cscpProc);
        PageActiviti pageActiviti = PageActivitiUtil.convertPageCfrm(list);
        return pageActiviti;
    }

    @Override
    public PageActiviti<CscpProcDTO> getSubProcessModelListForPageByTenantIdList(PageForm<CscpProcDTO> form) {
        if (form.getT() == null) {
            form.setT(new CscpProcDTO());
        }
        Page page = new Page(form.getCurrentPage(), form.getPageSize());
        CscpProcDTO cscpProcDTO = form.getT();
        Long tenantId = SecurityUtils.getCurrentCscpUserDetail().getTenantId();
        //获取当前租户的顶级租户
        Long topFloorTenamtId = SysTenantUtils.getTopFloorTenamtId(tenantId);
        //获取顶级租户下所有子租户
        List<TSysTenantDTO> tSysTenantDTOList  = itSysTenantService.getAllChildTenantList(topFloorTenamtId);
        List<Long> tenantIdList =  tSysTenantDTOList.stream().map(i -> i.getId()).collect(Collectors.toList());
        //添加顶级租户
        tenantIdList.add(topFloorTenamtId);
        cscpProcDTO.setTenantIdList(tenantIdList);
        IPage<CscpProcDTO> list = cscpActivitRepository.queryPageModelListBytenantIdList(page, cscpProcDTO);
        PageActiviti pageActiviti = PageActivitiUtil.convertPageCfrm(list);
        return pageActiviti;
    }

    /**
     * 更新流程的授权方式
     *
     * @param cscpProc
     */
    @Override
    public void updateCscpPermission(CscpProc cscpProc) {
        // CscpProc cscpProcOrigal = cscpActivitRepository.selectById(cscpProc.getId());
        if (cscpProc.getIsPermission()==1) {
            cscpProc.setIsPermission(cscpProc.getIsPermission());
            cscpProc.setPermissionType(cscpProc.getPermissionType());
            cscpProc.setPermissionTypeValues(cscpProc.getPermissionTypeValues());
        } else {
            cscpProc.setIsPermission(cscpProc.getIsPermission());
            cscpProc.setPermissionType("");
            cscpProc.setPermissionTypeValues("");
        }
        cscpActivitRepository.updateById(cscpProc);
    }

    @Override
    public List<CscpAuditContent> getAuditContentList(String processInstanceId, String nodeKey, LocalDateTime createTime) {
        LambdaQueryWrapper<CscpAuditContent> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(CscpAuditContent::getProcInstId, processInstanceId)
                .eq(CscpAuditContent::getActId, nodeKey)
                .eq(CscpAuditContent::getAuditStatus,String.valueOf(ActStatusEnum.NOMER.getCode()))
                .gt(CscpAuditContent::getCreateTime, createTime);
        return cscpAuditContentService.selectListNoAdd(lambdaQueryWrapper);
    }

    /**
     * 给在办流程设置办理人数据
     * */
    private void assembleAssigneeNames(IPage<HashMap<String, Object>> pageDataList){
        List<String> procInstIdList = pageDataList.getRecords().stream().map(item -> String.valueOf(item.get("procInstId"))).collect(Collectors.toList());
        // 流程实例id集合为空，则直接返回
        if(CollectionUtil.isEmpty(procInstIdList)){
            return ;
        }
        List<ProcessAssignee> processAssigneeList = new ArrayList<>();
        if(procInstIdList!=null && procInstIdList.size()>0){
            processAssigneeList = processAssigneeService.getProcessAssigneeListByProcInstIdList(procInstIdList);
        }
        List<String> normalFinish = processAssigneeList.stream().map(i -> i.getProcessInstanceId()).collect(Collectors.toList());
        List<String> subtract = ListUtils.subtract(procInstIdList, normalFinish);
        if (CollectionUtil.isNotEmpty(subtract)) {
            //查询办结人信息
            List<CscpAuditContent> auditContentList = cscpAuditContentService.getAuditContentListByInstanceIdList(subtract);
            if (CollectionUtil.isNotEmpty(auditContentList)) {
                Map<String, ProcessAssignee> map = auditContentList.stream().collect(Collectors.toMap(CscpAuditContent::getProcInstId,
                        i -> {
                            ProcessAssignee processAssignee = new ProcessAssignee();
                            processAssignee.setProcessInstanceId(i.getProcInstId());
                            processAssignee.setAssigneeName(i.getAuditorName());
                            processAssignee.setCreateTime(LocalDateTimeUtil.parse(i.getAuditTime(),DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                            return processAssignee;
                        },
                        (v1, v2) -> v1.getCreateTime().isBefore(v2.getCreateTime()) ? v1 : v2
                ));
                for (Map.Entry<String, ProcessAssignee> entry : map.entrySet()) {
                    processAssigneeList.add(entry.getValue());
                }
            }

        }

        // 分别获取开始时间和结束时间
        LocalDateTime today = LocalDateTimeUtil.now();
        LocalDateTime endate;
        // 获取系统动态参数，如设置了时间范围则已系统参数为准
        String value = sysConfigService.getSysConfigValueByCode(SysConfigConstant.PROCESS_DAY);
        if(StringUtils.isNotBlank(value)){
            if (Convert.toLong(value)==null){
                throw new BusinessException("系统动态参数process:day配置错误，应输入数字");
            }
            // 取系统动态参数
            endate = LocalDateTime.of(LocalDate.now().minusDays(Convert.toLong(value)), LocalTime.of(00, 00, 00));
        }else {
            // 默认取最近7天内
            endate = LocalDateTime.of(LocalDate.now().minusDays(7), LocalTime.of(00, 00, 00));
        }

        List<ProcessAssignee> finalProcessAssigneeList = processAssigneeList;
        pageDataList.getRecords().stream().forEach(item -> {
            List<ProcessAssignee> processAssigneeListNew = finalProcessAssigneeList.stream().filter(q -> q.getProcessInstanceId().equals(item.get("procInstId")))
                    .sorted(Comparator.comparing(ProcessAssignee::getCreateTime)).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(processAssigneeListNew)) {
                // 多实例策略时，存在多人同时办理，需要逗号分隔全部显示
                String assigneeName = processAssigneeListNew.stream().map(ProcessAssignee::getAssigneeName).collect(Collectors.joining(","));
                item.put("assignessName", assigneeName);
            }
            //是否7天内办结的
            if (ObjectUtil.equal((int) item.get("procStatus"), BpmStatusConstants.PROCESS_END)){
                String procEndTimeStr = (String) item.get("procEndTime");
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                LocalDateTime procEndTime = LocalDateTimeUtil.parse(procEndTimeStr, formatter);

                // reset: true-最近XX内天办结，显示重启，false-非最近XX天内不显示重启
                item.put("reset", procEndTime.isAfter(endate) && procEndTime.isBefore(today));
            }else {
                // 未办结的均为false
                item.put("reset", false);
            }
            //子流程添加节点信息
//            if (!item.containsKey("actDefUniqueName")) {
//                CscpProcBase cscpProcBase = cscpProcBaseRepository.selectOneOnlyAddTenantId(new LambdaQueryWrapper<CscpProcBase>()
//                        .select(CscpProcBase::getActDefUniqueName)
//                        .eq(CscpProcBase::getRootProcInstId, item.get("rootProcessInstanceId"))
//                        .last("and root_PROC_INST_ID != PROC_INST_ID"));
//                item.put("actDefUniqueName",cscpProcBase.getActDefUniqueName());
//            }
        });
    }

}
