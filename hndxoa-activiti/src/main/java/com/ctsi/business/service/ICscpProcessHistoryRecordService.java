package com.ctsi.business.service;

import com.ctsi.business.entity.dto.CscpProcessHistoryRecordDTO;
import com.ctsi.business.entity.CscpProcessHistoryRecord;
import com.ctsi.business.entity.dto.RecordChangeDTO;
import com.ctsi.hndx.common.SysBaseServiceI;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.ssdc.model.PageResult;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 表单修改留痕记录 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-26
 */
public interface ICscpProcessHistoryRecordService extends SysBaseServiceI<CscpProcessHistoryRecord> {



    /**
     * 分页查询
     *
     * @param entityDTO
     * @param page
     * @return
     */
    PageResult<CscpProcessHistoryRecordDTO> queryListPage(CscpProcessHistoryRecordDTO entityDTO, BasePageForm page);

    /**
     * 获取所有不分页
     *
     * @param entity
     * @return
     */
    List<CscpProcessHistoryRecordDTO> queryList(CscpProcessHistoryRecordDTO entity);

    /**
     * 根据主键id获取单个对象
     *
     * @param id
     * @return
     */
    CscpProcessHistoryRecordDTO findOne(Long id);

    /**
     * 新增
     *
     * @param entity
     * @return
     */
    CscpProcessHistoryRecordDTO create(CscpProcessHistoryRecordDTO entity);


    /**
     * 更新
     *
     * @param entity
     * @return
     */
    int update(CscpProcessHistoryRecordDTO entity);

    /**
     * 删除
     *
     * @param id
     * @return
     */
    int delete(Long id);

     /**
     * 是否存在
     *
     * existByCscpProcessHistoryRecordId
     * @param code
     * @return
     */
    boolean existByCscpProcessHistoryRecordId(Long code);

    /**
    * 批量新增
    *
    * create batch
    * @param dataList
    * @return
    */
    Boolean insertBatch(List<CscpProcessHistoryRecordDTO> dataList);


    /**
     * 获取最新的历史记录
     * @param formDataId 业务id
     * @return
     */
    CscpProcessHistoryRecordDTO selectOneByFormDataId(String formDataId);




    /**
     *  当前节点提交的数据 与上一个节点提交的表单数据差异
     * @param curNodeKey  当前节点
     * @return
     */
    List<RecordChangeDTO>  queryChangeInfo(CscpProcessHistoryRecordDTO curNodeKey);

    /**
     * 获取上一个节点通过pid
     * @param pid
     * @return
     */
    CscpProcessHistoryRecordDTO getProcessHistoryRecordByPid(Long pid);

    /**
     * 获取 表单字段列表
     * @param formId
     * @return
     */
    Map<String, Object> getFieldListByFormId(String formId);
}
