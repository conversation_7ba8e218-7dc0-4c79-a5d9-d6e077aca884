package com.ctsi.business.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ctsi.business.domain.CscpOrgan;
import com.ctsi.business.domain.CscpTree;
import com.ctsi.business.repository.CscpOrganRepository;
import com.ctsi.business.repository.CscpPersonRepository;
import com.ctsi.business.service.CscpPersonService;
import com.ctsi.ssdc.admin.domain.CscpOrg;
import com.ctsi.ssdc.admin.domain.CscpUser;
import com.ctsi.ssdc.admin.domain.CscpWorkGroup;
import com.ctsi.ssdc.admin.domain.dto.CscpUserDTO;
import com.ctsi.ssdc.admin.repository.CscpWorkGroupRepository;
import com.ctsi.ssdc.admin.service.CscpUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
public class CscpPersonServiceImpl implements CscpPersonService {

    @Autowired
    private CscpOrganRepository cscpOrganRepository;

    @Autowired
    private CscpPersonRepository cscpPersonRepository;

    @Autowired
    private CscpWorkGroupRepository cscpWorkGroupRepository;
    @Autowired
    private CscpUserService cscpUserService;


    @Override
    public List<CscpTree> getOrgPersonById(String parentId) {
        LambdaQueryWrapper<CscpOrgan> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(CscpOrgan::getParentId, parentId);
        List<CscpOrgan> list = cscpOrganRepository.selectList(lambdaQueryWrapper);
        return getChildrenList(list,parentId);
    }

    private List<CscpTree> getChildrenList(List<CscpOrgan> list,String parentId) {
        List<CscpTree> treeList=new ArrayList<>();
        list.forEach(i->{
            CscpTree cscpTree=new CscpTree();
            cscpTree.setId(String.valueOf(i.getId()));
            cscpTree.setTitle(i.getOrgName());
            cscpTree.setType("dept");
            treeList.add(cscpTree);
        });
        //获取组织下人员
        List<CscpUser> userlist=cscpPersonRepository.queryUserListByOrgId(parentId);
        if(userlist!=null && userlist.size()>0) {
            userlist.forEach(i -> {
                CscpUserDTO CscpUserDTO = cscpUserService.findByUserId(i.getId());

                CscpTree cscpTree = new CscpTree();
                cscpTree.setUserId(String.valueOf(i.getId()));
                cscpTree.setTitle(i.getLoginName());
                cscpTree.setType("user");
                cscpTree.setChildren(null);
                cscpTree.setRealName(CscpUserDTO==null ? "" : CscpUserDTO.getRealName());
                treeList.add(cscpTree);
            });
        }
        return treeList;
    }

    @Override
    public List<CscpTree> getGroupPersonById(String parentId) {
        List<CscpTree> treeList=new ArrayList<>();
        if("top".equals(parentId)) {
            List<CscpWorkGroup> list = cscpWorkGroupRepository.selectList(null);
            list.forEach(i -> {
                CscpTree cscpTree = new CscpTree();
                cscpTree.setId(String.valueOf(i.getId()));
                cscpTree.setTitle(i.getGroupName());
                cscpTree.setType("group");
                treeList.add(cscpTree);
            });
        }
        if(!"top".equals(parentId)){
            //获取工作组下人员
            List<CscpUser> userlist=cscpPersonRepository.queryUserListByGroupId(parentId);
            userlist.forEach(i->{
                CscpUserDTO CscpUserDTO=cscpUserService.findByUserId(i.getId());
                CscpTree cscpTree=new CscpTree();
                cscpTree.setUserId(String.valueOf(i.getId()));
                cscpTree.setTitle(i.getLoginName());
                cscpTree.setType("user");
                cscpTree.setChildren(null);
                cscpTree.setRealName(CscpUserDTO==null ? "" : CscpUserDTO.getRealName());
                treeList.add(cscpTree);
            });
        }
        return treeList;
    }

    @Override
    public List<CscpUserDTO> getPersonListByOrgId(String orgId) {
        List<CscpUserDTO> list=new ArrayList<>();
        List<String> userIdList=new ArrayList<>();
        List<CscpUser> userlist=cscpPersonRepository.queryUserListByOrgId(orgId);
        if(userlist!=null && userlist.size()>0) {
            userlist.forEach(i -> {
                CscpUserDTO CscpUserDTO = cscpUserService.findByUserId(i.getId());
                list.add(CscpUserDTO);
            });
        }

        return list;
    }

}
