package com.ctsi.business.service;

import com.ctsi.business.entity.CscpProcessModifyRecord;
import com.ctsi.business.entity.dto.CscpProcessModifyRecordDTO;
import com.ctsi.business.entity.dto.RecordChangeDTO;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.common.SysBaseServiceI;
import com.ctsi.ssdc.model.PageResult;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 表单修改留痕记录 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-11
 */
public interface ICscpProcessModifyRecordService extends SysBaseServiceI<CscpProcessModifyRecord> {
	/**
	 * 分页查询
	 *
	 * @param entityDTO
	 * @param page
	 * @return
	 */
	PageResult<CscpProcessModifyRecordDTO> queryListPage(CscpProcessModifyRecordDTO entityDTO, BasePageForm page);

	/**
	 * 获取所有不分页
	 *
	 * @param entity
	 * @return
	 */
	List<CscpProcessModifyRecordDTO> queryList(CscpProcessModifyRecordDTO entity);

	/**
	 * 根据主键id获取单个对象
	 *
	 * @param id
	 * @return
	 */
	CscpProcessModifyRecordDTO findOne(Long id);

	/**
	 * 新增
	 *
	 * @param entity
	 * @return
	 */
	CscpProcessModifyRecordDTO create(CscpProcessModifyRecordDTO entity);


	/**
	 * 更新
	 *
	 * @param entity
	 * @return
	 */
	int update(CscpProcessModifyRecordDTO entity);

	/**
	 * 删除
	 *
	 * @param id
	 * @return
	 */
	int delete(Long id);

	/**
	 * 是否存在
	 *
	 * existByCscpProcessModifyRecordId
	 * @param code
	 * @return
	 */
	boolean existByCscpProcessModifyRecordId(Long code);

	/**
	 * 批量新增
	 *
	 * create batch
	 * @param dataList
	 * @return
	 */
	Boolean insertBatch(List<CscpProcessModifyRecordDTO> dataList);

	/**
	 * 获取当前流程修改字段
	 * @param processInstanceId
	 * @return
	 */
	List<String> getModifyFields(String processInstanceId);


	//
	// /**
	//  * 获取 表单字段列表
	//  * @param formId
	//  * @return
	//  */
	// Map<String, Object> getFieldListByFormId(String formId);
}
