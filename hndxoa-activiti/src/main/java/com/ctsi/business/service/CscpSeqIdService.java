package com.ctsi.business.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.ctsi.business.domain.CscpSeqId;
import com.ctsi.common.bo.PageActiviti;
import com.ctsi.common.bo.PageForm;

/**
 * Service Interface for managing CscpSeqId.
 *
 * <AUTHOR>
 *
 */
public interface CscpSeqIdService 
	extends IService<CscpSeqId> {

	String getNextSeqIdText(String id);

    PageActiviti<CscpSeqId> all(PageForm<CscpSeqId> form);
}
