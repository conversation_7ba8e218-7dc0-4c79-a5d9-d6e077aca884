package com.ctsi.business.web;

import com.ctsi.business.domain.CscpOrgan;
import com.ctsi.common.bo.ResponseBO;
import com.ctsi.business.service.CscpOrganService;
import com.ctsi.common.utils.SysErrEnum;
import com.ctsi.ssdc.admin.domain.CscpOrg;
import io.swagger.annotations.Api;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import java.util.List;

@RestController
@RequestMapping("/api")
@Api(hidden = true)
@ApiIgnore

public class CscpOrganController {

    private final Logger log = LoggerFactory.getLogger(CscpOrganController.class);

    @Autowired
    private CscpOrganService cscpOrganService;

    @PostMapping("/getCscpOrgsTreeList/{parentId}")
    public ResponseBO<List<CscpOrgan>> getCscpOrgsTreeList(@PathVariable String parentId) {
        try {
            List<CscpOrgan> list=cscpOrganService.getCscpOrgsTreeList(parentId);
            return new ResponseBO<>(list, SysErrEnum.SUCCESS);
        } catch (Exception e) {
            log.error("getCscpOrgsTreeList Exception",e);
            return new ResponseBO<>(SysErrEnum.SUCCESS);
        }
    }
}
