package com.ctsi.business.web;

import java.net.URISyntaxException;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.lang.String;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ctsi.business.repository.CscpAppRepository;
import com.ctsi.common.bo.PageActiviti;
import com.ctsi.common.bo.PageActivitiUtil;
import com.ctsi.common.bo.PageForm;
import com.ctsi.common.bo.ResponseBO;
import com.ctsi.common.utils.SysErrEnum;
import com.ctsi.common.utils.UUidUtils;
import com.ctsi.hndx.enums.DBOperation;
import com.ctsi.hndx.utils.StringUtils;
import com.ctsi.ssdc.annotation.OperationLog;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.propertyeditors.CustomDateEditor;
import org.springframework.data.domain.Pageable;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.InitBinder;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.ctsi.business.service.CscpAppService;
import com.ctsi.business.domain.CscpApp;

import com.ctsi.ssdc.model.PageResult;


/**
 * REST controller for managing CscpApp.
 *
 * <AUTHOR>
 *
 */
@RestController
@RequestMapping("/api")
@Api( tags = "流程应用管理接口，流程分类中的顶级节点")
public class CscpAppController {

    private final Logger log = LoggerFactory.getLogger(CscpAppController.class);

    private static final String ENTITY_NAME = "cscpApp";

    private final CscpAppService cscpAppService;


    public CscpAppController(CscpAppService cscpAppService) {
        this.cscpAppService = cscpAppService;
    }

    @InitBinder   
    public void initBinder(WebDataBinder binder) {   
        DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");   
        dateFormat.setLenient(true);   
        binder.registerCustomEditor(Date.class, new CustomDateEditor(dateFormat, true));   
    } 
   
    /**
     * POST  /cscpApps : Create a new cscpApp.
     *
     * @param cscpApp the cscpApp to create
     * @return the ResponseEntity with status 201 (Created) and with body the new cscpApp, or with status 400 (Bad Request) if the cscpApp has already an id
     * @throws URISyntaxException if the Location URI syntax is incorrect
     */
    @PostMapping("/cscpApps")
    @OperationLog(dBOperation = DBOperation.ADD,message = "新增app")
    public ResponseBO<CscpApp> createCscpApp(@RequestBody CscpApp cscpApp) throws URISyntaxException {
        try {
            log.debug("REST request to save CscpApp : {}", cscpApp);
            cscpApp.setId(UUidUtils.createId());
            cscpAppService.save(cscpApp);
            return new ResponseBO<CscpApp>(cscpApp,SysErrEnum.SUCCESS);
        }catch(Exception ex){
            return new ResponseBO<CscpApp>(SysErrEnum.ERROR);
        }
    }
	
    /**
     * PUT  /cscpApps : Updates an existing cscpApp.
     *
     * @param cscpApp the cscpApp to update
     * @return the ResponseEntity with status 200 (OK) and with body the updated cscpApp,
     * or with status 400 (Bad Request) if the cscpApp is not valid,
     * or with status 500 (Internal Server Error) if the cscpApp couldn't be updated
     * @throws URISyntaxException if the Location URI syntax is incorrect
     */
    @PutMapping("/cscpApps")
    @OperationLog(dBOperation = DBOperation.UPDATE,message = "更新app")
    public ResponseBO<CscpApp> updateCscpApp(@RequestBody CscpApp cscpApp)  {
        try {
            log.debug("REST request to update CscpApp : {}", cscpApp);

            cscpAppService.updateById(cscpApp);

            return new ResponseBO<CscpApp>(cscpApp,SysErrEnum.SUCCESS);
        }catch(Exception ex){
            return new ResponseBO<CscpApp>(SysErrEnum.ERROR);
        }
    }
    
    /**
     * GET  /cscpApps : get the cscpApps with page.
     *
     * @return the ResponseEntity with status 200 (OK) and the list of cscpApps in body

    @GetMapping("/cscpAppsByCriteria")
    public PageResult<CscpApp> getCscpAppsByCriteria(CscpAppExample cscpAppExample, Pageable page) {
        log.debug("REST request to get CscpAppsByCriteria");
        return cscpAppService.findByExample(cscpAppExample, page);
    }
     */

    /**
     * GET  /cscpApps : get the cscpApps.
     *
     * @return the ResponseEntity with status 200 (OK) and the list of cscpApps in body

    @GetMapping("/cscpAppsList")
    public PageResult<CscpApp> getCscpAppsList(CscpAppExample cscpAppExample) {
        log.debug("REST request to get CscpAppsList");
        return cscpAppService.findByExample(cscpAppExample);
    }
     */

    /**
     * GET  /cscpApps/:id : get the "id" cscpApp.
     *
     * @param id the id of the cscpApp to retrieve
     * @return the ResponseEntity with status 200 (OK) and with body the cscpApp, or with status 404 (Not Found)
     */
    @GetMapping("/cscpApps/{id}")
    public ResponseBO<CscpApp> getCscpApp(@PathVariable String id) {
        try {
            log.debug("REST request to get CscpApp : {}", id);
            CscpApp cscpApp = cscpAppService.getById(id);
            return new ResponseBO<CscpApp>(cscpApp,SysErrEnum.SUCCESS);
        }catch (Exception ex){
            return new ResponseBO<CscpApp>(SysErrEnum.ERROR);
        }
    }
	
    /**
     * DELETE  /cscpApps/:id : delete the "id" cscpApp.
     *
     * @param id the id of the cscpApp to delete
     * @return the ResponseEntity with status 200 (OK)
     */
    @DeleteMapping("/cscpApps/{id}")
    @OperationLog(dBOperation = DBOperation.DELETE,message = "根据id删除app")
    public ResponseBO<Void> deleteCscpApp(@PathVariable String id) {
        try {
            log.debug("REST request to delete CscpApp : {}", id);
            cscpAppService.removeById(id);
            return new ResponseBO<>(SysErrEnum.SUCCESS);
        }catch (Exception ex){
            return new ResponseBO<>(ex);
        }
    }

    /**
     * GET /qaWebsistesPageList : 获取站点分页列表
     *
     * @return 返回 ResponseEntity  with status 200 (OK) and the list of qaWebsitess in body
     */
    @PostMapping("/cscpAppPageList")
    @ApiOperation(value="获取站点信息列表", notes="获取站点信息列表")
    public ResponseBO<PageActiviti<CscpApp>> cscpAppPageList(@RequestBody @ApiParam(required = true) PageForm<CscpApp> form){
        try {
            IPage<CscpApp> all = cscpAppService.all(form);
            PageActiviti<CscpApp> pageActiviti = PageActivitiUtil.convertPageCfrm(all);
            return new ResponseBO(pageActiviti, SysErrEnum.SUCCESS);
        }catch(Exception ex) {
            return new ResponseBO<>(SysErrEnum.ERROR);
        }
    }

    @PostMapping("/isAppCodeExists")
    public ResponseBO<Boolean> isAppCodeExists(String id,String appCode){
        try{
            /*CscpAppExample cscpAppExample = new CscpAppExample();
            CscpAppExample.Criteria criteria = cscpAppExample.or();
            if(id != null && id.trim().length() > 0){
                criteria.andIdNotEqualTo(id);
            }
            if(appCode != null && appCode.trim().length() > 0){
                criteria.andAppCodeEqualTo(appCode);
            }*/

            LambdaQueryWrapper<CscpApp> lambdaQueryWrapper = new LambdaQueryWrapper<>();
            CscpApp pr = cscpAppService.lambdaQuery().eq(StringUtils.isNotEmpty(appCode),CscpApp::getAppCode,appCode)
                    .ne(StringUtils.isNotEmpty(id),CscpApp::getId,id).select(CscpApp::getAppCode).last("limit 1").one();
            Boolean exists = (pr != null);
            return new ResponseBO<>(exists, SysErrEnum.SUCCESS);
        }catch(Exception ex) {
            return new ResponseBO<>(SysErrEnum.ERROR);
        }
    }
}
