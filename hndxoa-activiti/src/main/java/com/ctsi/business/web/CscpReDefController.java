package com.ctsi.business.web;

import com.ctsi.business.domain.CscpReDef;
import com.ctsi.business.service.CscpReDefService;
import com.ctsi.hndx.enums.DBOperation;
import com.ctsi.ssdc.annotation.OperationLog;
import com.ctsi.ssdc.util.HeaderUtil;
import com.ctsi.ssdc.util.ResponseUtil;
import io.swagger.annotations.Api;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.propertyeditors.CustomDateEditor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.net.URI;
import java.net.URISyntaxException;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Optional;


/**
 * REST controller for managing CscpReDef.
 *
 * <AUTHOR>
 *
 */
@RestController
@RequestMapping("/api")
@Api(hidden = true)
@ApiIgnore

public class CscpReDefController {

    private final Logger log = LoggerFactory.getLogger(CscpReDefController.class);

    private static final String ENTITY_NAME = "cscpReDef";

    private final CscpReDefService cscpReDefService;

    public CscpReDefController(CscpReDefService cscpReDefService) {
        this.cscpReDefService = cscpReDefService;
    }

    @InitBinder   
    public void initBinder(WebDataBinder binder) {   
        DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");   
        dateFormat.setLenient(true);   
        binder.registerCustomEditor(Date.class, new CustomDateEditor(dateFormat, true));   
    } 
   
    /**
     * POST  /cscpReDefs : Create a new cscpReDef.
     *
     * @param cscpReDef the cscpReDef to create
     * @return the ResponseEntity with status 201 (Created) and with body the new cscpReDef, or with status 400 (Bad Request) if the cscpReDef has already an id
     * @throws URISyntaxException if the Location URI syntax is incorrect
     */
    @PostMapping("/cscpReDefs")
    @OperationLog(dBOperation = DBOperation.ADD,message = "新建ReDefs")
    public ResponseEntity<CscpReDef> createCscpReDef(@RequestBody CscpReDef cscpReDef) throws URISyntaxException {
        log.debug("REST request to save CscpReDef : {}", cscpReDef);
        cscpReDefService.save(cscpReDef);
        return ResponseEntity.created(new URI("/api/cscpReDefs" + "/" +cscpReDef.getId() ))
            .headers(HeaderUtil.createEntityCreationAlert(ENTITY_NAME, cscpReDef.getId().toString()))
            .body(cscpReDef);
    }
	
    /**
     * PUT  /cscpReDefs : Updates an existing cscpReDef.
     *
     * @param cscpReDef the cscpReDef to update
     * @return the ResponseEntity with status 200 (OK) and with body the updated cscpReDef,
     * or with status 400 (Bad Request) if the cscpReDef is not valid,
     * or with status 500 (Internal Server Error) if the cscpReDef couldn't be updated
     * @throws URISyntaxException if the Location URI syntax is incorrect
     */
    @PutMapping("/cscpReDefs")
    @OperationLog(dBOperation = DBOperation.UPDATE,message = "更新ReDefs")
    public ResponseEntity<CscpReDef> updateCscpReDef(@RequestBody CscpReDef cscpReDef)  {
        log.debug("REST request to update CscpReDef : {}", cscpReDef);
        cscpReDefService.updateById(cscpReDef);
        return ResponseEntity.ok()
            .headers(HeaderUtil.createEntityUpdateAlert(ENTITY_NAME, cscpReDef.getId().toString()))
            .body(cscpReDef);
    }
    
    /**
     * GET  /cscpReDefs : get the cscpReDefs with page.
     *
     * @return the ResponseEntity with status 200 (OK) and the list of cscpReDefs in body
     */
   /* @GetMapping("/cscpReDefsByCriteria")
    public PageResult<CscpReDef> getCscpReDefsByCriteria(CscpReDefExample cscpReDefExample, Pageable page) {
        log.debug("REST request to get CscpReDefsByCriteria");
        return cscpReDefService.findByExample(cscpReDefExample, page);
    }
    
    *//**
     * GET  /cscpReDefs : get the cscpReDefs.
     *
     * @return the ResponseEntity with status 200 (OK) and the list of cscpReDefs in body
     *//*
    @GetMapping("/cscpReDefsList")
    public PageResult<CscpReDef> getCscpReDefsList(CscpReDefExample cscpReDefExample) {
        log.debug("REST request to get CscpReDefsList");
        return cscpReDefService.findByExample(cscpReDefExample);
    }*/

    /**
     * GET  /cscpReDefs/:id : get the "id" cscpReDef.
     *
     * @param id the id of the cscpReDef to retrieve
     * @return the ResponseEntity with status 200 (OK) and with body the cscpReDef, or with status 404 (Not Found)
     */
    @GetMapping("/cscpReDefs/{id}")
    public ResponseEntity<CscpReDef> getCscpReDef(@PathVariable String id) {
        log.debug("REST request to get CscpReDef : {}", id);
        CscpReDef cscpReDef = cscpReDefService.getById(id);
        return ResponseUtil.wrapOrNotFound(Optional.ofNullable(cscpReDef));
    }
	
    /**
     * DELETE  /cscpReDefs/:id : delete the "id" cscpReDef.
     *
     * @param id the id of the cscpReDef to delete
     * @return the ResponseEntity with status 200 (OK)
     */
    @DeleteMapping("/cscpReDefs/{id}")
    @OperationLog(dBOperation = DBOperation.DELETE,message = "删除ReDefs")
    public ResponseEntity<Void> deleteCscpReDef(@PathVariable String id) {
        log.debug("REST request to delete CscpReDef : {}", id);
        cscpReDefService.removeById(id);
        return ResponseEntity.ok().headers(HeaderUtil.createEntityDeletionAlert(ENTITY_NAME, id.toString())).build();
    }
    
}
