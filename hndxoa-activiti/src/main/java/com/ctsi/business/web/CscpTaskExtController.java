package com.ctsi.business.web;

import com.ctsi.business.domain.CscpTaskExt;
import com.ctsi.business.service.CscpTaskExtService;
import com.ctsi.hndx.enums.DBOperation;
import com.ctsi.ssdc.annotation.OperationLog;
import com.ctsi.ssdc.util.HeaderUtil;
import com.ctsi.ssdc.util.ResponseUtil;
import io.swagger.annotations.Api;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.propertyeditors.CustomDateEditor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.net.URI;
import java.net.URISyntaxException;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Optional;


/**
 * REST controller for managing CscpTaskExt.
 *
 * <AUTHOR>
 *
 */
@RestController
@RequestMapping("/api")
@Api(hidden = true)
@ApiIgnore

public class CscpTaskExtController {

    private final Logger log = LoggerFactory.getLogger(CscpTaskExtController.class);

    private static final String ENTITY_NAME = "cscpTaskExt";

    private final CscpTaskExtService cscpTaskExtService;

    public CscpTaskExtController(CscpTaskExtService cscpTaskExtService) {
        this.cscpTaskExtService = cscpTaskExtService;
    }

    @InitBinder   
    public void initBinder(WebDataBinder binder) {   
        DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");   
        dateFormat.setLenient(true);   
        binder.registerCustomEditor(Date.class, new CustomDateEditor(dateFormat, true));   
    } 
   
    /**
     * POST  /cscpTaskExts : Create a new cscpTaskExt.
     *
     * @param cscpTaskExt the cscpTaskExt to create
     * @return the ResponseEntity with status 201 (Created) and with body the new cscpTaskExt, or with status 400 (Bad Request) if the cscpTaskExt has already an id
     * @throws URISyntaxException if the Location URI syntax is incorrect
     */
    @PostMapping("/cscpTaskExts")
    @OperationLog(dBOperation = DBOperation.ADD,message = "新增任务ext(TaskExts)")
    public ResponseEntity<CscpTaskExt> createCscpTaskExt(@RequestBody CscpTaskExt cscpTaskExt) throws URISyntaxException {
        log.debug("REST request to save CscpTaskExt : {}", cscpTaskExt);
        cscpTaskExtService.save(cscpTaskExt);
        return ResponseEntity.created(new URI("/api/cscpTaskExts" + "/" +cscpTaskExt.getId() ))
            .headers(HeaderUtil.createEntityCreationAlert(ENTITY_NAME, cscpTaskExt.getId().toString()))
            .body(cscpTaskExt);
    }
	
    /**
     * PUT  /cscpTaskExts : Updates an existing cscpTaskExt.
     *
     * @param cscpTaskExt the cscpTaskExt to update
     * @return the ResponseEntity with status 200 (OK) and with body the updated cscpTaskExt,
     * or with status 400 (Bad Request) if the cscpTaskExt is not valid,
     * or with status 500 (Internal Server Error) if the cscpTaskExt couldn't be updated
     * @throws URISyntaxException if the Location URI syntax is incorrect
     */
    @PutMapping("/cscpTaskExts")
    @OperationLog(dBOperation = DBOperation.UPDATE,message = "更新任务ext(TaskExts)")
    public ResponseEntity<CscpTaskExt> updateCscpTaskExt(@RequestBody CscpTaskExt cscpTaskExt)  {
        log.debug("REST request to update CscpTaskExt : {}", cscpTaskExt);
        cscpTaskExtService.updateById(cscpTaskExt);
        return ResponseEntity.ok()
            .headers(HeaderUtil.createEntityUpdateAlert(ENTITY_NAME, cscpTaskExt.getId().toString()))
            .body(cscpTaskExt);
    }
    
    /**
     * GET  /cscpTaskExts : get the cscpTaskExts with page.
     *
     * @return the ResponseEntity with status 200 (OK) and the list of cscpTaskExts in body
     */
    /*@GetMapping("/cscpTaskExtsByCriteria")
    public PageResult<CscpTaskExt> getCscpTaskExtsByCriteria(CscpTaskExtExample cscpTaskExtExample, Pageable page) {
        log.debug("REST request to get CscpTaskExtsByCriteria");
        return cscpTaskExtService.findByExample(cscpTaskExtExample, page);
    }
    
    *//**
     * GET  /cscpTaskExts : get the cscpTaskExts.
     *
     * @return the ResponseEntity with status 200 (OK) and the list of cscpTaskExts in body
     *//*
    @GetMapping("/cscpTaskExtsList")
    public PageResult<CscpTaskExt> getCscpTaskExtsList(CscpTaskExtExample cscpTaskExtExample) {
        log.debug("REST request to get CscpTaskExtsList");
        return cscpTaskExtService.findByExample(cscpTaskExtExample);
    }*/

    /**
     * GET  /cscpTaskExts/:id : get the "id" cscpTaskExt.
     *
     * @param id the id of the cscpTaskExt to retrieve
     * @return the ResponseEntity with status 200 (OK) and with body the cscpTaskExt, or with status 404 (Not Found)
     */
    @GetMapping("/cscpTaskExts/{id}")
    public ResponseEntity<CscpTaskExt> getCscpTaskExt(@PathVariable String id) {
        log.debug("REST request to get CscpTaskExt : {}", id);
        CscpTaskExt cscpTaskExt = cscpTaskExtService.getById(id);
        return ResponseUtil.wrapOrNotFound(Optional.ofNullable(cscpTaskExt));
    }
	
    /**
     * DELETE  /cscpTaskExts/:id : delete the "id" cscpTaskExt.
     *
     * @param id the id of the cscpTaskExt to delete
     * @return the ResponseEntity with status 200 (OK)
     */
    @DeleteMapping("/cscpTaskExts/{id}")
    @OperationLog(dBOperation = DBOperation.DELETE,message = "删除任务ext(TaskExts)")
    public ResponseEntity<Void> deleteCscpTaskExt(@PathVariable String id) {
        log.debug("REST request to delete CscpTaskExt : {}", id);
        cscpTaskExtService.removeById(id);
        return ResponseEntity.ok().headers(HeaderUtil.createEntityDeletionAlert(ENTITY_NAME, id.toString())).build();
    }
    
}
