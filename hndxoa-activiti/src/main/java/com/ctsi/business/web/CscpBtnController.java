package com.ctsi.business.web;

import java.net.URISyntaxException;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.lang.String;
import java.util.stream.Collectors;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ctsi.business.domain.dto.CscpBtnDto;
import com.ctsi.business.repository.CscpBtnRepository;
import com.ctsi.common.bo.ResponseBO;
import com.ctsi.common.utils.SysErrEnum;
import com.ctsi.hndx.annotations.ResponseResultVo;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.enums.DBOperation;
import com.ctsi.hndx.result.ResultVO;
import com.ctsi.hndx.utils.BeanConvertUtils;
import com.ctsi.hndx.utils.ListCopyUtil;
import com.ctsi.hndx.utils.MybatisQueryUtil;
import com.ctsi.hndx.utils.PageHelperUtil;
import com.ctsi.ssdc.annotation.OperationLog;
import com.ctsi.ssdc.model.PageResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import io.swagger.models.auth.In;
import lombok.val;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.propertyeditors.CustomDateEditor;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.util.Assert;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.InitBinder;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.ctsi.business.service.CscpBtnService;
import com.ctsi.business.domain.CscpBtn;


/**
 * REST controller for managing CscpBtn.
 *
 * <AUTHOR>
 *
 */
@RestController
@RequestMapping("/api")
@Api(tags = "流程节点表单控制的按钮的接口例如控制正文，加签等")
public class CscpBtnController {

    private final Logger log = LoggerFactory.getLogger(CscpBtnController.class);

    private static final String ENTITY_NAME = "cscpBtn";

    private final CscpBtnService cscpBtnService;


    public CscpBtnController(CscpBtnService cscpBtnService) {
        this.cscpBtnService = cscpBtnService;
    }

    @InitBinder   
    public void initBinder(WebDataBinder binder) {   
        DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");   
        dateFormat.setLenient(true);   
        binder.registerCustomEditor(Date.class, new CustomDateEditor(dateFormat, true));   
    } 
   
    /**
     * POST  /cscpBtns : Create a new cscpBtn.
     *
     * @param cscpBtn the cscpBtn to create
     * @return the ResponseEntity with status 201 (Created) and with body the new cscpBtn, or with status 400 (Bad Request) if the cscpBtn has already an actionId
     * @throws URISyntaxException if the Location URI syntax is incorrect
     */
    @PostMapping("/cscpBtns")
    @OperationLog(dBOperation = DBOperation.ADD,message = "新增表单按钮")
    public ResultVO<Boolean> createCscpBtn(@Validated @RequestBody CscpBtn cscpBtn) throws URISyntaxException {
        log.debug("REST request to save CscpBtn : {}", cscpBtn);
        //只要不是1,都是0
        if(cscpBtn.getIsDefault()== null || !"1".equals(cscpBtn.getIsDefault())){
            cscpBtn.setIsDefault("0");
        }
        this.updateOrder(cscpBtn);
        cscpBtn.setSubType("form");
        cscpBtn.setActionType("WF");
        LambdaQueryWrapper<CscpBtn> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(CscpBtn::getFunctionName,cscpBtn.getFunctionName());
        Integer integer = cscpBtnService.selectCountNoAdd(lambdaQueryWrapper);
        if (integer > 0){
            return ResultVO.error(cscpBtn.getFunctionName()+"已经存在");
        }
        cscpBtnService.save(cscpBtn);
        return ResultVO.success(true);
    }
	
    /**
     * PUT  /cscpBtns : Updates an existing cscpBtn.
     *
     * @param cscpBtn the cscpBtn to update
     * @return the ResponseEntity with status 200 (OK) and with body the updated cscpBtn,
     * or with status 400 (Bad Request) if the cscpBtn is not valid,
     * or with status 500 (Internal Server Error) if the cscpBtn couldn't be updated
     * @throws URISyntaxException if the Location URI syntax is incorrect
     */
    @ApiOperation("更新表单按钮")
    @PostMapping("/updateCscpBtn")
    @OperationLog(dBOperation = DBOperation.UPDATE,message = "更新表单按钮")
    public ResultVO<Boolean> updateCscpBtn(@RequestBody CscpBtnDto cscpBtnDto)  {
        Assert.notNull(cscpBtnDto.getId(), "更新id不能为空");
        this.changeCscpBtn(cscpBtnDto);
        boolean isFlag  = cscpBtnService.updateCscpBtn(cscpBtnDto);
        return ResultVO.success(isFlag);
    }

    @ApiOperation(value = "获取表单列表")
    @GetMapping("/cscpBtnsList")
    public ResultVO<List<CscpBtnDto> > getCscpBtnsList(CscpBtn cscpBtn) {
        QueryWrapper<CscpBtn> cscpBtnQueryWrapper = MybatisQueryUtil.paddingDefaultConditionQuery(CscpBtn.class, cscpBtn);
        List<CscpBtn> cscpBtns = cscpBtnService.list(cscpBtnQueryWrapper);
        List<CscpBtnDto> copy = ListCopyUtil.copy(cscpBtns, CscpBtnDto.class);
        return ResultVO.success(copy);
    }




    @ApiOperation(value = "分页获取表单列表")
    @GetMapping("/getCscpBtnsPageList")
    public ResultVO<PageResult<CscpBtnDto>> getCscpBtnsPageList(CscpBtn cscpBtn, BasePageForm basePageForm) {
        QueryWrapper<CscpBtn> cscpBtnQueryWrapper = MybatisQueryUtil.paddingDefaultConditionQuery(CscpBtn.class, cscpBtn);
        cscpBtnQueryWrapper.orderByAsc("display_order");
        IPage<CscpBtn> page = cscpBtnService.page(PageHelperUtil.getMPlusPageByBasePage(basePageForm), cscpBtnQueryWrapper);

        List<CscpBtnDto> copy = ListCopyUtil.copy(page.getRecords(), CscpBtnDto.class);
        return ResultVO.success(new PageResult<>(copy,page.getTotal(),page.getTotal()));
    }

    /**
     * GET  /cscpBtns/:actionId : get the "actionId" cscpBtn.
     *
     * @param actionId the id of the cscpBtn to retrieve
     * @return the ResponseEntity with status 200 (OK) and with body the cscpBtn, or with status 404 (Not Found)
     */
    @ApiOperation("获取详情")
    @GetMapping("/cscpBtns/{id}")
    public ResultVO<CscpBtn> getCscpBtn(@PathVariable String id) {
        CscpBtn cscpBtn = cscpBtnService.getById(id);
         return ResultVO.success(cscpBtn);
    }
	
    /**
     * DELETE  /cscpBtns/:actionId : delete the "actionId" cscpBtn.
     *
     * @param actionId the id of the cscpBtn to delete
     * @return the ResponseEntity wtus 200 (OK)
     */
    @ApiOperation(value = "根据id删除表单")
    @DeleteMapping("/cscpBtns/{id}")
    @OperationLog(dBOperation = DBOperation.DELETE,message = "根据id删除表单")
    public ResultVO<Boolean> deleteCscpBtn(@PathVariable String id) {
        return ResultVO.success( cscpBtnService.removeById(id));
    }

    /**
     * 转换对象
     * @param cscpBtnDto
     * @return
     */
    public Boolean changeCscpBtn(CscpBtnDto cscpBtnDto){
        CscpBtn cscpBtn = BeanConvertUtils.copyProperties(cscpBtnDto,CscpBtn.class);
        this.updateOrder(cscpBtn);
        return true;
    }

    /**
     * 排序号自增
     * @param cscpBtn
     * @return
     */
    public Boolean updateOrder(CscpBtn cscpBtn){
        LambdaQueryWrapper<CscpBtn> lambdaQueryWrapper = Wrappers.lambdaQuery();
        lambdaQueryWrapper.eq(CscpBtn::getDisplayOrder,cscpBtn.getDisplayOrder());
        int count = cscpBtnService.count(lambdaQueryWrapper);
        if (count > 0){
            LambdaQueryWrapper<CscpBtn> lambdaQueryWrapperOrder = Wrappers.lambdaQuery();
            lambdaQueryWrapperOrder.ge(CscpBtn::getDisplayOrder,cscpBtn.getDisplayOrder());
            cscpBtnService.list(lambdaQueryWrapperOrder).stream().map(i -> {
                i.setDisplayOrder(i.getDisplayOrder() + 1);
                return cscpBtnService.updateById(i);
            }).collect(Collectors.toList());
        }
        return true;
    }
}
