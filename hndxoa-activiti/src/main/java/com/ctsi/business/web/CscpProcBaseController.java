package com.ctsi.business.web;

import com.ctsi.business.domain.CscpProcBase;
import com.ctsi.business.dto.QueryAllProcessMonitorDTO;
import com.ctsi.business.service.CscpProcBaseService;
import com.ctsi.business.vo.QueryAllProcessMonitorVO;
import com.ctsi.hndx.annotations.ResponseResultVo;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.enums.DBOperation;
import com.ctsi.hndx.result.ResultVO;
import com.ctsi.ssdc.annotation.OperationLog;
import com.ctsi.ssdc.model.PageResult;
import com.ctsi.ssdc.util.HeaderUtil;
import com.ctsi.ssdc.util.ResponseUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.propertyeditors.CustomDateEditor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.net.URI;
import java.net.URISyntaxException;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Map;
import java.util.Optional;


/**
 * REST controller for managing CscpProcBase.
 *
 * <AUTHOR>
 *
 */
@RestController
@RequestMapping("/api")
@Api(tags = "关于流程的接口")
public class CscpProcBaseController {

    private final Logger log = LoggerFactory.getLogger(CscpProcBaseController.class);

    private static final String ENTITY_NAME = "cscpProcBase";

    private final CscpProcBaseService cscpProcBaseService;

    public CscpProcBaseController(CscpProcBaseService cscpProcBaseService) {
        this.cscpProcBaseService = cscpProcBaseService;
    }

    @InitBinder
    public void initBinder(WebDataBinder binder) {
        DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        dateFormat.setLenient(true);
        binder.registerCustomEditor(Date.class, new CustomDateEditor(dateFormat, true));
    }

    /**
     * POST  /cscpProcBases : Create a new cscpProcBase.
     *
     * @param cscpProcBase the cscpProcBase to create
     * @return the ResponseEntity with status 201 (Created) and with body the new cscpProcBase, or with status 400 (Bad Request) if the cscpProcBase has already an id
     * @throws URISyntaxException if the Location URI syntax is incorrect
     */
    @PostMapping("/cscpProcBases")
    @OperationLog(dBOperation = DBOperation.ADD,message = "新增程序基础(ProcBase)")
    public ResponseEntity<CscpProcBase> createCscpProcBase(@RequestBody CscpProcBase cscpProcBase) throws URISyntaxException {
        log.debug("REST request to save CscpProcBase : {}", cscpProcBase);
        cscpProcBaseService.save(cscpProcBase);
        return ResponseEntity.created(new URI("/api/cscpProcBases" + "/" +cscpProcBase.getId() ))
            .headers(HeaderUtil.createEntityCreationAlert(ENTITY_NAME, cscpProcBase.getId().toString()))
            .body(cscpProcBase);
    }

    /**
     * PUT  /cscpProcBases : Updates an existing cscpProcBase.
     *
     * @param cscpProcBase the cscpProcBase to update
     * @return the ResponseEntity with status 200 (OK) and with body the updated cscpProcBase,
     * or with status 400 (Bad Request) if the cscpProcBase is not valid,
     * or with status 500 (Internal Server Error) if the cscpProcBase couldn't be updated
     * @throws URISyntaxException if the Location URI syntax is incorrect
     */
    @PutMapping("/cscpProcBases")
    @OperationLog(dBOperation = DBOperation.UPDATE,message = "更新程序基础(ProcBase)")
    public ResponseEntity<CscpProcBase> updateCscpProcBase(@RequestBody CscpProcBase cscpProcBase)  {
        log.debug("REST request to update CscpProcBase : {}", cscpProcBase);
        cscpProcBaseService.updateById(cscpProcBase);
        return ResponseEntity.ok()
            .headers(HeaderUtil.createEntityUpdateAlert(ENTITY_NAME, cscpProcBase.getId().toString()))
            .body(cscpProcBase);
    }

   /* *//**
     * GET  /cscpProcBases : get the cscpProcBases with page.
     *
     * @return the ResponseEntity with status 200 (OK) and the list of cscpProcBases in body
     *//*
    @GetMapping("/cscpProcBasesByCriteria")
    public PageResult<CscpProcBase> getCscpProcBasesByCriteria(CscpProcBaseExample cscpProcBaseExample, Pageable page) {
        log.debug("REST request to get CscpProcBasesByCriteria");
        return cscpProcBaseService.findByExample(cscpProcBaseExample, page);
    }

    *//**
     * GET  /cscpProcBases : get the cscpProcBases.
     *
     * @return the ResponseEntity with status 200 (OK) and the list of cscpProcBases in body
     *//*
    @GetMapping("/cscpProcBasesList")
    public PageResult<CscpProcBase> getCscpProcBasesList(CscpProcBaseExample cscpProcBaseExample) {
        log.debug("REST request to get CscpProcBasesList");
        return cscpProcBaseService.findByExample(cscpProcBaseExample);
    }*/

    /**`````````````````````````````````````````````````````1
     * GET  /cscpProcBases/:id : get the "id" cscpProcBase.
     *
     * @param id the id of the cscpProcBase to retrieve
     * @return the ResponseEntity with status 200 (OK) and with body the cscpProcBase, or with status 404 (Not Found)
     */
    @GetMapping("/cscpProcBases/{id}")
    public ResponseEntity<CscpProcBase> getCscpProcBase(@PathVariable String id) {
        log.debug("REST request to get CscpProcBase : {}", id);
        CscpProcBase cscpProcBase = cscpProcBaseService.getById(id);
        return ResponseUtil.wrapOrNotFound(Optional.ofNullable(cscpProcBase));
    }

    /**
     * DELETE  /cscpProcBases/:id : delete the "id" cscpProcBase.
     *
     * @param id the id of the cscpProcBase to delete
     * @return the ResponseEntity with status 200 (OK)
     */
    @DeleteMapping("/cscpProcBases/{id}")
    @OperationLog(dBOperation = DBOperation.DELETE,message = "删除程序基础(ProcBase)")
    public ResponseEntity<Void> deleteCscpProcBase(@PathVariable String id) {
        log.debug("REST request to delete CscpProcBase : {}", id);
        cscpProcBaseService.removeById(id);
        return ResponseEntity.ok().headers(HeaderUtil.createEntityDeletionAlert(ENTITY_NAME, id.toString())).build();
    }

    /**
     * 获取当前部门共享呈批件
     *
     */
    @GetMapping("/getDepartmentShareCscpProcPage")
    @ApiOperation(value = "获取当前部门共享呈批件", notes = "传入参数")
    public ResultVO<PageResult<CscpProcBase>> getDepartmentShareCscpProcPage(HttpServletRequest servletRequest, BasePageForm basePageForm) {
        String title = servletRequest.getParameter("title");
        return ResultVO.success(cscpProcBaseService.getDepartmentShareCscpProcPage(title,basePageForm));
    }


    @PostMapping("/queryAllProcessMonitorList")
    @ApiOperation(value = "获取所有起草流程监控", notes = "传入参数")
    public ResultVO<PageResult<QueryAllProcessMonitorDTO>> queryAllProcessMonitorList(@RequestBody QueryAllProcessMonitorVO vo, BasePageForm basePageForm) {
        return ResultVO.success(cscpProcBaseService.queryAllProcessMonitorList(vo, basePageForm));
    }



    /**
     *  三服务-办文办件和批阅文件数量统计
     */
    @ApiOperation(value = "三服务-办文办件和批阅文件数量统计，proceedCount-办理中，waiteCount-待批公文，finishedCount -已办结，circulateCount -待阅信息")
    @GetMapping("/getApprovalCountOnCompanyId/{strId}")
    @ResponseResultVo
    public ResultVO<Map<String,Long>> getApprovalCountOnCompanyId(@PathVariable("strId") String strId) {
        Map<String, Long> map = cscpProcBaseService.getApprovalCountOnCompanyId(strId);
        return ResultVO.success(map);
    }
}
