package com.ctsi.business.web;

import java.net.URI;
import java.net.URISyntaxException;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.lang.String;

import com.ctsi.business.repository.CscpAuditLogRepository;
import com.ctsi.common.bo.ResponseBO;
import com.ctsi.common.utils.SysErrEnum;
import com.ctsi.hndx.enums.DBOperation;
import com.ctsi.ssdc.annotation.OperationLog;
import io.swagger.annotations.Api;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.propertyeditors.CustomDateEditor;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.InitBinder;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.ctsi.business.service.CscpAuditLogService;
import com.ctsi.business.domain.CscpAuditLog;

import com.ctsi.ssdc.model.PageResult;
import com.ctsi.ssdc.util.HeaderUtil;
import com.ctsi.ssdc.util.ResponseUtil;
import springfox.documentation.annotations.ApiIgnore;


/**
 * REST controller for managing CscpAuditLog.
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api")
@Api(hidden = true)
@ApiIgnore
public class CscpAuditLogController {

    private final Logger log = LoggerFactory.getLogger(CscpAuditLogController.class);

    private static final String ENTITY_NAME = "cscpAuditLog";

    private final CscpAuditLogService cscpAuditLogService;

    @Autowired
    private CscpAuditLogRepository cscpAuditLogRepository;

    public CscpAuditLogController(CscpAuditLogService cscpAuditLogService) {
        this.cscpAuditLogService = cscpAuditLogService;
    }

    @InitBinder
    public void initBinder(WebDataBinder binder) {
        DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        dateFormat.setLenient(true);
        binder.registerCustomEditor(Date.class, new CustomDateEditor(dateFormat, true));
    }

    /**
     * POST  /cscpAuditLogs : Create a new cscpAuditLog.
     *
     * @param cscpAuditLog the cscpAuditLog to create
     * @return the ResponseEntity with status 201 (Created) and with body the new cscpAuditLog, or with status 400 (Bad Request) if the cscpAuditLog has already an id
     * @throws URISyntaxException if the Location URI syntax is incorrect
     */
    @PostMapping("/cscpAuditLogs")
    @OperationLog(dBOperation = DBOperation.ADD,message = "新增审计日志")
    public ResponseEntity<CscpAuditLog> createCscpAuditLog(@RequestBody CscpAuditLog cscpAuditLog) throws URISyntaxException {
        log.debug("REST request to save CscpAuditLog : {}", cscpAuditLog);
        cscpAuditLogRepository.insert(cscpAuditLog);
        return ResponseEntity.created(new URI("/api/cscpAuditLogs" + "/" + cscpAuditLog.getId()))
                .headers(HeaderUtil.createEntityCreationAlert(ENTITY_NAME, cscpAuditLog.getId().toString()))
                .body(cscpAuditLog);
    }

    /**
     * PUT  /cscpAuditLogs : Updates an existing cscpAuditLog.
     *
     * @param cscpAuditLog the cscpAuditLog to update
     * @return the ResponseEntity with status 200 (OK) and with body the updated cscpAuditLog,
     * or with status 400 (Bad Request) if the cscpAuditLog is not valid,
     * or with status 500 (Internal Server Error) if the cscpAuditLog couldn't be updated
     * @throws URISyntaxException if the Location URI syntax is incorrect
     */
    @PutMapping("/cscpAuditLogs")
    @OperationLog(dBOperation = DBOperation.UPDATE,message = "更新审计日志")
    public ResponseEntity<CscpAuditLog> updateCscpAuditLog(@RequestBody CscpAuditLog cscpAuditLog) {
        log.debug("REST request to update CscpAuditLog : {}", cscpAuditLog);
        cscpAuditLogRepository.updateById(cscpAuditLog);
        return ResponseEntity.ok()
                .headers(HeaderUtil.createEntityUpdateAlert(ENTITY_NAME, cscpAuditLog.getId().toString()))
                .body(cscpAuditLog);
    }

    /**
     * GET  /cscpAuditLogs : get the cscpAuditLogs with page.
     *
     * @return the ResponseEntity with status 200 (OK) and the list of cscpAuditLogs in body

     @GetMapping("/cscpAuditLogsByCriteria") public PageResult<CscpAuditLog> getCscpAuditLogsByCriteria(CscpAuditLogExample cscpAuditLogExample, Pageable page) {
     log.debug("REST request to get CscpAuditLogsByCriteria");
     return cscpAuditLogService.findByExample(cscpAuditLogExample, page);
     }

     /**
      * GET  /cscpAuditLogs : get the cscpAuditLogs.
      *
      * @return the ResponseEntity with status 200 (OK) and the list of cscpAuditLogs in body

     @GetMapping("/cscpAuditLogsList") public PageResult<CscpAuditLog> getCscpAuditLogsList(CscpAuditLogExample cscpAuditLogExample) {
     log.debug("REST request to get CscpAuditLogsList");
     return cscpAuditLogService.findByExample(cscpAuditLogExample);
     }
     */
    /**
     * GET  /cscpAuditLogs/:id : get the "id" cscpAuditLog.
     *
     * @param id the id of the cscpAuditLog to retrieve
     * @return the ResponseEntity with status 200 (OK) and with body the cscpAuditLog, or with status 404 (Not Found)
     */
    @GetMapping("/cscpAuditLogs/{id}")
    public ResponseEntity<CscpAuditLog> getCscpAuditLog(@PathVariable String id) {
        log.debug("REST request to get CscpAuditLog : {}", id);
        CscpAuditLog cscpAuditLog = cscpAuditLogRepository.selectById(id);
        return ResponseUtil.wrapOrNotFound(Optional.ofNullable(cscpAuditLog));
    }

    /**
     * DELETE  /cscpAuditLogs/:id : delete the "id" cscpAuditLog.
     *
     * @param id the id of the cscpAuditLog to delete
     * @return the ResponseEntity with status 200 (OK)
     */
    @DeleteMapping("/cscpAuditLogs/{id}")
    @OperationLog(dBOperation = DBOperation.DELETE,message = "删除审计日志")
    public ResponseEntity<Void> deleteCscpAuditLog(@PathVariable String id) {
        log.debug("REST request to delete CscpAuditLog : {}", id);
        cscpAuditLogRepository.deleteById(id);
        return ResponseEntity.ok().headers(HeaderUtil.createEntityDeletionAlert(ENTITY_NAME, id.toString())).build();
    }


    /**
     * 审批意见列表
     *
     * @param PROCESS_INSTANCE_ID
     * @return
     */
    @GetMapping("/getAuditLogList/{PROCESS_INSTANCE_ID}")
    public ResponseBO<List<CscpAuditLog>> getAuditLogList(@PathVariable String PROCESS_INSTANCE_ID) {
        try {
            List<CscpAuditLog> cscpAuditLogs = cscpAuditLogService.getAuditLogList(PROCESS_INSTANCE_ID); //cscpAuditContentService.getAuditContentList(PROCESS_INSTANCE_ID);
            return new ResponseBO<>(cscpAuditLogs, SysErrEnum.SUCCESS);
        } catch (Exception e) {
            log.error("getActSet Exception", e);
            return new ResponseBO<>(SysErrEnum.ERROR);
        }
    }
}
