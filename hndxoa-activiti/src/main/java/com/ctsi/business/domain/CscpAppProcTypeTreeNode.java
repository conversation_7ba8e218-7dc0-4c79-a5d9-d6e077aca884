package com.ctsi.business.domain;

import java.io.Serializable;
import java.util.List;

public class CscpAppProcTypeTreeNode implements Serializable {

    public static String NODE_TYPE_ROOT = "root";
    public static String NODE_TYPE_APP = "app";
    public static String NODE_TYPE_PROC_TYPE = "procType";


    private String id;
    private String title;
    private String appCode;
    private String appName;
    private String nodeType;
    private boolean checked = false;
    private List<CscpAppProcTypeTreeNode> children;


    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getAppCode() {
        return appCode;
    }

    public void setAppCode(String appCode) {
        this.appCode = appCode;
    }

    public List<CscpAppProcTypeTreeNode> getChildren() {
        return children;
    }

    public void setChildren(List<CscpAppProcTypeTreeNode> children) {
        this.children = children;
    }

    public String getNodeType() {
        return nodeType;
    }

    public void setNodeType(String nodeType) {
        this.nodeType = nodeType;
    }

    public String getAppName() {
        return appName;
    }

    public void setAppName(String appName) {
        this.appName = appName;
    }

    public boolean isChecked() {
        return checked;
    }

    public void setChecked(boolean checked) {
        this.checked = checked;
    }
}
