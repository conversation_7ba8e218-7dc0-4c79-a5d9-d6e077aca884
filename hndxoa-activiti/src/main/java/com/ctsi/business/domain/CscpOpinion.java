package com.ctsi.business.domain;

import com.ctsi.hndx.common.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class CscpOpinion extends BaseEntity {

    @ApiModelProperty(value = "意见类型 0：个人意见 1:单位意见")
    private String opinionType;

    private String opinion;

    private Integer sort;


}