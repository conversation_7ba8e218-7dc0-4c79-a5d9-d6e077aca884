package com.ctsi.business.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.ctsi.hndx.common.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
*/
@Data
@EqualsAndHashCode(callSuper = true)
public class CscpAuditContent extends BaseEntity {


    public static final String  SXQP = "文本手写签批。";

    public static final String  WYQP = "wyqianpi";

    public static final String  WYQP_ZC = "手写签批。";

    private String procDefId;

    private String procInstId;

    @ApiModelProperty("父节点的流程实例id")
    private String rootProcInstId;

    @ApiModelProperty("流程步骤")
    private String actId;

    @ApiModelProperty("审核名称")
    private String actName;

    @ApiModelProperty("审核人id")
    private String auditorId;

    @ApiModelProperty("审核人")
    private String auditorName;

    @ApiModelProperty("审核时间")
    private String auditTime;

    @ApiModelProperty("委托人id")
    private String delegatorId;

    @ApiModelProperty("委托人姓名")
    private String delegatorName;

    @ApiModelProperty("审核内容")
    private String auditContent;

    @ApiModelProperty("意见，不出现再处理单的")
    private String processContent;

    @ApiModelProperty("审核状态0正常，1回退 2是移交，3跳转，5-暂存")
    private String auditStatus;


    @ApiModelProperty("审核客户端")
    private String clientType;


    @ApiModelProperty("对应表单的类型")
    private String commentsFormId;

    @ApiModelProperty("业务id")
    private String businessId;

    @ApiModelProperty("图片签章的路径")
    private String signImage;

    /**
     * 签名图片url
     */
    @ApiModelProperty(value = "签名图片url")
    @TableField(value = "signature_image_url")
    private String signatureImageURL;

    @ApiModelProperty("签批类型  0-文本签批 1-手写签批 2-手写和文本签批")
    private Integer signType;

    @ApiModelProperty("排序编号")
    private String pathCode;

    @ApiModelProperty("手机号码")
    private String mobile;

    @ApiModelProperty("任务ID")
    private String taskId;

    /**
     * 流程状态：0业务没有与流程关联，1启动流程，2办理中，3完成，4暂停，5作废。其他参考具体文档，见常量BpmStatusConstants
     */
    @ApiModelProperty("流程状态：0业务没有与流程关联，1启动流程，2办理中，3完成，4暂停，5作废。其他参考具体文档，见常量BpmStatusConstants")
    @TableField(exist = false)
    private Integer bpmStatus;

    @ApiModelProperty("手写签批X坐标")
    private Integer xCoordinate;

    @ApiModelProperty("手写签批Y坐标")
    private Integer yCoordinate;

    @ApiModelProperty("手写签批宽度")
    private String width;

    @ApiModelProperty("手写签批高度")
    private String high;
}