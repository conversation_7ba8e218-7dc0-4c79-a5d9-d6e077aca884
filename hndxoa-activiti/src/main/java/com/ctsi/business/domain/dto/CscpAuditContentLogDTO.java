package com.ctsi.business.domain.dto;

import com.ctsi.hndx.common.BaseDtoEntity;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 审核意见日志表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-13
 */
@Getter
@Setter
@ApiModel(value = "CscpAuditContentLogDTO对象", description = "审核意见日志表")
public class CscpAuditContentLogDTO extends BaseDtoEntity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("流程历史id")
    private String contentId;

    @ApiModelProperty("流程实例id")
    private String procInstId;

    @ApiModelProperty("审核名称")
    private String actName;

    @ApiModelProperty("流程步骤")
    private String actId;

    @ApiModelProperty("修改前的审核内容")
    private String oldAuditContent;

    @ApiModelProperty("修改后的审核内容")
    private String newAuditContent;


}
