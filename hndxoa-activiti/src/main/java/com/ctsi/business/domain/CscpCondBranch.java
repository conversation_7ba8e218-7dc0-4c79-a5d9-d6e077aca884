package com.ctsi.business.domain;

import java.io.Serializable;

/**
 * <AUTHOR>
*/
public class CscpCondBranch implements Serializable {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column CSCP_COND_BRANCH.ID
     *
     * @mbg.generated Wed Dec 04 18:09:18 CST 2019
     */
    private String id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column CSCP_COND_BRANCH.TARGET_ACT_ID
     *
     * @mbg.generated Wed Dec 04 18:09:18 CST 2019
     */
    private String targetActId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column CSCP_COND_BRANCH.SHOWNUM
     *
     * @mbg.generated Wed Dec 04 18:09:18 CST 2019
     */
    private String shownum;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column CSCP_COND_BRANCH.MODEL_KEY
     *
     * @mbg.generated Wed Dec 04 18:09:18 CST 2019
     */
    private String modelKey;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column CSCP_COND_BRANCH.CREATE_DATE
     *
     * @mbg.generated Wed Dec 04 18:09:18 CST 2019
     */
    private String createDate;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column CSCP_COND_BRANCH.CREATE_USER
     *
     * @mbg.generated Wed Dec 04 18:09:18 CST 2019
     */
    private String createUser;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column CSCP_COND_BRANCH.CURRENT_ACT_ID
     *
     * @mbg.generated Wed Dec 04 18:09:18 CST 2019
     */
    private String currentActId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column CSCP_COND_BRANCH.PROC_DEF_ID
     *
     * @mbg.generated Wed Dec 04 18:09:18 CST 2019
     */
    private String procDefId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column CSCP_COND_BRANCH.EXPRESSION
     *
     * @mbg.generated Wed Dec 04 18:09:18 CST 2019
     */
    private String expression;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column CSCP_COND_BRANCH.ID
     *
     * @return the value of CSCP_COND_BRANCH.ID
     *
     * @mbg.generated Wed Dec 04 18:09:18 CST 2019
     */
    public String getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column CSCP_COND_BRANCH.ID
     *
     * @param id the value for CSCP_COND_BRANCH.ID
     *
     * @mbg.generated Wed Dec 04 18:09:18 CST 2019
     */
    public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column CSCP_COND_BRANCH.TARGET_ACT_ID
     *
     * @return the value of CSCP_COND_BRANCH.TARGET_ACT_ID
     *
     * @mbg.generated Wed Dec 04 18:09:18 CST 2019
     */
    public String getTargetActId() {
        return targetActId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column CSCP_COND_BRANCH.TARGET_ACT_ID
     *
     * @param targetActId the value for CSCP_COND_BRANCH.TARGET_ACT_ID
     *
     * @mbg.generated Wed Dec 04 18:09:18 CST 2019
     */
    public void setTargetActId(String targetActId) {
        this.targetActId = targetActId == null ? null : targetActId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column CSCP_COND_BRANCH.SHOWNUM
     *
     * @return the value of CSCP_COND_BRANCH.SHOWNUM
     *
     * @mbg.generated Wed Dec 04 18:09:18 CST 2019
     */
    public String getShownum() {
        return shownum;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column CSCP_COND_BRANCH.SHOWNUM
     *
     * @param shownum the value for CSCP_COND_BRANCH.SHOWNUM
     *
     * @mbg.generated Wed Dec 04 18:09:18 CST 2019
     */
    public void setShownum(String shownum) {
        this.shownum = shownum == null ? null : shownum.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column CSCP_COND_BRANCH.MODEL_KEY
     *
     * @return the value of CSCP_COND_BRANCH.MODEL_KEY
     *
     * @mbg.generated Wed Dec 04 18:09:18 CST 2019
     */
    public String getModelKey() {
        return modelKey;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column CSCP_COND_BRANCH.MODEL_KEY
     *
     * @param modelKey the value for CSCP_COND_BRANCH.MODEL_KEY
     *
     * @mbg.generated Wed Dec 04 18:09:18 CST 2019
     */
    public void setModelKey(String modelKey) {
        this.modelKey = modelKey == null ? null : modelKey.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column CSCP_COND_BRANCH.CREATE_DATE
     *
     * @return the value of CSCP_COND_BRANCH.CREATE_DATE
     *
     * @mbg.generated Wed Dec 04 18:09:18 CST 2019
     */
    public String getCreateDate() {
        return createDate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column CSCP_COND_BRANCH.CREATE_DATE
     *
     * @param createDate the value for CSCP_COND_BRANCH.CREATE_DATE
     *
     * @mbg.generated Wed Dec 04 18:09:18 CST 2019
     */
    public void setCreateDate(String createDate) {
        this.createDate = createDate == null ? null : createDate.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column CSCP_COND_BRANCH.CREATE_USER
     *
     * @return the value of CSCP_COND_BRANCH.CREATE_USER
     *
     * @mbg.generated Wed Dec 04 18:09:18 CST 2019
     */
    public String getCreateUser() {
        return createUser;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column CSCP_COND_BRANCH.CREATE_USER
     *
     * @param createUser the value for CSCP_COND_BRANCH.CREATE_USER
     *
     * @mbg.generated Wed Dec 04 18:09:18 CST 2019
     */
    public void setCreateUser(String createUser) {
        this.createUser = createUser == null ? null : createUser.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column CSCP_COND_BRANCH.CURRENT_ACT_ID
     *
     * @return the value of CSCP_COND_BRANCH.CURRENT_ACT_ID
     *
     * @mbg.generated Wed Dec 04 18:09:18 CST 2019
     */
    public String getCurrentActId() {
        return currentActId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column CSCP_COND_BRANCH.CURRENT_ACT_ID
     *
     * @param currentActId the value for CSCP_COND_BRANCH.CURRENT_ACT_ID
     *
     * @mbg.generated Wed Dec 04 18:09:18 CST 2019
     */
    public void setCurrentActId(String currentActId) {
        this.currentActId = currentActId == null ? null : currentActId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column CSCP_COND_BRANCH.PROC_DEF_ID
     *
     * @return the value of CSCP_COND_BRANCH.PROC_DEF_ID
     *
     * @mbg.generated Wed Dec 04 18:09:18 CST 2019
     */
    public String getProcDefId() {
        return procDefId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column CSCP_COND_BRANCH.PROC_DEF_ID
     *
     * @param procDefId the value for CSCP_COND_BRANCH.PROC_DEF_ID
     *
     * @mbg.generated Wed Dec 04 18:09:18 CST 2019
     */
    public void setProcDefId(String procDefId) {
        this.procDefId = procDefId == null ? null : procDefId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column CSCP_COND_BRANCH.EXPRESSION
     *
     * @return the value of CSCP_COND_BRANCH.EXPRESSION
     *
     * @mbg.generated Wed Dec 04 18:09:18 CST 2019
     */
    public String getExpression() {
        return expression;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column CSCP_COND_BRANCH.EXPRESSION
     *
     * @param expression the value for CSCP_COND_BRANCH.EXPRESSION
     *
     * @mbg.generated Wed Dec 04 18:09:18 CST 2019
     */
    public void setExpression(String expression) {
        this.expression = expression == null ? null : expression.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table CSCP_COND_BRANCH
     *
     * @mbg.generated Wed Dec 04 18:09:18 CST 2019
     */
    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        CscpCondBranch other = (CscpCondBranch) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getTargetActId() == null ? other.getTargetActId() == null : this.getTargetActId().equals(other.getTargetActId()))
            && (this.getShownum() == null ? other.getShownum() == null : this.getShownum().equals(other.getShownum()))
            && (this.getModelKey() == null ? other.getModelKey() == null : this.getModelKey().equals(other.getModelKey()))
            && (this.getCreateDate() == null ? other.getCreateDate() == null : this.getCreateDate().equals(other.getCreateDate()))
            && (this.getCreateUser() == null ? other.getCreateUser() == null : this.getCreateUser().equals(other.getCreateUser()))
            && (this.getCurrentActId() == null ? other.getCurrentActId() == null : this.getCurrentActId().equals(other.getCurrentActId()))
            && (this.getProcDefId() == null ? other.getProcDefId() == null : this.getProcDefId().equals(other.getProcDefId()))
            && (this.getExpression() == null ? other.getExpression() == null : this.getExpression().equals(other.getExpression()));
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table CSCP_COND_BRANCH
     *
     * @mbg.generated Wed Dec 04 18:09:18 CST 2019
     */
    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getTargetActId() == null) ? 0 : getTargetActId().hashCode());
        result = prime * result + ((getShownum() == null) ? 0 : getShownum().hashCode());
        result = prime * result + ((getModelKey() == null) ? 0 : getModelKey().hashCode());
        result = prime * result + ((getCreateDate() == null) ? 0 : getCreateDate().hashCode());
        result = prime * result + ((getCreateUser() == null) ? 0 : getCreateUser().hashCode());
        result = prime * result + ((getCurrentActId() == null) ? 0 : getCurrentActId().hashCode());
        result = prime * result + ((getProcDefId() == null) ? 0 : getProcDefId().hashCode());
        result = prime * result + ((getExpression() == null) ? 0 : getExpression().hashCode());
        return result;
    }
}