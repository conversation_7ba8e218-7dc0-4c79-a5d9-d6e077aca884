package com.ctsi.business.domain.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import net.sf.json.JSONObject;

import java.io.Serializable;

/**
 * @Classname CscspProcPermissionParam
 * @Description
 * @Date 2021/11/23 12:49
 * @Created liubin
 */

@Data
@ApiModel
public class CscspProcPermissionParam implements Serializable {


    @ApiModelProperty("主键id")
    private String id;


    @ApiModelProperty("是否需要进行权限控制，默认不进行权限控制")
    private boolean permission;

    @ApiModelProperty("授权的类型，例如按照工作组，按照部门，按照个人")
    private String permissionType;

    @ApiModelProperty("授权的类型集合，例如部门组集合，工作组集合")
    private JSONObject permissionTypeValues;
}
