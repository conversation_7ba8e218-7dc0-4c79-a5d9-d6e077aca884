package com.ctsi.common.bo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class AppDetailValue{



    @ApiModelProperty(value = "字段值")
    String filedValue;

    @ApiModelProperty("是否只读")
    private Boolean disabled;


    @ApiModelProperty("是否必填")
    private Boolean required;


    @ApiModelProperty("字段类型")
    private String fieldType;


    @ApiModelProperty("字段标题'")
    private String fieldName;


    @ApiModelProperty("字段名称")
    private String modelItemId;


}