package com.ctsi.activiti.bpmn.entity;

import java.io.Serializable;

/**
 * <AUTHOR>
*/
public class BpmnLine implements Serializable {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column CSCP_PROC_LINE.ID_
     *
     * @mbg.generated Wed Jun 12 11:07:39 CST 2019
     */
    private String id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column CSCP_PROC_LINE.PROCESS_DEFINITION_KEY_
     *
     * @mbg.generated Wed Jun 12 11:07:39 CST 2019
     */
    private String processDefinitionKey;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column CSCP_PROC_LINE.PROCESS_DEFINITION_ID_
     *
     * @mbg.generated Wed Jun 12 11:07:39 CST 2019
     */
    private String processDefinitionId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column CSCP_PROC_LINE.PROCESS_DEPLOYMENT_ID_
     *
     * @mbg.generated Wed Jun 12 11:07:39 CST 2019
     */
    private String processDeploymentId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column CSCP_PROC_LINE.NAME_
     *
     * @mbg.generated Wed Jun 12 11:07:39 CST 2019
     */
    private String name;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column CSCP_PROC_LINE.LINE_KEY_
     *
     * @mbg.generated Wed Jun 12 11:07:39 CST 2019
     */
    private String lineKey;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column CSCP_PROC_LINE.FROM_KEY_
     *
     * @mbg.generated Wed Jun 12 11:07:39 CST 2019
     */
    private String fromKey;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column CSCP_PROC_LINE.TO_KEY_
     *
     * @mbg.generated Wed Jun 12 11:07:39 CST 2019
     */
    private String toKey;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column CSCP_PROC_LINE.EXPRESSION_
     *
     * @mbg.generated Wed Jun 12 11:07:39 CST 2019
     */
    private String expression;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column CSCP_PROC_LINE.MULTI_INSTANCE_RESULT
     *
     * @mbg.generated Wed Jun 12 11:07:39 CST 2019
     */
    private String multiInstanceResult;


    /**
     * 1 表示默认线路
     */
    private String defaultLine;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column CSCP_PROC_LINE.ID_
     *
     * @return the value of CSCP_PROC_LINE.ID_
     *
     * @mbg.generated Wed Jun 12 11:07:39 CST 2019
     */
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }

    public String getProcessDefinitionKey() {
        return processDefinitionKey;
    }

    public void setProcessDefinitionKey(String processDefinitionKey) {
        this.processDefinitionKey = processDefinitionKey == null ? null : processDefinitionKey.trim();
    }

    public String getProcessDefinitionId() {
        return processDefinitionId;
    }

    public void setProcessDefinitionId(String processDefinitionId) {
        this.processDefinitionId = processDefinitionId == null ? null : processDefinitionId.trim();
    }

    public String getProcessDeploymentId() {
        return processDeploymentId;
    }

    public void setProcessDeploymentId(String processDeploymentId) {
        this.processDeploymentId = processDeploymentId == null ? null : processDeploymentId.trim();
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name == null ? null : name.trim();
    }

    public String getLineKey() {
        return lineKey;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column CSCP_PROC_LINE.LINE_KEY_
     *
     * @param lineKey the value for CSCP_PROC_LINE.LINE_KEY_
     *
     * @mbg.generated Wed Jun 12 11:07:39 CST 2019
     */
    public void setLineKey(String lineKey) {
        this.lineKey = lineKey == null ? null : lineKey.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column CSCP_PROC_LINE.FROM_KEY_
     *
     * @return the value of CSCP_PROC_LINE.FROM_KEY_
     *
     * @mbg.generated Wed Jun 12 11:07:39 CST 2019
     */
    public String getFromKey() {
        return fromKey;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column CSCP_PROC_LINE.FROM_KEY_
     *
     * @param fromKey the value for CSCP_PROC_LINE.FROM_KEY_
     *
     * @mbg.generated Wed Jun 12 11:07:39 CST 2019
     */
    public void setFromKey(String fromKey) {
        this.fromKey = fromKey == null ? null : fromKey.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column CSCP_PROC_LINE.TO_KEY_
     *
     * @return the value of CSCP_PROC_LINE.TO_KEY_
     *
     * @mbg.generated Wed Jun 12 11:07:39 CST 2019
     */
    public String getToKey() {
        return toKey;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column CSCP_PROC_LINE.TO_KEY_
     *
     * @param toKey the value for CSCP_PROC_LINE.TO_KEY_
     *
     * @mbg.generated Wed Jun 12 11:07:39 CST 2019
     */
    public void setToKey(String toKey) {
        this.toKey = toKey == null ? null : toKey.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column CSCP_PROC_LINE.EXPRESSION_
     *
     * @return the value of CSCP_PROC_LINE.EXPRESSION_
     *
     * @mbg.generated Wed Jun 12 11:07:39 CST 2019
     */
    public String getExpression() {
        return expression;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column CSCP_PROC_LINE.EXPRESSION_
     *
     * @param expression the value for CSCP_PROC_LINE.EXPRESSION_
     *
     * @mbg.generated Wed Jun 12 11:07:39 CST 2019
     */
    public void setExpression(String expression) {
        this.expression = expression == null ? null : expression.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column CSCP_PROC_LINE.MULTI_INSTANCE_RESULT
     *
     * @return the value of CSCP_PROC_LINE.MULTI_INSTANCE_RESULT
     *
     * @mbg.generated Wed Jun 12 11:07:39 CST 2019
     */
    public String getMultiInstanceResult() {
        return multiInstanceResult;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column CSCP_PROC_LINE.MULTI_INSTANCE_RESULT
     *
     * @param multiInstanceResult the value for CSCP_PROC_LINE.MULTI_INSTANCE_RESULT
     *
     * @mbg.generated Wed Jun 12 11:07:39 CST 2019
     */
    public void setMultiInstanceResult(String multiInstanceResult) {
        this.multiInstanceResult = multiInstanceResult == null ? null : multiInstanceResult.trim();
    }


    public String getDefaultLine() {
        return defaultLine;
    }

    public void setDefaultLine(String defaultLine) {
        this.defaultLine = defaultLine;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table CSCP_PROC_LINE
     *
     * @mbg.generated Wed Jun 12 11:07:39 CST 2019
     */
    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        BpmnLine other = (BpmnLine) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getProcessDefinitionKey() == null ? other.getProcessDefinitionKey() == null : this.getProcessDefinitionKey().equals(other.getProcessDefinitionKey()))
            && (this.getProcessDefinitionId() == null ? other.getProcessDefinitionId() == null : this.getProcessDefinitionId().equals(other.getProcessDefinitionId()))
            && (this.getProcessDeploymentId() == null ? other.getProcessDeploymentId() == null : this.getProcessDeploymentId().equals(other.getProcessDeploymentId()))
            && (this.getName() == null ? other.getName() == null : this.getName().equals(other.getName()))
            && (this.getLineKey() == null ? other.getLineKey() == null : this.getLineKey().equals(other.getLineKey()))
            && (this.getFromKey() == null ? other.getFromKey() == null : this.getFromKey().equals(other.getFromKey()))
            && (this.getToKey() == null ? other.getToKey() == null : this.getToKey().equals(other.getToKey()))
            && (this.getExpression() == null ? other.getExpression() == null : this.getExpression().equals(other.getExpression()))
            && (this.getMultiInstanceResult() == null ? other.getMultiInstanceResult() == null : this.getMultiInstanceResult().equals(other.getMultiInstanceResult()));
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table CSCP_PROC_LINE
     *
     * @mbg.generated Wed Jun 12 11:07:39 CST 2019
     */
    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getProcessDefinitionKey() == null) ? 0 : getProcessDefinitionKey().hashCode());
        result = prime * result + ((getProcessDefinitionId() == null) ? 0 : getProcessDefinitionId().hashCode());
        result = prime * result + ((getProcessDeploymentId() == null) ? 0 : getProcessDeploymentId().hashCode());
        result = prime * result + ((getName() == null) ? 0 : getName().hashCode());
        result = prime * result + ((getLineKey() == null) ? 0 : getLineKey().hashCode());
        result = prime * result + ((getFromKey() == null) ? 0 : getFromKey().hashCode());
        result = prime * result + ((getToKey() == null) ? 0 : getToKey().hashCode());
        result = prime * result + ((getExpression() == null) ? 0 : getExpression().hashCode());
        result = prime * result + ((getMultiInstanceResult() == null) ? 0 : getMultiInstanceResult().hashCode());
        return result;
    }
}