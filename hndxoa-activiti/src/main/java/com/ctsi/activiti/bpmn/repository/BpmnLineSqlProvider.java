package com.ctsi.activiti.bpmn.repository;

import com.ctsi.activiti.bpmn.entity.BpmnLine;
import com.ctsi.activiti.bpmn.entity.BpmnLineExample;
import com.ctsi.activiti.bpmn.entity.BpmnLineExample.Criteria;
import com.ctsi.activiti.bpmn.entity.BpmnLineExample.Criterion;
import org.apache.ibatis.jdbc.SQL;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
*/
public class BpmnLineSqlProvider {

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table CSCP_PROC_LINE
     *
     * @mbg.generated Wed Jun 12 11:07:39 CST 2019
     */
    public String countByExample(BpmnLineExample example) {
        SQL sql = new SQL();
        sql.SELECT("count(*)").FROM("CSCP_PROC_LINE");
        applyWhere(sql, example, false);
        return sql.toString();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table CSCP_PROC_LINE
     *
     * @mbg.generated Wed Jun 12 11:07:39 CST 2019
     */
    public String deleteByExample(BpmnLineExample example) {
        SQL sql = new SQL();
        sql.DELETE_FROM("CSCP_PROC_LINE");
        applyWhere(sql, example, false);
        return sql.toString();
    }

    public String selectForNextNode(BpmnLineExample example) {
        StringBuilder sql = new StringBuilder();
        if(example != null && example.isDistinct()) {
            sql.append("SELECT DISTINCT CL.ID_, ");
        } else {
            sql.append("SELECT  CL.ID_, ");
        }
        sql.append(" CL.PROCESS_DEFINITION_KEY_, CL.PROCESS_DEFINITION_ID_, CL.PROCESS_DEPLOYMENT_ID_, CN.NAME as NAME_," +
                " CL.LINE_KEY_, CL.FROM_KEY_, CL.TO_KEY_, CL.EXPRESSION_, CL.MULTI_INSTANCE_RESULT,cL.default_line FROM CSCP_PROC_LINE CL ");
        sql.append(" INNER JOIN CSCP_PROC_NODE CN ON CN.NODE_KEY = CL.TO_KEY_ AND CN.PROCESS_DEPLOYMENT_ID = CL.PROCESS_DEPLOYMENT_ID_ ");
        sql.append(" WHERE ");
        sql.append(" CL.PROCESS_DEFINITION_ID_ = '");
        sql.append(example.getOredCriteria().get(0).getCriteria().get(0).getValue());
        sql.append("'");
        sql.append(" AND ");
        sql.append(" CL.FROM_KEY_ = '");
        sql.append(example.getOredCriteria().get(0).getCriteria().get(1).getValue());
        sql.append("'");
        return sql.toString();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table CSCP_PROC_LINE
     *
     * @mbg.generated Wed Jun 12 11:07:39 CST 2019
     */
    public String selectByExample(BpmnLineExample example) {
        SQL sql = new SQL();
        if (example != null && example.isDistinct()) {
            sql.SELECT_DISTINCT("ID_");
        } else {
            sql.SELECT("ID_");
        }
        sql.SELECT("PROCESS_DEFINITION_KEY_");
        sql.SELECT("PROCESS_DEFINITION_ID_");
        sql.SELECT("PROCESS_DEPLOYMENT_ID_");
        sql.SELECT("NAME_");
        sql.SELECT("LINE_KEY_");
        sql.SELECT("FROM_KEY_");
        sql.SELECT("TO_KEY_");
        sql.SELECT("EXPRESSION_");
        sql.SELECT("MULTI_INSTANCE_RESULT");
        sql.SELECT("default_line");
        sql.FROM("CSCP_PROC_LINE");
        applyWhere(sql, example, false);
        
        if (example != null && example.getOrderByClause() != null) {
            sql.ORDER_BY(example.getOrderByClause());
        }
        
        return sql.toString();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table CSCP_PROC_LINE
     *
     * @mbg.generated Wed Jun 12 11:07:39 CST 2019
     */
    public String updateByExampleSelective(Map<String, Object> parameter) {
        BpmnLine record = (BpmnLine) parameter.get("record");
        BpmnLineExample example = (BpmnLineExample) parameter.get("example");
        
        SQL sql = new SQL();
        sql.UPDATE("CSCP_PROC_LINE");
        
        if (record.getId() != null) {
            sql.SET("ID_ = #{record.id,jdbcType=VARCHAR}");
        }
        
        if (record.getProcessDefinitionKey() != null) {
            sql.SET("PROCESS_DEFINITION_KEY_ = #{record.processDefinitionKey,jdbcType=VARCHAR}");
        }
        
        if (record.getProcessDefinitionId() != null) {
            sql.SET("PROCESS_DEFINITION_ID_ = #{record.processDefinitionId,jdbcType=VARCHAR}");
        }
        
        if (record.getProcessDeploymentId() != null) {
            sql.SET("PROCESS_DEPLOYMENT_ID_ = #{record.processDeploymentId,jdbcType=VARCHAR}");
        }
        
        if (record.getName() != null) {
            sql.SET("NAME_ = #{record.name,jdbcType=VARCHAR}");
        }
        
        if (record.getLineKey() != null) {
            sql.SET("LINE_KEY_ = #{record.lineKey,jdbcType=VARCHAR}");
        }
        
        if (record.getFromKey() != null) {
            sql.SET("FROM_KEY_ = #{record.fromKey,jdbcType=VARCHAR}");
        }
        
        if (record.getToKey() != null) {
            sql.SET("TO_KEY_ = #{record.toKey,jdbcType=VARCHAR}");
        }
        
        if (record.getExpression() != null) {
            sql.SET("EXPRESSION_ = #{record.expression,jdbcType=VARCHAR}");
        }
        
        if (record.getMultiInstanceResult() != null) {
            sql.SET("MULTI_INSTANCE_RESULT = #{record.multiInstanceResult,jdbcType=VARCHAR}");
        }
        
        applyWhere(sql, example, true);
        return sql.toString();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table CSCP_PROC_LINE
     *
     * @mbg.generated Wed Jun 12 11:07:39 CST 2019
     */
    public String updateByExample(Map<String, Object> parameter) {
        SQL sql = new SQL();
        sql.UPDATE("CSCP_PROC_LINE");
        
        sql.SET("ID_ = #{record.id,jdbcType=VARCHAR}");
        sql.SET("PROCESS_DEFINITION_KEY_ = #{record.processDefinitionKey,jdbcType=VARCHAR}");
        sql.SET("PROCESS_DEFINITION_ID_ = #{record.processDefinitionId,jdbcType=VARCHAR}");
        sql.SET("PROCESS_DEPLOYMENT_ID_ = #{record.processDeploymentId,jdbcType=VARCHAR}");
        sql.SET("NAME_ = #{record.name,jdbcType=VARCHAR}");
        sql.SET("LINE_KEY_ = #{record.lineKey,jdbcType=VARCHAR}");
        sql.SET("FROM_KEY_ = #{record.fromKey,jdbcType=VARCHAR}");
        sql.SET("TO_KEY_ = #{record.toKey,jdbcType=VARCHAR}");
        sql.SET("EXPRESSION_ = #{record.expression,jdbcType=VARCHAR}");
        sql.SET("MULTI_INSTANCE_RESULT = #{record.multiInstanceResult,jdbcType=VARCHAR}");
        
        BpmnLineExample example = (BpmnLineExample) parameter.get("example");
        applyWhere(sql, example, true);
        return sql.toString();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table CSCP_PROC_LINE
     *
     * @mbg.generated Wed Jun 12 11:07:39 CST 2019
     */
    protected void applyWhere(SQL sql, BpmnLineExample example, boolean includeExamplePhrase) {
        if (example == null) {
            return;
        }
        
        String parmPhrase1;
        String parmPhrase1_th;
        String parmPhrase2;
        String parmPhrase2_th;
        String parmPhrase3;
        String parmPhrase3_th;
        if (includeExamplePhrase) {
            parmPhrase1 = "%s #{example.oredCriteria[%d].allCriteria[%d].value}";
            parmPhrase1_th = "%s #{example.oredCriteria[%d].allCriteria[%d].value,typeHandler=%s}";
            parmPhrase2 = "%s #{example.oredCriteria[%d].allCriteria[%d].value} and #{example.oredCriteria[%d].criteria[%d].secondValue}";
            parmPhrase2_th = "%s #{example.oredCriteria[%d].allCriteria[%d].value,typeHandler=%s} and #{example.oredCriteria[%d].criteria[%d].secondValue,typeHandler=%s}";
            parmPhrase3 = "#{example.oredCriteria[%d].allCriteria[%d].value[%d]}";
            parmPhrase3_th = "#{example.oredCriteria[%d].allCriteria[%d].value[%d],typeHandler=%s}";
        } else {
            parmPhrase1 = "%s #{oredCriteria[%d].allCriteria[%d].value}";
            parmPhrase1_th = "%s #{oredCriteria[%d].allCriteria[%d].value,typeHandler=%s}";
            parmPhrase2 = "%s #{oredCriteria[%d].allCriteria[%d].value} and #{oredCriteria[%d].criteria[%d].secondValue}";
            parmPhrase2_th = "%s #{oredCriteria[%d].allCriteria[%d].value,typeHandler=%s} and #{oredCriteria[%d].criteria[%d].secondValue,typeHandler=%s}";
            parmPhrase3 = "#{oredCriteria[%d].allCriteria[%d].value[%d]}";
            parmPhrase3_th = "#{oredCriteria[%d].allCriteria[%d].value[%d],typeHandler=%s}";
        }
        
        StringBuilder sb = new StringBuilder();
        List<Criteria> oredCriteria = example.getOredCriteria();
        boolean firstCriteria = true;
        for (int i = 0; i < oredCriteria.size(); i++) {
            Criteria criteria = oredCriteria.get(i);
            if (criteria.isValid()) {
                if (firstCriteria) {
                    firstCriteria = false;
                } else {
                    sb.append(" or ");
                }
                
                sb.append('(');
                List<Criterion> criterions = criteria.getAllCriteria();
                boolean firstCriterion = true;
                for (int j = 0; j < criterions.size(); j++) {
                    Criterion criterion = criterions.get(j);
                    if (firstCriterion) {
                        firstCriterion = false;
                    } else {
                        sb.append(" and ");
                    }
                    if (criterion.isNoValue()) {

                        sb.append(criterion.getCondition());
                    } else if (criterion.isSingleValue()) {
                        if (criterion.getTypeHandler() == null) {
                            sb.append(String.format(parmPhrase1, criterion.getCondition(), i, j));
                        } else {
                            sb.append(String.format(parmPhrase1_th, criterion.getCondition(), i, j,criterion.getTypeHandler()));
                        }
                    } else if (criterion.isBetweenValue()) {
                        if (criterion.getTypeHandler() == null) {
                            sb.append(String.format(parmPhrase2, criterion.getCondition(), i, j, i, j));
                        } else {
                            sb.append(String.format(parmPhrase2_th, criterion.getCondition(), i, j, criterion.getTypeHandler(), i, j, criterion.getTypeHandler()));
                        }
                    } else if (criterion.isListValue()) {
                        sb.append(criterion.getCondition());
                        sb.append(" (");
                        List<?> listItems = (List<?>) criterion.getValue();
                        boolean comma = false;
                        for (int k = 0; k < listItems.size(); k++) {
                            if (comma) {
                                sb.append(", ");
                            } else {
                                comma = true;
                            }
                            if (criterion.getTypeHandler() == null) {
                                sb.append(String.format(parmPhrase3, i, j, k));
                            } else {
                                sb.append(String.format(parmPhrase3_th, i, j, k, criterion.getTypeHandler()));
                            }
                        }
                        sb.append(')');
                    }
                }
                sb.append(')');
            }
        }
        
        if (sb.length() > 0) {
            sql.WHERE(sb.toString());
        }
    }
}