package com.ctsi.activiti.bpmn.repository;

import com.ctsi.activiti.bpmn.entity.BpmnLine;
import com.ctsi.activiti.bpmn.entity.BpmnLineExample;
import org.apache.ibatis.annotations.*;
import org.apache.ibatis.type.JdbcType;

import java.util.List;

/**
 * <AUTHOR>
*/
@Mapper
public interface BpmnLineRepository extends com.ctsi.ssdc.repository.BaseRepository<BpmnLine, String, BpmnLineExample> {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table CSCP_PROC_LINE
     *
     * @mbg.generated Wed Jun 12 11:07:39 CST 2019
     */
    @Override
    @SelectProvider(type= BpmnLineSqlProvider.class, method="countByExample")
    long countByExample(BpmnLineExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table CSCP_PROC_LINE
     *
     * @mbg.generated Wed Jun 12 11:07:39 CST 2019
     */
    @Override
    @DeleteProvider(type= BpmnLineSqlProvider.class, method="deleteByExample")
    int deleteByExample(BpmnLineExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table CSCP_PROC_LINE
     *
     * @mbg.generated Wed Jun 12 11:07:39 CST 2019
     */
    @Override
    @Delete({
        "delete from CSCP_PROC_LINE",
        "where ID_ = #{id,jdbcType=VARCHAR}"
    })
    int deleteByPrimaryKey(String id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table CSCP_PROC_LINE
     *
     * @mbg.generated Wed Jun 12 11:07:39 CST 2019
     */
    @Override
    @Insert({
        "insert into CSCP_PROC_LINE (ID_, PROCESS_DEFINITION_KEY_, ",
        "PROCESS_DEFINITION_ID_, PROCESS_DEPLOYMENT_ID_, ",
        "NAME_, LINE_KEY_, FROM_KEY_, ",
        "TO_KEY_, EXPRESSION_, ",
        "MULTI_INSTANCE_RESULT,default_line)",
        "values (#{id,jdbcType=VARCHAR}, #{processDefinitionKey,jdbcType=VARCHAR}, ",
        "#{processDefinitionId,jdbcType=VARCHAR}, #{processDeploymentId,jdbcType=VARCHAR}, ",
        "#{name,jdbcType=VARCHAR}, #{lineKey,jdbcType=VARCHAR}, #{fromKey,jdbcType=VARCHAR}, ",
        "#{toKey,jdbcType=VARCHAR}, #{expression,jdbcType=VARCHAR}, ",
        "#{multiInstanceResult,jdbcType=VARCHAR},",
            "#{defaultLine,jdbcType=VARCHAR})"
    })
    int insert(BpmnLine record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table CSCP_PROC_LINE
     *
     * @mbg.generated Wed Jun 12 11:07:39 CST 2019
     */
//    @InsertProvider(type= BpmnLineSqlProvider.class, method="insertSelective")
//    int insertSelective(BpmnLine record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table CSCP_PROC_LINE
     *
     * @mbg.generated Wed Jun 12 11:07:39 CST 2019
     */
    @Override
    @SelectProvider(type= BpmnLineSqlProvider.class, method="selectByExample")
    @Results({
        @Result(column="ID_", property="id", jdbcType=JdbcType.VARCHAR, id=true),
        @Result(column="PROCESS_DEFINITION_KEY_", property="processDefinitionKey", jdbcType=JdbcType.VARCHAR),
        @Result(column="PROCESS_DEFINITION_ID_", property="processDefinitionId", jdbcType=JdbcType.VARCHAR),
        @Result(column="PROCESS_DEPLOYMENT_ID_", property="processDeploymentId", jdbcType=JdbcType.VARCHAR),
        @Result(column="NAME_", property="name", jdbcType=JdbcType.VARCHAR),
        @Result(column="LINE_KEY_", property="lineKey", jdbcType=JdbcType.VARCHAR),
        @Result(column="FROM_KEY_", property="fromKey", jdbcType=JdbcType.VARCHAR),
        @Result(column="TO_KEY_", property="toKey", jdbcType=JdbcType.VARCHAR),
        @Result(column="EXPRESSION_", property="expression", jdbcType=JdbcType.VARCHAR),
        @Result(column="MULTI_INSTANCE_RESULT", property="multiInstanceResult", jdbcType=JdbcType.VARCHAR),
            @Result(column="default_line", property="defaultLine", jdbcType=JdbcType.VARCHAR)
    })
    List<BpmnLine> selectByExample(BpmnLineExample example);

    @SelectProvider(type= BpmnLineSqlProvider.class, method="selectForNextNode")
    @Results({
            @Result(column="ID_", property="id", jdbcType=JdbcType.VARCHAR, id=true),
            @Result(column="PROCESS_DEFINITION_KEY_", property="processDefinitionKey", jdbcType=JdbcType.VARCHAR),
            @Result(column="PROCESS_DEFINITION_ID_", property="processDefinitionId", jdbcType=JdbcType.VARCHAR),
            @Result(column="PROCESS_DEPLOYMENT_ID_", property="processDeploymentId", jdbcType=JdbcType.VARCHAR),
            @Result(column="NAME_", property="name", jdbcType=JdbcType.VARCHAR),
            @Result(column="LINE_KEY_", property="lineKey", jdbcType=JdbcType.VARCHAR),
            @Result(column="FROM_KEY_", property="fromKey", jdbcType=JdbcType.VARCHAR),
            @Result(column="TO_KEY_", property="toKey", jdbcType=JdbcType.VARCHAR),
            @Result(column="EXPRESSION_", property="expression", jdbcType=JdbcType.VARCHAR),
            @Result(column="MULTI_INSTANCE_RESULT", property="multiInstanceResult", jdbcType=JdbcType.VARCHAR),
            @Result(column="default_line", property="defaultLine", jdbcType=JdbcType.VARCHAR)
    })
    List<BpmnLine> selectForNextNode(BpmnLineExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table CSCP_PROC_LINE
     *
     * @mbg.generated Wed Jun 12 11:07:39 CST 2019
     */
    @Override
    @Select({
        "select",
        "ID_, PROCESS_DEFINITION_KEY_, PROCESS_DEFINITION_ID_, PROCESS_DEPLOYMENT_ID_, ",
        "NAME_, LINE_KEY_, FROM_KEY_, TO_KEY_, EXPRESSION_, MULTI_INSTANCE_RESULT,default_line ",
        "from CSCP_PROC_LINE",
        "where ID_ = #{id,jdbcType=VARCHAR}"
    })
    @Results({
        @Result(column="ID_", property="id", jdbcType=JdbcType.VARCHAR, id=true),
        @Result(column="PROCESS_DEFINITION_KEY_", property="processDefinitionKey", jdbcType=JdbcType.VARCHAR),
        @Result(column="PROCESS_DEFINITION_ID_", property="processDefinitionId", jdbcType=JdbcType.VARCHAR),
        @Result(column="PROCESS_DEPLOYMENT_ID_", property="processDeploymentId", jdbcType=JdbcType.VARCHAR),
        @Result(column="NAME_", property="name", jdbcType=JdbcType.VARCHAR),
        @Result(column="LINE_KEY_", property="lineKey", jdbcType=JdbcType.VARCHAR),
        @Result(column="FROM_KEY_", property="fromKey", jdbcType=JdbcType.VARCHAR),
        @Result(column="TO_KEY_", property="toKey", jdbcType=JdbcType.VARCHAR),
        @Result(column="EXPRESSION_", property="expression", jdbcType=JdbcType.VARCHAR),
        @Result(column="MULTI_INSTANCE_RESULT", property="multiInstanceResult", jdbcType=JdbcType.VARCHAR),
            @Result(column="default_line", property="defaultLine", jdbcType=JdbcType.VARCHAR)
    })
    BpmnLine selectByPrimaryKey(String id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table CSCP_PROC_LINE
     *
     * @mbg.generated Wed Jun 12 11:07:39 CST 2019
     */
    @Override
    @UpdateProvider(type= BpmnLineSqlProvider.class, method="updateByExampleSelective")
    int updateByExampleSelective(@Param("record") BpmnLine record, @Param("example") BpmnLineExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table CSCP_PROC_LINE
     *
     * @mbg.generated Wed Jun 12 11:07:39 CST 2019
     */
    @Override
    @UpdateProvider(type= BpmnLineSqlProvider.class, method="updateByExample")
    int updateByExample(@Param("record") BpmnLine record, @Param("example") BpmnLineExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table CSCP_PROC_LINE
     *
     * @mbg.generated Wed Jun 12 11:07:39 CST 2019
     */
    @Override
    @Update({
        "update CSCP_PROC_LINE",
        "set PROCESS_DEFINITION_KEY_ = #{processDefinitionKey,jdbcType=VARCHAR},",
          "PROCESS_DEFINITION_ID_ = #{processDefinitionId,jdbcType=VARCHAR},",
          "PROCESS_DEPLOYMENT_ID_ = #{processDeploymentId,jdbcType=VARCHAR},",
          "NAME_ = #{name,jdbcType=VARCHAR},",
          "LINE_KEY_ = #{lineKey,jdbcType=VARCHAR},",
          "FROM_KEY_ = #{fromKey,jdbcType=VARCHAR},",
          "TO_KEY_ = #{toKey,jdbcType=VARCHAR},",
          "EXPRESSION_ = #{expression,jdbcType=VARCHAR},",
          "MULTI_INSTANCE_RESULT = #{multiInstanceResult,jdbcType=VARCHAR}",
        "where ID_ = #{id,jdbcType=VARCHAR}"
    })
    int updateByPrimaryKey(BpmnLine record);
}