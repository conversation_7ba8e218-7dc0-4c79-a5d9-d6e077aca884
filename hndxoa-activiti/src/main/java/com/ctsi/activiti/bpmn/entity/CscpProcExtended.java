package com.ctsi.activiti.bpmn.entity;

import java.util.List;

public class CscpProcExtended extends CscpProc {
    private String appCode;
    private List<String> listTypeId;
    private String typeId;

    @Override
    public String getTypeId() {
        return typeId;
    }

    @Override
    public void setTypeId(String typeId) {
        this.typeId = typeId;
    }

    public String getAppCode() {
        return appCode;
    }

    public void setAppCode(String appCode) {
        this.appCode = appCode;
    }

    public List<String> getListTypeId() {
        return listTypeId;
    }

    public void setListTypeId(List<String> listTypeId) {
        this.listTypeId = listTypeId;
    }
}
