package com.ctsi.activiti.bpmn.constant;

/**
 * 流程变量
 *
 * @author: PANJL
 * @date: 2019-05-08 08:44
 */
public enum BpmnVariables {

    /**
     * 受理人
     */
    ASSIGNEE_,

    /**
     * 设置子流程的第一个节点的人员
     */
    SUB_START_ASSIGNEE,

    /**
     * 受理人集合
     */
    ASSIGNEE_ARR_,

    /**
     * 出口节点KEY，连接线设置了该参数作为判断是否走指定线路的唯一依据
     */
    TARGET_KEY,

    /**
     * 表单变量
     */
    FORM_ID_,

    /**
     * 已经历的路线Key集合
     */
    HIS_LINE_KEY_,

    /**
     * 表达式流程变量属性
     */
    PROC_INST_VARIABLE_MAP,

    /**
     * 业务类型，用于区分不同的业务
     */
    BUSINESS_TYPE,

    /**
     * 流程分类id
     */
    PROC_TYPE_ID;
}
