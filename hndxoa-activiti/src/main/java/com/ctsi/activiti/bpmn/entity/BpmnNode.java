package com.ctsi.activiti.bpmn.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>  请注意此处对应的表结构为  proc_node
*/
@Data
@TableName(value = "cscp_proc_node")
public class BpmnNode implements Serializable {

    @TableId(value = "ID",type = IdType.ASSIGN_ID)
    private String id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column CSCP_PROC_NODE.PROCESS_DEFINITION_KEY_
     *
     * @mbg.generated Thu May 23 14:39:42 CST 2019
     */
    private String processDefinitionKey;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column CSCP_PROC_NODE.PROCESS_DEFINITION_ID_
     *
     * @mbg.generated Thu May 23 14:39:42 CST 2019
     */
    private String processDefinitionId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column CSCP_PROC_NODE.PROCESS_DEPLOYMENT_ID_
     *
     * @mbg.generated Thu May 23 14:39:42 CST 2019
     */
    private String processDeploymentId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column CSCP_PROC_NODE.NAME_
     *
     * @mbg.generated Thu May 23 14:39:42 CST 2019
     */
    private String name;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column CSCP_PROC_NODE.NODE_KEY_
     *
     * @mbg.generated Thu May 23 14:39:42 CST 2019
     */
    private String nodeKey;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column CSCP_PROC_NODE.TASK_TYPE_
     *
     * @mbg.generated Thu May 23 14:39:42 CST 2019
     */
    private String taskType;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column CSCP_PROC_NODE.TYPE_
     *
     * @mbg.generated Thu May 23 14:39:42 CST 2019
     */
    private String type;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column CSCP_PROC_NODE.MULTI_INSTANCE_
     *
     * @mbg.generated Thu May 23 14:39:42 CST 2019
     */
    private Integer multiInstance;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column CSCP_PROC_NODE.SEQUENTIAL_
     *
     * @mbg.generated Thu May 23 14:39:42 CST 2019
     */
    private Integer sequential;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column CSCP_PROC_NODE.DECISION_TYPE_
     *
     * @mbg.generated Thu May 23 14:39:42 CST 2019
     */
    private Integer decisionType;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column CSCP_PROC_NODE.VOTING_TYPE_
     *
     * @mbg.generated Thu May 23 14:39:42 CST 2019
     */
    private Integer votingType;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column CSCP_PROC_NODE.VOTES_
     *
     * @mbg.generated Thu May 23 14:39:42 CST 2019
     */
    private String votes;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column CSCP_PROC_NODE.MULTI_STRATEGY_
     *
     * @mbg.generated Thu May 23 14:39:42 CST 2019
     */
    private String multiStrategy;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column CSCP_PROC_NODE.SUB_PROCESS_DEFINITION_KEY_
     *
     * @mbg.generated Thu May 23 14:39:42 CST 2019
     */
    private String subProcessDefinitionKey;

    private String subProcessDefinitionName;

    private String subProcessDefinitionId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column CSCP_PROC_NODE.SEND_MAIL_
     *
     * @mbg.generated Thu May 23 14:39:42 CST 2019
     */
    private Integer sendMail;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column CSCP_PROC_NODE.GATEWAY_
     *
     * @mbg.generated Thu May 23 14:39:42 CST 2019
     */
    private Integer gateway;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column CSCP_PROC_NODE.ASSIGNEES_
     *
     * @mbg.generated Thu May 23 14:39:42 CST 2019
     */
    private String assignees;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column CSCP_PROC_NODE.TASK_LISTENER_
     *
     * @mbg.generated Thu May 23 14:39:42 CST 2019
     */
    private String taskListener;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column CSCP_PROC_NODE.FORM_URL_
     *
     * @mbg.generated Thu May 23 14:39:42 CST 2019
     */
    private String formUrl;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column CSCP_PROC_NODE.ASSIGNEE_RULE_
     *
     * @mbg.generated Thu May 23 14:39:42 CST 2019
     */
    private String assigneeRule;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column CSCP_PROC_NODE.FORM_AUTH_
     *
     * @mbg.generated Thu May 23 14:39:42 CST 2019
     */
    private String formAuth;

    @TableField(exist = false)
    private List<BpmnNode> subElements;

    private String btnIds;

    private String formItems;

    private String filterRule;

    private String formId;

    private String nodeExpression;

    private Integer timeLimitHours;

   private String appFormItems;


   @ApiModelProperty(value = "是否共用处理单，1表示共用，0表示不共用")
   private int processingSheet;

    @ApiModelProperty(value = "领导用户ID")
    private String leaderUserId;

    @ApiModelProperty(value = "排序号")
    private Integer sequenceNo;


}