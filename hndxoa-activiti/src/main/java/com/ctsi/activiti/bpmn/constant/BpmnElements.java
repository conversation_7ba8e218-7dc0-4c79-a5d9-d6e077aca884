package com.ctsi.activiti.bpmn.constant;

import com.ctsi.common.utils.ActivitiUtils;

/**
 * Bpmn模型元素类型
 *
 * @author: PANJL
 * @date: 2019-05-07 20:19
 */
public enum BpmnElements {

    /**
     * 开始事件
     */
    StartEvent,

    /**
     * 结束事件
     */
    EndEvent,

    /**
     * 人工任务
     */
    UserTask,

    /**
     * 排他网关
     */
    ExclusiveGateway,

    /**
     * 并行网关
     */
    ParallelGateway,

    /**
     * 调用子流程
     */
    CallActivity,

    /**
     * 子流程
     */
    SubProcess,

    /**
     * Java服务任务
     */
    ServiceTask,

    /**
     * 流程连线
     */
    SequenceFlow,
    ;

    public static BpmnElements get(String type) {
        if (ActivitiUtils.isEmpty(type)) {
            return null;
        }
        if (type.startsWith("bpmn:")) {
            type = type.replace("bpmn:", "");
        }
        for (BpmnElements value : BpmnElements.values()) {
            if (value.name().equals(type)) {
                return value;
            }
        }
        return null;
    }

    public static boolean isStartEvent(String type) {
        if (ActivitiUtils.isEmpty(type)) {
            return false;
        }
        if (type.startsWith("bpmn:")) {
            type = type.replace("bpmn:", "");
        }
        return BpmnElements.StartEvent.name().equals(type);
    }

    public static boolean isUserTask(String type) {
        if (ActivitiUtils.isEmpty(type)){
            return false;
        }
        if (type.startsWith("bpmn:")) {
            type = type.replace("bpmn:", "");
        }
        return BpmnElements.UserTask.name().equals(type);
    }

    public static boolean isEndEvent(String type) {
        if (ActivitiUtils.isEmpty(type)){
            return false;
        }
        if (type.startsWith("bpmn:")) {
            type = type.replace("bpmn:", "");
        }
        return BpmnElements.EndEvent.name().equals(type);
    }

    public static boolean isExclusiveGateway(String type) {
        if (ActivitiUtils.isEmpty(type)) {
            return false;
        }
        if (type.startsWith("bpmn:")) {
            type = type.replace("bpmn:", "");
        }
        return BpmnElements.ExclusiveGateway.name().equals(type);
    }

    public static boolean isParallelGateway(String type) {
        if (ActivitiUtils.isEmpty(type)) {
            return false;
        }
        if (type.startsWith("bpmn:")) {
            type = type.replace("bpmn:", "");
        }
        return BpmnElements.ParallelGateway.name().equals(type);
    }

    public static boolean isCallActivity(String type) {
        if (ActivitiUtils.isEmpty(type)) {
            return false;
        }
        if (type.startsWith("bpmn:")) {
            type = type.replace("bpmn:", "");
        }
        return BpmnElements.CallActivity.name().equals(type);
    }

    public static boolean isSubProcess(String type) {
        if (ActivitiUtils.isEmpty(type)) {
            return false;
        }
        if (type.startsWith("bpmn:")) {
            type = type.replace("bpmn:", "");
        }
        return BpmnElements.SubProcess.name().equals(type);
    }

    public static boolean isSequenceFlow(String type) {
        if (ActivitiUtils.isEmpty(type)) {
            return false;
        }
        if (type.startsWith("bpmn:")) {
            type = type.replace("bpmn:", "");
        }
        return BpmnElements.SequenceFlow.name().equals(type);
    }

    public static boolean isServiceTask(String type) {
        if (ActivitiUtils.isEmpty(type)) {
            return false;
        }
        if (type.startsWith("bpmn:")) {
            type = type.replace("bpmn:", "");
        }
        return BpmnElements.ServiceTask.name().equals(type);
    }

    public static boolean isGateway(String type) {
        return isExclusiveGateway(type) || isParallelGateway(type);
    }

}
