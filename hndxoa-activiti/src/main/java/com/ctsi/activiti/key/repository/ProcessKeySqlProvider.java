package com.ctsi.activiti.key.repository;

import com.ctsi.activiti.key.entity.ProcessKey;
import com.ctsi.activiti.key.entity.ProcessKeyExample;
import com.ctsi.activiti.key.entity.ProcessKeyExample.Criteria;
import com.ctsi.activiti.key.entity.ProcessKeyExample.Criterion;
import org.apache.ibatis.jdbc.SQL;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
*/
public class ProcessKeySqlProvider {

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table CSCP_PROC_KEY
     *
     * @mbg.generated Mon May 20 09:44:08 CST 2019
     */
    public String countByExample(ProcessKeyExample example) {
        SQL sql = new SQL();
        sql.SELECT("count(*)").FROM("CSCP_PROC_KEY");
        applyWhere(sql, example, false);
        return sql.toString();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table CSCP_PROC_KEY
     *
     * @mbg.generated Mon May 20 09:44:08 CST 2019
     */
    public String deleteByExample(ProcessKeyExample example) {
        SQL sql = new SQL();
        sql.DELETE_FROM("CSCP_PROC_KEY");
        applyWhere(sql, example, false);
        return sql.toString();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table CSCP_PROC_KEY
     *
     * @mbg.generated Mon May 20 09:44:08 CST 2019
     */
    public String insertSelective(ProcessKey record) {
        SQL sql = new SQL();
        sql.INSERT_INTO("CSCP_PROC_KEY");
        
        if (record.getName() != null) {
            sql.VALUES("NAME_", "#{name,jdbcType=VARCHAR}");
        }
        
        return sql.toString();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table CSCP_PROC_KEY
     *
     * @mbg.generated Mon May 20 09:44:08 CST 2019
     */
    public String selectByExample(ProcessKeyExample example) {
        SQL sql = new SQL();
        if (example != null && example.isDistinct()) {
            sql.SELECT_DISTINCT("KEY_");
        } else {
            sql.SELECT("KEY_");
        }
        sql.SELECT("NAME_");
        sql.FROM("CSCP_PROC_KEY");
        applyWhere(sql, example, false);
        
        if (example != null && example.getOrderByClause() != null) {
            sql.ORDER_BY(example.getOrderByClause());
        }
        
        return sql.toString();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table CSCP_PROC_KEY
     *
     * @mbg.generated Mon May 20 09:44:08 CST 2019
     */
    public String updateByExampleSelective(Map<String, Object> parameter) {
        ProcessKey record = (ProcessKey) parameter.get("record");
        ProcessKeyExample example = (ProcessKeyExample) parameter.get("example");
        
        SQL sql = new SQL();
        sql.UPDATE("CSCP_PROC_KEY");
        
        if (record.getKey() != null) {
            sql.SET("KEY_ = #{record.key,jdbcType=BIGINT}");
        }
        
        if (record.getName() != null) {
            sql.SET("NAME_ = #{record.name,jdbcType=VARCHAR}");
        }
        
        applyWhere(sql, example, true);
        return sql.toString();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table CSCP_PROC_KEY
     *
     * @mbg.generated Mon May 20 09:44:08 CST 2019
     */
    public String updateByExample(Map<String, Object> parameter) {
        SQL sql = new SQL();
        sql.UPDATE("CSCP_PROC_KEY");
        
        sql.SET("KEY_ = #{record.key,jdbcType=BIGINT}");
        sql.SET("NAME_ = #{record.name,jdbcType=VARCHAR}");
        
        ProcessKeyExample example = (ProcessKeyExample) parameter.get("example");
        applyWhere(sql, example, true);
        return sql.toString();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table CSCP_PROC_KEY
     *
     * @mbg.generated Mon May 20 09:44:08 CST 2019
     */
    public String updateByPrimaryKeySelective(ProcessKey record) {
        SQL sql = new SQL();
        sql.UPDATE("CSCP_PROC_KEY");
        
        if (record.getName() != null) {
            sql.SET("NAME_ = #{name,jdbcType=VARCHAR}");
        }
        
        sql.WHERE("KEY_ = #{key,jdbcType=BIGINT}");
        
        return sql.toString();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table CSCP_PROC_KEY
     *
     * @mbg.generated Mon May 20 09:44:08 CST 2019
     */
    protected void applyWhere(SQL sql, ProcessKeyExample example, boolean includeExamplePhrase) {
        if (example == null) {
            return;
        }
        
        String parmPhrase1;
        String parmPhrase1_th;
        String parmPhrase2;
        String parmPhrase2_th;
        String parmPhrase3;
        String parmPhrase3_th;
        if (includeExamplePhrase) {
            parmPhrase1 = "%s #{example.oredCriteria[%d].allCriteria[%d].value}";
            parmPhrase1_th = "%s #{example.oredCriteria[%d].allCriteria[%d].value,typeHandler=%s}";
            parmPhrase2 = "%s #{example.oredCriteria[%d].allCriteria[%d].value} and #{example.oredCriteria[%d].criteria[%d].secondValue}";
            parmPhrase2_th = "%s #{example.oredCriteria[%d].allCriteria[%d].value,typeHandler=%s} and #{example.oredCriteria[%d].criteria[%d].secondValue,typeHandler=%s}";
            parmPhrase3 = "#{example.oredCriteria[%d].allCriteria[%d].value[%d]}";
            parmPhrase3_th = "#{example.oredCriteria[%d].allCriteria[%d].value[%d],typeHandler=%s}";
        } else {
            parmPhrase1 = "%s #{oredCriteria[%d].allCriteria[%d].value}";
            parmPhrase1_th = "%s #{oredCriteria[%d].allCriteria[%d].value,typeHandler=%s}";
            parmPhrase2 = "%s #{oredCriteria[%d].allCriteria[%d].value} and #{oredCriteria[%d].criteria[%d].secondValue}";
            parmPhrase2_th = "%s #{oredCriteria[%d].allCriteria[%d].value,typeHandler=%s} and #{oredCriteria[%d].criteria[%d].secondValue,typeHandler=%s}";
            parmPhrase3 = "#{oredCriteria[%d].allCriteria[%d].value[%d]}";
            parmPhrase3_th = "#{oredCriteria[%d].allCriteria[%d].value[%d],typeHandler=%s}";
        }
        
        StringBuilder sb = new StringBuilder();
        List<Criteria> oredCriteria = example.getOredCriteria();
        boolean firstCriteria = true;
        for (int i = 0; i < oredCriteria.size(); i++) {
            Criteria criteria = oredCriteria.get(i);
            if (criteria.isValid()) {
                if (firstCriteria) {
                    firstCriteria = false;
                } else {
                    sb.append(" or ");
                }
                
                sb.append('(');
                List<Criterion> criterions = criteria.getAllCriteria();
                boolean firstCriterion = true;
                for (int j = 0; j < criterions.size(); j++) {
                    Criterion criterion = criterions.get(j);
                    if (firstCriterion) {
                        firstCriterion = false;
                    } else {
                        sb.append(" and ");
                    }
                    
                    if (criterion.isNoValue()) {
                        sb.append(criterion.getCondition());
                    } else if (criterion.isSingleValue()) {
                        if (criterion.getTypeHandler() == null) {
                            sb.append(String.format(parmPhrase1, criterion.getCondition(), i, j));
                        } else {
                            sb.append(String.format(parmPhrase1_th, criterion.getCondition(), i, j,criterion.getTypeHandler()));
                        }
                    } else if (criterion.isBetweenValue()) {
                        if (criterion.getTypeHandler() == null) {
                            sb.append(String.format(parmPhrase2, criterion.getCondition(), i, j, i, j));
                        } else {
                            sb.append(String.format(parmPhrase2_th, criterion.getCondition(), i, j, criterion.getTypeHandler(), i, j, criterion.getTypeHandler()));
                        }
                    } else if (criterion.isListValue()) {
                        sb.append(criterion.getCondition());
                        sb.append(" (");
                        List<?> listItems = (List<?>) criterion.getValue();
                        boolean comma = false;
                        for (int k = 0; k < listItems.size(); k++) {
                            if (comma) {
                                sb.append(", ");
                            } else {
                                comma = true;
                            }
                            if (criterion.getTypeHandler() == null) {
                                sb.append(String.format(parmPhrase3, i, j, k));
                            } else {
                                sb.append(String.format(parmPhrase3_th, i, j, k, criterion.getTypeHandler()));
                            }
                        }
                        sb.append(')');
                    }
                }
                sb.append(')');
            }
        }
        
        if (sb.length() > 0) {
            sql.WHERE(sb.toString());
        }
    }
}