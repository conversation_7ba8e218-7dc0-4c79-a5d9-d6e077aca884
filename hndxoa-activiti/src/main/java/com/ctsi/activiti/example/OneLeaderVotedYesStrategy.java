package com.ctsi.activiti.example;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ctsi.activiti.multi.annotation.Multi;
import com.ctsi.activiti.multi.model.MultiInstanceDecisionResult;
import com.ctsi.activiti.multi.strategy.IMulti;
import com.ctsi.activiti.multi.strategy.MultiInstanceDecisionParam;
import com.ctsi.hndx.exception.BusinessException;
import com.ctsi.hndx.utils.StringUtils;
import com.ctsi.ssdc.admin.domain.CscpRoles;
import com.ctsi.ssdc.admin.domain.CscpUser;
import com.ctsi.ssdc.admin.domain.CscpUserRole;

import java.util.List;

/**
 * 某领导一票通过
 */
@Multi(name = "某领导一票通过", sort = 11)
public class OneLeaderVotedYesStrategy implements IMulti {
    @Override
    public MultiInstanceDecisionResult run(MultiInstanceDecisionParam param) {
        String userId = param.getProcessNode().getLeaderUserId();
        if (StringUtils.isEmpty(userId)){
            throw new BusinessException("节点任务选择的多实例，某个领导签批即可，但是没有选择处理人");
        }
        if(userId.contains(param.getVoter()))
        {
            return MultiInstanceDecisionResult.APPROVE();
        }else{
            return MultiInstanceDecisionResult.WAITING();
        }
    }
}
