package com.ctsi.activiti.callact.model;

import com.ctsi.activiti.core.model.DataModel;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * 子流程向主流程传递的参数
 */
public class CallActivityOutModel implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 子流程定义id
     */
    private String subProcessDefinitionId;

    /**
     * 子流程实例id
     */
    private String subProcessInstanceId;

    /**
     * 子流程execution id
     */
    private String subExecutionId;

    /**
     * 下一环节执行人列表
     */
    private List<String> nextHandlers;

    /**
     * 子流程出口路线KEY
     */
    private String callActivityOutLineKey;

    /**
     * 其他参数
     */
    private DataModel transData = new DataModel();

    /**
     * 任务提交时携带的表达式参数信息
     */
    private Map<String, Object> exprMap;

    public static long getSerialVersionUID() {
        return serialVersionUID;
    }

    public String getSubProcessDefinitionId() {
        return subProcessDefinitionId;
    }

    public void setSubProcessDefinitionId(String subProcessDefinitionId) {
        this.subProcessDefinitionId = subProcessDefinitionId;
    }

    public String getSubProcessInstanceId() {
        return subProcessInstanceId;
    }

    public void setSubProcessInstanceId(String subProcessInstanceId) {
        this.subProcessInstanceId = subProcessInstanceId;
    }

    public String getSubExecutionId() {
        return subExecutionId;
    }

    public void setSubExecutionId(String subExecutionId) {
        this.subExecutionId = subExecutionId;
    }

    public List<String> getNextHandlers() {
        return nextHandlers;
    }

    public void setNextHandlers(List<String> nextHandlers) {
        this.nextHandlers = nextHandlers;
    }

    public String getCallActivityOutLineKey() {
        return callActivityOutLineKey;
    }

    public void setCallActivityOutLineKey(String callActivityOutLineKey) {
        this.callActivityOutLineKey = callActivityOutLineKey;
    }

    public DataModel getTransData() {
        return transData;
    }

    public void setTransData(DataModel transData) {
        this.transData = transData;
    }

    public Map<String, Object> getExprMap() {
        return exprMap;
    }

    public void setExprMap(Map<String, Object> exprMap) {
        this.exprMap = exprMap;
    }
}
