package com.ctsi.activiti.callact.service;

import org.activiti.engine.delegate.DelegateExecution;

import java.util.List;

/**
 * 子流程服务
 *
 * @author: PANJL
 * @date: 2019-05-13 10:07
 */
public interface CallActivityService {

    /**
     * 判断是否子流程
     *
     * @param executionId
     * @return
     */
    boolean isSubProcess(String executionId);


    /**
     * 流程图调用：注入多实例节点的受理人集合
     *
     * @param execution
     * @return
     * <AUTHOR>
     */
    List<String> pourAssigneeCollection(DelegateExecution execution);

    boolean hasComplete(DelegateExecution execution);
}
