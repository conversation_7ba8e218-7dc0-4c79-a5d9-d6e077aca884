package com.ctsi.activiti.callact.model;

import com.ctsi.activiti.bpmn.entity.BpmnNode;
import com.ctsi.activiti.core.model.DataModel;
import com.ctsi.activiti.core.model.ExecutionLine;

import java.io.Serializable;
import java.util.List;

/**
 * 主流程向子流程传递的参数
 */
public class CallActivityInModel implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主流程定义id
     */
    private String superProcessDefinitionId;

    /**
     * 主流程实例id
     */
    private String superProcessInstanceId;

    /**
     * 主流程execution id
     */
    private String superExecutionId;

    /**
     * 子流程环节定义
     */
    private BpmnNode subProcessDefinitionNode;

    /**
     * 下一环节执行人列表
     */
    private List<String> nextHandlers;

    /**
     * 子流程可选择出口路线列表
     */
    private List<ExecutionLine> subProcessOutLineKeyList;

    /**
     * 其他参数
     */
    private DataModel transData = new DataModel();

    /**
     * 从下一环节处理人列表中获取子流程处理人的位置下标
     */
    private int index = -1;

    /**
     * 增加一位，并获取下标
     *
     * @return
     */
    public int index() {
        return ++this.index;
    }

    public static long getSerialVersionUID() {
        return serialVersionUID;
    }

    public String getSuperProcessDefinitionId() {
        return superProcessDefinitionId;
    }

    public void setSuperProcessDefinitionId(String superProcessDefinitionId) {
        this.superProcessDefinitionId = superProcessDefinitionId;
    }

    public String getSuperProcessInstanceId() {
        return superProcessInstanceId;
    }

    public void setSuperProcessInstanceId(String superProcessInstanceId) {
        this.superProcessInstanceId = superProcessInstanceId;
    }

    public String getSuperExecutionId() {
        return superExecutionId;
    }

    public void setSuperExecutionId(String superExecutionId) {
        this.superExecutionId = superExecutionId;
    }

    public BpmnNode getSubProcessDefinitionNode() {
        return subProcessDefinitionNode;
    }

    public void setSubProcessDefinitionNode(BpmnNode subProcessDefinitionNode) {
        this.subProcessDefinitionNode = subProcessDefinitionNode;
    }

    public List<String> getNextHandlers() {
        return nextHandlers;
    }

    public void setNextHandlers(List<String> nextHandlers) {
        this.nextHandlers = nextHandlers;
    }

    public List<ExecutionLine> getSubProcessOutLineKeyList() {
        return subProcessOutLineKeyList;
    }

    public void setSubProcessOutLineKeyList(List<ExecutionLine> subProcessOutLineKeyList) {
        this.subProcessOutLineKeyList = subProcessOutLineKeyList;
    }

    public DataModel getTransData() {
        return transData;
    }

    public void setTransData(DataModel transData) {
        this.transData = transData;
    }

}
