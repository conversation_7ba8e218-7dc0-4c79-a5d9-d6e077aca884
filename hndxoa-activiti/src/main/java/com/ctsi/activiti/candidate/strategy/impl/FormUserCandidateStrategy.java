package com.ctsi.activiti.candidate.strategy.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ctsi.activiti.candidate.annotation.Candidate;
import com.ctsi.activiti.candidate.chooseassignee.AssigneeType;
import com.ctsi.activiti.candidate.strategy.CandidateRuleParam;
import com.ctsi.activiti.candidate.strategy.ICandidate;
import com.ctsi.common.utils.ActivitiUtils;
import com.ctsi.ssdc.admin.domain.dto.CscpUserDTO;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

@Candidate(name = "表单选人", sort = 7)
public class FormUserCandidateStrategy   extends CandidateAbstract {
    @Override
    public String url() {
        return null;
    }

    /**
     * [u1, u2, u3, u4, ...]
     * @return
     */
    @Override
    public List<String> candidates(CandidateRuleParam param) {
        Object definitionRuleData = param.getDefinitionRuleData();
        List<String> results = new ArrayList<>();
        if(definitionRuleData != null){
            JSONArray arrays = ActivitiUtils.bean().toJSONArray(definitionRuleData);
            if (arrays != null && arrays.size() != 0) {
                arrays.forEach(s -> {
                    JSONObject user = ActivitiUtils.bean().toJSONObject(s);
                    results.add(user.getString("id"));
                });
            }
        }
        return results;
    }

    @Override
    public Set<Long> getCandidateSize(List<Long> results) {
        return null;
    }

    @Override
    public AssigneeType getTypeassigneeType(List<Long> results) {
        return null;
    }
}
