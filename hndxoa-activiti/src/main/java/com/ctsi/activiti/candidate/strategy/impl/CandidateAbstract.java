package com.ctsi.activiti.candidate.strategy.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ctsi.activiti.candidate.chooseassignee.AssigneeType;
import com.ctsi.activiti.candidate.chooseassignee.ChooseType;
import com.ctsi.activiti.candidate.chooseassignee.NodeStrategyType;
import com.ctsi.activiti.candidate.strategy.CandidateRuleParam;
import com.ctsi.activiti.candidate.strategy.ICandidate;
import com.ctsi.common.utils.ActivitiUtils;
import com.ctsi.hndx.exception.BusinessException;
import com.ctsi.hndx.result.ResultCode;
import com.ctsi.ssdc.admin.domain.CscpUser;
import com.ctsi.ssdc.admin.domain.CscpUserOrg;
import com.ctsi.ssdc.admin.domain.dto.CscpUserDTO;
import com.ctsi.ssdc.admin.service.CscpUserService;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.*;

public abstract class CandidateAbstract implements ICandidate {




    @Autowired
    protected CscpUserService cscpUserService;


    /**
     * 流程定义时，候选人设置页面跳转路径
     *
     * @return
     */
    @Override
    public String url(){
        return null;
    }

    /**
     * 只有一个人的时候
     * @return
     */
    protected AssigneeType getOnePeople(CscpUserDTO cscpUserDTO){
        NodeStrategyType nodeStrategyType = new NodeStrategyType();
        nodeStrategyType.setId(cscpUserDTO.getId());
        nodeStrategyType.setName(cscpUserDTO.getRealName());
        AssigneeType assigneeType = AssigneeType.builder().chooseType(ChooseType.ONE_USER_TYPE)
                .nodePeopleTypeList(Arrays.asList(nodeStrategyType))
                .build();
        return assigneeType;
    }

    /**
     * 获取下一步人员的个数
     * @param param
     * @return
     */
    public abstract Set<Long> getCandidateSize(List<Long> results);

    /**
     * 获取类型，特殊情况下请直接继承次接口，例如流程发起人已经基本明确只有一个人的时候
     * @param param
     * @return
     */
    @Override
    public AssigneeType getNodeAssigneeType(CandidateRuleParam param){
        Object definitionRuleData = param.getDefinitionRuleData();
        List<Long> results = new ArrayList<>();
        if(definitionRuleData != null){
            JSONArray arrays = ActivitiUtils.bean().toJSONArray(definitionRuleData);
            if (arrays != null && arrays.size() != 0) {
                arrays.forEach(s -> {
                    results.add(Long.parseLong(String.valueOf(s)));
                });
            }
        }

        if(CollectionUtils.isEmpty(results)){
            throw new BusinessException(ResultCode.USER_NOT_EXIST);
        }
        Set<Long> longSet = getCandidateSize(results);

        if (longSet.size() == 0){
            return null;
        }else {
            ArrayList<Long> longs = CollectionUtil.newArrayList(longSet);
            List<CscpUserDTO> cscpUserDTOList = cscpUserService.listQueryUserDTO(longs, true);
            if (CollectionUtils.isEmpty(cscpUserDTOList)){
                throw new BusinessException(ResultCode.USER_NOT_EXIST);
            }

            if (CollectionUtils.size(cscpUserDTOList) == 1){
                return getOnePeople(cscpUserDTOList.get(0));
            }
        }

        return getTypeassigneeType(results);
    }


    public abstract AssigneeType getTypeassigneeType( List<Long> results);
}
