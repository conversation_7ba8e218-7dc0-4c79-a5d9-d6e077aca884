package com.ctsi.activiti.candidate.strategy.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ctsi.activiti.candidate.annotation.Candidate;
import com.ctsi.activiti.candidate.chooseassignee.AssigneeType;
import com.ctsi.activiti.candidate.chooseassignee.ChooseType;
import com.ctsi.activiti.candidate.chooseassignee.NodePeopleType;
import com.ctsi.activiti.candidate.chooseassignee.NodeStrategyType;
import com.ctsi.activiti.candidate.strategy.CandidateRuleParam;
import com.ctsi.business.domain.Cscp;
import com.ctsi.common.utils.ActivitiUtils;
import com.ctsi.procgroup.entity.CscpProcGroup;
import com.ctsi.procgroup.entity.CscpProcUser;
import com.ctsi.procgroup.mapper.CscpProcGroupMapper;
import com.ctsi.procgroup.mapper.CscpProcUserMapper;
import com.ctsi.ssdc.admin.domain.CscpUserRole;
import com.ctsi.ssdc.admin.domain.dto.CscpUserDTO;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.*;

/**
 * 流程跨单位时选择不同的单位的组的人员
 */
@Candidate(name = "不同单位组", sort = 6)
public class ProcComGroupCandidateStrategy extends CandidateAbstract {

    @Autowired
    private CscpProcGroupMapper cscpProcGroupMapper;

    @Autowired
    private CscpProcUserMapper cscpProcUserMapper;


    @Override
    public String url() {
        return null;
    }

    /**
     * [r1, r2, r3, r4, ...]
     * 获取流程选择的跨单位的流程组的所有组的id集合
     *
     * @return
     */
    @Override
    public List<String> candidates(CandidateRuleParam param) {
        Object definitionRuleData = param.getDefinitionRuleData();
        if (definitionRuleData == null) {
            return null;
        }

        JSONArray arrays = ActivitiUtils.bean().toJSONArray(definitionRuleData);
        if (arrays == null || arrays.size() == 0) {
            return null;
        }

        List<String> results = new ArrayList<>();
        arrays.forEach(i -> {
            results.addAll(getUserListByRoleId(Long.parseLong(String.valueOf(i))));
        });
        return results;
    }

    private List<String> getUserListByRoleId(long parseLong) {
        LambdaQueryWrapper<CscpProcUser> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.eq(CscpProcUser::getPermissionGroupId ,parseLong);
        List<CscpProcUser> cscpProcUserList = cscpProcUserMapper.selectListNoAdd(queryWrapper);

        List<String> user = new ArrayList<>();
        Optional.of(cscpProcUserList).ifPresent(urs -> urs.forEach(ur -> user.add(String.valueOf(ur.getUserId()))));

        return user;
    }


    /**
     * 获取所有组的id的集合下面有多少个人
     *
     * @param results
     * @return
     */
    @Override
    public Set<Long> getCandidateSize(List<Long> results) {
        LambdaQueryWrapper<CscpProcUser> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(CscpProcUser::getPermissionGroupId,results);
        List<CscpProcUser> cscpProcUserList = cscpProcUserMapper.selectListNoAdd(queryWrapper);
        Set<Long> longSet = new HashSet<>();
        cscpProcUserList.forEach(cscpUserWorkGroup -> {
            longSet.add(cscpUserWorkGroup.getUserId());
        });
        return  longSet;

    }

    /**
     * 返回次组的类型，参考角色的实例化
     *
     * @param results
     * @return
     */
    @Override
    public AssigneeType getTypeassigneeType(List<Long> results) {
        List<CscpProcGroup> cscpProcGroups = cscpProcGroupMapper
                .selectListNoAdd(new LambdaQueryWrapper<CscpProcGroup>().in(CscpProcGroup::getId, results));

        List<NodePeopleType> arrrayList = new ArrayList<>();
        cscpProcGroups.forEach(cscpRole -> {
            arrrayList.add(NodeStrategyType.builder().id(cscpRole.getId()).name(cscpRole.getPermissionName()).build());
        });
        AssigneeType assigneeType = AssigneeType.builder().chooseType(ChooseType.PROC_COM_GROUP_TYPE).nodePeopleTypeList(arrrayList)
                .url("/api/cscpProcUser/queryCscpUserByCscpGroupListIds").build();
        return assigneeType;
    }
}
