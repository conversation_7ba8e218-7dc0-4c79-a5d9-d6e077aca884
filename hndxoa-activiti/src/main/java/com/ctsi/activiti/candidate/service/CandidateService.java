package com.ctsi.activiti.candidate.service;

import com.ctsi.activiti.candidate.chooseassignee.AssigneeType;
import com.ctsi.activiti.candidate.strategy.CandidateRuleParam;

import java.util.List;

/**
 * 候选人服务
 *
 * @author: PANJL
 * @date: 2019-05-14 09:18
 */
public interface CandidateService {

    /**
     * 根据流程定义时指定的候选人策略，获取候选列表
     *
     * @param param 候选人匹配参数对象
     * @return
     * <AUTHOR>
     */
    List<String> getCandidates(CandidateRuleParam param);


    /**
     * 获取流程选人时候的不同类型
     * @param param
     * @return
     */
    AssigneeType getAssigneeType(CandidateRuleParam param);

}
