package com.ctsi.activiti.candidate.loader;

/**
 * 多实例策略详情
 */
public class CandidateDetail {

    /**
     * 名称
     */
    private String name;

    /**
     * 排序
     */
    private int sort;

    /**
     * key
     */
    private String key;

    /**
     * 页面跳转路径
     */
    private String url;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public int getSort() {
        return sort;
    }

    public void setSort(int sort) {
        this.sort = sort;
    }

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }
}
