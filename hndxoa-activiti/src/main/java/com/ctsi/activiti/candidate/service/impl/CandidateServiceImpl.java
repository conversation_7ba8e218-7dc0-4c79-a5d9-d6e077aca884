package com.ctsi.activiti.candidate.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ctsi.activiti.bpmn.constant.BpmnElements;
import com.ctsi.activiti.bpmn.entity.CscpProc;
import com.ctsi.activiti.bpmn.entity.BpmnNode;
import com.ctsi.activiti.bpmn.service.ActivitiBpmnDefinitionService;
import com.ctsi.activiti.candidate.chooseassignee.AssigneeType;
import com.ctsi.activiti.candidate.service.CandidateService;
import com.ctsi.activiti.candidate.strategy.CandidateRuleParam;
import com.ctsi.activiti.candidate.strategy.ICandidate;
import com.ctsi.common.utils.ActivitiUtils;
import lombok.AllArgsConstructor;
import lombok.Data;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * @author: PANJL
 * @date: 2019-05-13 17:44
 */
@Service
public class CandidateServiceImpl implements CandidateService {
    @Autowired
    private ActivitiBpmnDefinitionService activitiBpmnDefinitionService;

    @Override
    public List<String> getCandidates(CandidateRuleParam param) {
        List<String> assignees = new ArrayList<>();
         Assignee assignee = getNodeType(param);
         String assigneesStrategy = assignee.getAssigneesStrategy();
         String assigneeRule = assignee.getAssigneeRule();
        if (ActivitiUtils.isNotEmpty(assigneesStrategy)) {
            JSONArray arr = ActivitiUtils.toJSONArray(assigneesStrategy);
            for (Object clsName : arr) {
                ICandidate iCandidate = getICandidate((String) clsName);

                Object definitionRule  = getDefinitionRule((String) clsName,assigneeRule);
                param.setDefinitionRuleData(definitionRule);
                // 调用策略接口，计算最终的候选人
                List<String> candidates = iCandidate.candidates(param);
                if (candidates != null && candidates.size() > 0) {
                    assignees.addAll(candidates);
                }
            }
        }
        return assignees;
    }

    /**
     * 获取规则和策略
     * @param param
     * @return
     */
    private Assignee getNodeType(CandidateRuleParam param){
        BpmnNode processNode = param.getProcessNode();
        String assigneesStrategy = processNode.getAssignees();
        String assigneeRule = processNode.getAssigneeRule();

        // 子流程
        if (BpmnElements.isCallActivity(processNode.getType())) {
            CscpProc subProcessDefinition = activitiBpmnDefinitionService.getBpmnModelMaxVersionByProcessDefinitionKey(processNode.getSubProcessDefinitionKey());
            if (subProcessDefinition == null) {
                throw new IllegalArgumentException("没有找到相应的子流程");
            }
            BpmnNode subProcessFirstNode = activitiBpmnDefinitionService.getFirstNode(subProcessDefinition.getProcessDefinitionId());
            assigneesStrategy = subProcessFirstNode.getAssignees();
            assigneeRule = subProcessFirstNode.getAssigneeRule();
        }
        Assignee assignee = new Assignee(assigneesStrategy,assigneeRule);
        return assignee;
    }

    /**
     * 获取实现类
     * @param clsName
     * @return
     */
    private ICandidate getICandidate(String clsName){
        Class<?> cls = null;
        try {
            cls = Class.forName((String) clsName);
        } catch (ClassNotFoundException e) {
            throw new IllegalArgumentException("候选人策略未能成功实例化");
        }
        String simpleName = cls.getSimpleName();
        simpleName = ActivitiUtils.toLowerCaseInitial(simpleName, true);
        ICandidate strategy = null;
        try {
            strategy = ((ICandidate) ActivitiUtils.springContext().getBean(simpleName));
        } catch (Exception e) {
            strategy = ActivitiUtils.springContext().getBean(cls);
        }
        return strategy;
    }

    /**\
     * 获取规则条件
     * @param clsName
     * @param assigneeRule
     * @return
     */
    private Object  getDefinitionRule(String clsName,String assigneeRule){
        // 调用用户自定义的获取候选人列表的方法，作为参数条件
        if (ActivitiUtils.bean().isJSONArray(assigneeRule)) {
            JSONArray definitionRuleArray = ActivitiUtils.bean().toJSONArray(assigneeRule);
            Object definitionRule = null;
            for (Object o : definitionRuleArray) {
                JSONObject jsonObject = ActivitiUtils.bean().toJSONObject(o);
                if (jsonObject.getString("key").equals(clsName)) {
                    definitionRule = jsonObject.get("value");
                    break;
                }
            }
            return definitionRule;
        }
        return null;
    }


    @Override
    public AssigneeType getAssigneeType(CandidateRuleParam param){
        AssigneeType assigneeType = new AssigneeType();
        Assignee assignee = getNodeType(param);
        String assigneesStrategy = assignee.getAssigneesStrategy();
        String assigneeRule = assignee.getAssigneeRule();
        //此处虽然有多个，但只要求选择一种类型
        if (ActivitiUtils.isNotEmpty(assigneesStrategy)) {
            JSONArray arr = ActivitiUtils.toJSONArray(assigneesStrategy);
            for (Object clsName : arr) {
                ICandidate iCandidate = getICandidate((String) clsName);
                Object definitionRule = getDefinitionRule((String) clsName, assigneeRule);
                param.setDefinitionRuleData(definitionRule);
                // 调用策略接口，计算最终的候选人
                assigneeType = iCandidate.getNodeAssigneeType(param);

            }
        }
        return assigneeType;
    }



    @Data
    @AllArgsConstructor
    class  Assignee{
        String assigneesStrategy;
        String assigneeRule;

    }
}
