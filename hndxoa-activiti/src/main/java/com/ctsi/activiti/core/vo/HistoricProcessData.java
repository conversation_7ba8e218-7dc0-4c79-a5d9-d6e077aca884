package com.ctsi.activiti.core.vo;

import org.activiti.engine.history.HistoricActivityInstance;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 流程历史任务数据
 *
 * @author: PANJL
 * @date: 2019-05-14 10:31
 */
public class HistoricProcessData extends ProcessData {

    /**
     * 任务列表
     */
    protected List<TaskData> taskList;

    /**
     * 获取流程执行的历史任务列表
     *
     * @return
     */
    public void loadTaskList() {
        this.taskList = new ArrayList<>();
        List<TaskData> nodes = new ArrayList<>();
        List<HistoricActivityInstance> actList = historyService.createHistoricActivityInstanceQuery().processInstanceId(processInstanceId).orderByHistoricActivityInstanceStartTime().asc().list();
        Optional.ofNullable(actList).ifPresent(acts -> acts.forEach(l -> nodes.add(TaskData.newInstance(l))));
        Optional.ofNullable(nodes).ifPresent(n -> {
            // 找到任务环节、子流程环节，这些环节需要显示在流转历史中
            List<TaskData> ts = nodes.stream().filter(s -> s.isUserTask() || s.isCallActivity()).collect(Collectors.toList());
            this.taskList.addAll(ts);
        });
    }

    public List<TaskData> getTaskList() {
        return taskList;
    }

    public void setTaskList(List<TaskData> taskList) {
        this.taskList = taskList;
    }

}
