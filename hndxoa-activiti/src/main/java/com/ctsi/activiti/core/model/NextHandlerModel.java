package com.ctsi.activiti.core.model;

import java.util.List;
import java.util.Map;

/**
 * @author: PANJL
 * @date: 2019-05-29 16:25
 */
public class NextHandlerModel {

    /**
     * 下一环节KEY
     */
    private String nextNodeKey;

    /**
     * 下一环节名称
     */
    private String nextNodeName;

    /**
     * 候选人列表
     */
    private List<Map<String, Object>> candidates;

    public String getNextNodeKey() {
        return nextNodeKey;
    }

    public void setNextNodeKey(String nextNodeKey) {
        this.nextNodeKey = nextNodeKey;
    }

    public String getNextNodeName() {
        return nextNodeName;
    }

    public void setNextNodeName(String nextNodeName) {
        this.nextNodeName = nextNodeName;
    }

    public List<Map<String, Object>> getCandidates() {
        return candidates;
    }

    public void setCandidates(List<Map<String, Object>> candidates) {
        this.candidates = candidates;
    }
}
