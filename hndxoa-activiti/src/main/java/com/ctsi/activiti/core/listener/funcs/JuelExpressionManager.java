package com.ctsi.activiti.core.listener.funcs;

import de.odysseus.el.ExpressionFactoryImpl;
import org.activiti.engine.delegate.Expression;
import org.activiti.engine.delegate.VariableScope;
import org.activiti.engine.impl.bpmn.data.ItemInstance;
import org.activiti.engine.impl.el.*;
import org.activiti.engine.impl.el.JuelExpression;
import org.activiti.engine.impl.persistence.entity.VariableScopeImpl;

import javax.el.*;
import java.util.Map;

public class JuelExpressionManager {
    protected ExpressionFactory expressionFactory;
    protected ELContext parsingElContext;
    protected Map<Object, Object> beans;

    public JuelExpressionManager() {
        this((Map)null);
    }

    public JuelExpressionManager(boolean initFactory) {
        this((Map)null, false);
    }

    public JuelExpressionManager(Map<Object, Object> beans) {
        this(beans, true);
    }

    public JuelExpressionManager(Map<Object, Object> beans, boolean initFactory) {
        this.parsingElContext = new ParsingElContext();
        this.expressionFactory = new ExpressionFactoryImpl();
        this.beans = beans;
    }

    public Expression createExpression(String expression) {
        ValueExpression valueExpression = this.expressionFactory.createValueExpression(this.parsingElContext, expression.trim(), Object.class);
        return new JuelExpression(valueExpression, expression);
    }

    public void setExpressionFactory(ExpressionFactory expressionFactory) {
        this.expressionFactory = expressionFactory;
    }

    public ELContext getElContext(VariableScope variableScope) {
        ELContext elContext = null;
        if (variableScope instanceof VariableScopeImpl) {
            VariableScopeImpl variableScopeImpl = (VariableScopeImpl)variableScope;
            elContext = variableScopeImpl.getCachedElContext();
        }

        if (elContext == null) {
            elContext = this.createElContext(variableScope);
            if (variableScope instanceof VariableScopeImpl) {
                ((VariableScopeImpl)variableScope).setCachedElContext((ELContext)elContext);
            }
        }

        return (ELContext)elContext;
    }

    protected ActivitiElContext createElContext(VariableScope variableScope) {
        ELResolver elResolver = this.createElResolver(variableScope);
        return new ActivitiElContext(elResolver);
    }

    protected ELResolver createElResolver(VariableScope variableScope) {
        CompositeELResolver elResolver = new CompositeELResolver();
        elResolver.add(new VariableScopeElResolver(variableScope));
        if (this.beans != null) {
            elResolver.add(new ReadOnlyMapELResolver(this.beans));
        }

        elResolver.add(new ArrayELResolver());
        elResolver.add(new ListELResolver());
        elResolver.add(new MapELResolver());
        elResolver.add(new JsonNodeELResolver());
        elResolver.add(new DynamicBeanPropertyELResolver(ItemInstance.class, "getFieldValue", "setFieldValue"));
        elResolver.add(new BeanELResolver());
        return elResolver;
    }

    public Map<Object, Object> getBeans() {
        return this.beans;
    }

    public void setBeans(Map<Object, Object> beans) {
        this.beans = beans;
    }
}
