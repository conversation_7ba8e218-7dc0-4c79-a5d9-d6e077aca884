package com.ctsi.activiti.core.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@ApiModel("经办管理属性")
public class HistoryVo implements Serializable {

    @ApiModelProperty("流程定义的key")
    String processDefinitionKey;

    @ApiModelProperty("流程实例id")
    String processInstanceId;

    @ApiModelProperty("流程任务id")
    String taskId;

    @ApiModelProperty("流程任务定义的key")
    String taskDefinitionKey;

    @ApiModelProperty("表单id")
    String formId;

    @ApiModelProperty("流程标题")
    String formMainName;

    @ApiModelProperty("流程的任务类型")
    String procTypeName;

    @ApiModelProperty("当前环节")
    String name;

    @ApiModelProperty("流程办理类别 0: 本人办理  1:代人办理")
    int assignType;

    @ApiModelProperty("任务创建时间")
    LocalDateTime taskCreateTime;

    @ApiModelProperty("流程创建时间")
    LocalDateTime processCreatTime;

    @ApiModelProperty("业务标题")
    String formDataId;
}
