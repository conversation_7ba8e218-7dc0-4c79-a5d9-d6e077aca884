package com.ctsi.activiti.core.listener;

import com.ctsi.activiti.core.listener.funcs.JuelExpressonCommand;
import com.ctsi.activiti.core.listener.funcs.TaskListenerExpression;
import com.ctsi.activiti.core.vo.TaskExpression;
import org.activiti.engine.RuntimeService;
import org.activiti.engine.TaskService;
import org.activiti.engine.delegate.DelegateTask;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.Map;

@Transactional
@Component
public class TaskCompleteExpressionListener implements TaskListenerExpression {

    @Autowired
    private RuntimeService runtimeService;

    @Autowired
    private TaskService taskService;

    @Override
    public void created(DelegateTask delegateTask, String taskListener) {

    }

    @Override
    public void completed(DelegateTask delegateTask, String taskListener) {
        // 流程变量
        String proc_inst_id = delegateTask.getProcessInstanceId();

        String task_id = delegateTask.getId();

        Map<String, Object> variables = runtimeService.getVariables(proc_inst_id);
        variables = taskService.getVariables(task_id);

        runtimeService.setVariables(proc_inst_id, variables);
//        variables = delegateTask.getVariables();
        JuelExpressonCommand juelExpressonCommand = new JuelExpressonCommand(taskListener, variables);
        Object obj = juelExpressonCommand.execute();
    }
}
