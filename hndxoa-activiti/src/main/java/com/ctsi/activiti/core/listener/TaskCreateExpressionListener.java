package com.ctsi.activiti.core.listener;

import com.ctsi.activiti.core.listener.funcs.JuelExpressonCommand;
import com.ctsi.activiti.core.listener.funcs.TaskListenerExpression;
import org.activiti.engine.delegate.DelegateTask;
import org.activiti.engine.delegate.TaskListener;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.Map;

@Transactional
@Component
public class TaskCreateExpressionListener implements TaskListenerExpression {

    @Override
    public void created(DelegateTask delegateTask, String taskListener) {
        // 流程变量
        Map<String, Object> variables =  delegateTask.getVariables();
        JuelExpressonCommand juelExpressonCommand = new JuelExpressonCommand(taskListener, variables);
        Object obj = juelExpressonCommand.execute();
    }

    @Override
    public void completed(DelegateTask delegateTask, String taskListener) {

    }
}
