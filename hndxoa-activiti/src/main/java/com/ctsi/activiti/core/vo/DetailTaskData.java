package com.ctsi.activiti.core.vo;

import com.ctsi.activiti.bpmn.entity.BpmnNode;
import com.ctsi.business.domain.CscpProcBase;

/**
 * 任务详情 + 流程信息
 *
 * @author: PANJL
 * @date: 2019-05-14 11:06
 */
public class DetailTaskData extends TaskData {

    /**
     * 流程数据
     */
    protected ProcessData process;

    protected BpmnNode bpmnNode;

    protected CscpProcBase cscpProcBase;

    //待办人类型: 0 当前人任务  1 代理任务
    protected String assignType;

    public CscpProcBase getCscpProcBase() {
        return cscpProcBase;
    }

    public void setCscpProcBase(CscpProcBase cscpProcBase) {
        this.cscpProcBase = cscpProcBase;
    }

    public ProcessData getProcess() {
        return process;
    }

    public void setProcess(ProcessData process) {
        this.process = process;
    }

    public String getAssignType() {
        return assignType;
    }

    public void setAssignType(String assignType) {
        this.assignType = assignType;
    }
}
