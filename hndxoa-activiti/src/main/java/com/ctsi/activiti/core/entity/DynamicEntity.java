package com.ctsi.activiti.core.entity;

import lombok.Data;

import java.util.List;

public class DynamicEntity {

    private String tableName;

    private Long updateBy;

    private String updateName;

    private Integer BpmStatus;

    private Long id;



    public String getTableName() {
        return tableName;
    }

    public void setTableName(String tableName) {
        this.tableName = tableName;
    }

    public Long getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(Long updateBy) {
        this.updateBy = updateBy;
    }

    public String getUpdateName() {
        return updateName;
    }

    public void setUpdateName(String updateName) {
        this.updateName = updateName;
    }

    public Integer getBpmStatus() {
        return BpmStatus;
    }

    public void setBpmStatus(Integer bpmStatus) {
        BpmStatus = bpmStatus;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

}
