package com.ctsi.activiti.core.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ctsi.activiti.bpmn.constant.BpmnElements;
import com.ctsi.activiti.bpmn.constant.BpmnVariables;
import com.ctsi.activiti.bpmn.entity.BpmnLine;
import com.ctsi.activiti.bpmn.entity.BpmnNode;
import com.ctsi.activiti.bpmn.entity.CscpProc;
import com.ctsi.activiti.bpmn.service.ActivitiBpmnDefinitionService;
import com.ctsi.activiti.callact.constant.CallActivityVariables;
import com.ctsi.activiti.callact.model.CallActivityEndData;
import com.ctsi.activiti.callact.model.CallActivityInModel;
import com.ctsi.activiti.callact.service.CallActivityService;
import com.ctsi.activiti.candidate.service.CandidateService;
import com.ctsi.activiti.core.model.ExecutionLine;
import com.ctsi.activiti.core.model.TaskQueryParam;
import com.ctsi.activiti.core.service.ActivitiQueryService;
import com.ctsi.activiti.core.vo.*;
import com.ctsi.activiti.enums.ActStatusEnum;
import com.ctsi.activiti.enums.ProcUtilsEnum;
import com.ctsi.activiti.ext.entity.ProcessAssignee;
import com.ctsi.activiti.ext.service.ProcessAssigneeService;
import com.ctsi.activiti.multi.model.MultiInstanceDecisionResult;
import com.ctsi.activiti.multi.service.MultiInstanceService;
import com.ctsi.business.domain.CscpAuditContent;
import com.ctsi.business.domain.CscpBtn;
import com.ctsi.business.domain.CscpProcBase;
import com.ctsi.business.domain.CscpProcTaskExtend;
import com.ctsi.business.domain.dto.CscpBtnDto;
import com.ctsi.business.domain.dto.ProcessHandlerDTO;
import com.ctsi.business.dto.QueryApprovalDTO;
import com.ctsi.business.dto.QueryProcessSignDTO;
import com.ctsi.business.service.*;
import com.ctsi.business.vo.QueryApproveManagementCodeVO;
import com.ctsi.common.utils.ActivitiUtils;
import com.ctsi.common.utils.PageData;
import com.ctsi.hndx.constant.BpmStatusConstants;
import com.ctsi.hndx.constant.SysConstant;
import com.ctsi.hndx.utils.JsonUtils;
import com.ctsi.hndx.utils.ListCopyUtil;
import com.ctsi.ssdc.admin.domain.dto.CscpUserDTO;
import com.ctsi.ssdc.admin.service.CscpUserService;
import com.ctsi.ssdc.entity.BizApprovalGiveAdviceRecord;
import com.ctsi.ssdc.entity.dto.BizApprovalGiveAdviceRecordDTO;
import com.ctsi.ssdc.model.PageResult;
import com.ctsi.ssdc.repository.BizApprovalGiveAdviceRecordMapper;
import com.ctsi.ssdc.security.CscpUserDetail;
import com.ctsi.ssdc.security.SecurityUtils;
import com.ctsi.ssdc.util.DateUtil;
import com.googlecode.aviator.AviatorEvaluator;
import org.activiti.engine.HistoryService;
import org.activiti.engine.RuntimeService;
import org.activiti.engine.TaskService;
import org.activiti.engine.history.HistoricProcessInstance;
import org.activiti.engine.history.HistoricTaskInstance;
import org.activiti.engine.history.HistoricTaskInstanceQuery;
import org.activiti.engine.runtime.Execution;
import org.activiti.engine.runtime.ExecutionQuery;
import org.activiti.engine.task.Comment;
import org.activiti.engine.task.Task;
import org.activiti.engine.task.TaskQuery;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.json.JSONArray;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * @author: PANJL
 * @date: 2019-05-13 09:55
 */
@Service
public class ActivitiQueryServiceImpl implements ActivitiQueryService {

    private static final Logger LOGGER = LoggerFactory.getLogger(ActivitiQueryServiceImpl.class);

    @Autowired
    private ActivitiBpmnDefinitionService activitiBpmnDefinitionService;

    @Autowired
    private TaskService taskService;

    @Autowired
    private RuntimeService runtimeService;

    @Autowired
    private CscpUserService cscpUserService;

    @Autowired
    private MultiInstanceService multiInstanceService;

    @Autowired
    private CallActivityService callActivityService;

    @Autowired
    private HistoryService historyService;

    @Autowired
    private ProcessAssigneeService processAssigneeService;

    @Autowired
    private CandidateService candidateService;
    @Autowired
    private CscpProcBaseService cscpProcBaseService;

    @Autowired
    private CscpBtnService cscpBtnService;


    @Autowired
    private CscpActivitiService cscpActivitiService;

    @Autowired
    private CscpProcTaskExtendService cscpProcTaskExtendService;

    @Autowired
    private CscpAuditContentService cscpAuditContentService;

    @Autowired
    private BizApprovalGiveAdviceRecordMapper approvalGiveAdviceRecordMapper;


    @Override
    public ExecutionLine getFinalOutLineListByExpression(String processInstanceId, String processDefinitionId, String processNodeKey, MultiInstanceDecisionResult multiInstanceDecisionResult, Map<String, Object> expressionMap) {
        // 获取流程环节可用的出口路线
        List<ExecutionLine> outLineList = this.getOutLineList(processDefinitionId, processNodeKey);
        if (ActivitiUtils.isEmpty(outLineList)) {
            return null;
        }

//        List<String> elapsedLines = new ArrayList<>();

        // 如果只有一条路线，直接返回，不做任何校验
        if (outLineList.size() == 1) {
            return outLineList.get(0);
        }

        Map<String, Object> expressionParamMap = new HashMap<>();

        // 表达式参数信息增加多实例结果数据，配合路线校验
        if (multiInstanceDecisionResult != null) {
            expressionParamMap.put("multiInstanceResult", multiInstanceDecisionResult.getMultiCalVal());
        }

        if (expressionMap != null) {
            expressionParamMap.putAll(expressionMap);
        }

        // 加入任务提交时携带的参数信息，如果参数名称相同，会直接覆盖
        Map<String, Double> exprDoubleMap = new HashMap<>();
        if (expressionParamMap.size() > 0) {
            try {
//                for (String k : expressionParamMap.keySet()) {
//                    if (isNumeric(expressionParamMap.get(k))) {
//                        exprDoubleMap.put(k, Double.parseDouble(String.valueOf(expressionParamMap.get(k))));
//                    }
//                }
                for (Map.Entry<String, Object> map : expressionParamMap.entrySet()) {
                    if (isNumeric(map.getValue())) {
                        exprDoubleMap.put(map.getKey(), Double.parseDouble(String.valueOf(map.getValue())));
                    }
                }
            } catch (Exception e) {
                throw new IllegalArgumentException("表达式参数值无法转换为有效的数字");
            }
        }

        Map<String, Object> variablesMap = new HashMap<>();
        variablesMap.putAll(exprDoubleMap);

        try {
            // 循环遍历每个路线，校验各路线的表达式
            for (ExecutionLine line : outLineList) {
                String expression = line.getExpression();
                LOGGER.debug("    路线{}（{}）表达式：{}", line.getLineKey(), ActivitiUtils.isEmpty(line.getName()) ? line.getLineKey() : line.getName(), expression);

                if (ActivitiUtils.isEmpty(expression) || "null".equalsIgnoreCase(expression)
                      || "${null}".equalsIgnoreCase(expression)) {
                    continue;
                }

                // 找到符合表达式的出口环节，一旦找到，直接退出循环，不再找后面的路线
                if ((Boolean) AviatorEvaluator.compile(expression).execute(variablesMap)) {
                    return line;
                }
            }
        } catch (Exception e) {
            throw new IllegalArgumentException("表达式验证发生错误");
        }
        return null;
    }

    @Override
    public List<BpmnNode> getOutNodeList(String processDefinitionId, String lineKey) {
        List<BpmnNode> resultNodeList = new ArrayList<>();
        BpmnLine processLine = activitiBpmnDefinitionService.getBpmnLineByLineKey(processDefinitionId, lineKey);

        if (processLine == null) {
            return null;
        }

        String nodeKey = processLine.getToKey();
        if (nodeKey == null) {
            return null;
        }

        BpmnNode processNode = this.getBpmnNodeByNodeKey(processDefinitionId, nodeKey);
        // 如果并行网关，获取后续线路连接的任务环节
        if (processNode.getGateway() == 1 && BpmnElements.isParallelGateway(processNode.getType())) {
            List<BpmnLine> list = activitiBpmnDefinitionService.getBpmnLineListByFromKey(processDefinitionId, processNode.getNodeKey());
            Optional.ofNullable(list).ifPresent(ls -> ls.forEach(l -> {
                BpmnNode node = activitiBpmnDefinitionService.getBpmnNodeByNodeKey(processDefinitionId, l.getToKey());
                resultNodeList.add(node);
            }));
        } else {
            resultNodeList.add(processNode);
        }
        return resultNodeList;
    }

    @Override
    public List<ExecutionLine> getOutLineList(String processDefinitionId, String processNodeKey) {
        List<ExecutionLine> list = new ArrayList<>();
        // 环节直接的出口路线
        List<BpmnLine> processLines = activitiBpmnDefinitionService.getBpmnLineListByFromKey(processDefinitionId, processNodeKey);
        processLines.forEach(l -> {
            BpmnNode targetNode = this.getBpmnNodeByNodeKey(processDefinitionId, l.getToKey());
            ExecutionLine item = ExecutionLine.newInstance(l);
            item.setSequenceNo(targetNode.getSequenceNo());
            if (targetNode.getGateway() == 1) {
                // 网关
                // 并行网关，是一个可用的出口路线
                if (BpmnElements.isParallelGateway(targetNode.getType())) {
                    list.add(item);
                }
                // 排他网关，找到该网关的后续路线，这些路线都是可用的出口路线
                else {
                    List<BpmnLine> targetLineList = activitiBpmnDefinitionService.getBpmnLineListByFromKey(processDefinitionId, targetNode.getNodeKey());


                    Optional.ofNullable(targetLineList).ifPresent(ls -> ls.forEach(
                            tl -> list.add(item)));
                }
            } else {
                // 不是网关，是一个可用的出口路线
                list.add(item);
            }
        });
        return list.stream().map(i -> {
            if (Objects.isNull(i.getSequenceNo())) {
                i.setSequenceNo(0);
            }
            return i;
        }).sorted(Comparator.comparing(ExecutionLine::getSequenceNo)).collect(Collectors.toList());

    }

    @Override
    public List<ExecutionLine> getOutLineList(String taskId) {
        Task task = taskService.createTaskQuery().taskId(taskId).singleResult();
        if (task == null) {
            throw new IllegalArgumentException("没有找到流程任务");
        }
        String processInstanceId = task.getProcessInstanceId();
        List<ExecutionLine> list = getOutLineList(task.getProcessInstanceId(), task.getProcessDefinitionId(), task.getTaskDefinitionKey());
        Set<String> hasGONode = cscpProcTaskExtendService.getHasGONode(processInstanceId);
        int size = list.size();
        Set<String> sameNameSet = new HashSet<>();
        List<ExecutionLine> realList = new ArrayList<>();
        list.forEach(i -> {
            if (sameNameSet.add(i.getToKey())) {
                BpmnNode bpmnNode = this.getBpmnNodeByNodeKey(i.getProcessDefinitionId(), i.getToKey());
                i.setFilterRule(bpmnNode.getFilterRule());
                i.setTaskType(bpmnNode.getType());
                i.setIsMultiInstance(bpmnNode.getMultiInstance());
                // 只有一个的时候不显示
                if (hasGONode.contains(i.getToKey()) && size > 1) {
                    i.setHasNodeGo(true);
                }
                realList.add(i);
            }

        });
        return realList;
    }

    @Override
    public List<CscpBtnDto> getCscpBtnByTaskId(String taskId) {
        Task task = taskService.createTaskQuery().taskId(taskId).singleResult();
        if (task == null) {
            throw new IllegalArgumentException("没有找到流程任务");
        }
        String procDefId = task.getProcessDefinitionId();
        String nodeKey = task.getTaskDefinitionKey();
        BpmnNode currentNode = activitiBpmnDefinitionService.getBpmnNodeByNodeKey(procDefId, nodeKey);
        String btnIdsJson = currentNode.getBtnIds();
        List<CscpBtn> listCscpBtn = new ArrayList<>();
        if (btnIdsJson == null) {
            List<CscpBtn> prBtn = cscpBtnService.findDefaultBtns();
            if (CollectionUtils.isNotEmpty(prBtn)) {
                listCscpBtn.addAll(prBtn);
            }
            return ListCopyUtil.copy(listCscpBtn, CscpBtnDto.class);
        }
        JSONArray jArr = new JSONArray(btnIdsJson);
        for (int i = 0; i < jArr.length(); i++) {
            //String btnId = jArr.getString(i);
            JSONObject jObj = jArr.getJSONObject(i);
            String btnId = jObj.getString("id");
            /*LambdaQueryWrapper<CscpBtn> lambdaQueryWrapper = new LambdaQueryWrapper<>();
            lambdaQueryWrapper.eq(CscpBtn::getFunctionName, btnId).last(SysConstant.LIMIT_ONE);*/
            CscpBtn cscpBtn = cscpBtnService.getById(btnId);
            if (cscpBtn != null) {
                listCscpBtn.add(cscpBtn);
            }
        }
        List<CscpBtn> prBtn = cscpBtnService.findDefaultBtns();
        if (CollectionUtils.isNotEmpty(prBtn)) {
            listCscpBtn.addAll(prBtn);
        }
        if(CollectionUtils.isNotEmpty(listCscpBtn)){
            listCscpBtn = listCscpBtn.stream().sorted(Comparator.comparing(CscpBtn::getDisplayOrder)).collect(Collectors.toList());
        }
        return ListCopyUtil.copy(listCscpBtn, CscpBtnDto.class);

    }

    @Override
    public List<ExecutionLine> getOutLineList(String processInstanceId, String processDefinitionId, String processNodeKey) {
        // 先判断是否配置了条件表达式
        List<ExecutionLine> outLineListExpression = new ArrayList<ExecutionLine>();
        PageData pd = new PageData();
        pd.put("PROCDEFID", processDefinitionId);
        pd.put("CURRENTNODEID", processNodeKey);

        List<PageData> expressionNodeList = cscpActivitiService.getExpressionByNode(pd);

        Map<String, Object> variablesMap = runtimeService.getVariables(processInstanceId);
        String formDataStr = (String) variablesMap.get("formData");
        Map formData = JsonUtils.jsonToPojo(formDataStr, Map.class);
        for (PageData pageData : expressionNodeList) {
            String expression = pageData.getString("EXPRESSION");
            boolean checkFlag = false;
            try {
                checkFlag = (Boolean) AviatorEvaluator.compile(expression).execute(formData);
            } catch (Exception e) {
                LOGGER.error("流程实例id：{}解析表达式：{}出错。 {}", processInstanceId, expression, e.getMessage());
            }

            if (checkFlag) {
                BpmnNode targetBpmnNode = activitiBpmnDefinitionService.getBpmnNodeByNodeKey(processDefinitionId, pageData.getString("TARGETNODEID"));
                ExecutionLine executionLine = new ExecutionLine();
                executionLine.setId(null);
                executionLine.setName(targetBpmnNode.getName());
                executionLine.setLineKey(targetBpmnNode.getNodeKey());
                executionLine.setToKey(targetBpmnNode.getNodeKey());
                executionLine.setProcessDefinitionId(processDefinitionId);
                executionLine.setProcessDeploymentId(targetBpmnNode.getProcessDeploymentId());
                executionLine.setProcessDefinitionKey(targetBpmnNode.getProcessDefinitionKey());
                outLineListExpression.add(executionLine);
            }
        }

        if (outLineListExpression.size() > 0) {
            return outLineListExpression;
        }

        // 获取流程环节可用的出口路线
        List<ExecutionLine> outLineList = getOutLineList(processDefinitionId, processNodeKey);

        // 根据当前环节，

        // 当前环节
        BpmnNode currentNode = this.getBpmnNodeByNodeKey(processDefinitionId, processNodeKey);

        if (BpmnElements.isCallActivity(currentNode.getType())) {
            return outLineList;
        }

        // 当前环节是多实例任务
        if (currentNode.getMultiInstance() == 1) {
            // 多实例任务由于它的特殊性（只有固定的选项），不在这里判断他的出口路线信息，在多实例任务确认需要进入下一环节时，再计算
            // 当前环节是普通任务

            // 判断是否子流程，如果是子流程，判断是否已经到达了结束前的环节
            // 如果到达了结束前的环节，获取主流程传给子流程的出口路线信息
            CallActivityEndData callActivityEndData = getCallActivityEndData(processInstanceId, processDefinitionId, outLineList);
            if (callActivityEndData == null) {
                return multiInstanceService.getMultiTaskExecutionLineList(currentNode, outLineList);
            }
            // 已经确认该环节是子流程的结束前环节，重新计算出口路线
            return multiInstanceService.getMultiTaskExecutionLineList(currentNode, getNormalOutLineList(callActivityEndData, outLineList));
        } else {
            // 当前环节是普通任务

            // 判断是否子流程，如果是子流程，判断是否已经到达了结束前的环节
            // 如果到达了结束前的环节，获取主流程传给子流程的出口路线信息
            CallActivityEndData callActivityEndData = getCallActivityEndData(processInstanceId, processDefinitionId, outLineList);
            if (callActivityEndData == null) {
                return outLineList;
            }
            // 已经确认该环节是子流程的结束前环节，重新计算出口路线
            return getNormalOutLineList(callActivityEndData, outLineList);
        }
    }

    /**
     * 判断是否子流程，如果是子流程，将子流程数据返回
     *
     * @param processInstanceId
     * @param processDefinitionId
     * @param outLineList
     * @return
     */
    private CallActivityEndData getCallActivityEndData(String processInstanceId, String processDefinitionId, List<ExecutionLine> outLineList) {
        // 没有设置流程实例，退出
        if (processInstanceId == null) {
            return null;
        }
        CallActivityEndData callActivityEndData = null;
        // 如果是子流程
        if (callActivityService.isSubProcess(processInstanceId)) {
            // 获取当前流程结束环节
            List<BpmnNode> endNodeList = activitiBpmnDefinitionService.getEndNodeList(processDefinitionId);
            // 匹配当前是否已到达结束前的环节
            ExecutionLine executionLine = outLineList.stream().filter(s -> endNodeList.stream().anyMatch(n -> n.getNodeKey().equals(s.getToKey()))).findFirst().orElse(null);
            if (executionLine != null) {
                callActivityEndData = new CallActivityEndData();
                callActivityEndData.setExecutionLine(executionLine);
                Execution execution = runtimeService.createExecutionQuery().executionId(processInstanceId).singleResult();
                CallActivityInModel callActivityInModel = runtimeService.getVariable(execution.getId(), CallActivityVariables.CALL_ACTIVITY_IN.name(), CallActivityInModel.class);
                callActivityEndData.setCallActivityInModel(callActivityInModel);
            }
        }
        return callActivityEndData;
    }

    /**
     * 子流程普通任务的出口路线
     *
     * @param callActivityEndData
     * @param outLineList
     * @return
     */
    private List<ExecutionLine> getNormalOutLineList(CallActivityEndData callActivityEndData, List<ExecutionLine> outLineList) {
        // 当前环节匹配了一个结束出口路线，从子流程的入参中获取主流程的出口路线，加入到当前路线中
        ExecutionLine callActivityEndLine = callActivityEndData.getExecutionLine();
        CallActivityInModel callActivityInModel = callActivityEndData.getCallActivityInModel();
        List<ExecutionLine> subProcessOutLineKeyList = callActivityInModel.getSubProcessOutLineKeyList();

        // 多实例子流程，替换为主流程唯一的出口路线（主流程的子流程出口有且只有一个）
        if (callActivityInModel.getSubProcessDefinitionNode().getMultiInstance() == 1) {
            ExecutionLine endLine = outLineList.stream().filter(s -> callActivityEndLine.getId().equals(s.getId())).findFirst().orElse(null);
            if (endLine != null) {
                if(StringUtils.isEmpty(endLine.getName())){
                    endLine.setName("完成");
                }
                // 多实例子流程后续任务要求只能连接任务环节、结束环节或子流程环节
                endLine.setCallActivityOutLineKey(subProcessOutLineKeyList.get(0).getLineKey());
            }
            return outLineList;
        }

        /*
         * 达到的效果：
         * 得到子流程的出口路线（outLineList）: 路线1 = "退回(lineKey1)"，路线2 = "提交xxx环节(lineKey2)"，路线3 = "结束(lineKey3)"（该路线到达子流程结束环节）
         * 主流程传给子流程的出口信息（subProcessOutLineKeyList）： 路线4 = "提交Z1环节(lineKey4)"，路线5 = "提交Z2环节(lineKey5)"，路线6 = "提交Z3环节(lineKey6)"
         * 将outLineList中的 "路线3" 删除，得到：路线1 = "退回(lineKey1)"，路线2 = "提交xxx环节(lineKey2)"
         * 将subProcessOutLineKeyList中每个路线的CallActivityOutLineKey设置为自身的lineKey，这表示，该子流程跳入主流程时走 CallActivityOutLineKey 路线
         * 将subProcessOutLineKeyList中每个路线的LineKey设置为子流程结束路线的LineKey，这表示，该子流程实际执行的是子流程的结束路线
         * 用户最终得到的是：路线1 = "退回(lineKey1)"，路线2 = "提交xxx环节(lineKey2)"，路线4 = "提交Z1环节(lineKey4)"，路线5 = "提交Z2环节(lineKey5)"，路线6 = "提交Z3环节(lineKey6)"
         *
         */
        // 普通子流程，在子流程中，并且到达子流程出口环节，替换为主流程传入的出口路线
        // 剔除了结束出口路线的路线集合
        List<ExecutionLine> finalLineList = outLineList.stream().filter(s -> !callActivityEndLine.getId().equals(s.getId())).collect(Collectors.toList());
        subProcessOutLineKeyList.forEach(s -> {
            // 指定子流程入参中的出口Key
            s.setCallActivityOutLineKey(s.getLineKey());    // 子流程出口KEY
            // 指定结束出口是实际的出口路线Key
            s.setLineKey(callActivityEndLine.getLineKey());
        });
        finalLineList.addAll(subProcessOutLineKeyList);
        return finalLineList;
    }

    @Override
    public List<ExecutionLine> getFirstNodeLineList(String processDefinitionId) {
        // 获取流程第一个任务环节
        BpmnNode firstNode = activitiBpmnDefinitionService.getFirstNode(processDefinitionId);
        if (!(BpmnElements.isUserTask(firstNode.getType()) && firstNode.getMultiInstance() == 0)) {
            throw new IllegalArgumentException("快速启动的流程第一个环节必须是个人任务");

        }
        // 获取流程环节可用的出口路线
        return this.getOutLineList(processDefinitionId, firstNode.getNodeKey());
    }


    private String getUserName(long userId) {
        CscpUserDTO user = cscpUserService.findByUserId(userId);
        if (user != null) {
            String n = user.getRealName();
            if (n.length() == 0) {
                n = user.getLoginName();
            }
            return n;
        }
        return null;
    }



    private BpmnNode getBpmnNodeByNodeKey(String processDefinitionId, String nodeKey) {
        return activitiBpmnDefinitionService.getBpmnNodeByNodeKey(processDefinitionId, nodeKey);
    }

    @Override
    public String getTaskComment(String taskId) {
        List<Comment> taskComments = taskService.getTaskComments(taskId);
        return taskComments == null || taskComments.size() == 0 ? null : taskComments.get(0).getFullMessage();
    }


    @Override
    public DetailTaskData getTaskById(String taskId) {
        Task task = taskService.createTaskQuery().taskId(taskId).singleResult();

        // 任务数组
        DetailTaskData detailTaskData = TaskData.newInstance(task);
        return detailTaskData;
    }

    @Override
    public PageResult<DetailTaskData> getActivelyTaskList(int pageNumber, int pageSize, String assignee,
                                                          String processDefinitionKey, String processName, String typeId) {
        if (ActivitiUtils.isEmpty(assignee)) {
            throw new IllegalArgumentException("查找待办任务列表时，没有设置用户ID");
        }
        TaskQuery taskQuery = taskService.createTaskQuery();
        // 流程定义KEY
        if (ActivitiUtils.isNotEmpty(processDefinitionKey)) {
            taskQuery.processDefinitionKey(processDefinitionKey);
        }
        // 流程名称
        if (ActivitiUtils.isNotEmpty(processName)) {
            taskQuery.processDefinitionNameLike("%" + processName + "%");
        }
        // 受理人、组任务
        taskQuery.taskCandidateOrAssigned(assignee);

        /*BpmnModelExample bpmnModelExample = new BpmnModelExample();

        BpmnModelExample.Criteria bmeCri1 = bpmnModelExample.createCriteria();
        bmeCri1.andOperateTypeIn(Arrays.asList(new String[]{"1","2"}));
        if(typeId != null && typeId.trim().length() > 0){
            bmeCri1.andTypeIdEqualsTo(typeId.trim());
        }*/

        LambdaQueryWrapper<CscpProc> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(StringUtils.isNotEmpty(typeId), CscpProc::getTypeId, typeId.trim())
                .in(CscpProc::getOperateType, Arrays.asList(new String[]{"1", "2"}));

        List<CscpProc> listCscpProc = cscpActivitiService.list(lambdaQueryWrapper);

        Set<String> setDefKey = new HashSet<>(listCscpProc.size());

        for (CscpProc model : listCscpProc) {
            String pid = model.getProcessDefinitionKey();
            setDefKey.add(pid);
        }

        List<String> listDefKey = new ArrayList<>(setDefKey.size());
        listDefKey.addAll(setDefKey);

        if (listDefKey.size() == 0) {
            listDefKey.add("~");//防止因无数据导致全部查出
        }

        taskQuery.processDefinitionKeyIn(listDefKey);

        List<Task> list = taskQuery.orderByTaskCreateTime().active().desc().listPage(pageNumber * pageSize, pageSize);
        long count = taskQuery.count();

        // 转换任务信息
        List<DetailTaskData> taskDataList = new ArrayList<>();
        if (list != null) {
            list.forEach(task -> {
                TaskData taskData = TaskData.newInstance(task);
                if (taskData == null) {
                    return;
                }
                DetailTaskData detailTaskData = ActivitiUtils.toBean(DetailTaskData.class, taskData);
                // 获取历史流程实例
                HistoricProcessInstance historicProcessInstance = historyService.createHistoricProcessInstanceQuery().processInstanceId(task.getProcessInstanceId()).singleResult();
                ProcessData processData = ProcessData.newInstance(historicProcessInstance);
                CscpProcBase cscpProcBase = cscpProcBaseService.findProcBaseGeneral(task.getProcessInstanceId(), task.getProcessDefinitionId());
                if (cscpProcBase != null) {
                    detailTaskData.setProcess(processData);
                    detailTaskData.setTaskTitle(cscpProcBase.getTitle());
                    detailTaskData.setFormDataId(cscpProcBase.getFormDataId());
                    detailTaskData.setFormMainName(cscpProcBase.getTitle());
                    detailTaskData.setProcessCreatTime(cscpProcBase.getCreateTime());
                    detailTaskData.setFormId(cscpProcBase.getFormDefId());
                }
                /*CscpProcTaskExtendExample cscpProcTaskExtendExample=new CscpProcTaskExtendExample();
                CscpProcTaskExtendExample.Criteria criteria=cscpProcTaskExtendExample.createCriteria();
                criteria.andProcinstidEqualTo(cscpProcBase.getProcInstId());
                criteria.andProcdefidEqualTo(cscpProcBase.getProcDefId());
                criteria.andTaskidEqualTo(taskData.getTaskId());*/

                LambdaQueryWrapper<CscpProcTaskExtend> lambdaQueryWrapperExtend = new LambdaQueryWrapper<>();
                lambdaQueryWrapperExtend.eq(CscpProcTaskExtend::getProcinstId, cscpProcBase.getProcInstId())
                        .eq(CscpProcTaskExtend::getProcdefId, cscpProcBase.getProcDefId())
                        .eq(CscpProcTaskExtend::getTaskId, taskData.getTaskId());

                CscpProcTaskExtend cscpProcTaskExtend = cscpProcTaskExtendService.getOne(lambdaQueryWrapperExtend);
                if (cscpProcTaskExtend != null) {
                   /* if(StringUtils.isNotEmpty(cscpProcTaskExtend.getStarttime())){
                        detailTaskData.setStartTime(DateUtil.parseDate3(cscpProcTaskExtend.getStarttime()));
                    }else{
                        detailTaskData.setStartTime(DateUtil.parseDate3(detailTaskData.getProcessCreatTime()));
                    }*/
                    if (StringUtils.isNotEmpty(cscpProcTaskExtend.getEndTime())) {
                        detailTaskData.setEndTime(DateUtil.parseDate3(cscpProcTaskExtend.getEndTime()));
                    }
                }
                taskDataList.add(detailTaskData);
            });
        }
        return new PageResult(taskDataList, count, count);
    }

    @Override
    public PageResult<TaskVO> getActivelyTaskListWithMultiAssignee(int pageNumber, int pageSize, TaskQueryParam taskQueryParam, boolean isCountMark) {
        if (ActivitiUtils.isEmpty(taskQueryParam.getTaskCandidateOrAssignedIn())) {
            throw new IllegalArgumentException("查找待办任务列表时，没有设置用户ID");
        }
        String typeId = taskQueryParam.getTypeId();

        TaskQuery taskQuery = taskService.createTaskQuery().active().taskCandidateOrAssigned(taskQueryParam.getTaskCandidateOrAssignedIn().get(0));
        if (StringUtils.isNotBlank(taskQueryParam.getBusinessType())) {
            // 按照业务区分
            if (taskQueryParam.getIsApproval() != null) {
                if (taskQueryParam.getIsApproval() == 2) { //省委项目需求，参数为2表示查询不是呈批件的待办数据
                    taskQuery.processVariableValueNotEquals(BpmnVariables.BUSINESS_TYPE.name(), taskQueryParam.getBusinessType().trim());
                } else {
                    taskQuery.processVariableValueEquals(BpmnVariables.BUSINESS_TYPE.name(), taskQueryParam.getBusinessType().trim());
                }
            } else {
                taskQuery.processVariableValueEquals(BpmnVariables.BUSINESS_TYPE.name(), taskQueryParam.getBusinessType().trim());
            }

        }
        if (StringUtils.isNotBlank(taskQueryParam.getTitle())) {
            taskQuery.processVariableValueLike(ProcUtilsEnum.TITLE.getCode(), "%" + taskQueryParam.getTitle() + "%");
        }
        if (StringUtils.isNotBlank(typeId)) {
            // 按照表单类型区分
            taskQuery.processVariableValueEquals(BpmnVariables.PROC_TYPE_ID.name(), typeId.trim());
        }

        long count = taskQuery.count();
        // 转换任务信息
        List<TaskVO> taskDataList = new ArrayList<>();
        if (!isCountMark) {
            List<Task> taskList = taskQuery.orderByTaskCreateTime().desc().listPage((pageNumber - 1) * pageSize, pageSize);
            taskList.forEach(task -> {
                TaskVO taskVO = TaskVO.newInstance(task);
                CscpProcBase cscpProcBase = cscpProcBaseService.findProcBaseGeneral(taskVO.getProcessInstanceId(), "");
                if (cscpProcBase != null) {
                    taskVO.setProcessInstanceId(taskVO.getProcessInstanceId());
                    taskVO.setFormDataId(cscpProcBase.getFormDataId());
                    taskVO.setTitle(cscpProcBase.getTitle());
                    taskVO.setProcessCreatTime(cscpProcBase.getCreateTime());
                    taskVO.setFormId(cscpProcBase.getFormDefId());
                    taskVO.setProcessDefinitionKey(cscpProcBase.getModelkey());
                    taskVO.setProcTypeName(cscpProcBase.getProcTypeName());
                    taskVO.setBpmStatus(cscpProcBase.getBpmStatus());
                    taskVO.setCreateName(cscpProcBase.getCreateName());
                    taskVO.setProcessDefinitionId(cscpProcBase.getProcDefId());
                    taskVO.setDepartmentName(cscpProcBase.getDepartmentName());
                    taskVO.setRootProcessInstanceId(cscpProcBase.getRootProcInstId());
                    taskVO.setUrgency(cscpProcBase.getUrgency());
                    taskVO.setTelegraph(cscpProcBase.getTelegraph());
                }
                //查询当前环节处理人信息
                List<ProcessAssignee> processAssignee = processAssigneeService.getProcessAssigneeListByProcInstId(taskVO.getProcessInstanceId(),null);
                if(processAssignee!=null && processAssignee.size()>0){
                    String assigneeName = processAssignee.stream().map(ProcessAssignee::getAssigneeName).collect(Collectors.joining(","));
                    taskVO.setAssigneeName(assigneeName);
                    String uid = String.valueOf(SecurityUtils.getCurrentUserId());
                    List<ProcessAssignee> assignee = processAssignee.stream().filter(q->q.getAssignee().equals(uid)).collect(Collectors.toList());
                    if(assignee!=null && assignee.size()>0){
                        taskVO.setReadStatus(assignee.get(0).getReadStatus());
                    }
                }
                taskDataList.add(taskVO);
            });
        }

        return new PageResult(taskDataList, count, pageNumber);
    }

    @Override
    public PageResult<TaskVO> getLiaisonActivelyTaskListUnionOutAuth(int pageNumber, int pageSize, TaskQueryParam taskQueryParam, boolean isCountMark) {
        if (ActivitiUtils.isEmpty(taskQueryParam.getTaskCandidateOrAssignedIn())) {
            throw new IllegalArgumentException("查找待办任务列表时，没有设置用户ID");
        }
        IPage iPage = new Page(pageNumber, pageSize);
        IPage<TaskVO> taskVOIPage = cscpProcBaseService.getLiaisonActivelyTaskListUnionOutAuth(iPage, taskQueryParam);
        List<TaskVO> taskVoList = taskVOIPage.getRecords();
        if (CollectionUtils.isEmpty(taskVoList)) {
            return new PageResult<>(Collections.emptyList(), 0, 0);
        }
        List<Long> formDataIdList = taskVoList.stream().map(TaskVO::getFormDataId)
                .map(Long::parseLong).distinct().collect(Collectors.toList());
        List<BizApprovalGiveAdviceRecord> approvalGiveAdviceRecordList = approvalGiveAdviceRecordMapper.selectListNoAdd(Wrappers.<BizApprovalGiveAdviceRecord>lambdaQuery()
                .in(BizApprovalGiveAdviceRecord::getFormDataId, formDataIdList)
                .orderByDesc(BizApprovalGiveAdviceRecord::getAdviceTime));
        Map<String, List<BizApprovalGiveAdviceRecord>> approvalGiveAdviceRecordMap = approvalGiveAdviceRecordList.stream().collect(Collectors.groupingBy(BizApprovalGiveAdviceRecord::getFormDataId));
        taskVoList.forEach(vo -> {
            List<BizApprovalGiveAdviceRecord> approvalGiveAdviceList = approvalGiveAdviceRecordMap.getOrDefault(vo.getFormDataId(), Collections.emptyList());
            List<BizApprovalGiveAdviceRecordDTO> approvalGiveAdviceRecordDTOList = ListCopyUtil.copy(approvalGiveAdviceList, BizApprovalGiveAdviceRecordDTO.class);
            vo.setApprovalGiveAdviceRecordList(approvalGiveAdviceRecordDTOList);
            vo.setShowCreateFlag(approvalGiveAdviceRecordDTOList.stream()
                    .noneMatch(dto -> dto.getCreateBy() != null && dto.getCreateBy() == SecurityUtils.getCurrentUserId()));
        });
        return new PageResult(taskVoList, taskVOIPage.getTotal(), taskVOIPage.getTotal());
    }

    @Override
    public Integer getLiaisonActivelyTaskListUnionOutAuthSubscript(TaskQueryParam taskQueryParam, boolean isCountMark) {
        if (ActivitiUtils.isEmpty(taskQueryParam.getTaskCandidateOrAssignedIn())) {
            return 0;
        }
        List<TaskVO> taskVOList = cscpProcBaseService.getLiaisonActivelyTaskListUnionOutAuthSubscript(taskQueryParam);
        return taskVOList.size();
    }

    @Override
    public PageResult<TaskVO> getSecretaryActivelyTaskListUnionOutAuth(int pageNumber, int pageSize, TaskQueryParam taskQueryParam) {
        IPage iPage = new Page(pageNumber, pageSize);
        IPage<TaskVO> taskVOIPage = cscpProcBaseService.getSecretaryActivelyTaskListUnionOutAuth(iPage, taskQueryParam);
        return new PageResult(taskVOIPage.getRecords(), taskVOIPage.getTotal(), taskVOIPage.getTotal());
    }

    @Override
    public PageResult<TaskVO> getHandleUserProcessList(int pageNumber, int pageSize, TaskQueryParam taskQueryParam) {
        IPage iPage = new Page(pageNumber, pageSize);
        Long userId = SecurityUtils.getCurrentUserId();
        // 查询处理人得流程
        List<ProcessAssignee> processAssigneeList = new ArrayList<>();
        //processAssigneeList = processAssigneeService.getProcessAssigneeListByAssigneeId(userId);
        //List<String> processInstanceIdList = processAssigneeList.stream().map(ProcessAssignee::getProcessInstanceId).collect(Collectors.toList());
        //  包括办结的已经完成的数据
        IPage<TaskVO> taskVOIPage = cscpProcBaseService.getHandleUserProcessList(iPage, taskQueryParam);

        //获取流程
        List<String> processInstanceIdList = taskVOIPage.getRecords().stream().map(TaskVO::getProcessInstanceId).collect(Collectors.toList());
        if(processInstanceIdList!=null && processInstanceIdList.size()>0){
            processAssigneeList = processAssigneeService.getProcessAssigneeListByProcInstIdList(processInstanceIdList);
        }

        List<ProcessAssignee> finalProcessAssigneeList = processAssigneeList;
        taskVOIPage.getRecords().forEach(item->{

            item.setIsHandleBtn(0);
            if(userId.longValue() == item.getCreateBy().longValue()){
                item.setIsHandleBtn(1);
            }

            //   已办列表里面显示的名称，0 表示什么都不显示 ，1 表示显示撤回  2 表示显示减人和加人按钮
            BpmnNode bpmnNodeByNodeKey = null;
            if (StringUtils.isNotEmpty(item.getProcessDefinitionId()) && StringUtils.isNotEmpty(item.getTaskDefinitionKey())){
                bpmnNodeByNodeKey = activitiBpmnDefinitionService.getBpmnNodeByNodeKey(item.getProcessDefinitionId(), item.getTaskDefinitionKey());
            }
            DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            List<ProcessAssignee> processAssigneeListNew = finalProcessAssigneeList.stream().filter(q->q.getProcessInstanceId().equals(item.getProcessInstanceId()))
                    .sorted(Comparator.comparing(ProcessAssignee::getCreateTime)).collect(Collectors.toList());
            if (processAssigneeListNew!=null && processAssigneeListNew.size()==1){
                ProcessAssignee processAssignee = processAssigneeListNew.get(0);
                item.setTaskId(processAssignee.getTaskId().toString());
                item.setAssingee(Long.valueOf(processAssignee.getAssignee()));
                item.setAssigneeName(processAssignee.getAssigneeName());
                item.setReadStatus(processAssignee.getReadStatus());
                item.setName(processAssignee.getNodeName());
                if(processAssignee.getReadStatus()==0){
                    item.setReadStatusStr("未处理");
                }else{
                    item.setReadStatusStr("正在处理");
                }
                //String createTime = sdf.format(processAssignee.getCreateTime());
                item.setHandleTime(processAssignee.getCreateTime());


                if (userId.equals(processAssignee.getCreateBy()) && bpmnNodeByNodeKey != null && bpmnNodeByNodeKey.getMultiInstance() == 1){
                    // 1、会签，显示加减签按钮
                    item.setShowName(2);
                }else if (userId.equals(processAssignee.getCreateBy()) && bpmnNodeByNodeKey != null && bpmnNodeByNodeKey.getMultiInstance() != 1){
                    // 2、下一节点为当前用户创建，且不是会签节点，显示撤回按钮
                    item.setShowName(1);
                }else {
                    // 3、不显示按钮
                    item.setShowName(0);
                }
                // item.setHandleTime(LocalDateTime.parse(createTime, df));
            }else if(processAssigneeListNew!=null && processAssigneeListNew.size()>1){
                ProcessAssignee processAssignee = processAssigneeListNew.get(0);
                String assigneeName = processAssigneeListNew.stream().map(ProcessAssignee::getAssigneeName).collect(Collectors.joining(","));
                item.setAssigneeName(assigneeName);
                List<String> readStatusStr = new ArrayList<>();
                processAssigneeListNew.forEach(i->{
                    if(i.getReadStatus()==0){
                        readStatusStr.add("未处理");
                    }else{
                        readStatusStr.add("正在处理");
                    }
                });
                item.setReadStatusStr(StringUtils.join(readStatusStr,","));
                item.setName(processAssigneeListNew.get(0).getNodeName());
                // String createTime = sdf.format(processAssigneeListNew.get(0).getCreateTime());
                // item.setHandleTime(LocalDateTime.parse(createTime, df));
                List<String> userIdList = processAssigneeListNew.stream().map(ProcessAssignee::getAssignee).collect(Collectors.toList());
                item.setAssingeeList(userIdList);
                if (userId.equals(processAssignee.getCreateBy())){
                    item.setShowName(2);
                }
            }
            if (item.getBpmStatus() == 3){
                item.setReadStatusStr("一键办结");
                item.setBpmStatus(BpmStatusConstants.PROCESS_END);
                // 查找最新办理人
                CscpAuditContent cscpAuditContent = cscpAuditContentService.getAuditContentListByInstanceId(item.getProcessInstanceId()).get(0);
                item.setAssigneeName(cscpAuditContent.getAuditorName());
                item.setName("办结");
                item.setReadStatusStr("已完成");
            }
            if (item.getBpmStatus() == 2 && CollectionUtil.isEmpty(processAssigneeListNew)){
                //  走的子流程，目前流程已经进行，获取其所有子流程，差当前子流程的处理人
                LambdaQueryWrapper<CscpProcBase> cscpProcBaseLambdaQueryWrapper = new LambdaQueryWrapper<>();
                cscpProcBaseLambdaQueryWrapper.eq(CscpProcBase::getRootProcInstId,item.getProcessInstanceId())
                        .ne(CscpProcBase::getProcInstId,item.getProcessInstanceId());
                List<CscpProcBase> cscpProcBaseList = cscpProcBaseService.selectListNoAdd(cscpProcBaseLambdaQueryWrapper);
                if (CollectionUtil.isNotEmpty(cscpProcBaseList)){
                    List<String> idList =  cscpProcBaseList.stream().map(i -> i.getProcInstId()).collect(Collectors.toList());
                    List<ProcessAssignee>  processAssigneeListSub = processAssigneeService.getProcessAssigneeListByProcInstIdList(idList);
                    if (CollectionUtil.isNotEmpty(processAssigneeListSub)){
                        String assigneeName = processAssigneeListSub.stream().map(ProcessAssignee::getAssigneeName).collect(Collectors.joining(","));
                        item.setAssigneeName(assigneeName);
                        List<String> readStatusStr = new ArrayList<>();
                        processAssigneeListSub.forEach(i->{
                            if(i.getReadStatus()==0){
                                readStatusStr.add("未处理");
                            }else{
                                readStatusStr.add("正在处理");
                            }
                        });
                        item.setReadStatusStr(StringUtils.join(readStatusStr,","));
                        String nodeName = processAssigneeListSub.stream().map(ProcessAssignee::getNodeName).distinct().collect(Collectors.joining(","));
                        item.setName(nodeName);
                        item.setTaskId(String.valueOf(processAssigneeListSub.get(0).getTaskId()));
                        // String createTime = sdf.format(processAssigneeListSub.get(0).getCreateTime());
                        // item.setHandleTime(LocalDateTime.parse(createTime, df));
                        List<String> userIdList = processAssigneeListSub.stream().map(ProcessAssignee::getAssignee).collect(Collectors.toList());
                        item.setAssingeeList(userIdList);
                    }

                }
            }
        });
        return new PageResult(taskVOIPage.getRecords(), taskVOIPage.getTotal(), taskVOIPage.getCurrent());
    }

    /**
     * 获取待办在办已办三种状态列表的数据
     * @param pageNumber           当前页码
     * @param pageSize             每页长度
     * @param taskQueryParam
     * @return
     */
    @Override
    public PageResult<TaskVO> getUserProcessList(int pageNumber, int pageSize, TaskQueryParam taskQueryParam) {
        IPage iPage = new Page(pageNumber, pageSize);
        IPage<TaskVO> taskVOIPage = cscpProcBaseService.getUserProcessList(iPage, taskQueryParam);
        Long userId = SecurityUtils.getCurrentUserId();
        //获取流程
        List<String> processInstanceIdList = taskVOIPage.getRecords().stream().map(TaskVO::getProcessInstanceId).collect(Collectors.toList());
        List<ProcessAssignee> processAssigneeList = new ArrayList<>();
        if(processInstanceIdList!=null && processInstanceIdList.size()>0){
            processAssigneeList = processAssigneeService.getProcessAssigneeListByProcInstIdList(processInstanceIdList);
        }

        List<ProcessAssignee> finalProcessAssigneeList = processAssigneeList;
        taskVOIPage.getRecords().forEach(item->{
            item.setIsHandleBtn(0);
            if(userId.longValue() == item.getCreateBy().longValue()){
                item.setIsHandleBtn(1);
            }

            if(item.getBpmStatus() == BpmStatusConstants.PROCESS_END){
                item.setName("结束");
            }
            //判断流程处理人ID是否跟当前用户ID一致，如果一致则认为待办
            List<ProcessAssignee> userProcList = finalProcessAssigneeList.stream().filter(q->q.getProcessInstanceId().equals(item.getProcessInstanceId()) && q.getAssignee().equals(userId.toString())).collect(Collectors.toList());
            if(userProcList!=null && userProcList.size()>0){
                item.setBpmStatus(6);
                ProcessAssignee processAssignee = userProcList.get(0);
                item.setTaskId(processAssignee.getTaskId().toString());
                item.setAssingee(Long.valueOf(processAssignee.getAssignee()));
                item.setAssigneeName(processAssignee.getAssigneeName());
                item.setReadStatus(processAssignee.getReadStatus());
                item.setName(processAssignee.getNodeName());
                if(processAssignee.getReadStatus()==0){
                    item.setReadStatusStr("未处理");
                }else{
                    item.setReadStatusStr("正在处理");
                }
            }else{
                //在办多个处理人增加合并操作
                DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                List<ProcessAssignee> processAssigneeListNew = finalProcessAssigneeList.stream().filter(q->q.getProcessInstanceId().equals(item.getProcessInstanceId()))
                        .sorted(Comparator.comparing(ProcessAssignee::getCreateTime)).collect(Collectors.toList());
                if (processAssigneeListNew!=null && processAssigneeListNew.size()==1){
                    ProcessAssignee processAssignee = processAssigneeListNew.get(0);
                    item.setTaskId(processAssignee.getTaskId().toString());
                    item.setAssingee(Long.valueOf(processAssignee.getAssignee()));
                    item.setAssigneeName(processAssignee.getAssigneeName());
                    item.setReadStatus(processAssignee.getReadStatus());
                    item.setName(processAssignee.getNodeName());
                    if(processAssignee.getReadStatus()==0){
                        item.setReadStatusStr("未处理");
                    }else{
                        item.setReadStatusStr("正在处理");
                    }

                    String createTime = sdf.format(processAssignee.getCreateTime());
                    item.setHandleTime(LocalDateTime.parse(createTime, df));
                }else if(processAssigneeListNew!=null && processAssigneeListNew.size()>1){
                    String assigneeName = processAssigneeListNew.stream().map(ProcessAssignee::getAssigneeName).collect(Collectors.joining(","));
                    item.setAssigneeName(assigneeName);
                    List<String> readStatusStr = new ArrayList<>();
                    processAssigneeListNew.forEach(i->{
                        if(i.getReadStatus()==0){
                            readStatusStr.add("未处理");
                        }else{
                            readStatusStr.add("正在处理");
                        }
                    });
                    item.setReadStatusStr(StringUtils.join(readStatusStr,","));
                    item.setName(processAssigneeListNew.get(0).getNodeName());
                    String createTime = sdf.format(processAssigneeListNew.get(0).getCreateTime());
                    item.setHandleTime(LocalDateTime.parse(createTime, df));
                    List<String> userIdList = processAssigneeListNew.stream().map(ProcessAssignee::getAssignee).collect(Collectors.toList());
                    item.setAssingeeList(userIdList);
                }
            }

        });
        return new PageResult(taskVOIPage.getRecords(), taskVOIPage.getTotal(), taskVOIPage.getCurrent());
    }

    @Deprecated
    @Override
    public PageResult<TaskVO> getUserApproveProcessList(int pageNumber, int pageSize, TaskQueryParam taskQueryParam) {
        IPage iPage = new Page(pageNumber, pageSize);
        IPage<TaskVO> taskVOIPage = cscpProcBaseService.getUserEndProcessList(iPage, taskQueryParam);
        Long userId = SecurityUtils.getCurrentUserId();
        taskVOIPage.getRecords().forEach(item->{
            if(taskQueryParam.getBpmStatus() == BpmStatusConstants.PROCESS_END){
                item.setName("结束");
            }
            item.setIsHandleBtn(0);
            if(userId.longValue() == item.getCreateBy().longValue()){
                item.setIsHandleBtn(1);
            }
//            CscpAuditContent cscpAuditContent = cscpAuditContentService.getAuditContentByInstanceId(item.getProcessInstanceId());
//            if(cscpAuditContent!=null){
//                item.setHandleTime(cscpAuditContent.getCreateTime());
//            }
        });
        return new PageResult(taskVOIPage.getRecords(), taskVOIPage.getTotal(), taskVOIPage.getTotal());
    }

    /**
     * 已办件动态
     *
     * @param pageNumber           当前页码
     * @param pageSize             每页长度
     * @return
     */
    @Override
    public PageResult<TaskContentVO> queryDoneApprovalList(TaskQueryParam taskQueryParam, int pageNumber, int pageSize){
        IPage iPage = new Page(pageNumber, pageSize);
        IPage<TaskContentVO> taskVOIPage = new Page<TaskContentVO>();
        List<String> procInstIdList = cscpAuditContentService.getProcInstIdByUsrId(SecurityUtils.getCurrentUserId());
        if (CollectionUtil.isNotEmpty(procInstIdList)){
            taskQueryParam.setProcessDefinitionKeyIn(procInstIdList);
            taskVOIPage = cscpProcBaseService.queryDoneApprovalList(iPage, taskQueryParam);
            return new PageResult(taskVOIPage.getRecords(), taskVOIPage.getTotal(), taskVOIPage.getCurrent());
        }else{
            return new PageResult(taskVOIPage.getRecords(), 0, 0);
        }

    }

    @Deprecated
    @Override
    public PageResult<TaskVO> getLiaisonUserEndProcessList(int pageNumber, int pageSize, TaskQueryParam taskQueryParam) {
        IPage iPage = new Page(pageNumber, pageSize);
        IPage<TaskVO> taskVOIPage = cscpProcBaseService.getLiaisonUserEndProcessList(iPage, taskQueryParam);
        /*taskVOIPage.getRecords().forEach(item->{
            if(taskQueryParam.getBpmStatus() == BpmStatusConstants.PROCESS_END){
                item.setName("结束");
            }
        });*/
        return new PageResult(taskVOIPage.getRecords(), taskVOIPage.getTotal(), taskVOIPage.getTotal());
    }

    @Deprecated
    @Override
    public PageResult<TaskVO> getOneClickFinishProcessList(int pageNumber, int pageSize, TaskQueryParam taskQueryParam) {
        IPage iPage = new Page(pageNumber, pageSize);
        IPage<TaskVO> taskVOIPage = cscpProcBaseService.getOneClickFinishProcessList(iPage, taskQueryParam);
        taskVOIPage.getRecords().forEach(item->{
            if(taskQueryParam.getBpmStatus() == BpmStatusConstants.PROCESS_END){
                item.setName("结束");
            }
        });
        return new PageResult(taskVOIPage.getRecords(), taskVOIPage.getTotal(), taskVOIPage.getTotal());
    }


    @Override
    public CscpProc getByProcessInstanceId(String processInstanceId) {
        LambdaQueryWrapper<CscpProcBase> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(CscpProcBase::getProcInstId, processInstanceId).select(CscpProcBase::getProcDefId);

        CscpProcBase cscpProcBase = cscpProcBaseService.selectOneOnlyAddTenantId(lambdaQueryWrapper);

        LambdaQueryWrapper<CscpProc> lambdaQueryWrapperCscpProc = new LambdaQueryWrapper<>();
        lambdaQueryWrapperCscpProc.eq(CscpProc::getProcessDefinitionId, cscpProcBase.getProcDefId()).last(SysConstant.LIMIT_ONE);
        // 查询
        return cscpActivitiService.selectOneNoAdd(lambdaQueryWrapperCscpProc);
    }

    @Override
    public List<TaskData> getProcessInstanceDiagram(String processInstanceId) {
        List<TaskData> nodes = new ArrayList<>();

        List<CscpProcTaskExtend> cscpProcTaskExtendList = cscpProcTaskExtendService.getTaskExtendByProInstanceIdList(processInstanceId);
        Optional.ofNullable(cscpProcTaskExtendList).ifPresent(acts -> acts.forEach(l -> nodes.add(TaskData.newInstance(l))));
        // 加入连线
        if (cscpProcTaskExtendList != null && cscpProcTaskExtendList.size() > 0) {
            String processDefinitionId = cscpProcTaskExtendList.get(0).getProcdefId();
            // 获取所有环节、连线
            ExecutionQuery executionQuery = runtimeService.createExecutionQuery().processInstanceId(processInstanceId);
            if (executionQuery != null) {
                List<Execution> list = executionQuery.list();
                if (CollectionUtils.isNotEmpty(list)) {
                    String executionId = list.get(0).getId();
                    Set<String> hisLineKeys = (Set<String>) runtimeService.getVariable(executionId, BpmnVariables.HIS_LINE_KEY_.name());
                    Optional.ofNullable(hisLineKeys).ifPresent(ls -> ls.forEach(key -> {
                        BpmnLine l = activitiBpmnDefinitionService.getBpmnLineByLineKey(processDefinitionId, key);
                        if (l != null) {
                            TaskData node = new TaskData();
                            node.setName(l.getName());
                            node.setKey(l.getLineKey());
                            node.setElementType(BpmnElements.SequenceFlow.name());
                            nodes.add(node);
                        }
                    }));
                }
            }

        }
        return nodes;
    }


    /**
     * 判断是否数字
     *
     * @return
     */
    private static boolean isNumeric(Object obj) {
        if (obj instanceof Number) {
            return true;
        }

        String s = String.valueOf(obj);
        Pattern pattern = Pattern.compile("^[-\\+]?\\d+[.]?[\\d]*$");
        return pattern.matcher(s).matches();
    }

    @Override
    public List<PageData> getTaskByProcess(String processInstanceId, String taskId, String nodeKey) {
        List<PageData> list = new ArrayList<>();
        List<PageData> finish = new ArrayList<>();
        List<PageData> unfinish = new ArrayList<>();
        HistoricTaskInstanceQuery historicTaskInstanceQuery = historyService.createHistoricTaskInstanceQuery().processInstanceId(processInstanceId).taskDefinitionKey(nodeKey);
        List<HistoricTaskInstance> historicTaskInstanceList = historicTaskInstanceQuery.list();
        for (HistoricTaskInstance h : historicTaskInstanceList) {
            if ("deleteMultiInstance".equals(h.getDeleteReason())) {
                continue;
            }
            PageData temp = new PageData();
            String dealUserId = h.getAssignee();
            temp.put("taskId", h.getId());
            if (h.getEndTime() == null) {
                temp.put("status", "unfinish");
            } else {
                temp.put("status", "finish");
            }
            if (StringUtils.isNotEmpty(dealUserId)) {
                CscpUserDTO CscpUserDTO = cscpUserService.findByUserId(Long.valueOf(dealUserId));
                temp.put("userName", CscpUserDTO.getRealName());
                temp.put("userId", dealUserId);
            }


            if (h.getId().equals(taskId)) {
                temp.put("disable", 1);
                list.add(temp);
            } else {
                if (h.getEndTime() == null) {
                    temp.put("disable", 0);
                    unfinish.add(temp);
                } else {
                    temp.put("disable", 1);
                    finish.add(temp);
                }
            }
        }
        if (list.size() == 0 && finish.size() == 0 && unfinish.size() > 0) {
            PageData pageData = unfinish.get(0);
            pageData.put("disable", 1);
            unfinish.add(0, pageData);
        }
        list.addAll(finish);
        list.addAll(unfinish);
        return list;
    }

    @Override
    public List<ProcessHandlerDTO> getProcessHandler(String processInstanceId, String nodeKey, int type) {
        BlockingQueue<ProcessHandlerDTO> processHandlerQueue = new LinkedBlockingQueue<>();
        List<ProcessHandlerDTO> list = new ArrayList<>();
        // type=0，查加签，显示全部办理人员。type=1，查减签，只显示为阅读的人员
        List<ProcessAssignee> assigneeList = processAssigneeService.getProcessAssigneeListByProcInstId(processInstanceId, null);
        if(type == 0){
            assigneeList.forEach(item->{
                ProcessHandlerDTO dto = new ProcessHandlerDTO();
                dto.setUserId(item.getAssignee());
                dto.setUserName(item.getAssigneeName());
                if(item.getReadStatus() == 1){
                    dto.setStatus("处理中");
                }else{
                    dto.setStatus("未处理");
                }
//                list.add(dto);
                processHandlerQueue.add(dto);
            });

            processHandlerQueue.forEach(item->{
                list.add(item);
            });

//            for (ProcessAssignee item : assigneeList) {
//                ProcessHandlerDTO dto = new ProcessHandlerDTO();
//                dto.setUserId(item.getAssignee());
//                dto.setUserName(item.getAssigneeName());
//                if (item.getReadStatus() == 1) {
//                    dto.setStatus("处理中");
//                } else {
//                    dto.setStatus("未处理");
//                }
//                list.add(dto);
//            }
        }else{
            // 减签人员列表查询
            assigneeList.forEach(item->{
                ProcessHandlerDTO dto = new ProcessHandlerDTO();
                dto.setUserId(item.getAssignee());
                dto.setUserName(item.getAssigneeName());
                dto.setTaskId(item.getTaskId().toString());
                if(item.getReadStatus() == 0){
                    dto.setStatus("未处理");
//                    list.add(dto);
                }else {
                    dto.setStatus("处理中");
                }
                processHandlerQueue.add(dto);
            });

            processHandlerQueue.forEach(item->{
                list.add(item);
            });

//            for (ProcessAssignee item : assigneeList) {
//                ProcessHandlerDTO dto = new ProcessHandlerDTO();
//                dto.setUserId(item.getAssignee());
//                dto.setUserName(item.getAssigneeName());
//                dto.setTaskId(item.getTaskId().toString());
//                if (item.getReadStatus() == 0) {
//                    dto.setStatus("未处理");
//                    list.add(dto);
//                }
//            }
        }

        // 查询已处理的人员
        LambdaQueryWrapper<CscpAuditContent> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(CscpAuditContent::getProcInstId, processInstanceId)
                .eq(CscpAuditContent::getActId,nodeKey)
                .eq(CscpAuditContent::getAuditStatus,String.valueOf(ActStatusEnum.NOMER.getCode())).orderByDesc(CscpAuditContent::getCreateTime);
        List<CscpAuditContent> cscpAuditContentList = cscpAuditContentService.selectListNoAdd(lambdaQueryWrapper);
        cscpAuditContentList.forEach(item->{
            ProcessHandlerDTO dto = new ProcessHandlerDTO();
            List<ProcessHandlerDTO> userList = list.stream().filter(q->q.getUserId().equals(item.getAuditorId())).collect(Collectors.toList());
            if(userList==null || userList.size()==0){
                // 流程节点唯一，通过时间判断是否需要显示该人是否在当前节点
                if (CollUtil.isEmpty(assigneeList) || item.getCreateTime().isAfter(assigneeList.get(0).getCreateTime())) {
                    dto.setUserId(item.getAuditorId());
                    dto.setUserName(item.getAuditorName());
                    dto.setStatus("已处理");
                    list.add(dto);
                }
            }
        });

        return list;
    }

    @Override
    public List<ExecutionLine> getOutLineListByProcessDefinitionKey(String processDefinitionKey) {
        CscpProc cscpProc = activitiBpmnDefinitionService.getBpmnModelMaxVersionByProcessDefinitionKey(processDefinitionKey);

        BpmnNode bpmnNodeFirst = activitiBpmnDefinitionService.getFirstNode(cscpProc.getProcessDefinitionId());
        List<ExecutionLine> list = getOutLineListFirstNode(cscpProc.getProcessDefinitionId(), bpmnNodeFirst.getNodeKey());
        Set<String> sameNameSet = new HashSet<>();
        List<ExecutionLine> realList = new ArrayList<>();
        int size = list.size();
        for (ExecutionLine i : list) {
            BpmnNode bpmnNode = this.getBpmnNodeByNodeKey(i.getProcessDefinitionId(), i.getToKey());
            i.setFilterRule(bpmnNode.getFilterRule());
            i.setTaskType(bpmnNode.getType());
            i.setIsMultiInstance(bpmnNode.getMultiInstance());
            i.setSequenceNo(bpmnNode.getSequenceNo());
            if (sameNameSet.add(i.getToKey())) {
                realList.add(i);
            }
        }
        realList = realList.stream().map(i -> {
            if (Objects.isNull(i.getSequenceNo())) {
                i.setSequenceNo(0);
            }
            return i;
        }).sorted(Comparator.comparing(ExecutionLine::getSequenceNo)).collect(Collectors.toList());
        return realList;
    }


    public List<ExecutionLine> getOutLineListFirstNode(String processDefinitionId, String processNodeKey) {
        // 先判断是否配置了条件表达式
        List<ExecutionLine> outLineListExpression = new ArrayList<ExecutionLine>();
        PageData pd = new PageData();
        pd.put("PROCDEFID", processDefinitionId);
        pd.put("CURRENTNODEID", processNodeKey);

        if (outLineListExpression.size() > 0) {
            return outLineListExpression;
        }
        // 获取流程环节可用的出口路线
        List<ExecutionLine> outLineList = getOutLineList(processDefinitionId, processNodeKey);

        // 根据当前环节，

        // 当前环节
        BpmnNode currentNode = this.getBpmnNodeByNodeKey(processDefinitionId, processNodeKey);

        if (BpmnElements.isCallActivity(currentNode.getType())) {
            return outLineList;
        }
        return outLineList;

    }

    /***
     * 通过流程实例ID，获取当前任务名称
     * @param processInstanceId
     * @return
     */
    @Override
    public String getTaskNameByProcessInstanceId(String processInstanceId) {
        //根据流程实例 ID 获取当前活动任务
        Task  task = taskService.createTaskQuery().processInstanceId(processInstanceId).active().singleResult();
        String taskName = "";
        if(null!=task){
            taskName = task.getName();
        }
        return taskName;
    }

    @Override
    public String getApproveManagementCode(String receiveType) {
        return cscpProcBaseService.getApproveManagementCode(receiveType);
    }

    @Override
    public QueryApprovalDTO getApproveInnerCode(QueryApproveManagementCodeVO vo) {
        return cscpProcBaseService.getApproveInnerCode(vo);
    }

    @Override
    public QueryApprovalDTO getApproveManagementMaxCode(QueryApproveManagementCodeVO vo) {

        // // 文号不足四位补0 例如 0001
        // String approvalCode = vo.getApprovalCode();
        // if(StrUtil.isNotEmpty(approvalCode)){
        //     int length = approvalCode.length();
        //     String wh="0000";
        //     if(length < 4){
        //         approvalCode = wh.substring(length) + approvalCode;
        //         vo.setApprovalCode(approvalCode);
        //     }
        // }

        return cscpProcBaseService.getApproveManagementMaxCode(vo);
    }

    @Override
    public List<String> checkOfficialDocumentNumber(QueryApproveManagementCodeVO vo) {
        return  cscpProcBaseService.checkOfficialDocumentNumber(vo);
    }

    @Override
    public QueryProcessSignDTO getVerifySign(String processDefinitionId, String processInstanceId) {
        QueryProcessSignDTO dto = new QueryProcessSignDTO();
        dto.setShowSign(0);
        if (StringUtils.isEmpty(processInstanceId)){
            return dto;
        }

        List<ProcessAssignee> processAssigneeList = processAssigneeService.getProcessAssigneeListByProcInstId(processInstanceId,0);
        LambdaQueryWrapper<CscpAuditContent> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(CscpAuditContent::getProcInstId, processInstanceId)
                .eq(CscpAuditContent::getActId,processAssigneeList.get(0).getNodeKey())
                .eq(CscpAuditContent::getAuditStatus,String.valueOf(ActStatusEnum.NOMER.getCode()))
                .gt(CscpAuditContent::getCreateTime,processAssigneeList.get(0).getCreateTime());
        List<CscpAuditContent> cscpAuditContentList = cscpAuditContentService.selectListNoAdd(lambdaQueryWrapper);
        if (CollectionUtils.isEmpty(processAssigneeList)|| processAssigneeList.size()+cscpAuditContentList.size() == 1){
            return dto;
        }else if(ObjectUtil.equal(processAssigneeList.get(0).getCreateBy(), SecurityUtils.getCurrentUserId())){
            for (ProcessAssignee processAssignee : processAssigneeList) {
                if (processAssignee.getReadStatus() != 1) {
                    dto.setShowSign(1);
                    break;
                }
            }
            if (dto.getShowSign()==0){
                return dto;
            }
        }
        BpmnNode bpmnNode = activitiBpmnDefinitionService.getBpmnNodeByNodeKey(processDefinitionId, processAssigneeList.get(0).getNodeKey());
        // 1：显示减签按钮，0：不显示按钮
        dto.setShowSign(ObjectUtil.equal(1, bpmnNode.getMultiInstance()) ? 1:0);
        dto.setNodeKey(processAssigneeList.get(0).getNodeKey());
        return dto;
    }

    @Override
    public boolean createOrUpdateGiveAdvice(BizApprovalGiveAdviceRecordDTO dto) {
        // 查询参谋意见
        BizApprovalGiveAdviceRecord approvalGiveAdviceRecord = approvalGiveAdviceRecordMapper.selectOneNoAdd(Wrappers.<BizApprovalGiveAdviceRecord>lambdaQuery()
                .eq(BizApprovalGiveAdviceRecord::getFormDataId, dto.getFormDataId())
                .eq(BizApprovalGiveAdviceRecord::getLeaderId, dto.getLeaderId())
                .eq(BizApprovalGiveAdviceRecord::getCreateBy, SecurityUtils.getCurrentUserId()));
        if (approvalGiveAdviceRecord == null) {
            BizApprovalGiveAdviceRecord bizApprovalGiveAdviceRecord = new BizApprovalGiveAdviceRecord();
            BeanUtils.copyProperties(dto, bizApprovalGiveAdviceRecord);
            bizApprovalGiveAdviceRecord.setAdviceTime(LocalDateTime.now());
            approvalGiveAdviceRecordMapper.insert(bizApprovalGiveAdviceRecord);
        } else {
            if (!Objects.equals(dto.getGiveAdvice(), approvalGiveAdviceRecord.getGiveAdvice())) {
                approvalGiveAdviceRecordMapper.update(null, Wrappers.<BizApprovalGiveAdviceRecord>lambdaUpdate()
                        .set(BizApprovalGiveAdviceRecord::getGiveAdvice, dto.getGiveAdvice())
                        .set(BizApprovalGiveAdviceRecord::getAdviceTime, LocalDateTime.now())
                        .eq(BizApprovalGiveAdviceRecord::getFormDataId, dto.getFormDataId())
                        .eq(BizApprovalGiveAdviceRecord::getLeaderId, dto.getLeaderId())
                        .eq(BizApprovalGiveAdviceRecord::getCreateBy, SecurityUtils.getCurrentUserId()));
            }
        }
        return Boolean.TRUE;
    }

    @Override
    public boolean deleteGiveAdvice(BizApprovalGiveAdviceRecordDTO dto) {
        return approvalGiveAdviceRecordMapper.delete(Wrappers.<BizApprovalGiveAdviceRecord>lambdaQuery()
                .eq(BizApprovalGiveAdviceRecord::getFormDataId, dto.getFormDataId())
                .eq(BizApprovalGiveAdviceRecord::getLeaderId, dto.getLeaderId())
                .eq(BizApprovalGiveAdviceRecord::getCreateBy, SecurityUtils.getCurrentUserId())) > 0;
    }

    @Override
    public BizApprovalGiveAdviceRecordDTO queryGiveAdvice(BizApprovalGiveAdviceRecordDTO dto) {
        BizApprovalGiveAdviceRecord bizApprovalGiveAdviceRecord = approvalGiveAdviceRecordMapper.selectOneNoAdd(Wrappers.<BizApprovalGiveAdviceRecord>lambdaQuery()
                .eq(BizApprovalGiveAdviceRecord::getFormDataId, dto.getFormDataId())
                .eq(BizApprovalGiveAdviceRecord::getLeaderId, dto.getLeaderId())
                .eq(BizApprovalGiveAdviceRecord::getCreateBy, SecurityUtils.getCurrentUserId()));
        BizApprovalGiveAdviceRecordDTO approvalGiveAdviceRecordDTO = new BizApprovalGiveAdviceRecordDTO();
        BeanUtils.copyProperties(bizApprovalGiveAdviceRecord, approvalGiveAdviceRecordDTO);
        return approvalGiveAdviceRecordDTO;
    }

    @Override
    public List<BizApprovalGiveAdviceRecordDTO> queryGiveAdviceListByLeaderIdAndDataId(String formDataId) {
        List<BizApprovalGiveAdviceRecord> approvalGiveAdviceRecordList = approvalGiveAdviceRecordMapper.selectListNoAdd(Wrappers.<BizApprovalGiveAdviceRecord>lambdaQuery()
                .eq(BizApprovalGiveAdviceRecord::getFormDataId, formDataId)
                .eq(BizApprovalGiveAdviceRecord::getLeaderId, SecurityUtils.getCurrentUserId())
                .orderByDesc(BizApprovalGiveAdviceRecord::getAdviceTime));
        return ListCopyUtil.copy(approvalGiveAdviceRecordList, BizApprovalGiveAdviceRecordDTO.class);
    }

    @Override
    public void syncApproveManagementCode() {
        cscpProcBaseService.syncApproveManagementCode();
    }

}
