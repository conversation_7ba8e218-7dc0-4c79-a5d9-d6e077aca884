package com.ctsi.activiti.core.listener.funcs;

import org.activiti.engine.delegate.Expression;
import org.activiti.engine.impl.context.Context;
import org.activiti.engine.impl.el.ExpressionManager;


import java.util.Map;

public class JuelExpressonCommand{

    /**
     * 取消沟通
     */
    private String el;

    /**
     * 流程变量
     */
    private Map<String, Object> formData;

    public JuelExpressonCommand(String el, Map<String, Object> formData) {
        super();
        this.el = el;
        this.formData = formData;
    }

    public Object execute() {
        ExpressionManager expressionManager = Context.getProcessEngineConfiguration().getExpressionManager();
        Expression e = expressionManager.createExpression(el);
        MyVariableScope variableScope = new MyVariableScope(formData);
        return e.getValue(variableScope);
    }

}
