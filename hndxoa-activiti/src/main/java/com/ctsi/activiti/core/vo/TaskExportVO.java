package com.ctsi.activiti.core.vo;


import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;


/**
 * 新建的任务对象
 */
@Data
@ApiModel("领导批示导出信息")
public class TaskExportVO implements Serializable {


    @ExcelProperty("领导批示编码")
    private String leaderStampInfo;

    @ExcelProperty("流程标题")
    String title;

    @ExcelProperty("流程拟稿人")
    String createName;

    @ExcelProperty("来文单位")
    String departmentName;

    @ExcelProperty("批示内容")
    String content;

    // @ExcelProperty("流程创建时间")
    // LocalDateTime processCreatTime;

    @ExcelProperty("流程处理时间")
    LocalDateTime handleTime;
}
