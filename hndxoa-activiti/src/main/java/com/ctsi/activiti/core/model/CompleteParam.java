package com.ctsi.activiti.core.model;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * 流程任务提交参数
 *PANJL
 * @author:
 * @date: 2019-05-13 09:20
 */
@ApiModel("提交流程的参数")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CompleteParam implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * [必填] 任务ID
     */
    @NotEmpty
    @ApiModelProperty(value = "任务id",required = true)
    private String taskId;

    /**
     * [非必填]出口路线KEY<br>
     * 如果为null，会在所有出口路线中寻找表达式符合的路线，<br>
     * 如果提交的任务是多实例任务，该属性必须有值，
     * 一般情况下，多实例任务只有 同意（lineKey = 1）、拒绝（lineKey = 0），如果不想调用上面的方法，可以手动传值
     */
    @ApiModelProperty(value = "出口路径key")
    private String lineKey;

    /**
     * [非必填]子流程出口路线KEY<br>
     * 非子流程结束前任务可为null<br>
     */
    @ApiModelProperty(value = "子流程出口路线KEY，非子流程结束前任务可为null")
    private String callActivityOutLineKey;

    /**
     * 任务批注
     */
    @ApiModelProperty(value = "任务批注意见")
    private String comment;

    @ApiModelProperty(value = "任务非处理单意见")
    private String processComment;

    /**
     * 下一环节处理人集合，如果没有指定，将会从流程设置时设置的候选人中抽取用户随机发送，如果没有候选人，抛出异常<br>
     * 普通任务随机抽取一人，多实例任务会发送给所有候选人
     */
    @ApiModelProperty(value = "下一环节处理人集合")
    private List<String> nextHandlers;

    /**
     * 携带的表达式参数信息
     */
    private Map<String, Object> expressionMap;

    /**
     * 持久化信息
     */
    private Map<String, Object> persistentMap;

    /**
     * 签收人（当前登录人）
     */
    private String claimer;

    @ApiModelProperty(value = "对应的显示的签批位置的表单项目的id")
    private String commentsFormId;


    private String formMainName;

    private String smsContent;

    private boolean isNeedForm=true;

    //是否发送短信1 表示发送短信
    @ApiModelProperty(value = "是否发送短信")
    private int hasSms;


    @ApiModelProperty(value = "签批意见的时候签批的文件，传过来是base64编码，转为图片后存文件的路径")
    private String signImage;


    @ApiModelProperty(value = "处理单签批的的文件，传过来是base64编码，转为图片后存文件的路径")
    private String signature;

    /**
     * 签名图片url
     */
    @ApiModelProperty(value = "签名图片url")
    @TableField(value = "signature_image_url")
    private String signatureImageURL;

    @ApiModelProperty("签批类型  0-文本签批 1-手写签批 2-手写和文字同时签批")
    private Integer signType;

    @ApiModelProperty("手写签批X坐标")
    private Integer coordinatex;

    @ApiModelProperty("手写签批Y坐标")
    private Integer coordinatey;

    @ApiModelProperty("手写签批宽度")
    private String width;

    @ApiModelProperty("手写签批高度")
    private String high;

}
