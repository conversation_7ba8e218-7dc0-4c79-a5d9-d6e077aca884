package com.ctsi.activiti.ext.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ctsi.activiti.bpmn.entity.BpmnNode;
import com.ctsi.activiti.bpmn.service.ActivitiBpmnDefinitionService;
import com.ctsi.activiti.ext.entity.ProcessAssignee;
import com.ctsi.activiti.ext.repository.ProcessAssigneeRepository;
import com.ctsi.activiti.ext.service.ProcessAssigneeService;
import com.ctsi.business.domain.CscpProcBase;
import com.ctsi.business.service.CscpProcBaseService;
import com.ctsi.common.utils.ActivitiUtils;
import com.ctsi.hndx.common.SysBaseServiceImpl;
import com.ctsi.hndx.utils.StringUtils;
import com.ctsi.ssdc.admin.domain.CscpUser;
import com.ctsi.ssdc.admin.service.CscpUserService;
import com.ctsi.ssdc.security.SecurityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;

/**
 * @author: PANJL
 * @date: 2019-05-13 17:39
 */
@Service
public class ProcessAssigneeServiceImpl extends SysBaseServiceImpl<ProcessAssigneeRepository,ProcessAssignee>
	implements ProcessAssigneeService {

    private static final Logger LOGGER = LoggerFactory.getLogger(ProcessAssigneeServiceImpl.class);



    @Autowired
    private ActivitiBpmnDefinitionService activitiBpmnDefinitionService;

    @Autowired
    private ProcessAssigneeRepository processAssigneeRepository;

    @Autowired
    private CscpUserService cscpUserService;

    @Autowired
    private CscpProcBaseService cscpProcBaseService;
    @Override
    @Transactional
    public void generate(String processInstanceId, String processDefinitionId,
                         String assignee, BpmnNode bpmnNode,String taskId) {
        if (ActivitiUtils.isEmpty(processInstanceId)) {
            throw new IllegalArgumentException("流程实例不能为空");
        }
        if (ActivitiUtils.isEmpty(assignee)) {
            throw new IllegalArgumentException("受理人不能为空");
        }

        CscpUser cscpUser = cscpUserService.getById(assignee);
        String realName = cscpUser.getRealName();

        //            //异步调用短信接口发送短信
//        CompleteParam completeParam = ActivitiCoreService.COMPLETE_PARAM.get();
//        if (completeParam != null){
//            int hasSms = completeParam.getHasSms();
//            if(SysConstant.SMS == hasSms){
////                SmsSendUtil.sendSms(cscpUser.getMobile(),completeParam.getFormMainName(), SmsSendEnum.PROCESS_SMS);
//                SmsSendUtil.sendSms(cscpUser.getMobile(), completeParam.getSmsContent(), null);
//            }
//        }

        LambdaQueryWrapper<ProcessAssignee> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(ProcessAssignee::getProcessInstanceId,processInstanceId)
                .eq(ProcessAssignee::getAssignee,assignee);

        ProcessAssignee processAssignee = processAssigneeRepository.selectOne(lambdaQueryWrapper);
        if (processAssignee != null ) {
            processAssignee.setNodeKey(bpmnNode.getNodeKey());
            processAssignee.setNodeName(bpmnNode.getName());
            processAssignee.setAssignee(assignee);
            processAssignee.setAssigneeName(realName);
            processAssignee.setUpdateBy(SecurityUtils.getCurrentUserId());
//            processAssignee.setCreateTime(new Date());
            if (StringUtils.isNotEmpty(taskId)){
                processAssignee.setTaskId(Long.valueOf(taskId));
            }

            processAssigneeRepository.updateById(processAssignee);
        } else {

            LOGGER.debug("    生成流程受理人关系：{}-{}", processInstanceId, assignee);
            ProcessAssignee processAssigneeNew = new ProcessAssignee();
            processAssigneeNew.setProcessInstanceId(processInstanceId);
            processAssigneeNew.setProcessDefinitionId(processDefinitionId);
            processAssigneeNew.setProcessDefinitionKey(bpmnNode.getProcessDefinitionKey());
            processAssigneeNew.setAssignee(assignee);
            processAssigneeNew.setNodeKey(bpmnNode.getNodeKey());
            processAssigneeNew.setNodeName(bpmnNode.getName());
            processAssigneeNew.setAssigneeName(realName);
            processAssigneeNew.setCreateTime(LocalDateTime.now());
            processAssigneeNew.setCreateBy(SecurityUtils.getCurrentUserId());
            if (StringUtils.isNotEmpty(taskId)){
                processAssigneeNew.setTaskId(Long.valueOf(taskId));
            }
            processAssigneeRepository.insert(processAssigneeNew);
        }
    }

    @Override
    @Transactional
    public void ended(String processInstanceId) {
        LambdaQueryWrapper<ProcessAssignee> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(ProcessAssignee::getProcessInstanceId,processInstanceId);

        //流程结束了将数据删除，老版本流程结束了只是改变了流程的状态位
        processAssigneeRepository.delete(lambdaQueryWrapper);

       /* ProcessAssignee processAssignee = processAssigneeRepository.selectOne(lambdaQueryWrapper);
        if (processAssignee != null) {
            processAssignee.setBpmStatus(BpmStatusConstants.PROCESS_END);
            processAssigneeRepository.updateById(processAssignee);
        }*/
    }

    @Override
    public void removeCompleteNode(String processInstanceId, String assignee, String nodeKey) {
        LambdaQueryWrapper<ProcessAssignee> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(ProcessAssignee::getProcessInstanceId,processInstanceId)
                .eq(ProcessAssignee::getNodeKey,nodeKey)
                .eq(ProcessAssignee::getAssignee,assignee);
        processAssigneeRepository.delete(lambdaQueryWrapper);
    }


    @Override
    public List<ProcessAssignee> getCurrentNodeByProcessInstanceId(String processInstanceId){

        List<ProcessAssignee> processAssigneeList = new ArrayList<>();

        // 获取该流程的子流程列表
        LambdaQueryWrapper<CscpProcBase> lambdaQueryWrapper1 = new LambdaQueryWrapper<>();
        lambdaQueryWrapper1.eq(CscpProcBase::getRootProcInstId, processInstanceId);
        List<CscpProcBase> list  = cscpProcBaseService.selectListNoAdd(lambdaQueryWrapper1);
        Set<Long> set = new HashSet<>();
        list.forEach(cscpProcBase -> {
            if (set.add(Long.valueOf(cscpProcBase.getProcInstId()))){
                LambdaQueryWrapper<ProcessAssignee> lambdaQueryWrapper = new LambdaQueryWrapper<>();
                lambdaQueryWrapper.eq(ProcessAssignee::getProcessInstanceId,cscpProcBase.getProcInstId());
                processAssigneeList.addAll(processAssigneeRepository.selectList(lambdaQueryWrapper));
            }

        });

        return processAssigneeList;
    }

    @Override
    public ProcessAssignee getProcessAssigneeByProcInstId(String processInstanceId){
        LambdaQueryWrapper<ProcessAssignee> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(ProcessAssignee::getProcessInstanceId,processInstanceId);
        lambdaQueryWrapper.orderByDesc(ProcessAssignee::getCreateTime).last("limit 1");
        return processAssigneeRepository.selectOneNoAdd(lambdaQueryWrapper);
    }
    @Override
    public List<ProcessAssignee> getProcessAssigneeListByProcInstId(String processInstanceId,Integer readStatus ){
        LambdaQueryWrapper<ProcessAssignee> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(ProcessAssignee::getProcessInstanceId,processInstanceId);
        lambdaQueryWrapper.eq(readStatus!=null, ProcessAssignee::getReadStatus,readStatus);
        return processAssigneeRepository.selectListNoAdd(lambdaQueryWrapper);
    }
    @Override
    public List<ProcessAssignee> getProcessAssigneeListByProcInstIdList(List<String> processInstanceIdList){
        LambdaQueryWrapper<ProcessAssignee> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.in(ProcessAssignee::getProcessInstanceId,processInstanceIdList);
        return processAssigneeRepository.selectListNoAdd(lambdaQueryWrapper);
    }

    @Override
    public List<ProcessAssignee> getProcessAssigneeListByAssigneeId(Long assigneeId){
        LambdaQueryWrapper<ProcessAssignee> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(ProcessAssignee::getAssignee,assigneeId);
        return processAssigneeRepository.selectListNoAdd(lambdaQueryWrapper);
    }
    @Override
    public ProcessAssignee getProcessAssigneeByTaskId(String taskId){
        LambdaQueryWrapper<ProcessAssignee> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(ProcessAssignee::getTaskId,taskId);
        return processAssigneeRepository.selectOneNoAdd(lambdaQueryWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteByTaskIdAndProinstId(String taskId, String processInstanceId) {
        LambdaQueryWrapper<ProcessAssignee> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(ProcessAssignee::getProcessInstanceId,processInstanceId)
                           .eq(ProcessAssignee::getTaskId,taskId);
        processAssigneeRepository.delete(lambdaQueryWrapper);
    }

    @Override
    public void deleteByTaskId(String taskId) {
        LambdaQueryWrapper<ProcessAssignee> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(ProcessAssignee::getTaskId,taskId);
        processAssigneeRepository.delete(lambdaQueryWrapper);
    }

    @Override
    public void updateProcessAssignee(String processInstanceId) {
        LambdaQueryWrapper<ProcessAssignee> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(ProcessAssignee::getProcessInstanceId,processInstanceId);
        lambdaQueryWrapper.eq(ProcessAssignee::getReadStatus,0);
        lambdaQueryWrapper.eq(ProcessAssignee::getAssignee, SecurityUtils.getCurrentUserId());
        ProcessAssignee processAssignee = processAssigneeRepository.selectOneNoAdd(lambdaQueryWrapper);
        if(processAssignee!=null){
            processAssignee.setReadStatus(1);
            processAssigneeRepository.updateById(processAssignee);
        }
    }

}
