package com.ctsi.activiti.ext.service;


import com.ctsi.activiti.bpmn.entity.BpmnNode;
import com.ctsi.activiti.ext.entity.ProcessAssignee;
import com.ctsi.hndx.common.SysBaseServiceI;

import java.util.List;

/**
 * 流程实例、受理人关系服务，主要解决流程的查询问题，在ProcessAssignee中维护流程实例和受理人之间的关系。<br>
 * 在任务创建、转办、代理、委派、签收时，都会触发generate()生成关系，同时也维护了流程是否结束的标记，辅助查询。<br>
 *
 * 虽然流程查询不是原生的，但确实方便很多，activiti5没有提供这样的API，要自己写SQL，麻烦死了。<br>
 * 这张表维护的两者之间的关系，在同一流程实例中只会生成一次关系，即使在该流程中执行了多次任务，也不会再生成关系，只会
 * 关系最后操作时间。<br>
 */
public interface ProcessAssigneeService  extends SysBaseServiceI<ProcessAssignee> {

    /**
     * 生成关系，如果存在，只修改最后一次操作时间
     *
     * @param processInstanceId   流程实例ID
     * @param processDefinitionId 流程定义ID
     * @param assignee            受理人
     *
     */
    void generate(String processInstanceId, String processDefinitionId,
                  String assignee, BpmnNode bpmnNode,String taskId);


    /**
     * 设为流程结束，同时删除数据
     *
     * @param processInstanceId 流程实例ID
     */
    void ended(String processInstanceId);


    void removeCompleteNode(String processInstanceId,String assignee,String nodeKey);


    /**
     * 获取当前的最新步骤
     * @param processInstanceId
     * @return
     */
    public List<ProcessAssignee> getCurrentNodeByProcessInstanceId(String processInstanceId);

    ProcessAssignee getProcessAssigneeByProcInstId(String processInstanceId);

    List<ProcessAssignee> getProcessAssigneeListByProcInstId(String processInstanceId, Integer readStatus);

    List<ProcessAssignee> getProcessAssigneeListByProcInstIdList(List<String> processInstanceIdList);
    List<ProcessAssignee> getProcessAssigneeListByAssigneeId(Long assigneeId);

    public void deleteByTaskIdAndProinstId(String taskId,String processInstanceId);

    void deleteByTaskId(String taskId);
    /**
     * 根据流程ID获取当前的最新步骤
     * @param taskId
     * @return
     */
    ProcessAssignee getProcessAssigneeByTaskId(String taskId);

    /**
     * 修改流程的阅读状态
     * @param processInstanceId
     */
    void updateProcessAssignee(String processInstanceId);

}
