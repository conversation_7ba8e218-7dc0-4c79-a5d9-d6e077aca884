package com.ctsi.mapper;

import com.ctsi.activiti.core.vo.PageQuery;
import com.ctsi.domain.CformPlug;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface CformPlugRepository {
    void delete(String id) throws Exception;

    CformPlug queryCfromPlugById(String id) throws Exception;

    void update(CformPlug cfromPlug) throws Exception;

    void insert(CformPlug cfromPlug) throws Exception;

    List<CformPlug> queryPageCfromPlugList(PageQuery pq) throws Exception;

    List<CformPlug> queryCfromPlugsListByWhere(CformPlug cfromPlug) throws Exception;
}
