package com.ctsi.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Range;

@Data
@ApiModel(value = "表单发布时候的判断")
public class CformFormDTO {

    @ApiModelProperty(value = "主键id，新增不设置，修改或其它时设值")
    private String id;

    @ApiModelProperty(value = "1表示处理单的表单，2表示新增数据的表单")
    @Range(min = 1,max = 3)
    private Integer type;
}
