package com.ctsi.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;


/**
 * 表单字段的属性例如是否必填
 */
@Data
@ApiModel("表单字段的属性")
public class FormItems implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("字段标题'")
    private String fieldName;

    @ApiModelProperty("是否显示")
    private Boolean visible;

    @ApiModelProperty("是否只读")
    private Boolean disabled;

    @ApiModelProperty("字段名称")
    private String modelItemId;

    @ApiModelProperty("字段类型")
    private String fieldType;
    @ApiModelProperty("是否必填")
    private Boolean required;
    @ApiModelProperty("其他标志用于签批，当此字段值sign的时候,disabled为false，前端为签批显示，告诉移动端此节点需要在处理单显示" +
            "处理意见必填")
    private String opinionType;

    private String  fieldId;
}
