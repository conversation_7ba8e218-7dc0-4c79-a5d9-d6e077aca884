package com.ctsi.domain.dto;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * CformModelItemDTO对象
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-30
 */
@Data
@ApiModel(value = "CformModelItemDTO对象", description = "")
public class CformModelItemDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private String id;

    private LocalDateTime updateTime;

    private Long updateBy;

    private String updateName;

    private Long createBy;

    private String createName;

    private LocalDateTime createTime;


    @TableLogic
    private Integer deleted;

    /**
     * 模型表的字段名
     */
    @ApiModelProperty(value = "模型表的字段名")
    private String modelItemId;

    /**
     * 模型表的字段名称
     */
    @ApiModelProperty(value = "模型表的字段名称")
    private String modelItemName;


    /**
     * 模型表的字段名称
     */
//    @ApiModelProperty(value = "模型表的字段驼峰名称")
    private String modelItemCamelName;

    /**
     * 是否外键 1表示外键
     */
    @ApiModelProperty(value = "是否外键 1表示外键")
    private String isFk;

    /**
     * 类型
     */
    @ApiModelProperty(value = "类型")
    private String modelItemType;

    /**
     * 模型发布状态
     */
    @ApiModelProperty(value = "模型发布状态")
    private String publishFlag;

    /**
     * 默认值
     */
    @ApiModelProperty(value = "默认值")
    private String defaultVal;

    /**
     * 妯″瀷id，与模表关联
     */
    @ApiModelProperty(value = "妯″瀷id，与模表关联")
    private String modelId;

    /**
     * 模型数据库表名字段长度
     */
    @ApiModelProperty(value = "模型数据库表名字段长度")
    private String modelItemLen;

    /**
     * 是否主键 1表示主键
     */
    @ApiModelProperty(value = "是否主键 1表示主键")
    private String isPk;

    /**
     * 是否默认字段：1表示是，0表示否
     */
    @ApiModelProperty(value = "是否默认字段：1表示是，0表示否")
    private String defaultField;

    /**
     * 是否主键 1表示主键
     */
    @ApiModelProperty(value = "是否为空：1表示为空，0表示不为空")
    private String IsNull;

}
