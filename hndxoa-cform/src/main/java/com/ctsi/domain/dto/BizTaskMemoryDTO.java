package com.ctsi.domain.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 *
 *
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BizTaskMemoryDTO {

    private Long id;

    // 例如 CformModel  model_Data_Type = task_supervision
    @JsonIgnore
	@ApiModelProperty(value = "业务类别")
    private String businessType;
    //业务id
    private String formId;


    /**
     * 任务概叙
     */
    @JsonIgnore    @ApiModelProperty(value = "字段名称")
    private String fieldKey;

    @JsonIgnore    @ApiModelProperty(value = "取多少条记录默认10条")
    private Integer limitValue = 10;



}
