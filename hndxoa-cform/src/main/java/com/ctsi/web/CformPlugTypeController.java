package com.ctsi.web;

import java.net.URISyntaxException;
import java.lang.String;
import java.util.List;

import com.ctsi.common.bo.PageForm;
import com.ctsi.common.core.bo.ResponseBO;
import com.ctsi.common.util.SysErrEnum;
import com.ctsi.hndx.activitcform.PageCform;
import com.ctsi.hndx.enums.DBOperation;
import com.ctsi.ssdc.annotation.OperationLog;
import io.swagger.annotations.ApiParam;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.ctsi.service.CformPlugTypeService;
import com.ctsi.domain.CformPlugType;



@RestController
@RequestMapping("/api")
public class CformPlugTypeController {

    private final Logger log = LoggerFactory.getLogger(CformPlugTypeController.class);

    @Autowired
    private  CformPlugTypeService cformPlugTypeService;


    @PostMapping("/createCformPlugType")
    @OperationLog(dBOperation = DBOperation.ADD,message = "创建CformPlugType")
    public ResponseBO<Void> createCformPlugType(@RequestBody CformPlugType cformPlugType) throws URISyntaxException {
        try {
            cformPlugTypeService.insert(cformPlugType);
            return new ResponseBO<>(SysErrEnum.SUCCESS);
        } catch (Exception e) {
            log.error("cformPlugTypes Exception",e);
            return new ResponseBO<>(e);
        }
    }
	

    @PostMapping("/updateCformPlugType")
    @OperationLog(dBOperation = DBOperation.UPDATE,message = "更新CformPlugType")
    public ResponseBO<Void> updateCformPlugType(@RequestBody CformPlugType cformPlugType)  {
        try {
            cformPlugTypeService.update(cformPlugType);
            return new ResponseBO<>(SysErrEnum.SUCCESS);
        } catch (Exception e) {
            log.error("cformPlugTypes Exception",e);
            return new ResponseBO<>(e);
        }
    }

    @PostMapping("/getCformPlugTypesListForPage")
    public ResponseBO<PageCform<CformPlugType>> getCformPlugTypesListForPage(@RequestBody @ApiParam(required = true) PageForm<CformPlugType> form) {
        try {
            PageCform<CformPlugType> page=cformPlugTypeService.getCformPlugTypesListForPage(form);
            return new ResponseBO<>(page,SysErrEnum.SUCCESS);
        } catch (Exception e) {
            log.error("createCfromPlug Exception",e);
            return new ResponseBO<>(e);
        }
    }

    @PostMapping("/getCformPlugTypesListByWhere")
    public ResponseBO<List<CformPlugType>> getCformPlugTypesListByWhere(@RequestBody CformPlugType cformPlugType) {
        try {
            List<CformPlugType> list=cformPlugTypeService.getCformPlugTypesListByWhere(cformPlugType);
            return new ResponseBO<>(list,SysErrEnum.SUCCESS);
        } catch (Exception e) {
            log.error("createCfromPlug Exception",e);
            return new ResponseBO<>(e);
        }
    }

    @PostMapping("/getCformPlugTypeById/{id}")
    public ResponseBO<CformPlugType> getCformPlugTypeById(@PathVariable String id) {
        try {
            CformPlugType cformPlugType=cformPlugTypeService.getCformPlugTypeById(id);
            return new ResponseBO<>(cformPlugType,SysErrEnum.SUCCESS);
        } catch (Exception e) {
            log.error("createCfromPlug Exception",e);
            return new ResponseBO<>(e);
        }
    }

    @DeleteMapping("/deleteCformPlugType/{id}")
    @OperationLog(dBOperation = DBOperation.DELETE,message = "删除CformPlugType")
    public ResponseBO<Void> deleteCformPlugType(@PathVariable String id) {
        try {
            cformPlugTypeService.delete(id);
            return new ResponseBO<>(SysErrEnum.SUCCESS);
        } catch (Exception e) {
            log.error("createCfromPlug Exception",e);
            return new ResponseBO<>(e);
        }
    }
    
}
