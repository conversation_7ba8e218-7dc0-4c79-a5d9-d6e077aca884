package com.ctsi.web;


import com.ctsi.common.bo.PageForm;
import com.ctsi.common.core.bo.ResponseBO;
import com.ctsi.common.util.SysErrEnum;
import com.ctsi.domain.CformModelItem;
import com.ctsi.domain.dto.CformModelItemDTO;
import com.ctsi.hndx.activitcform.PageCform;
import com.ctsi.hndx.enums.DBOperation;
import com.ctsi.hndx.result.ResultVO;
import com.ctsi.service.CformModelItemService;
import com.ctsi.ssdc.annotation.OperationLog;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;



/**
 * REST controller for managing CfromModelItem.
 *
 * <AUTHOR>
 *
 */



@RestController
@Api(tags = "表单设计-模型管理")
@RequestMapping("/api")
public class CformModelItemController {
    private final Logger log = LoggerFactory.getLogger(CformModelItemController.class);

    @Autowired
    private CformModelItemService cformModelItemService;

    /**
     * 批量创建模型
     * @param list
     * @return
     */
    @PostMapping("/createBatchCfromModelItem")
    @OperationLog(dBOperation = DBOperation.ADD,message = "批量创建模型")
    public ResponseBO<Void> createBatchCformModelItem(@RequestBody List<CformModelItem> list) {
        try {
            cformModelItemService.insert(list);
            return new ResponseBO<>(SysErrEnum.SUCCESS);
        } catch (Exception e) {
            log.error("createCfromModelItem Exception",e);
            return new ResponseBO<>(e);
        }
    }

    /**
     * 创建模型
     * @param entity
     * @return
     */
    @PostMapping("/createCfromModelItem")
    @ApiOperation(value = "创建模型", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.ADD,message = "创建Cform模型项目(CformModelItem)")
    public ResponseBO<Void> createCformModelItem(@RequestBody CformModelItem entity) {
        try {
            List<CformModelItem> cformModelItems = new ArrayList<>();
            cformModelItems.add(entity);
            cformModelItemService.insert(cformModelItems);
            return new ResponseBO<>(SysErrEnum.SUCCESS);
        } catch (Exception e) {
            log.error("createCfromModelItem Exception",e);
            return new ResponseBO<>(e);
        }
    }

    /**
     * 更新模型
     * @param cfromModelItem
     * @return
     */
    @PostMapping("/updateCfromModelItem")
    @OperationLog(dBOperation = DBOperation.UPDATE,message = "更新Cform模型项目(CformModelItem)")
    public ResponseBO<Void> updateCfromModelItem(@RequestBody CformModelItem cfromModelItem)  {
        try {
            cformModelItemService.update(cfromModelItem);
            return new ResponseBO<>(SysErrEnum.SUCCESS);
        } catch (Exception e) {
            log.error("createCfromModelItem Exception",e);
            return new ResponseBO<>(e);
        }
    }

    /**
     * 获取模型列表
     * @param form
     * @return
     */
    @PostMapping("/getCfromModelItemsListForPage")
    public ResponseBO<PageCform<CformModelItemDTO>> getCfromModelItemsListForPage(@RequestBody @ApiParam(required = true) PageForm<CformModelItem> form) {
        try {
            PageCform<CformModelItemDTO> page= cformModelItemService.getCfromModelItemsListForPage(form);
            return new ResponseBO<>(page,SysErrEnum.SUCCESS);
        } catch (Exception e) {
            log.error("createCfromModelItem Exception",e);
            return new ResponseBO<>(e);
        }
    }

    /**
     * 动态参数获取模型项列表
     * @param cfromModelItem
     * @return
     */
    @PostMapping("/getCfromModelItemsListByWhere")
    public ResponseBO<List<CformModelItem>> getCfromModelItemsListByWhere(@RequestBody @ApiParam(required = true) CformModelItem cfromModelItem) {
        try {
            List<CformModelItem> list = cformModelItemService.getCfromModelItemsListByWhere(cfromModelItem);
            return new ResponseBO<>(list,SysErrEnum.SUCCESS);
        } catch (Exception e) {
            log.error("createCfromModelItem Exception",e);
            return new ResponseBO<>(e);
        }
    }

    @ApiOperation("表单设计的时候通过模型id和表单id来区分是驼峰类型还是下划线类型")
    @GetMapping("/getCfromModelItemsListByFormId/{modelItemId}/{formTypeId}")
    public ResultVO<List<CformModelItem>> getCfromModelItemsListByFormId(@PathVariable @ApiParam("模型id") String  modelItemId,
                                                                         @PathVariable @ApiParam("表单分类id") String formTypeId) {
        List<CformModelItem> list = cformModelItemService.getCfromModelItemsListByFormId(modelItemId,formTypeId);
        return ResultVO.success(list);
    }


    /**
     * 根据id获取模型
     * @param id
     * @return
     */
    @GetMapping("/getCfromModelItem/{id}")
    public ResponseBO<CformModelItem> getCfromModelItem(@PathVariable String id) {
        try {
            CformModelItem cfromModelItem= cformModelItemService.getCfromModelItem(id);
            return new ResponseBO<>(cfromModelItem,SysErrEnum.SUCCESS);
        } catch (Exception e) {
            log.error("createCfromModelItem Exception",e);
            return new ResponseBO<>(e);
        }
    }

    /**
     * 根据id删除模型
     * @param id
     * @return
     */
    @DeleteMapping("/deleteCfromModelItem/{id}")
    @OperationLog(dBOperation = DBOperation.DELETE,message = "根据id删除Cform模型项目(CformModelItem)")
    public ResponseBO<Void> deleteCfromModelItem(@PathVariable String id) {
        try {
            cformModelItemService.delete(id);
            return new ResponseBO<>(SysErrEnum.SUCCESS);
        } catch (Exception e) {
            log.error("deleteCfromModelItem Exception",e);
            return new ResponseBO<>(e);
        }
    }

}
