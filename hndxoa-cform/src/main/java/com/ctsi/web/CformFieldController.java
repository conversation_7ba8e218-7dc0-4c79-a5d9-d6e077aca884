package com.ctsi.web;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ctsi.common.bo.PageForm;
import com.ctsi.common.core.bo.ResponseBO;
import com.ctsi.common.util.SysErrEnum;
import com.ctsi.domain.CformField;
import com.ctsi.domain.CformForm;
import com.ctsi.hndx.activitcform.PageCform;
import com.ctsi.hndx.enums.DBOperation;
import com.ctsi.hndx.result.ResultVO;
import com.ctsi.service.CformFieldService;
import com.ctsi.service.CformFormService;
import com.ctsi.ssdc.annotation.OperationLog;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;



/**
 * REST controller for managing CformField.
 *
 * <AUTHOR>
 *
 */
@RestController
@RequestMapping("/api")
public class CformFieldController {
    private final Logger log = LoggerFactory.getLogger(CformFieldController.class);

    @Autowired
    private CformFieldService cformFieldService;
    @Autowired
    private CformFormService cformFormService;
    /**
     * 创建模型
     * @param cfromField
     * @return
     */
    @PostMapping("/createCfromField")
    @OperationLog(dBOperation = DBOperation.ADD,message = "创建模型(CformField)")
    public ResponseBO<CformField> createCfromField(@RequestBody CformField cfromField) {
        try {
            cfromField= cformFieldService.insert(cfromField);
            return new ResponseBO<>(cfromField, SysErrEnum.SUCCESS);
        } catch (Exception e) {
            log.error("createCfromField Exception",e);
            return new ResponseBO<>(SysErrEnum.ERROR);
        }
    }

    /**
     * 更新模型
     * @param cfromField
     * @return
     */
    @PutMapping("/updateCfromField")
    @OperationLog(dBOperation = DBOperation.UPDATE,message = "更新模型(CformField)")
    public ResponseBO<CformField> updateCfromField(@RequestBody CformField cfromField)  {
        try {
            cfromField= cformFieldService.update(cfromField);
            return new ResponseBO<>(cfromField,SysErrEnum.SUCCESS);
        } catch (Exception e) {
            log.error("createCfromField Exception",e);
            return new ResponseBO<>(SysErrEnum.ERROR);
        }
    }

    /**
     * 获取模型列表
     * @param form
     * @return
     */
    @GetMapping("/getCfromFieldsList")
    public ResponseBO<PageCform<CformField>> getCfromFieldsList(@RequestBody @ApiParam(required = true) PageForm<CformField> form) {
        try {
            PageCform<CformField> page=new PageCform<>();
            return new ResponseBO<>(page,SysErrEnum.SUCCESS);
        } catch (Exception e) {
            log.error("createCfromField Exception",e);
            return new ResponseBO<>(SysErrEnum.ERROR);
        }
    }

    /**
     * 根据id获取模型
     * @param id
     * @return
     */
    @GetMapping("/getCfromField/{id}")
    public ResponseBO<CformField> getCfromField(@PathVariable String id) {
        try {
            CformField cfromField= cformFieldService.getCfromField(id);
            return new ResponseBO<>(cfromField,SysErrEnum.SUCCESS);
        } catch (Exception e) {
            log.error("createCfromField Exception",e);
            return new ResponseBO<>(SysErrEnum.ERROR);
        }
    }

    /**
     * 根据id获取模型
     * @param formId
     * @return
     */
    @GetMapping("/getCfromFieldListByFormId/{formId}")
    public ResponseBO<List<CformField>> getCfromFieldListByFormId(@PathVariable String formId) {
        try {
            List<CformField> list= cformFieldService.getCfromFieldListByFormId(formId);
            return new ResponseBO<>(list,SysErrEnum.SUCCESS);
        } catch (Exception e) {
            log.error("createCfromField Exception",e);
            return new ResponseBO<>(SysErrEnum.ERROR);
        }
    }


    /**
     * 根据新增的表单的表单id来获取模型
     * @param addFormId     表单id
     * @return CformField   前端表单模型
     */
    @ApiOperation(value = "根据新增的表单的表单addFormId来获取模型")
    @GetMapping("/getCfromFieldListByAddFormId/{addFormId}")
    public ResultVO<List<CformField>> getCfromFieldListByAddFormId(@PathVariable Long addFormId) {
        List<CformField> list= cformFieldService.getCfromFieldDynamicRowListByAddFormId(addFormId);
        return ResultVO.success(list);
    }

    /**
     * 根据新增的表单的表单id来获取模型
     * @param formId
     * @return
     */
    @ApiOperation(value = "app表单addFormId来获取模型")
    @GetMapping("/getCfromFieldListByFormIdApp/{formId}")
    public ResultVO<List<CformField>> getCfromFieldListByFormIdApp(@PathVariable String formId) {
        CformForm cfromForm = new CformForm();
        cfromForm.setFormId(formId);
        LambdaQueryWrapper<CformForm> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(CformForm::getFormId,formId).select(CformForm::getAppFormId);
        Long addFormId = cformFormService.getOne(lambdaQueryWrapper).getAppFormId();
        List<CformField> list= cformFieldService.getCfromFieldListNoSubByAddFormId(addFormId);
        return ResultVO.success(list);
    }



    /**
     * 根据id删除模型
     * @param id
     * @return
     */
    @DeleteMapping("/deleteCfromField/{id}")
    @OperationLog(dBOperation = DBOperation.DELETE,message = "根据id删除模型(CformField)")
    public ResponseBO<Void> deleteCfromField(@PathVariable String id) {
        try {
            cformFieldService.delete(id);
            return new ResponseBO<>(SysErrEnum.SUCCESS);
        } catch (Exception e) {
            log.error("deleteCfromField Exception",e);
            return new ResponseBO<>(SysErrEnum.ERROR);
        }
    }
}
