package com.ctsi.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ctsi.common.bo.PageForm;
import com.ctsi.common.util.Constants;
import com.ctsi.common.util.CustomException;
import com.ctsi.domain.CformModel;
import com.ctsi.domain.CformModelItem;
import com.ctsi.domain.CformType;
import com.ctsi.domain.dto.CformModelItemDTO;
import com.ctsi.domain.dto.QueryTableFidldsListDTO;
import com.ctsi.hndx.activitcform.PageCform;
import com.ctsi.hndx.activitcform.PageCformUtil;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.constant.SysConstant;
import com.ctsi.hndx.utils.*;
import com.ctsi.mapper.CformFormRepository;
import com.ctsi.mapper.CformModelItemRepository;
import com.ctsi.mapper.CformModelRepository;
import com.ctsi.mapper.CformTypeRepository;
import com.ctsi.service.CformModelItemService;
import com.ctsi.ssdc.database.enums.EnumDatabaseName;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class CformModelItemServiceImpl implements CformModelItemService {

    @Autowired
    private CformModelItemRepository cformModelItemRepository;

    @Autowired
    private CformFormRepository cformFormRepository;

    @Autowired
    private CformModelRepository cformModelRepository;

    @Autowired
    private CformTypeRepository cformTypeRepository;

    @Value("${cform.datasourceTableSchema}")
    private String tableSchema;

    @Autowired
    private DatabaseTypeUtils databaseTypeUtils;


    private static final List<String> FIELDLIST = Arrays.asList("id", SysConstant.CREARE_BY, SysConstant.DEPARTMENT_ID,
            SysConstant.COMPANY_ID, SysConstant.TENANT_ID,"department_name","company_name","mobile","create_name","create_time",
            "update_by", "update_name", "update_time", "deleted", "bpm_status", "process_instance_id","title","document","annex");


    private static final List<String> NO_SHOW_FIELD_LIST = Arrays.asList("id", SysConstant.CREARE_BY, SysConstant.DEPARTMENT_ID,
            SysConstant.COMPANY_ID, SysConstant.TENANT_ID,"create_time",
            "update_by", "update_name", "update_time", "deleted", "bpm_status", "process_instance_id");

    @Override
    @Transactional
    public void insert(List<CformModelItem> list){
        for (CformModelItem item : list) {
            item.setPublishFlag(Constants.MODEL_RELEASE_0);
            // 防止查询的条件有sql注入，需要验证字段名
//            SqlInjectionUtil.filterContent(item.getModelItemId());  字段名不做sql注入验证
            if (checkModelItemIdIsExist(item.getModelId(), item.getModelItemId())) {
                throw new CustomException(item.getModelItemId() + "模型项ID已存在！");
            }
            cformModelItemRepository.insert(item);
        }
    }

    @Override
    public void update(CformModelItem cfromModelItem) throws Exception {
//        CformModel cformModel1 = cformModelRepository.selectById(cfromModelItem.getModelId());
//        cfromModelItem.setPublishFlag(Constants.MODEL_RELEASE_0);
        cformModelItemRepository.updateById(cfromModelItem);
    }

    @Override
    public CformModelItem getCfromModelItem(String id) throws Exception {
        return cformModelItemRepository.selectById(id);
    }

    @Override
    public void delete(String id) throws Exception {
//        CformModelItem cfromModelItem = cformModelItemRepository.selectById(id);
//        if (Constants.MODEL_RELEASE_1.equals(cfromModelItem.getPublishFlag())) {
//            throw new CustomException("模型项已发布，禁止删除！");
//        }
        cformModelItemRepository.deleteById(id);
    }

    @Override
    public PageCform<CformModelItemDTO> getCfromModelItemsListForPage(PageForm<CformModelItem> form) throws Exception {
        Page page = new Page(form.getCurrentPage(), form.getPageSize());
        CformModelItem cformModelItem = form.getT();
        CformModel cformModel1 = cformModelRepository.selectById(cformModelItem.getModelId());
        IPage<CformModelItem> formModelItemPage=null;

        // 判断当前查看的模型是否与业务关联
        if (String.valueOf(cformModel1.getBusinessAdd()).equals(Constants.BUSINESS_ADD_1)){
            // 是，查询业务关联表的所有字段
            Map<String, Object> map = new HashMap<>();
            map.put("tableName", cformModel1.getModelData());
            map.put("modelItemName", cformModelItem.getModelItemName());
            map.put("tableSchema", tableSchema);
            formModelItemPage = this.queryDatabaseTableFidldsList(page, map);
        }else {
            // 否，查询非业务关联表的所有字段
            Map<String, Object> map = new HashMap<>();
            map.put("tableName", cformModel1.getModelData());
            map.put("modelItemName", cformModelItem.getModelItemName());
            map.put("tableSchema", tableSchema);
            formModelItemPage = this.queryDatabaseTableFidldsList(page, map);
        }

        IPage<CformModelItemDTO> data = formModelItemPage.convert(entity -> BeanConvertUtils.copyProperties(entity, CformModelItemDTO.class));
        data.setRecords(data.getRecords().stream().map(i -> {
            /*if ("PRI".equals(i.getIsFk())) {
                i.setIsPk("1");
            } else {
                i.setIsPk("0");
            }*/
            // 通过正则表达式判断是否为默认字段（默认字段不允许用户修改），便于前端标识
            if (FIELDLIST.contains(i.getModelItemId().toLowerCase()) || FIELDLIST.contains(i.getModelItemId()) ) {
                i.setDefaultField(Constants.FIELD_IS_DEFAULT_1);
            } else {
                i.setDefaultField(Constants.FIELD_IS_DEFAULT_0);
            }
            i.setModelId(cformModelItem.getModelId());
            return i;
        }).collect(Collectors.toList()));
        PageCform pageCform = PageCformUtil.convertPageCfrm(data);
        return pageCform;
    }

    /**
     * 查询非业务关联表的所有字段
     *
     * @param form 查询参数
     * @return pageCform
     */
    private PageCform<CformModelItemDTO> getCfromModelItemsListForPageNotBiz(PageForm<CformModelItem> form){
        BasePageForm basePageForm = new BasePageForm(form.getCurrentPage(), form.getPageSize());
        CformModelItem cformModelItem = form.getT();
        LambdaQueryWrapper<CformModelItem> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(CformModelItem::getModelItemId,cformModelItem.getModelId());
        IPage<CformModelItem> pageData = cformModelItemRepository.selectPage(
                PageHelperUtil.getMPlusPageByBasePage(basePageForm), lambdaQueryWrapper);
        PageCform resultForm = PageCformUtil.convertPageCfrm(pageData);
        return resultForm;
    }

    /**
     * 此处需要修改
     *
     * @param cfromModelItem
     * @return
     */
    @Override
    public List<CformModelItem> getCfromModelItemsListByWhere(CformModelItem cfromModelItem) {

        CformModel cformModel1 = cformModelRepository.selectById(cfromModelItem.getModelId());

        List<CformModelItem> cformModelItems = this
                .queryDatabaseTableFidldsList(QueryTableFidldsListDTO
                        .builder()
                        .tableSchema(tableSchema)
                        .tableName(cformModel1.getModelData())
                        .build()).stream()
                .map(i -> {
                    i.setModelId(cfromModelItem.getModelId());
                    i.setIsPk("0");
                    return i;
                }).collect(Collectors.toList());
        // 转换成驼峰
        for (CformModelItem cformModelItem : cformModelItems) {
            String modelItemCamelId = ConvertUtils.underlineToCamel(cformModelItem.getModelItemId());
            cformModelItem.setModelItemId(modelItemCamelId);
            String modelItemType = cformModelItem.getModelItemType();
            if ("varchar".equals(modelItemType)) {
                cformModelItem.setModelItemType("string");
            } else if ("int".equals(modelItemType)) {
                cformModelItem.setModelItemType("integer");
            }
        }
        return cformModelItems;
    }

    @Override
    public List<CformModelItem> getCfromModelItemsListByFormId(String modelItemId, String formTypeId) {
        CformModel cformModel1 = cformModelRepository.selectById(modelItemId);
        List<CformModelItem> cformModelItems = this.queryDatabaseTableFidldsList(QueryTableFidldsListDTO
                .builder()
                .tableSchema(tableSchema)
                .tableName(cformModel1.getModelData())
                .build()).stream().filter(cformModelItem -> {
            if (NO_SHOW_FIELD_LIST.contains(cformModelItem.getModelItemId().toLowerCase())) {
                return false;
            }
            cformModelItem.setModelId(modelItemId);
            cformModelItem.setIsPk("0");
            return true;
        }).collect(Collectors.toList());

        CformType cformType = cformTypeRepository.selectById(formTypeId);

        // 转换成驼峰
        for (CformModelItem cformModelItem : cformModelItems) {
            String modelItemCamelId = ConvertUtils.underlineToCamel(cformModelItem.getModelItemId());
            cformModelItem.setModelItemId(modelItemCamelId);
            String modelItemType = cformModelItem.getModelItemType();
            if ("varchar".equals(modelItemType)) {
                cformModelItem.setModelItemType("string");
            } else if ("int".equals(modelItemType)) {
                cformModelItem.setModelItemType("integer");
            }
        }
       /* if (StringUtils.isNotBlank(cformType.getBusinessAdd())
                && Constants.BUSINESS_ADD_1.equals(cformType.getBusinessAdd())) {
            for (CformModelItem cformModelItem : cformModelItems) {
                String modelItemCamelId = ConvertUtils.underlineToCamel(cformModelItem.getModelItemId());
                cformModelItem.setModelItemId(modelItemCamelId);
            }
        }*/
        return cformModelItems;
    }

    public boolean checkModelItemIdIsExist(String modelId, String modelItemId){
        CformModelItem cfromModelItem = new CformModelItem();
        cfromModelItem.setModelId(modelId);
        cfromModelItem.setModelItemId(modelItemId);
        LambdaQueryWrapper<CformModelItem> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(CformModelItem::getModelId, modelId).eq(CformModelItem::getModelItemId, modelItemId);
        int list = cformModelItemRepository.selectCount(lambdaQueryWrapper).intValue();
        return list > 0;
    }

    private IPage<CformModelItem> queryDatabaseTableFidldsList(Page page,Map<String, Object> map){
        if(databaseTypeUtils.getDatabaseType() == EnumDatabaseName.DMDREVER){//达梦数据库
            return cformModelItemRepository.queryDMTablefieldsPage(page, map);
        } else if (databaseTypeUtils.getDatabaseType() == EnumDatabaseName.KINGBASE8){//人大金仓数据库
            return cformModelItemRepository.queryKingbaseTablefieldsPage(page, map);
        }else{//mysql 数据库
            return cformModelItemRepository.querytablefieldsPage(page, map);
        }
    }

    private List<CformModelItem> queryDatabaseTableFidldsList(QueryTableFidldsListDTO tableName){
        if(databaseTypeUtils.getDatabaseType() == EnumDatabaseName.DMDREVER){//达梦数据库
            return cformModelItemRepository.queryDMTableFidldsList(tableName);
        } else if (databaseTypeUtils.getDatabaseType() == EnumDatabaseName.KINGBASE8){//人大金仓数据库
            return cformModelItemRepository.queryKingbaseTableFidldsList(tableName);
        }else{//mysql 数据库
            return cformModelItemRepository.queryTableFidldsList(tableName);
        }
    }
}
