package com.ctsi.service.impl;


import com.ctsi.common.bo.PageForm;
import com.ctsi.activiti.core.vo.PageQuery;
import com.ctsi.common.util.CustomException;
import com.ctsi.domain.CformPlugType;
import com.ctsi.domain.CformPlug;
import com.ctsi.hndx.activitcform.PageCform;
import com.ctsi.mapper.CformPlugTypeRepository;
import com.ctsi.mapper.CformPlugRepository;
import com.ctsi.service.CformPlugTypeService;
import com.github.pagehelper.PageHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.UUID;

@Service
public class CformPlugTypeServiceImpl implements CformPlugTypeService {

	@Autowired
	private CformPlugTypeRepository cformPlugTypeRepository;

	@Autowired
	private CformPlugRepository cformPlugRepository;


	@Override
	public void insert(CformPlugType cformPlugType) throws Exception {
		cformPlugType.setId(UUID.randomUUID().toString());
		cformPlugTypeRepository.insert(cformPlugType);
	}

	@Override
	public void update(CformPlugType cformPlugType) throws Exception {
		cformPlugTypeRepository.update(cformPlugType);
	}

	@Override
	public CformPlugType getCformPlugTypeById(String id) throws Exception {
		return cformPlugTypeRepository.queryCformPlugTypeById(id);
	}

	@Override
	public void delete(String id) throws Exception {
		CformPlug cformPlug=new CformPlug();
		cformPlug.setPlugTypeId(id);
		List<CformPlug> list=cformPlugRepository.queryCfromPlugsListByWhere(cformPlug);
		if(list!=null && list.size()>0 ){
			throw new CustomException("该分类下插件不为空，禁止删除！");
		}
		cformPlugTypeRepository.delete(id);
	}

	@Override
	public PageCform<CformPlugType> getCformPlugTypesListForPage(PageForm<CformPlugType> form) throws Exception {
		PageCform<CformPlugType> page=new PageCform<>();
		if(form.getT()==null){
			form.setT(new CformPlugType());
		}
		PageQuery pq= PageQuery.toPageQuery(form, CformPlugType.class);
		PageHelper.startPage(form.getCurrentPage(), form.getPageSize());
		List<CformPlugType> list=cformPlugTypeRepository.queryPageCformPlugTypesList(pq);
		page = PageCform.listToPage(list);
//		if(list !=null){
//            page=Page.toPage(pq,list);
//        }
		return page;
	}

	@Override
	public List<CformPlugType> getCformPlugTypesListByWhere(CformPlugType cformPlugType) throws Exception {
		return cformPlugTypeRepository.queryCformPlugTypesListByWhere(cformPlugType);
	}
}
