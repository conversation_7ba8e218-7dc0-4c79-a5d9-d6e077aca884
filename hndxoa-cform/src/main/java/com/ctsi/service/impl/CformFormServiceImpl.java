package com.ctsi.service.impl;

import cn.hutool.core.map.MapUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ctsi.common.bo.PageForm;
import com.ctsi.common.util.Constants;
import com.ctsi.common.util.CustomException;
import com.ctsi.common.util.JsonUtils;
import com.ctsi.domain.CformDomain;
import com.ctsi.domain.CformField;
import com.ctsi.domain.CformForm;
import com.ctsi.domain.CformModel;
import com.ctsi.domain.dto.CformFormDTO;
import com.ctsi.domain.dto.CformImportDTO;
import com.ctsi.domain.dto.FormItems;
import com.ctsi.domain.dto.RenderCformDTO;
import com.ctsi.hndx.activitcform.PageCform;
import com.ctsi.hndx.activitcform.PageCformUtil;
import com.ctsi.hndx.cformbizdatacontent.entity.CformBizDataContent;
import com.ctsi.hndx.cformbizdatacontent.service.CformBizDataContentService;
import com.ctsi.hndx.common.SysBaseServiceImpl;
import com.ctsi.hndx.constant.RedisKeyConstant;
import com.ctsi.hndx.enums.FormVersionTypeEnum;
import com.ctsi.hndx.formversion.entity.CformFormVersion;
import com.ctsi.hndx.formversion.service.ICformFormVersionService;
import com.ctsi.hndx.utils.MybatisQueryUtil;
import com.ctsi.hndx.utils.SnowflakeIdUtil;
import com.ctsi.mapper.*;
import com.ctsi.service.*;
import com.ctsi.ssdc.admin.domain.CscpOrg;
import com.ctsi.ssdc.admin.service.CscpOrgService;
import com.ctsi.ssdc.security.SecurityUtils;
import com.ctsi.ssdc.util.ExportUtil;
import com.ctsi.ssdc.util.RedisUtil;
import com.google.gson.Gson;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.*;

@Service
public class CformFormServiceImpl extends SysBaseServiceImpl<CformFormRepository,CformForm> implements CformFormService {

    @Autowired
    private CformFormRepository cformFormRepository;

    @Autowired
    private CformFieldRepository cformFieldRepository;

    @Autowired
    private CformDomainRepository cformDomainRepository;

    @Autowired
    private CformDomainService cformDomainService;

    @Autowired
    private CformModelRepository cformModelRepository;
    @Autowired
    private CformTypeRepository cformTypeRepository;
    @Autowired
    private CformBizDataContentService cformBizDataContentService;
    @Autowired
    private CFormDataService cFormDataService;

    @Autowired
    private CformFieldService cformFieldService;

    @Autowired
    private CformModelService cformModelService;

    @Autowired
    private ICformFormVersionService cformFormVersionService;

    @Autowired
    private IBizFormTemporaryStorageService bizFormTemporaryStorageService;

    @Autowired
    private CscpOrgService cscpOrgService;

    @Autowired
    private RedisUtil redisUtil;

    @Value("${cform.datasourceTableSchema}")
    private String tableSchema;

    @Override
    public String insert(CformForm cfromForm) throws Exception {
        Long count = getCfromCountByFormId(cfromForm.getFormId());
        if (count.intValue() > 0) {
            throw new CustomException("表单id已存在！");
        }
        cfromForm.setIsRelease(Constants.MODEL_RELEASE_0);
        String modelId = cfromForm.getModelId();
        CformModel cformModel = cformModelRepository.selectById(modelId);
        String tableName = cformModel.getModelDataType();
        cfromForm.setModelData(tableName);
        String id = SnowflakeIdUtil.getSnowFlakeId();
        cfromForm.setFormId(id);
        cfromForm.setId(id);
        cfromForm.setAddFormId(SnowflakeIdUtil.getSnowFlakeLongId());
        cfromForm.setAppFormId(SnowflakeIdUtil.getSnowFlakeLongId());
        cformFormRepository.insert(cfromForm);

        return cfromForm.getId();
    }



    @Override
    public CformForm getCfromForm(String id) throws Exception {
        CformForm cformForm = cformFormRepository.selectById(id);
        String parentId = cformForm.getFormTypeId();
        if (StringUtils.isNotEmpty(parentId)) {
            cformForm.setFormTypeName(cformTypeRepository.selectById(parentId).getFormTypeName());
        }
        return cformForm;
    }


    @Override
    public CformForm getCfromFormById(String id){
        CformForm cformForm = cformFormRepository.selectById(id);
        return cformForm;
    }
    @Override
    public Long getCfromCountByFormId(String id) {
        CformForm cfromForm = new CformForm();
        cfromForm.setFormId(id);
        QueryWrapper<CformForm> queryWrapper = MybatisQueryUtil.paddingDefaultConditionQuery(CformForm.class, cfromForm);
        long value = cformFormRepository.selectCount(queryWrapper).longValue();
        return value;
    }

    @Override
    public void delete(String id) throws Exception {
        CformForm cfromForm = cformFormRepository.selectById(id);
        if (Constants.MODEL_RELEASE_1.equals(cfromForm.getIsRelease())) {
            throw new CustomException("表单已发布，禁止删除！");
        }
        cformFormRepository.deleteById(id);
    }

    @Override
    public PageCform<CformForm> getCfromFormsListForPage(PageForm<CformForm> form) throws Exception {
        Page page = new Page(form.getCurrentPage(), form.getPageSize());
        CformForm cformForm = form.getT();
//        LambdaQueryWrapper<CformForm> lambdaQueryWrapper = new LambdaQueryWrapper<>();

//        if (com.ctsi.hndx.utils.StringUtils.isNotEmpty(cformForm.getFormTypeId())){
//            lambdaQueryWrapper.eq(CformForm::getFormName,cformFormm.getFormName());
//        }
        //  租户和系统取自己建立的,普通用户取单位的
        if (SecurityUtils.isTenantName() || SecurityUtils.isSystemName()){
            cformForm.setCreateBy(SecurityUtils.getCurrentUserId());
        }
        IPage<CformForm> cformModelIPage = cformFormRepository.queryPageCfromFormList(page, cformForm);
        cformModelIPage.getRecords().forEach(item -> {
             String cformType = cformTypeRepository.selectById(item.getFormTypeId()).getFormTypeName();
            item.setFormTypeName(cformType);
        });
        PageCform pageCform = PageCformUtil.convertPageCfrm(cformModelIPage);
        return pageCform;
    }

    @Override
    @Transactional
    public void publishCfrom(CformFormDTO cformFormDTO) throws Exception {
        final boolean defaults = Constants.ADD_IS_DEFAULT_1.equals(String.valueOf(cformFormDTO.getType()));
        //起草
        final boolean draft = Constants.ADD_IS_DEFAULT_2.equals(String.valueOf(cformFormDTO.getType()));

        // app的新增
        boolean appDraft = Constants.APP_IS_DEFAULT_3.equals(String.valueOf(cformFormDTO.getType()));
        CformForm cfromForm = new CformForm();
        cfromForm.setId(cformFormDTO.getId());
        CformForm cf = cformFormRepository.selectById(cformFormDTO.getId());

        if (defaults && ( cf == null || StringUtils.isEmpty(cf.getDefContent()) || StringUtils.isEmpty(cf.getDataContent()))) {
            throw new CustomException("请先设计表单并保存后再发布！");
        }
        if (draft && ( cf == null || StringUtils.isEmpty(cf.getAddDefContent()) || StringUtils.isEmpty(cf.getAddDataContent()))) {
            throw new CustomException("请先设计起草表单并保存后再发布！");
        }

        String formId = cf.getFormId();
        if (StringUtils.isEmpty(formId)) {
            throw new CustomException("FormId不能为空！");
        }

        //解析json
        String dataContent = cf.getDataContent();

        if( draft){
            dataContent = cf.getAddDataContent();
        }else if (appDraft){
            dataContent = cf.getAppDataContent();
        }
        Map map = JsonUtils.jsonToPojo(dataContent, Map.class);

        List<CformField> cfromFieldList = new ArrayList<>();
        List<CformDomain> cfromDomainList = new ArrayList<>();

        if (map.containsKey("M")) {
            Map dataMap = MapUtils.getMap(map, "M");
            if (dataMap.containsKey(formId)) {
                JSONObject paramsObj = new JSONObject(dataMap);
                JSONArray valobj = paramsObj.getJSONArray(formId);
                List<CformField> list = JsonUtils.jsonToList(valobj.toString(), CformField.class);
                if (list == null || list.size() == 0) {
                    throw new CustomException("请先设计表单并保存后再发布！");
                }
                cfromFieldList.addAll(list);

               /* CformDomain cfromDomain = new CformDomain();
                cfromDomain.setModelId(MapUtils.getString(dataMap, "modelId"));
                cfromDomain.setParentDomainId("root");
                cfromDomain.setFormId(formId);
                cfromDomain.setDomainId(formId);*/
            } else {
                throw new CustomException("主表单数据异常！");
            }
        }

        if (map.containsKey("S")) {
            JSONObject paramsObj = new JSONObject(map);
            JSONArray valobj = paramsObj.getJSONArray("S");
            List<Map> list = JsonUtils.jsonToList(valobj.toString(), Map.class);

            for (Map item : list) {

                String subData = new JSONObject(item).getJSONArray("subData").toString();
                String domainId = MapUtils.getString(item, "domainId");
                String subModelId = MapUtils.getString(item, "modelId");
                String domainName = MapUtils.getString(item, "domainName");
                String domainType = MapUtils.getString(item, "domainType");
                List<CformField> fieldList = JsonUtils.jsonToList(subData, CformField.class);
                if (fieldList != null && fieldList.size() > 0) {
                    subModelId = fieldList.get(0).getModelId();
                }
                cfromFieldList.addAll(fieldList);

                CformDomain cfromDomain = new CformDomain();
                cfromDomain.setModelId(subModelId);
                cfromDomain.setDomainId(domainId);
                cfromDomain.setDomainName(domainName);
                cfromDomain.setFormId(formId);
                cfromDomain.setParentDomainId(cf.getId());
                cfromDomain.setDomainType(domainType);
                cfromDomainList.add(cfromDomain);
            }
        } else {
            throw new CustomException("子表单数据异常！");
        }
        CformForm updateForm = new CformForm();
        //新增表单的时候
        Long orignalAddFormId = cf.getAddFormId();
        if( draft) {
            // 新增
            formId = String.valueOf(cf.getAddFormId());
            LambdaQueryWrapper<CformField> lambdaQueryWrapper = new LambdaQueryWrapper<>();
            lambdaQueryWrapper.eq(CformField::getAddFormId, orignalAddFormId);
            cformFieldRepository.delete(lambdaQueryWrapper);
            for (CformField cfromField : cfromFieldList) {
                cfromField.setId(SnowflakeIdUtil.getSnowFlakeId());
                cfromField.setAddFormId(orignalAddFormId);
                cfromField.setFormId(String.valueOf(orignalAddFormId));
                String fieldType = cfromField.getFieldType();
                if (StringUtils.isNotEmpty(fieldType) && "dynamicRow".equals(fieldType)) {
                    cfromField.setDomainId(formId);
                }
                cformFieldRepository.insert(cfromField);
            }
            //发布如果未设置表单字段可见，默认所有字段显示
            if(cf.getAddFormItem() == null || cf.getAddFormItem().length()==0) {
                List<FormItems> formItems = new ArrayList<>();
                for (CformField cfromField : cfromFieldList) {
                    FormItems item = new FormItems();
                    item.setFieldId(cfromField.getFieldId());
                    item.setFieldName(cfromField.getFieldName());
                    item.setFieldType(cfromField.getFieldType());
                    item.setModelItemId(cfromField.getModelItemId());
                    item.setOpinionType("");
                    item.setDisabled(false);
                    item.setVisible(true);
                    item.setRequired(false);
                    formItems.add(item);
                }
                String formItemsString = net.sf.json.JSONArray.fromObject(formItems).toString();
                updateForm.setAddFormItem(formItemsString);
            }

        }else if (appDraft){
            // app表单
            formId = String.valueOf(cf.getAppFormId());
            LambdaQueryWrapper<CformField> lambdaQueryWrapper = new LambdaQueryWrapper<>();
            lambdaQueryWrapper.eq(CformField::getAddFormId,cf.getAppFormId());
            cformFieldRepository.delete(lambdaQueryWrapper);
            for (CformField cfromField : cfromFieldList) {
                cfromField.setId(SnowflakeIdUtil.getSnowFlakeId());
                cfromField.setAddFormId(cf.getAppFormId());
                cfromField.setModelId(cf.getModelId());
                cfromField.setFormId(String.valueOf(cf.getAppFormId()));
                cfromField.setDomainId(String.valueOf(cf.getAppFormId()));
                cformFieldRepository.insert(cfromField);
            }
        }else {
            cformFieldRepository.deletByFormId(formId);

            for (CformField cfromField : cfromFieldList) {
                String fieldType = cfromField.getFieldType();
                if (StringUtils.isNotEmpty(fieldType) && "dynamicRow".equals(fieldType)){
                    cfromField.setDomainId(formId);
                }
                cfromField.setId(SnowflakeIdUtil.getSnowFlakeId());
                cformFieldRepository.insert(cfromField);
            }
        }

        cformDomainRepository.deleteByFormId(formId);

        for (CformDomain cfromDomain : cfromDomainList) {
            if( draft){
                cfromDomain.setHasUse(1);
            }
            cfromDomain.setId(SnowflakeIdUtil.getSnowFlakeId());
            cfromDomain.setFormId(formId);
            cformDomainRepository.insert(cfromDomain);
        }

        //更新表单
        updateForm.setId(cf.getId());

        //插入历史版本
        Long formVersionFormId= null;
        FormVersionTypeEnum formVersionTypeEnum = null;
        String formVersionDataContent="";
        //起草
        if (defaults) {
            updateForm.setIsRelease(Constants.MODEL_RELEASE_1);
            updateForm.setPubContent(cf.getDefContent());
            formVersionFormId = Long.valueOf(cf.getFormId());
            formVersionDataContent = cf.getPubContent();
            formVersionTypeEnum = FormVersionTypeEnum.DEAL;
        } else if (draft) {
            updateForm.setAddIsRelease(Constants.ADD_IS_RELEASE_1);
            updateForm.setAddPubContent(cf.getAddDefContent());
            formVersionFormId = cf.getAddFormId();
            formVersionDataContent = cf.getAddPubContent();
            formVersionTypeEnum = FormVersionTypeEnum.ADD;
        }else if (appDraft){
            updateForm.setAppIsRelease(Constants.ADD_IS_RELEASE_1);
            updateForm.setAppPubContent(cf.getAppDefContent());
            formVersionFormId = cf.getAppFormId();
            formVersionDataContent = cf.getAppPubContent();
            formVersionTypeEnum = FormVersionTypeEnum.APP;
        }

        cformFormRepository.updateById(updateForm);
        CformFormVersion cformFormVersionDTO = new CformFormVersion();
        int maxVersion = cformFormVersionService.getMaxFormVersion(formVersionFormId, formVersionTypeEnum)+1;
        cformFormVersionDTO.setDataContent(formVersionDataContent);
        cformFormVersionDTO.setFormId(formVersionFormId);
        cformFormVersionDTO.setFormType(formVersionTypeEnum.getKey());
        cformFormVersionDTO.setFormVersion(formVersionFormId+":"+maxVersion);
        cformFormVersionDTO.setVersion(maxVersion);
        cformFormVersionService.save(cformFormVersionDTO);
        // 兼容 formid情况
        redisUtil.set(RedisKeyConstant.CFORM_VRESION+cf.getFormId()+":"+formVersionTypeEnum.getKey(), maxVersion);
        redisUtil.set(RedisKeyConstant.CFORM_VRESION+formVersionFormId, maxVersion);

        redisUtil.set(RedisKeyConstant.CFORM_MODEL+cf.getFormId(), cf.getModelData());
    }

    @Override
    public CformForm queryCfromListByWhere(CformForm cond) {
        QueryWrapper<CformForm> queryWrapper = MybatisQueryUtil.paddingDefaultConditionQuery(CformForm.class, cond);
        return cformFormRepository.selectOne(queryWrapper);
    }

    @Override
    public List<CformForm> queryCfromList(CformForm cformForm) {
        QueryWrapper<CformForm> queryWrapper = MybatisQueryUtil.paddingDefaultConditionQuery(CformForm.class, cformForm);

        return
                cformFormRepository.selectList(queryWrapper);
    }

    @Override
    public CformForm queryCfromByFormId(String formId) {
        LambdaQueryWrapper<CformForm> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(CformForm::getFormId,formId).or()
                .eq(CformForm::getAddFormId,formId);
        CformForm cf = cformFormRepository.selectOneNoAdd(lambdaQueryWrapper);
        return cf;
    }

    /**
     * 获取不与工作流关联的业务
     * @param businessType
     * @return
     */
    @Override
    public List<CformForm> getCfromFormsListNoContactActiviti(String businessType) {
        LambdaQueryWrapper<CformForm> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(CformForm::getModelData,businessType);
        List<CformForm> cformForms = cformFormRepository.selectList(lambdaQueryWrapper);
        return cformForms;
    }

    /**
     * \
     * 根据表单组装表单需要显示的数据格式
     *
     * @param formId
     * @return
     */
    @Override
    public Map<String, Object> getFormDataByFormId(Long formId) {
        Map<String, Object> result = new HashMap<>();

        LambdaQueryWrapper<CformForm> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CformForm::getFormId,formId.toString()).or()
                .eq(CformForm::getAddFormId,formId);
        CformForm cf = cformFormRepository.selectOneNoAdd(queryWrapper);
        if (cf == null) {
            throw new CustomException("【" + formId + "】表单不存在！");
        }
        if (StringUtils.isEmpty(cf.getPubContent()) && StringUtils.isEmpty(cf.getAddPubContent()) ){
            throw new CustomException("表单未发布！");
        }

        //获取表单数据
        CformModel cfromModel = cformModelRepository.queryModelByFormId(cf.getFormId());
        if (Constants.MODEL_RELEASE_0.equals(cfromModel.getIsRelease())) {
            throw new CustomException("表单模型未发布！");
        }

        if (StringUtils.isEmpty(cfromModel.getModelData())) {
            throw new CustomException("模型表名不存在！");
        }

        //若为本地存储,检查是否有表
        List<Map<String, Object>> tableList = cformModelService.getDatabaseTable(tableSchema, cfromModel.getModelData());
        if ((tableList == null || tableList.size() == 0) && cfromModel.getStoreType().equals(Constants.MODEL_STORE_0)) {
            throw new CustomException("表单物理数据不存在或模型未发布！");
        }
        Map<String, Object> resMap = new HashMap<>();
        Map<String, Object> mainMap = new HashMap<>();
        Map<String, Object> formData = new HashMap<>();
        Map<String, Object> subMap = new HashMap<>();

        List<CformField> listCfromField = cformFieldService.getCfromFieldListByAddFormId(cf.getAddFormId());
        for (CformField item : listCfromField) {
            if (StringUtils.isNotBlank(item.getModelItemId())) {
                formData.put(item.getModelItemId(), "");
            }
        }
        //动态行数量
        List<CformDomain> cfromDomainList = cformDomainRepository.queryCfromDomainListByParentId(cf.getId());

        if (cfromDomainList != null && cfromDomainList.size() > 0) {
            cfromDomainList.forEach(d -> {
                //获取每个动态行的模型项
                List<CformField> listSubCfromField = cformFieldService.getSubCfromFieldList(d.getDomainId(), d.getFormId());
                Map<String, Object> fieldMap = new HashMap<>();
                for (CformField item : listSubCfromField) {
                    fieldMap.put(item.getModelItemId(), "");
                }
                List<Map<String, Object>> tempList = new ArrayList<>();
                tempList.add(fieldMap);
                subMap.put(d.getDomainId(), tempList);
            });
        }
        mainMap.put("modelId", cfromModel.getModelDataType());
        mainMap.put(String.valueOf(formId), formData);
        resMap.put("M", mainMap);
        resMap.put("S", subMap);
        result.put("formData", resMap);
        return result;
    }

    @Override
    public void exportFormJson(Long formId, HttpServletResponse response) {
        Map<String,Object> map = new HashMap<>();
        //查询表单基本信息
        CformForm cformForm = cformFormRepository.selectById(formId);
        String modelId = cformForm.getModelId();
        LambdaQueryWrapper<CformModel> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(CformModel::getParentModelId,modelId);
        CformModel cformModel = cformModelService.selectOneNoAdd(lambdaQueryWrapper);

        if (cformModel != null){
            // 使用这个代替  子表单的值
            map.put("subModelId",cformModel.getId());
        }
        map.put("formInfo",cformForm);
//        //查询表单字段信息
//        LambdaQueryWrapper<CformField> cformFieldLambdaQueryWrapper = new LambdaQueryWrapper<>();
//        cformFieldLambdaQueryWrapper.eq(CformField::getFormId, formId);
//        List<CformField> cformFieldList = cformFieldRepository.selectList(cformFieldLambdaQueryWrapper);
//        map.put("cformField",cformFieldList);
//        //查询表单表格信息
//        LambdaQueryWrapper<CformDomain> lambdaQueryWrapper = new LambdaQueryWrapper<>();
//        lambdaQueryWrapper.eq(CformDomain::getFormId, formId);
//        List<CformDomain> cformDomainList = cformDomainRepository.selectList(lambdaQueryWrapper);
//        map.put("cformDomain",cformDomainList);

        Object obj = com.alibaba.fastjson.JSONArray.toJSON(map);
        String json = obj.toString();
        try {
            ExportUtil.exportJson(response,json);
        } catch (Exception e) {
            throw new CustomException("下载模型失败！");
        }
    }

    @Override
    @Transactional
    public Boolean importFormJson(Long formId,int type, MultipartFile file) {
        Gson gson = new Gson();
        try {
            InputStream inputStream = file.getInputStream();
            InputStreamReader reader = new InputStreamReader(inputStream);
            CformImportDTO dto = gson.fromJson(reader, CformImportDTO.class);//获取json数据
            //判断导入的表单模型是否存在
            LambdaQueryWrapper<CformModel> modelLambdaQueryWrapper = new LambdaQueryWrapper<>();
            modelLambdaQueryWrapper.eq(CformModel::getModelDataType, dto.getFormInfo().getModelData());
            CformModel cformModel = cformModelRepository.selectOne(modelLambdaQueryWrapper);
            if(cformModel == null){
                throw new CustomException("上传失败，请先创建表单模型！");
            }
            //修改表单信息
            CformForm cf = cformFormRepository.selectById(formId);

            //  获取子表单
            LambdaQueryWrapper<CformModel> lambdaQueryWrapper = new LambdaQueryWrapper<>();
            lambdaQueryWrapper.eq(CformModel::getParentModelId,cformModel.getId());
            CformModel cformModelSub = cformModelService.selectOneNoAdd(lambdaQueryWrapper);
            String newFormId = "";
            String subFormIdOld = "";
            if(cformModelSub != null){
                 newFormId = cformModelSub.getId();
                 subFormIdOld = dto.getSubModelId();
            }

            String finalNewFormId = newFormId;
            String finalSubFormIdOld = subFormIdOld;
            // 替换子表单的数据信息
            String importFormId  = dto.getSubModelId();
            List<CformDomain> cfromDomainListByFormId = cformDomainService.getDomainByFormId(importFormId);
            if (CollectionUtils.isNotEmpty(cfromDomainListByFormId)){
                cfromDomainListByFormId.forEach(cformDomain -> {
                    String domainId = cformDomain.getDomainId();
                    String domainType = cformDomain.getDomainType();
                    String newDomainType = domainType+"_"+System.currentTimeMillis();
                    String defContent = dto.getFormInfo().getDefContent();
                    if (defContent != null){
                        dto.getFormInfo().setDefContent(defContent.replace(domainId,newDomainType));
                        if(StringUtils.isNotEmpty(finalNewFormId)){
                            dto.getFormInfo().setDefContent(defContent.replace(finalSubFormIdOld, finalNewFormId));
                        }
                    }
                    String pubContent = dto.getFormInfo().getPubContent();
                    if (pubContent != null){
                        dto.getFormInfo().setPubContent(pubContent.replace(domainId,newDomainType));
                        if(StringUtils.isNotEmpty(finalNewFormId)){
                            dto.getFormInfo().setDefContent(defContent.replace(finalSubFormIdOld, finalNewFormId));
                        }

                    }
                    String dataContent = dto.getFormInfo().getDataContent();
                    if (dataContent != null){
                        dto.getFormInfo().setDataContent(dataContent.replace(domainId,newDomainType));
                        if(StringUtils.isNotEmpty(finalNewFormId)){
                            dto.getFormInfo().setDefContent(defContent.replace(finalSubFormIdOld, finalNewFormId));
                        }

                    }
                });
            }

            // 替换子表单起草的domainId
            Long importAddFormId = dto.getFormInfo().getAddFormId();
            List<CformDomain> cfromDomainListAdd = cformDomainService.getDomainByFormId(String.valueOf(importAddFormId));
            if (CollectionUtils.isNotEmpty(cfromDomainListAdd)){
                cfromDomainListAdd.forEach(cformDomain -> {
                    String domainId = cformDomain.getDomainId();
                    String domainType = cformDomain.getDomainType();
                    String newDomainType = domainType+"_"+System.currentTimeMillis();
                    String defContent = dto.getFormInfo().getAddDefContent();
                    if (defContent != null){
                        dto.getFormInfo().setAddDefContent(defContent.replace(domainId,newDomainType));
                        if(StringUtils.isNotEmpty(finalNewFormId)){
                            dto.getFormInfo().setDefContent(defContent.replace(finalSubFormIdOld, finalNewFormId));
                        }
                    }
                    String pubContent = dto.getFormInfo().getAddPubContent();
                    if (pubContent != null){
                        dto.getFormInfo().setAddPubContent(pubContent.replace(domainId,newDomainType));
                        if(StringUtils.isNotEmpty(finalNewFormId)){
                            dto.getFormInfo().setDefContent(defContent.replace(finalSubFormIdOld, finalNewFormId));
                        }

                    }
                    String dataContent = dto.getFormInfo().getAddDataContent();
                    if (dataContent != null){
                        dto.getFormInfo().setAddDataContent(dataContent.replace(domainId,newDomainType));
                        if(StringUtils.isNotEmpty(finalNewFormId)){
                            dto.getFormInfo().setDefContent(defContent.replace(finalSubFormIdOld, finalNewFormId));
                        }

                    }
                    String ddFormItem = dto.getFormInfo().getAddFormItem();
                    if (ddFormItem != null){
                        dto.getFormInfo().setAddFormItem(ddFormItem.replace(domainId,newDomainType));
                        if(StringUtils.isNotEmpty(finalNewFormId)){
                            dto.getFormInfo().setDefContent(defContent.replace(finalSubFormIdOld, finalNewFormId));
                        }
                    }
                });
            }
            // 替换appde
            Long importAppFormId = dto.getFormInfo().getAppFormId();
            List<CformDomain> cfromDomainListApp = cformDomainService.getDomainByFormId(String.valueOf(importAppFormId));
            if (CollectionUtils.isNotEmpty(cfromDomainListApp)){
                cfromDomainListApp.forEach(cformDomain -> {
                    String domainId = cformDomain.getDomainId();
                    String domainType = cformDomain.getDomainType();
                    String newDomainType = domainType+"_"+System.currentTimeMillis();
                    String defContent = dto.getFormInfo().getAppDefContent();
                    if (defContent != null){
                        dto.getFormInfo().setAppDefContent(defContent.replace(domainId,newDomainType));
                        if(StringUtils.isNotEmpty(finalNewFormId)){
                            dto.getFormInfo().setDefContent(defContent.replace(finalSubFormIdOld, finalNewFormId));
                        }

                    }
                    String pubContent = dto.getFormInfo().getAppPubContent();
                    if (pubContent != null){
                        dto.getFormInfo().setAppPubContent(pubContent.replace(domainId,newDomainType));
                        if(StringUtils.isNotEmpty(finalNewFormId)){
                            dto.getFormInfo().setDefContent(defContent.replace(finalSubFormIdOld, finalNewFormId));
                        }

                    }
                    String dataContent = dto.getFormInfo().getAppDataContent();
                    if (dataContent != null){
                        dto.getFormInfo().setAppDataContent(dataContent.replace(domainId,newDomainType));
                        if(StringUtils.isNotEmpty(finalNewFormId)){
                            dto.getFormInfo().setDefContent(defContent.replace(finalSubFormIdOld, finalNewFormId));
                        }

                    }
                });
            }

            if(type == 0) { //一键导入
                cf.setModelId(cformModel.getId());
                cf.setFormDataId(dto.getFormInfo().getFormDataId());

                cf.setDefContent(dto.getFormInfo().getPubContent());
                //cf.setPubContent(dto.getFormInfo().getPubContent());
                if (dto.getFormInfo().getDataContent() != null) {
                    String dataContent = dto.getFormInfo().getDataContent().replace(dto.getFormInfo().getFormId().toString(), formId.toString());
                    dataContent = dataContent.replace(dto.getFormInfo().getModelId().toString(), cformModel.getId().toString());
                    cf.setDataContent(dataContent);
                }

                cf.setAddDefContent(dto.getFormInfo().getAddDefContent());
                cf.setAddPubContent(dto.getFormInfo().getAddPubContent());
                if (dto.getFormInfo().getAddDataContent() != null) {
                    String addDataContent = dto.getFormInfo().getAddDataContent().replace(dto.getFormInfo().getFormId().toString(), formId.toString());
                    addDataContent = addDataContent.replace(dto.getFormInfo().getModelId().toString(), cformModel.getId().toString());
                    cf.setAddDataContent(addDataContent);
                }

                cf.setAddFormItem(dto.getFormInfo().getAddFormItem());

                cf.setAppDefContent(dto.getFormInfo().getAppDefContent());
                cf.setAppPubContent(dto.getFormInfo().getAppPubContent());
                if (dto.getFormInfo().getAppDataContent() != null) {
                    String appDataContent = dto.getFormInfo().getAppDataContent().replace(dto.getFormInfo().getFormId().toString(), formId.toString());
                    appDataContent = appDataContent.replace(dto.getFormInfo().getModelId().toString(), cformModel.getId().toString());
                    cf.setAppDataContent(appDataContent);
                }

                cf.setAddIsRelease("0");
                cf.setAppIsRelease("0");
                cf.setIsRelease("0");
            } else if (type == 1){ //起草设计导入
                cf.setAddDefContent(dto.getFormInfo().getAddDefContent());
                cf.setAddPubContent(dto.getFormInfo().getAddPubContent());
                if (dto.getFormInfo().getAddDataContent() != null) {
                    String addDataContent = dto.getFormInfo().getAddDataContent().replace(dto.getFormInfo().getFormId().toString(), formId.toString());
                    addDataContent = addDataContent.replace(dto.getFormInfo().getModelId().toString(), cformModel.getId().toString());
                    cf.setAddDataContent(addDataContent);
                }
                cf.setAddFormItem(dto.getFormInfo().getAddFormItem());
                cf.setIsRelease(dto.getFormInfo().getIsRelease());
            }else if (type == 2){ //处理单导入
                cf.setDefContent(dto.getFormInfo().getDefContent());
                cf.setPubContent(dto.getFormInfo().getPubContent());
                if (dto.getFormInfo().getDataContent() != null) {
                    String dataContent = dto.getFormInfo().getDataContent().replace(dto.getFormInfo().getFormId().toString(), formId.toString());
                    dataContent = dataContent.replace(dto.getFormInfo().getModelId().toString(), cformModel.getId().toString());
                    cf.setDataContent(dataContent);
                }
                cf.setAddIsRelease(dto.getFormInfo().getAddIsRelease());

            }else if (type == 3){ //移动端表单导入
                cf.setAppDefContent(dto.getFormInfo().getAppDefContent());
                cf.setAppPubContent(dto.getFormInfo().getAppPubContent());
                if (dto.getFormInfo().getAppDataContent() != null) {
                    String appDataContent = dto.getFormInfo().getAppDataContent().replace(dto.getFormInfo().getFormId().toString(), formId.toString());
                    appDataContent = appDataContent.replace(dto.getFormInfo().getModelId().toString(), cformModel.getId().toString());
                    cf.setAppDataContent(appDataContent);
                }
                cf.setAppIsRelease(dto.getFormInfo().getAppIsRelease());
            }

            cformFormRepository.updateById(cf);
            //新增表单字段数据
//            LambdaQueryWrapper<CformField> lambdaQueryWrapper = new LambdaQueryWrapper<>();
//            lambdaQueryWrapper.eq(CformField::getDomainId, formId.toString());
//            cformFieldRepository.delete(lambdaQueryWrapper);
//            dto.getCformField().forEach(item->{
//                item.setId("");
//                item.setDomainId(formId.toString());
//                item.setFormId(cf.getAddFormId().toString());
//                item.setModelId(cformModel.getId());
//                if(item.getAddFormId() == dto.getFormInfo().getAddFormId()){
//                    item.setAddFormId(cf.getAppFormId());
//                }else if(item.getAddFormId() == dto.getFormInfo().getAddFormId()){
//                    item.setAddFormId(cf.getAddFormId());
//                }else if(item.getAddFormId() == null){
//                    item.setFormId(formId.toString());
//                }
//                cformFieldRepository.insert(item);
//            });
            //新增表单表格数据
//            cformDomainRepository.deleteByFormId(formId.toString());
//            dto.getCformDomain().forEach(item->{
//                item.setFormId(formId.toString());
//                item.setModelId(cformModel.getId());
//                cformDomainRepository.insert(item);
//            });
            return true;
        } catch (IOException e) {
            e.printStackTrace();
            return false;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    @Override
    public String selectAddFormItem(String formId) {
        LambdaQueryWrapper<CformForm> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(CformForm::getFormId,formId).or()
                .eq(CformForm::getAddFormId,formId);
        lambdaQueryWrapper.select(CformForm::getAddFormItem);
        CformForm cf = cformFormRepository.selectOneNoAdd(lambdaQueryWrapper);
        if (cf != null){
            return cf.getAddFormItem();
        }else {
            return "";
        }
    }

    /**
     * 获取模型
     *
     * @param formId
     * @return
     */
    @Override
    public String getModelDataByFormId(String formId) {
        Object va = redisUtil.get(RedisKeyConstant.CFORM_MODEL + formId);
        if (!Objects.isNull(va)){
            return (String) va;
        }
        LambdaQueryWrapper<CformForm> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(CformForm::getFormId, formId).or().eq(CformForm::getAddFormId, formId).select(CformForm::getModelData);
        CformForm cf = cformFormRepository.selectOneNoAdd(lambdaQueryWrapper);
        redisUtil.set(RedisKeyConstant.CFORM_MODEL + formId,cf.getModelData());
        return cf.getModelData();
    }


    @Override
    public Map<String, Object> renderCfrom(RenderCformDTO renderCformDTO)  {
        String type = renderCformDTO.getType();
        String formDataId = renderCformDTO.getFormDataId();
        Map<String, Object> result = new HashMap<>();
        String formId = renderCformDTO.getFormId();
        String subUserFormId = formId;   //子表单中使用的fromId
        CformForm cf  = null;
        if (StringUtils.isNotEmpty(formDataId)){
            LambdaQueryWrapper<CformForm> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(CformForm::getFormId,formId).or()
                    .eq(CformForm::getAddFormId,formId).select(CformForm::getFormId,CformForm::getAddFormId,CformForm::getId,CformForm::getAppFormId,CformForm::getPubContent);
            cf = cformFormRepository.selectOneNoAdd(queryWrapper);
            // 新增或者编辑获取详情的新增页面
            LambdaQueryWrapper<CformBizDataContent> bizDataContentLambdaQueryWrapper = new LambdaQueryWrapper<>();
            bizDataContentLambdaQueryWrapper.eq(CformBizDataContent::getBizId,formDataId);
            CformBizDataContent cformBizDataContent = cformBizDataContentService.selectOneNoAdd(bizDataContentLambdaQueryWrapper);
            int formVersion = 0;
            Long formVersionFormId= null;
            if ("edit".equals(type) || "detail".equals(type) || "draft".equals(type)){
                subUserFormId= String.valueOf(cf.getAddFormId());
                formId = cf.getFormId();
                if(cformBizDataContent!=null){
                    formVersion  = cformBizDataContent.getCurrentAddDataContentVersion();
                }
                formVersionFormId = Long.valueOf(cf.getAddFormId());
            }else if ("app".equals(type)){
                //临时写法
                subUserFormId= String.valueOf(cf.getAppFormId());
                if(cformBizDataContent!=null){
                    formVersion  = cformBizDataContent.getAppDataContentVersion();
                }
                formVersionFormId = Long.valueOf(cf.getAppFormId());
            }else {
                subUserFormId= String.valueOf(cf.getFormId());
                if(cformBizDataContent!=null){
                    formVersion  = cformBizDataContent.getCurrentDataContentVersion();
                }
                formVersionFormId = Long.valueOf(cf.getFormId());
            }
            String content = "";
            if(cformBizDataContent!=null){
                content = cformFormVersionService.getFormVersionDataContent(formVersionFormId,formVersion);
            }else{
                content = bizFormTemporaryStorageService.getDataContent(formDataId);
            }

            //暂存后调整处理单功能查询处理单json
            if("Storage".equals(type)){
                content  = cf.getPubContent();
            }

            if(StringUtils.isNotEmpty(content)){
                result.put("formJson",content.replace(Constants.FORM_TITLE_NAME, SecurityUtils.getCurrentCscpUserDetail().getCompanyName()));
            }else{
                result.put("formJson",content);
            }

        }
        if (StringUtils.isEmpty(formDataId) || StringUtils.isEmpty(MapUtil.getStr(result, "formJson"))){
            LambdaQueryWrapper<CformForm> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(CformForm::getFormId,formId).or()
                    .eq(CformForm::getAddFormId,formId);
            cf = cformFormRepository.selectOneNoAdd(queryWrapper);
            if (cf == null) {
                throw new CustomException("【" + formId + "】表单不存在！");
            }
            if (StringUtils.isEmpty(cf.getPubContent()) && StringUtils.isEmpty(cf.getAddPubContent()) ){
                throw new CustomException("表单未发布！");
            }
            //  如果是起草和详情页面，去起草的数据，详情页面的标识为 draft
            if ("app".equals(type)){
                //替换标题
                String appPubContent  = cf.getAppPubContent();
                if (StringUtils.isNotEmpty(appPubContent)){
                    result.put("formJson",appPubContent.replace(Constants.FORM_TITLE_NAME, SecurityUtils.getCurrentCscpUserDetail().getCompanyName()));
                }else {
                    result.put("formJson", appPubContent);
                }
                subUserFormId = String.valueOf(cf.getAppFormId());
            }else if ("sign".equals(type)){
                //替换标题
                String pubContentubContent  = cf.getPubContent();
                if (StringUtils.isNotEmpty(pubContentubContent)){
                    result.put("formJson",pubContentubContent.replace(Constants.FORM_TITLE_NAME, SecurityUtils.getCurrentCscpUserDetail().getCompanyName()));
                }else {
                    result.put("formJson", cf.getPubContent());

                }
            }else if ( StringUtils.isEmpty(formDataId) || "draft".equals(type)){
                //如果是第一次起草的时候传入的，给起草的表单
                String addPubContent  = cf.getAddPubContent();
                if (StringUtils.isNotEmpty(addPubContent)){
                    result.put("formJson",addPubContent.replace(Constants.FORM_TITLE_NAME, SecurityUtils.getCurrentCscpUserDetail().getCompanyName()));
                }else {
                    result.put("formJson", cf.getAddPubContent());

                }
                subUserFormId = String.valueOf(cf.getAddFormId());
            }else if (("edit".equals(type) || "detail".equals(type)) && StringUtils.isNotEmpty(formDataId)){
                subUserFormId = String.valueOf(cf.getAddFormId());
                //如果是第一次起草的时候传入的，给起草的表单
                String addPubContent  = cf.getAddPubContent();
                if (StringUtils.isNotEmpty(addPubContent)){
                    result.put("formJson",addPubContent.replace(Constants.FORM_TITLE_NAME, SecurityUtils.getCurrentCscpUserDetail().getCompanyName()));
                }else {
                    result.put("formJson", cf.getAddPubContent());

                }
            }else if (StringUtils.isEmpty(type)){
                //替换标题
                String pubContentubContent  = cf.getPubContent();
                if (StringUtils.isNotEmpty(pubContentubContent)){
                    result.put("formJson",pubContentubContent.replace(Constants.FORM_TITLE_NAME, SecurityUtils.getCurrentCscpUserDetail().getCompanyName()));
                }else {
                    result.put("formJson", cf.getPubContent());

                }
            }
        }
        //获取表单数据
        CformModel cfromModel = cformModelRepository.queryModelByFormId(cf.getFormId());
        if (Constants.MODEL_RELEASE_0.equals(cfromModel.getIsRelease())) {
            throw new CustomException("表单模型未发布！");
        }

        if (StringUtils.isEmpty(cfromModel.getModelData())) {
            throw new CustomException("模型表名不存在！");
        }


        //若为本地存储,检查是否有表
        List<Map<String, Object>> tableList = cformModelService.getDatabaseTable(tableSchema, cfromModel.getModelData());
        if ((tableList == null || tableList.size() == 0) && cfromModel.getStoreType().equals(Constants.MODEL_STORE_0)) {
            throw new CustomException("表单物理数据不存在或模型未发布！");
        }
        boolean formDataIsNull = false;
        if (StringUtils.isNotEmpty(formDataId) && cfromModel.getStoreType().equals(Constants.MODEL_STORE_0)) {
            String realAppFormId = cf.getFormId();
            if ("app".equals(type)){
                realAppFormId = String.valueOf(cf.getAppFormId());
            }
            Map<String, Object> formData = cFormDataService.getFormData(formDataId, cf.getFormId(),subUserFormId,renderCformDTO.getSubFormDataId(),realAppFormId);
            if (MapUtils.isEmpty(formData)) {
                formDataIsNull = true;
            }
            result.put("formData", formData);
        }

        if (StringUtils.isEmpty(formDataId) || formDataIsNull) {
            Map<String, Object> resMap = new HashMap<>();
            Map<String, Object> mainMap = new HashMap<>();
            Map<String, Object> formData = new HashMap<>();
            Map<String, Object> subMap = new HashMap<>();

            // 如果从收件箱过来，直接去收件箱表中查询数据，
            String  inboxId = renderCformDTO.getInboxId();
            if (StringUtils.isNotEmpty(inboxId)){
               // 在收件箱表中找数据
                formData = cFormDataService.getFormDataByInbox(inboxId );
                String formDataId2 = formData.get("formDataId").toString();
                String formId2 = formData.get("formId").toString();
                Map<String, Object> formData2 = cFormDataService.getMainFormData(formDataId2, formId2,subUserFormId);
                if(formData2!=null){
                    formData2.remove("id");
                    formData2.remove("title");
                    formData.putAll(formData2);
                }
            }else{
                List<CformField> listCfromField = cformFieldService.getCfromFieldListNoSubByAddFormId(cf.getAddFormId());
                for (CformField item : listCfromField) {
                    if (StringUtils.isNotBlank(item.getModelItemId())) {
                        formData.put(item.getModelItemId(), "");
                    }
//                formData.put(item.getModelItemId(),"");
                }

                //动态行数量
                List<CformDomain> cfromDomainList = cformDomainRepository.queryCfromDomainListByParentId(cf.getId());

                if (cfromDomainList != null && cfromDomainList.size() > 0) {
                    //获取渲染的json字符串
                    String formJson = result.get("formJson").toString();
                    String finalFormId = formId;
                    cfromDomainList.forEach(d -> {
                        String domainId=d.getDomainId();
                        //判断子表列名domainId是否包含在渲染的字符串中，包含才展示出来
                        if(formJson.contains(domainId)){
                            //获取每个动态行的模型项
                            List<CformField> listSubCfromField = cformFieldService.getSubCfromFieldList(d.getDomainId(), d.getFormId());
                            Map<String, Object> fieldMap = new HashMap<>();
                            for (CformField item : listSubCfromField) {
                                fieldMap.put(item.getModelItemId(), "");
                            }
//                            //添加一个渲染id字段
//                            if(null!=fieldMap&&fieldMap.size()>0){
//                                fieldMap.put("id", "");
//                            }
                            List<Map<String, Object>> tempList = new ArrayList<>();
                            tempList.add(fieldMap);
                            subMap.put(d.getDomainId(), tempList);
                        }
                    });
                }
            }

            mainMap.put("modelId", cfromModel.getModelDataType());
            try {


            if(formData.containsKey("createTime")){
                String createTime = formData.get("createTime").toString().replace(".0","");
                formData.put("createTime",createTime);
            }
            if(formData.containsKey("updateTime")){
                String updateTime = formData.get("updateTime").toString().replace(".0","");
                formData.put("updateTime",updateTime);
            }
            }catch (Exception e){
                e.printStackTrace();
            }

            mainMap.put(formId, formData);
            resMap.put("M", mainMap);
            resMap.put("S", subMap);
            resMap.put("formDataId", StringUtils.isEmpty(formDataId) ? SnowflakeIdUtil.getSnowFlakeLongId() : formDataId);
            result.put("formData", resMap);
        }

        return result;
    }
    @Override
    public Boolean selectOrgModel(Long formId) {
        CscpOrg org = cscpOrgService.getById(SecurityUtils.getCurrentCompanyId());
        if(org.getSplitview() == 1) {
            CformForm cformForm = cformFormRepository.selectById(formId);
            String modelName = cformForm.getModelData();
            if (com.ctsi.hndx.utils.StringUtils.isNotEmpty(org.getModelDataType())) {
                String[] split = org.getModelDataType().trim().split(",");
                List<String> asList = Arrays.asList(split);
                if(asList.contains(modelName)){
                    return true;
                }
            }
            return false;
        }

        return false;
    }
}
