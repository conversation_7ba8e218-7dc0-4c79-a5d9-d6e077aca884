package com.ctsi.hndx.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;

/**
 * @ProjectName: biyi-oa-server
 * @Package: com.ctsi.hndx.fieldaccess.enums
 * @ClassName: FieldAccessAlignEnum
 * @Author: houting
 * @Description: 字段权限控制的枚举
 * @Date: 2022/2/25 11:08
 * @Version: 1.0
 */
public enum FieldAccessAlignEnum {

    center("center"),

    left("left"),

    right("right");

    @EnumValue
    private String key;


    FieldAccessAlignEnum(String key) {

        this.key = key;
    }


    public String getKey() {
        return key;
    }

}
