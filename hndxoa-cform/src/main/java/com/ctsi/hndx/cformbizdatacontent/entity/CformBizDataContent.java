package com.ctsi.hndx.cformbizdatacontent.entity;

import com.ctsi.hndx.common.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 与cscp_proc_base表关联，存储表单数据，避免大文本字段引起查询
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-14
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value="cformBizDataContentDTO对象", description="与业务表关联，存储表单数据，避免大文本字段引起查询")
public class CformBizDataContent extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 当前处理单的表单数据
     */
    @ApiModelProperty(value = "当前处理单的表单数据版本号")
    private int currentDataContentVersion;

    /**
     * 当前新增的表单
     */
    @ApiModelProperty(value = "当前新增的表单版本号")
    private int  currentAddDataContentVersion;

    /**
     * app表单数据
     */
    @ApiModelProperty(value = "app表单数据版本号")
    private int appDataContentVersion;

    /**
     * 业务表的主键id
     */
    @ApiModelProperty(value = "流程关联业务表的主键")
    private Long bizId;


    /**
     * 业务表的主键id
     */
    @ApiModelProperty(value = "与流程关联的业务cscp_proc_base表的主键id，不与流程关联业务为空")
    private Long processBaseId;

    @ApiModelProperty(value = "对应的formId")
    private String formId;

}
