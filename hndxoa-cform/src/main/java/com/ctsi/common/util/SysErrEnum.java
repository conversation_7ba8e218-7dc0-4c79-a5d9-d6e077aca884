package com.ctsi.common.util;


/**
 * 系统错误码枚举 0-99 范围
 *
 * <AUTHOR>
 */
public enum SysErrEnum implements BaseErrEnumInterface {
    SUCCESS(0, "操作成功"),
    ERROR(1, "其他错误"),
    UNAUTHORIZED_OPERATION(2, "无权操作"),
    METHOD_NOT_SUPPORT(3, "不支持的访问方式"),
    PARAM_NOT_NULL(4, "参数不能为空"),
    TOKEN_TIME_OUT(5, "Token超时"),
    PAGE_NOT_FOUND(6, "404,访问地址不存在"),
    DECRYPT_ERROR(7, "解密失败"),
    ENCRYPT_ERROR(8, "加密失败"),
    EXISTS_CONTENT(9,"该类型下存在内容不允许删除"),
	BAD_STRATEGY(10,"错误的抓取策略配置"),
	PROCESS_ERROR(11,"处理数据出错"),
	RECORD_EXISTS(12,"记录已存在"),
    FILE_TOO_LARGE(13,"超出指定大小限制");
    /**
     * 错误码
     */
    int code;
    /**
     * 消息
     */
    String msg;

    SysErrEnum(int code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    @Override
    public String msg() {
        return this.msg;
    }

    @Override
    public int code() {
        return this.code;
    }
}
