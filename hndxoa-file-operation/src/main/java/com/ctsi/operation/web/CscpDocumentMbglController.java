package com.ctsi.operation.web;

import com.ctsi.hndx.annotations.ResponseResultVo;
import com.ctsi.hndx.common.BaseController;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.constant.SysConfigConstant;
import com.ctsi.hndx.enums.DBOperation;
import com.ctsi.hndx.enums.FileFormat;
import com.ctsi.hndx.exception.BusinessException;
import com.ctsi.hndx.result.ResultCode;
import com.ctsi.hndx.result.ResultVO;
import com.ctsi.hndx.tsysconfig.service.ISysConfigService;
import com.ctsi.hndx.utils.FileNameUtil;
import com.ctsi.operation.domain.CscpDocumentMbgl;
import com.ctsi.operation.domain.dto.*;
import com.ctsi.operation.service.ICscpDocumentMbglService;
import com.ctsi.ssdc.annotation.OperationLog;
import com.ctsi.ssdc.model.PageResult;
import com.ctsi.ssdc.util.StreamUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URISyntaxException;
import java.net.UnknownHostException;
import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 正文模板管理
 * </p>
 *
 * <AUTHOR>
 * @Description 正文模板管理
 * @Date 2023/04/13
 */
@Slf4j
@RestController
@ResponseResultVo
@RequestMapping("/api/cscpDocumentMb")
@Api(value = "正文模板管理", tags = "正文模板管理接口")
public class CscpDocumentMbglController extends BaseController {

    private static final String ENTITY_NAME = "CscpDocumentMbgl";

    @Autowired
    private ICscpDocumentMbglService cscpDocumentMbglService;

    @Autowired
    private ISysConfigService sysConfigService;

    /**
     * 新增数据.
     */
    @PostMapping("/create")
    @ApiOperation(value = "新增", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.ADD,message = "新增正文模板管理数据")
    public ResultVO<CscpDocumentMbglDTO> create(@RequestBody CscpDocumentMbglDTO cscpDocumentMbglDTO) throws URISyntaxException {
        CscpDocumentMbglDTO result = cscpDocumentMbglService.create(cscpDocumentMbglDTO);
        return ResultVO.success(result);
    }

    /**
     * 更新存在数据.
     */
    @PostMapping("/update")
    @ApiOperation(value = "更新存在数据", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.UPDATE,message = "更新正文模板管理数据")
    public ResultVO update(@RequestBody CscpUpdataDocumentMbglDTO cscpUpdataDocumentMbglDTO) {
        Assert.notNull(cscpUpdataDocumentMbglDTO.getId(), "general.IdNotNull");
        int count = cscpDocumentMbglService.update(cscpUpdataDocumentMbglDTO);
        if (count > 0) {
            return ResultVO.success();
        } else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }

    /**
     * 删除存在数据.
     */
    @DeleteMapping("/delete")
    @ApiOperation(value = "删除存在数据", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.DELETE,message = "删除正文模板管理数据")
    public ResultVO delete(@RequestBody List<Long> id) {
        int count = cscpDocumentMbglService.delete(id);
        if (count > 0) {
            return ResultVO.success();
        } else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }

    /**
     * 查询单条数据.
     */
    @GetMapping("/get/{id}")
    @ApiOperation(value = "查询单条数据", notes = "传入参数")
    public ResultVO get(@PathVariable Long id) {
        CscpResDocumentMbglSingleDTO cscpResDocumentMbglSingleDTO = cscpDocumentMbglService.findOne(id);
        return ResultVO.success(cscpResDocumentMbglSingleDTO);
    }

    /**
     * 查询多条数据分页.
     */
    @GetMapping("/queryCscpDocumentMbPage")
    @ApiOperation(value = "分页查询多条数据", notes = "传入参数")
    public ResultVO<PageResult<CscpDocumentMbglDTO>> queryCscpDocumentMbPage(CscpQueryCscpDocumentMbglPageDTO condition, BasePageForm basePageForm) {
        PageResult<CscpDocumentMbglDTO> cscpDocumentMbglDTOPageResult = cscpDocumentMbglService.queryListPage(condition, basePageForm);
        return ResultVO.success(cscpDocumentMbglDTOPageResult);
    }

    /**
     * 不分页获取当前用户具有的正文模板列表
     */
    @GetMapping("/queryCscpDocumentMb")
    @ApiOperation(value = "不分页获取当前用户具有的正文模板列表", notes = "传入参数")
    public ResultVO<List<QueryCscpDocumentMbglDTO>> queryCscpDocumentMb(HttpServletRequest request) throws UnknownHostException {
        List<CscpResDocumentMbGlListDTO> list = cscpDocumentMbglService.queryList();
        List<QueryCscpDocumentMbglDTO> link = new LinkedList<>();
        String inteWeburl = sysConfigService.getSysConfigValueByCode(SysConfigConstant.WEB_API_URL);
        for (CscpResDocumentMbGlListDTO c : list) {
            CscpDocumentMbgl cscpDocumentMbgl = cscpDocumentMbglService.getById(c.getId());
            QueryCscpDocumentMbglDTO build = QueryCscpDocumentMbglDTO.builder()
                    .path(inteWeburl + "/api/cscpDocumentMb/downloadFormFiles/" + c.getId()+"/"+c.getId()+"."+cscpDocumentMbgl.getExtName())
                    .text(c.getMbName())
                    .fileName(cscpDocumentMbgl.getFileName()).build();
            link.add(build);
        }
        return ResultVO.success(link.stream().filter(StreamUtil.distinctByKey(QueryCscpDocumentMbglDTO::getPath)).collect(Collectors.toList()));
    }

    @ApiOperation(value = "文件上传")
    @PostMapping(value = "/uploadFormFiles", headers = "content-type=multipart/form-data")
    @OperationLog(dBOperation = DBOperation.ADD, message = "文件上传")
    public ResultVO<List<CscpDocumentMbgl>> uploadFormFiles(@RequestParam("file") MultipartFile[] file,
                                                    @RequestParam("id") Long id) throws IOException {
        List<CscpDocumentMbgl> lRes = new ArrayList<>();
        for (MultipartFile f : file) {
            String extNameByFileName = FileNameUtil.getExtNameByFileName(f.getOriginalFilename());
            if (FileFormat.asMyEnum(extNameByFileName)) {
                CscpDocumentMbgl cscpDocumentMbgl = new CscpDocumentMbgl();
                cscpDocumentMbgl.setId(id);
                cscpDocumentMbglService.uploadFormFile(cscpDocumentMbgl, f);
                lRes.add(cscpDocumentMbgl);
            } else {
                throw new BusinessException("上传的文件必须是doc和docx");
            }
        }
        return ResultVO.success(lRes);
    }

    @ApiOperation(value = "下载原始文件")
    @GetMapping("/downloadFormFiles/{id}/{fileName}")
    public void downloadFormFiles(@PathVariable long id, @PathVariable String fileName,HttpServletResponse response) throws Exception {
        cscpDocumentMbglService.downOriginFile(id, response);
    }

    @ApiOperation(value = "删除文件")
    @DeleteMapping("/deleteFormFiles/{id}")
    public ResultVO<Boolean> deleteFormFiles(@PathVariable("id") Long id) {
        Integer count = cscpDocumentMbglService.deleteFormFiles(id);
        if (count <= 0) {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
        return ResultVO.success();

    }
}
