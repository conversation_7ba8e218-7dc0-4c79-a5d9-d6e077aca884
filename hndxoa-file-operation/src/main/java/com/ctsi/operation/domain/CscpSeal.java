package com.ctsi.operation.domain;

import com.ctsi.hndx.filestore.CscpFile;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-03
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value="CscpSeal对象", description="")
public class CscpSeal extends CscpFile {

    private static final long serialVersionUID = 1L;

    /**
     * 签章名称
     */
    @ApiModelProperty(value = "签章名称")
    private String signatureName;

    /**
     * 工作组集合
     */
    @ApiModelProperty(value = "工作组集合")
    private String workingGroups;


}
