package com.ctsi.operation.domain.dto;

import com.ctsi.hndx.common.BaseDtoEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "CscpResDocumentMbGlListDTO对象", description = "")
public class CscpResDocumentMbGlListDTO extends BaseDtoEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 模板编号
     */
    @ApiModelProperty(value = "模板编号")
    private String mbId;

    @ApiModelProperty(value = "模板名称")
    private String mbName;

    @ApiModelProperty(value = "工作组集合")
    private String workingGroups;


    @ApiModelProperty(value = "创建人id")
    private Long createBy;


    @ApiModelProperty(value = "创建人名称")
    private String createName;

    @ApiModelProperty(value = "创建日期")
    private LocalDateTime createTime;

}
