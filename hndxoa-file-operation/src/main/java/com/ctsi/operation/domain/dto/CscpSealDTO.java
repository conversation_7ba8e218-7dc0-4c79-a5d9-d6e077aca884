package com.ctsi.operation.domain.dto;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.ctsi.hndx.common.BaseDtoEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-03
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "CscpSealDTO对象", description = "")
public class CscpSealDTO extends BaseDtoEntity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "签章名称")
    private String signatureName;

    @ApiModelProperty(value = "工作组集合")
    private String workingGroups;

    @ApiModelProperty(value = "创建名称")
    private String createName;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;
}
