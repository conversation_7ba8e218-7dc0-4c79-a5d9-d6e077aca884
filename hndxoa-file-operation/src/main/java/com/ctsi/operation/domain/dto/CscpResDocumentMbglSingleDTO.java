package com.ctsi.operation.domain.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class CscpResDocumentMbglSingleDTO {

    @ApiModelProperty(value = "主键id")
    private Long id;

    /**
     * 模板编号
     */
    @ApiModelProperty(value = "模板编号")
    private String mbId;

    @ApiModelProperty(value = "模板名称")
    private String mbName;

    @ApiModelProperty(value = "工作组集合")
    private String workingGroups;

    @ApiModelProperty(value = "文件名称")
    private String fileName;

    @ApiModelProperty(value = "文件路径")
    private String fileUrl;


}
