package com.ctsi.operation.wps.entity.dto;

import com.ctsi.hndx.common.BaseDtoEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * <p>
 * 金山中台文件缓存表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-05
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value="TWpsFileDTO对象", description="金山中台文件缓存表")
public class TWpsFileDTO extends BaseDtoEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 表单数据id及业务主键id
     */
    @ApiModelProperty(value = "表单数据id及业务主键id")
    private Long formDataId;

    /**
     * 在附件和正文表中的id
     */
    @ApiModelProperty(value = "在附件和正文表中的id")
    private Long fileOriginId;

    /**
     * 文件名称
     */
    @ApiModelProperty(value = "文件名称")
    private String name;

    /**
     * 是否为合并后的文件：1是，0否
     */
    @ApiModelProperty(value = "是否为合并后的文件：1是，0否")
    private Integer isMergedFile;

    /**
     * 文档版本号
     */
    @ApiModelProperty(value = "文档版本号")
    private Long version;

    /**
     * 文件大小
     */
    @ApiModelProperty(value = "文件大小")
    private Long size;

    /**
     * 创建者id
     */
    @ApiModelProperty(value = "创建者id")
    private String creator;

    /**
     * 修改者id
     */
    @ApiModelProperty(value = "修改者id")
    private String modifier;

    /**
     * 文件路径
     */
    @ApiModelProperty(value = "文件路径")
    private String fileUrl;

    /**
     * 后缀名称
     */
    @ApiModelProperty(value = "后缀名称")
    private String extName;

    /**
     * 限制预览页数
     */
    @ApiModelProperty(value = "限制预览页数")
    private Long previewPages;

    /**
     * 最近修改时间，单位：秒
     */
    @ApiModelProperty(value = "最近修改时间，单位：秒")
    private LocalDateTime modifyTime;


}
