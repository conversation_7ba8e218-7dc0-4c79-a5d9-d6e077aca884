package com.ctsi.operation.service;

import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.filestore.CscpFileService;
import com.ctsi.operation.domain.CscpDocumentMbgl;
import com.ctsi.operation.domain.dto.*;
import com.ctsi.ssdc.model.PageResult;

import java.util.List;

public interface ICscpDocumentMbglService extends CscpFileService<CscpDocumentMbgl> {


    /**
     * 查询所有数据
     */
    PageResult<CscpDocumentMbglDTO> queryListPage(CscpQueryCscpDocumentMbglPageDTO condition, BasePageForm page);


    /**
     * 根据主键id获取单个对象
     */
    CscpResDocumentMbglSingleDTO findOne(Long id);

    /**
     * 新增
     */
    CscpDocumentMbglDTO create(CscpDocumentMbglDTO entity);

    /**
     * 更新
     */
    int update(CscpUpdataDocumentMbglDTO entity);

    /**
     * 删除
     */
    int delete(List<Long> id);


    List<CscpResDocumentMbGlListDTO> queryList();

    /**
     * 删除文件
     */
    Integer deleteFormFiles(Long id);
}
