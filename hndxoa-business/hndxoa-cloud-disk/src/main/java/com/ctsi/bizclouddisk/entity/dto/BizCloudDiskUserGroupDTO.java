package com.ctsi.bizclouddisk.entity.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @ClassName:BizCloudDiskSelectedDTO
 * @descript:
 * @author: song
 * @date:2022/2/8
 */

@Data
@ApiModel(value="人员群组", description="包含人员、部门、单位信息")
public class BizCloudDiskUserGroupDTO {

    private static final long serialVersionUID = 1L;

    /**
     * 用户id
     */
    @ApiModelProperty(value = "用户id")
    private Long userId;

    /**
     * 用户姓名
     */
    @ApiModelProperty(value = "用户姓名")
    private String userName;

    /**
     * 被分享用户部门id
     */
    @ApiModelProperty(value = "被分享用户部门id")
    private Long userDeparmentId;

    /**
     * 被分享用户部门名称
     */
    @ApiModelProperty(value = "被分享用户部门名称")
    private String userDeparmentName;

    /**
     * 被分享用户单位id
     */
    @ApiModelProperty(value = "被分享用户单位id")
    private Long userCompanyId;

    /**
     * 被分享用户单位名称
     */
    @ApiModelProperty(value = "被分享用户单位名称")
    private String userCompanyName;

    // /**
    //  * 机构名称
    //  */
    // @ApiModelProperty(value = "机构名称")
    // private String orgName;
    //
    // /**
    //  * 机构编码
    //  */
    // @ApiModelProperty(value = "机构编码")
    // private Long orgCode;
}
