package com.ctsi.personMatters.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ctsi.hndx.common.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDate;

/**
 * <p>
 * 人事档案表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-15
 */
@Getter
@Setter
@TableName("t_person_dossier")
@ApiModel(value = "TPersonDossier对象", description = "人事档案表")
public class TPersonDossier extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("姓名")
    private String xingMing;

    @ApiModelProperty("性别")
    private String xingBie;
    @ApiModelProperty("手机号")
    private String mobile;
    @ApiModelProperty("出生年月")
    private String chuShengNianYue;

    @ApiModelProperty("民族")
    private String mingZu;

    @ApiModelProperty("籍贯")
    private String jiGuan;

    @ApiModelProperty("出生地")
    private String chuShengDi;

    @ApiModelProperty("入党时间")
    private String ruDangShiJian;

    @ApiModelProperty("参加工作时间")
    private String canJiaGongZuoShiJian;

    @ApiModelProperty("健康状况")
    private String jianKangZhuangKuang;

    @ApiModelProperty("专业技术职务")
    private String zhuanYeJiShuZhiWu;

    @ApiModelProperty("熟悉专业有何特长")
    private String shuXiZhuanYeYouHeZhuanChang;

    @ApiModelProperty("全日制学历")
    private String quanRiZhiJiaoYuXueLi;

    @ApiModelProperty("全日制学位")
    private String quanRiZhiJiaoYuXueWei;

    @ApiModelProperty("全日制学历毕业院校")
    private String quanRiZhiJiaoYuXueLiBiYeYuanXiaoXi;

    @ApiModelProperty("全日制学位毕业院校")
    private String quanRiZhiJiaoYuXueWeiBiYeYuanXiaoXi;

    @ApiModelProperty("在职教育学历")
    private String zaiZhiJiaoYuXueLi;

    @ApiModelProperty("在职教育学位")
    private String zaiZhiJiaoYuXueWei;

    @ApiModelProperty("在职教育学历毕业学校")
    private String zaiZhiJiaoYuXueLiBiYeYuanXiaoXi;

    @ApiModelProperty("在职教育学位毕业学校")
    private String zaiZhiJiaoYuXueWeiBiYeYuanXiaoXi;

    @ApiModelProperty("现任职务")
    private String xianRenZhiWu;

    @ApiModelProperty("简历")
    private String jianli;

    @ApiModelProperty("奖惩情况")
    private String jiangChengQiongKuang;

    @ApiModelProperty("年度考核结果")
    private String nianDuKaoHeJieGuo;

    @ApiModelProperty("计算年龄时间")
    private String jiSuanNianLingShiJian;

    @ApiModelProperty("填表时间")
    private String tianBiaoShiJian;

    @ApiModelProperty("填表人")
    private String tianBiaoRen;

    @ApiModelProperty("身份证")
    private String shenFenZheng;

    @ApiModelProperty("照片")
    private String zhaoPian;

    @ApiModelProperty("出生地")
    private String birthPlace;

    @ApiModelProperty("是否军转干部，0否 1是")
    private Integer isMilitaryTransfer;

    @ApiModelProperty("拟任职务职级")
    private String proposedPositionRank;

    @ApiModelProperty("拟免职务职级")
    private String proposedRemovalPositionRank;

    @ApiModelProperty("任现职务时间")
    @TableField(fill= FieldFill.UPDATE)
    private LocalDate currentPositionTime;

    @ApiModelProperty("现任职级")
    private String currentRank;

    @ApiModelProperty("任现职级时间")
    @TableField(fill= FieldFill.UPDATE)
    private LocalDate currentRankTime;

    @ApiModelProperty("任免理由")
    private String appointmentRemovalReason;

    @ApiModelProperty("机构id")
    private Long orgId;
    @ApiModelProperty("排序字段")
    private Integer sortBy;



}
