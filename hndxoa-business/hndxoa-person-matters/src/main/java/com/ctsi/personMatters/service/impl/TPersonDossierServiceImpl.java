package com.ctsi.personMatters.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.common.SysBaseServiceImpl;
import com.ctsi.hndx.exception.BusinessException;
import com.ctsi.hndx.tsysconfig.service.ISysConfigService;
import com.ctsi.hndx.utils.*;
import com.ctsi.personMatters.entity.BizDossierOrganization;
import com.ctsi.personMatters.entity.TPersonDossier;
import com.ctsi.personMatters.entity.TPersonDossierFamily;
import com.ctsi.personMatters.entity.dto.*;
import com.ctsi.personMatters.mapper.TPersonDossierFamilyMapper;
import com.ctsi.personMatters.mapper.TPersonDossierMapper;
import com.ctsi.personMatters.service.IBizDossierOrganizationService;
import com.ctsi.personMatters.service.IBizPersonDossierImportRecordService;
import com.ctsi.personMatters.service.ITPersonDossierFamilyService;
import com.ctsi.personMatters.service.ITPersonDossierService;
import com.ctsi.ssdc.model.PageResult;
import com.ctsi.ssdc.security.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.xml.bind.JAXBContext;
import javax.xml.bind.JAXBException;
import javax.xml.bind.Unmarshaller;
import java.io.File;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <p>
 * 人事档案表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-15
 */
@Service
@Slf4j
public class TPersonDossierServiceImpl extends SysBaseServiceImpl<TPersonDossierMapper, TPersonDossier> implements ITPersonDossierService {
    @Autowired
    private TPersonDossierMapper tPersonDossierMapper;
    @Autowired
    private ISysConfigService sysConfigService;
    @Autowired
    private IBizPersonDossierImportRecordService bizPersonDossierImportRecordService;
    @Autowired
    private IBizDossierOrganizationService bizDossierOrganizationService;
    @Autowired
    private TPersonDossierFamilyMapper tPersonDossierFamilyMapper;
    @Autowired
    private ITPersonDossierFamilyService tPersonDossierFamilyService;




    /**
     * 翻页
     *
     * @param entityDTO
     * @param basePageForm
     * @return
     */
    @Override
    public PageResult<TPersonDossierDTO> queryListPage(TPersonDossierDTO entityDTO, BasePageForm basePageForm) {
        // 查询处室单位
        BizDossierOrganizationDTO organizationDTO = new BizDossierOrganizationDTO();
        LambdaQueryWrapper<TPersonDossier> queryWrapper = new LambdaQueryWrapper();
        if(StringUtils.isNotEmpty(entityDTO.getOrganizationName())){//如果没有值，直接查所有的

            organizationDTO.setOrganizationName(entityDTO.getOrganizationName());
            List<BizDossierOrganizationDTO> organizationDTOS = bizDossierOrganizationService.queryOrgList(organizationDTO);
            if(CollUtil.isNotEmpty(organizationDTOS)){
                queryWrapper.in(TPersonDossier::getOrgId, organizationDTOS.stream().map(BizDossierOrganizationDTO::getId).collect(Collectors.toList()));
            }else if(StringUtils.isNotEmpty(entityDTO.getOrganizationName())){//如果匹配不到就需要过滤所有的数据
                queryWrapper.eq(TPersonDossier::getOrgId,-1);
            }
        }
        //设置条件
        queryWrapper.like(StrUtil.isNotBlank(entityDTO.getXingMing()), TPersonDossier::getXingMing, entityDTO.getXingMing());
        queryWrapper.like(StrUtil.isNotBlank(entityDTO.getMobile()), TPersonDossier::getMobile, entityDTO.getMobile());
        queryWrapper.like(StrUtil.isNotBlank(entityDTO.getXianRenZhiWu()), TPersonDossier::getXianRenZhiWu, entityDTO.getXianRenZhiWu());
        queryWrapper.like(StrUtil.isNotBlank(entityDTO.getCurrentRank()), TPersonDossier::getCurrentRank, entityDTO.getCurrentRank());

        IPage<TPersonDossier> pageData = tPersonDossierMapper.selectPageNoAdd(
                PageHelperUtil.getMPlusPageByBasePage(basePageForm), queryWrapper);
        //返回
        IPage<TPersonDossierDTO> data  = pageData.convert(entity -> BeanConvertUtils.copyProperties(entity,TPersonDossierDTO.class));

        for (TPersonDossierDTO record : data.getRecords()) {
            LambdaQueryWrapper<TPersonDossierFamily> queryFamily = new LambdaQueryWrapper();
            queryFamily.like(TPersonDossierFamily::getDossierId,record.getId());
            List<TPersonDossierFamily> tPersonDossierFamilies = tPersonDossierFamilyMapper.selectListNoAdd(queryFamily);
            List<TPersonDossierFamilyDTO> copy = ListCopyUtil.copy(tPersonDossierFamilies, TPersonDossierFamilyDTO.class);
            Item item = new Item();
            item.setTPersonDossierFamilyDTOs(copy);
            record.setItem(item);

            //查询出当前公司的名称访问
            BizDossierOrganization organization =bizDossierOrganizationService.getById(record.getOrgId());
            if(organization!=null){
                record.setOrganizationName(organization.getOrganizationName()==null?"":organization.getOrganizationName());
            }
              }
        return new PageResult<TPersonDossierDTO>(data.getRecords(),
                data.getTotal(), data.getCurrent());
    }

    /**
     * 列表查询
     *
     * @param entityDTO
     * @return
     */
    @Override
    public List<TPersonDossierDTO> queryList(TPersonDossierDTO entityDTO) {
        LambdaQueryWrapper<TPersonDossier> queryWrapper = new LambdaQueryWrapper();
        List<TPersonDossier> listData = tPersonDossierMapper.selectListNoAdd(queryWrapper);
        List<TPersonDossierDTO> tPersonDossierDTOList = ListCopyUtil.copy(listData, TPersonDossierDTO.class);
        return tPersonDossierDTOList;
    }

    /**
     * 列表查询
     *
     * @param entityDTO
     * @return
     */
    @Override
    public List<BizPersonDossierDTO> queryDossierList(TPersonDossierDTO entityDTO) {
        LambdaQueryWrapper<TPersonDossier> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.like(StrUtil.isNotBlank(entityDTO.getXingMing()),TPersonDossier::getXingMing, entityDTO.getXingMing());
        List<TPersonDossier> listData = tPersonDossierMapper.selectListNoAdd(queryWrapper);
        List<BizPersonDossierDTO> tPersonDossierDTOList = ListCopyUtil.copy(listData, BizPersonDossierDTO.class);

        for (BizPersonDossierDTO bizPersonDossierDTO : tPersonDossierDTOList) {
            bizPersonDossierDTO.setUserId(bizPersonDossierDTO.getId());
        }
        return tPersonDossierDTOList;
    }

    /**
     * 单个查询
     *
     * @param id the id of the entity
     * @return
     */
    @Override
    public TPersonDossierDTO findOne(Long id) {
        TPersonDossier  tPersonDossier =  tPersonDossierMapper.selectById(id);
        TPersonDossierDTO tPersonDossierDTO = BeanConvertUtils.copyProperties(tPersonDossier, TPersonDossierDTO.class);
        LambdaQueryWrapper<TPersonDossierFamily> queryFamily = new LambdaQueryWrapper();
        queryFamily.like(TPersonDossierFamily::getDossierId,tPersonDossierDTO.getId());
        List<TPersonDossierFamily> tPersonDossierFamilies = tPersonDossierFamilyMapper.selectListNoAdd(queryFamily);
        List<TPersonDossierFamilyDTO> copy = ListCopyUtil.copy(tPersonDossierFamilies, TPersonDossierFamilyDTO.class);
        for(TPersonDossierFamilyDTO dto : copy){
            dto.getAge();
        }
        Item item = new Item();
        item.setTPersonDossierFamilyDTOs(copy);
        tPersonDossierDTO.setItem(item);
        return  tPersonDossierDTO;
    }


    /**
     * 新增
     *
     * @param entityDTO the entity to create
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public TPersonDossierDTO create(TPersonDossierDTO entityDTO) {
        TPersonDossier tPersonDossier =  BeanConvertUtils.copyProperties(entityDTO,TPersonDossier.class);
        save(tPersonDossier);
        return  BeanConvertUtils.copyProperties(tPersonDossier,TPersonDossierDTO.class);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<TPersonDossierDTO> importPersonDossierZip(Long unitId, Long depId, MultipartFile file) {
        String fileExtendName = FileNameUtil.getExtNameByFileName(file.getOriginalFilename());
        if (StrUtil.isBlank(fileExtendName)){
            throw new BusinessException("文件类似错误！请上传 .zip 或 .xml 格式的文件");
        }
        if (file.getSize()==0L){
            throw new BusinessException("文件大小为空！");
        }


        String snowFlakeId = SnowflakeIdUtil.getSnowFlakeId();
        // 下载zip到本地处理
        String folderPath = "xmlImport";
        String osName = System.getProperty("os.name");
        log.info("操作系统=" + osName);
        if (osName.toLowerCase().contains("windows")) {
            folderPath = sysConfigService.getSysConfigValueByCode("oaweb.server.download.path.windows") +
                    File.separator + snowFlakeId;
        }
        if (osName.toLowerCase().contains("linux")) {
            folderPath = sysConfigService.getSysConfigValueByCode("oaweb.server.download.path.linux") +
                    File.separator + snowFlakeId;
        }
        File downloadFolder = new File(folderPath);
        if (!downloadFolder.exists()) {
            downloadFolder.mkdirs();
        }
        String zipFilePath = folderPath + File.separator + (snowFlakeId + ".zip");

        // 保存文件
        try {
            file.transferTo(new File(zipFilePath));
        }catch (Exception e){
            e.printStackTrace();
            throw new BusinessException("文件保存失败！");
        }
        List<String> xmlPaths =new ArrayList<>();

        //处理压缩包
        if("zip".equals(fileExtendName)) {
            try {
                xmlPaths = ZipFileUtil.unzip(zipFilePath, folderPath);
            }catch (Exception e){
                e.printStackTrace();
                throw new BusinessException("文件上传失败，请检查文件是否损坏！");
            }
        }else {
            xmlPaths.add(zipFilePath);
        }

        // 循环解析xml
        List<TPersonDossierDTO> tPersonDossierDTOList = new ArrayList<>();
        for (String imgPath : xmlPaths) {
            try {
                JAXBContext context = JAXBContext.newInstance(TPersonDossierDTO.class);
                Unmarshaller unmarshaller = context.createUnmarshaller();
                TPersonDossierDTO entity = (TPersonDossierDTO) unmarshaller.unmarshal(
                        new File(imgPath));

                tPersonDossierDTOList.add(entity);
            }catch (JAXBException jaxbException){
                jaxbException.printStackTrace();
            }
        }

        List<TPersonDossier> savelist = ListCopyUtil.copy(tPersonDossierDTOList, TPersonDossier.class);
        List<String> idCardList = savelist.stream().map(TPersonDossier::getShenFenZheng).collect(Collectors.toList());

        LambdaQueryWrapper<TPersonDossier> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.in(TPersonDossier::getShenFenZheng, idCardList);
        List<TPersonDossier> listData = tPersonDossierMapper.selectListOnlyAddTenantId(queryWrapper);

        // 查询最大排序号
        List<TPersonDossier> tPersonDossiers = tPersonDossierMapper.selectListOnlyAddTenantId(new LambdaQueryWrapper<TPersonDossier>().orderByDesc(TPersonDossier::getSortBy).last("limit 1"));
        int sort = 0;
        if (CollUtil.isNotEmpty(tPersonDossiers) && tPersonDossiers.get(0)!=null){
            TPersonDossier tPersonDossier = tPersonDossiers.get(0);
            sort = tPersonDossier.getSortBy()!=null? tPersonDossier.getSortBy() + 1 : 1;
        }
        //tPersonDossierDTOList = ListCopyUtil.copy(listData, TPersonDossierDTO.class);

        // 创建一个map方便查找是否存在相同身份证号的记录
        Map<String, TPersonDossier> listDataMap = listData.stream()
                .collect(Collectors.toMap(TPersonDossier::getShenFenZheng, dossier -> dossier));

        for (TPersonDossierDTO dto : tPersonDossierDTOList) {
            TPersonDossier dossier = BeanConvertUtils.copyProperties(dto, TPersonDossier.class);
            TPersonDossier existingDossier = listDataMap.get(dossier.getShenFenZheng());
            dossier.setOrgId(unitId);
            sort = sort + 1;
            dossier.setSortBy(sort);
            // 更新成功与否，如果需要记录日志的话进行相应处理。
            BizPersonDossierImportRecordDTO dossierImportRecord = new BizPersonDossierImportRecordDTO();
            dossierImportRecord.setOperator(SecurityUtils.getCurrentRealName());
            dossierImportRecord.setOperatorUnit(SecurityUtils.getCurrentCscpUserDetail().getDepartmentName());
            dossierImportRecord.setOrgId(unitId);

            Long dossierId;
            //处理照片
            if (StrUtil.isNotBlank(dossier.getZhaoPian())){
                // 正则表达式匹配常见的图片格式的Base64编码前缀
                String imagePrefixPattern = "^data:image/(gif|jpg|jpeg|tiff|png|bmp);base64,";
                // 编译正则表达式
                Pattern pattern = Pattern.compile(imagePrefixPattern);
                // 检查字符串是否匹配正则表达式
                try {
                    boolean b = pattern.matcher(dossier.getZhaoPian()).find();
                    if (!b){
                        String imagePrefix = "data:image/png;base64,";
                        // 如果不包含，则添加前缀
                        dossier.setZhaoPian(imagePrefix + dossier.getZhaoPian());
                    }
                }catch (Exception e){
                    log.error("照片解析失败");
                }
            }

            if (existingDossier != null) {

                dossierId = existingDossier.getId();
                dossier.setId(dossierId);
                // 如果存在相同的身份证号，执更新操作
                //LambdaUpdateWrapper<TPersonDossier> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
                //lambdaUpdateWrapper.eq(TPersonDossier::getShenFenZheng, dossier.getShenFenZheng());
                dossier.setUpdateBy(SecurityUtils.getCurrentUserId());
                dossier.setUpdateName(SecurityUtils.getCurrentRealName());
                int update = tPersonDossierMapper.updateById(dossier);

                // 更新家属信息前，先全部删除旧数据
                tPersonDossierFamilyMapper.deleteDossierFamilyByUserId(existingDossier.getId());

                //0-更新成功,1-更新失败
                dossierImportRecord.setLogStatus(update > 0 ? 0 : 1);
            } else {
                // 如果不存在，执行保存操作
                dossierId = SnowflakeIdUtil.getSnowFlakeLongId();
                dossier.setId(dossierId);
                try {
                    save(dossier);
                    // 设置导入状态
                    dossierImportRecord.setLogStatus(0);
                } catch (Exception e) {
                    dossierImportRecord.setLogStatus(1);
                }
            }

            // 保存家属信息
            if(dto.getItem()!=null){
                List<TPersonDossierFamilyDTO> tPersonDossierFamilyDTOList = dto.getItem().getTPersonDossierFamilyDTOs();
              if(tPersonDossierFamilyDTOList!=null&&tPersonDossierFamilyDTOList.size()>0){
                  tPersonDossierFamilyDTOList.forEach(i->{i.setDossierId(dossierId);});
                  tPersonDossierFamilyService.insertBatch(tPersonDossierFamilyDTOList);
              }

            }



            bizPersonDossierImportRecordService.create(dossierImportRecord);
        }

        return  tPersonDossierDTOList;
    }

    /**
     * 修改
     *
     * @param entity the entity to update
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int update(TPersonDossierDTO entity) {
        TPersonDossier tPersonDossier = BeanConvertUtils.copyProperties(entity,TPersonDossier.class);
        return tPersonDossierMapper.updateById(tPersonDossier);
    }

    /**
     * 删除
     *
     * @param id the id of the entity
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int delete(Long id) {
        return tPersonDossierMapper.deleteById(id);
    }


    /**
     * 验证是否存在
     *
     * @param TPersonDossierId
     * @return
     */
   /* @Override
    public boolean existByTPersonDossierId(Long TPersonDossierId) {
        if (TPersonDossierId != null) {
            LambdaQueryWrapper<TPersonDossier> queryWrapper = new LambdaQueryWrapper();
            queryWrapper.eq(TPersonDossier::getId, TPersonDossierId);
            List<TPersonDossier> result = tPersonDossierMapper.selectList(queryWrapper);
            return result.size() > 0;
        }
        return true;
    }*/

    /**
     * 批量新增
     *
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertBatch(List<TPersonDossierDTO> dataList) {
        List<TPersonDossier> result = ListCopyUtil.copy(dataList, TPersonDossier.class);
        return saveBatch(result);
    }


}
