package com.ctsi.personMatters.entity.dto;

import com.ctsi.hndx.common.BaseDtoEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * <p>
 * 干部人员导入记录
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-16
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value="BizPersonDossierImportRecordDTO对象", description="干部人员导入记录")
public class BizPersonDossierImportRecordDTO extends BaseDtoEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 操作人
     */
    @ApiModelProperty(value = "操作人")
    private String operator;

    /**
     * 操作人单位
     */
    @ApiModelProperty(value = "操作人单位")
    private String operatorUnit;

    /**
     * 日志状态：0-成果，1-失败
     */
    @ApiModelProperty(value = "日志状态：0-成果，1-失败")
    private Integer logStatus;

    private String departmentName;

    private String companyName;


    @ApiModelProperty("机构id")
    private Long orgId;

    @ApiModelProperty(value = "创建时间开始")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private String createTimeStart;

    @ApiModelProperty(value = "创建时间结束")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private String createTimeEnd;


}
