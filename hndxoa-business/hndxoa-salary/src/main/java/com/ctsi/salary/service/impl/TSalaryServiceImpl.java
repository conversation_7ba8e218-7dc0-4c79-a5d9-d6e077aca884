package com.ctsi.salary.service.impl;

import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ctsi.hndx.enums.FileBasePathName;
import com.ctsi.hndx.exception.BusinessException;
import com.ctsi.hndx.filestore.FileStoreTemplateService;
import com.ctsi.hndx.result.ResultVO;
import com.ctsi.hndx.tsysconfig.service.ISysConfigService;
import com.ctsi.hndx.utils.BeanConvertUtils;
import com.ctsi.hndx.utils.ListCopyUtil;
import com.ctsi.hndx.utils.StringUtils;
import com.ctsi.ssdc.admin.domain.CscpUser;
import com.ctsi.ssdc.admin.service.CscpUserService;
import com.ctsi.ssdc.model.PageResult;
import com.ctsi.salary.entity.TSalary;
import com.ctsi.salary.entity.dto.TSalaryDTO;
import com.ctsi.salary.mapper.TSalaryMapper;
import com.ctsi.salary.service.ITSalaryService;
import com.ctsi.hndx.common.SysBaseServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.utils.PageHelperUtil;
import com.ctsi.ssdc.security.SecurityUtils;
import com.ctsi.sysimport.domain.TSysImport;
import com.ctsi.sysimport.domain.dto.TSysImportDTO;
import com.ctsi.sysimport.mapper.TSysImportMapper;
import com.ctsi.sysimport.service.ISysImportService;
import com.ctsi.sysimport.util.SysImportTypeUtils;
import com.github.pagehelper.util.StringUtil;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import net.sf.json.JSONArray;
import org.apache.commons.collections.CollectionUtils;
import org.apache.poi.hssf.usermodel.*;
import org.apache.poi.hssf.util.HSSFColor;
import org.apache.poi.poifs.filesystem.POIFSFileSystem;
import org.apache.poi.ss.usermodel.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.File;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.*;

import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-10
 */
@Slf4j
@Service
public class TSalaryServiceImpl extends SysBaseServiceImpl<TSalaryMapper, TSalary> implements ITSalaryService {

    @Autowired
    private TSalaryMapper tSalaryMapper;

    @Autowired
    private ISysImportService iSysImportService;

    @Autowired
    private CscpUserService cscpUserService;

    @Autowired
    private TSysImportMapper tSysImportMapper;

    @Autowired
    private FileStoreTemplateService fileStoreTemplateService;

    @Autowired
    private ISysConfigService sysConfigService;

    /**
     * 翻页
     *
     * @param entityDTO
     * @param basePageForm
     * @return
     */
    @Override
    public PageResult<TSalaryDTO> queryListPage(TSalaryDTO entityDTO, BasePageForm basePageForm) {
        //设置条件
        LambdaQueryWrapper<TSalary> queryWrapper = new LambdaQueryWrapper();

        if(StringUtils.isNotEmpty(entityDTO.getSalaryYear())){
            queryWrapper.eq(TSalary::getSalaryYear, entityDTO.getSalaryYear());
        }
        if(StringUtils.isNotEmpty(entityDTO.getSalaryMonth())){
            queryWrapper.eq(TSalary::getSalaryMonth, entityDTO.getSalaryMonth());
        }
        if(StringUtils.isNotEmpty(entityDTO.getRealName())){
            queryWrapper.like(TSalary::getRealName, entityDTO.getRealName());
        }

        if(StringUtils.isNotEmpty(entityDTO.getMobile())){
            queryWrapper.like(TSalary::getMobile, entityDTO.getMobile());
        }
        queryWrapper.orderByDesc(TSalary::getCreateTime);

        IPage<TSalary> pageData = tSalaryMapper.selectPage(
             PageHelperUtil.getMPlusPageByBasePage(basePageForm), queryWrapper);
        //返回
        IPage<TSalaryDTO> data  = pageData.convert(entity -> BeanConvertUtils.copyProperties(entity,TSalaryDTO.class));

        return new PageResult<TSalaryDTO>(data.getRecords(),
            data.getTotal(), data.getCurrent());
    }

    /**
     * 列表查询
     *
     * @param entityDTO
     * @return
     */
    @Override
    public List<TSalaryDTO> queryList(TSalaryDTO entityDTO) {
        LambdaQueryWrapper<TSalary> queryWrapper = new LambdaQueryWrapper();
            List<TSalary> listData = tSalaryMapper.selectList(queryWrapper);
            List<TSalaryDTO> TSalaryDTOList = ListCopyUtil.copy(listData, TSalaryDTO.class);
        return TSalaryDTOList;
    }

    /**
     * 单个查询
     *
     * @param id the id of the entity
     * @return
     */
    @Override
    public TSalaryDTO findOne(Long id) {
        TSalary  tSalary =  tSalaryMapper.selectById(id);
        return  BeanConvertUtils.copyProperties(tSalary,TSalaryDTO.class);
    }


    /**
     * 新增
     *
     * @param entityDTO the entity to create
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public TSalaryDTO create(TSalaryDTO entityDTO) {
       TSalary tSalary =  BeanConvertUtils.copyProperties(entityDTO,TSalary.class);
        save(tSalary);
        return  BeanConvertUtils.copyProperties(tSalary,TSalaryDTO.class);
    }

    /**
     * 修改
     *
     * @param entity the entity to update
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int update(TSalaryDTO entity) {
        TSalary tSalary = BeanConvertUtils.copyProperties(entity,TSalary.class);
        return tSalaryMapper.updateById(tSalary);
    }

    /**
     * 删除
     *
     * @param id the id of the entity
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int delete(Long id) {
        return tSalaryMapper.deleteById(id);
    }


    /**
     * 验证是否存在
     *
     * @param TSalaryId
     * @return
     */
    @Override
    public boolean existByTSalaryId(Long TSalaryId) {
        if (TSalaryId != null) {
            LambdaQueryWrapper<TSalary> queryWrapper = new LambdaQueryWrapper();
            queryWrapper.eq(TSalary::getId, TSalaryId);
            List<TSalary> result = tSalaryMapper.selectList(queryWrapper);
            return result.size() > 0;
        }
        return true;
    }

    /**
    * 批量新增
    *
    */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertBatch(List<TSalaryDTO> dataList) {
        List<TSalary> result = ListCopyUtil.copy(dataList, TSalary.class);
        return saveBatch(result);
    }

    @SneakyThrows
    @Override
    public List<TSalaryDTO> getListSalary(MultipartFile file) {
        //成功的集合
        List<TSalaryDTO> tSalaryDTOlist = new ArrayList<>();

        // 新建一个保存失败记录的集合
        List<TSalaryDTO> failedList = new ArrayList<>();
        POIFSFileSystem poifs = new POIFSFileSystem(file.getInputStream());
        HSSFWorkbook wb = new HSSFWorkbook(poifs);
        HSSFSheet sheet = wb.getSheetAt(0);
        HSSFRow row = null;

        HSSFCellStyle cstyle = wb.createCellStyle();
        cstyle.setFillForegroundColor(HSSFColor.HSSFColorPredefined.RED.getIndex());
        cstyle.setFillBackgroundColor(HSSFColor.HSSFColorPredefined.RED.getIndex());
        cstyle.setAlignment(HorizontalAlignment.CENTER);
        cstyle.setVerticalAlignment(VerticalAlignment.CENTER);
        cstyle.setBorderBottom(BorderStyle.THIN);
        cstyle.setBorderLeft(BorderStyle.THIN);
        cstyle.setBorderRight(BorderStyle.THIN);
        cstyle.setBorderTop(BorderStyle.THIN);

        HSSFFont font = wb.createFont();
        font.setColor(HSSFColor.HSSFColorPredefined.RED.getIndex());
        cstyle.setFont(font);
        List errorList = new ArrayList();

        int dataStartRow = 4;
        //获取表格的表头
        row = sheet.getRow(dataStartRow - 1);
        String[] orignalStr = {"序号","手机号码","姓名"};     // 检验前三列是不是按照序号，手机号码，姓名排列
        int lastcell = row.getLastCellNum();
        int tableTop = orignalStr.length;
        StringBuffer salaryName = new StringBuffer("姓名");

        //比对表头数据
        for(int i = 0; i <=2; i++){
            String value = getCellValue(row.getCell(i));
            if(StringUtil.isNotEmpty(value)){
                if(!(orignalStr[i].equals(value.trim()))){
                    log.error("导入数据失败,请按照模板导入数据,从第五行开始数据,前3列依次为序号，手机号码,姓名");
                }
            }else{
                log.error("导入数据失败,请按照模板导入数据,从第五行开始数据,前3列依次为序号，手机号码,姓名");
            }
        }
        //剔除重复值
        Set<String> staffsSet = new HashSet<>(Arrays.asList(orignalStr));
        String[] topName = new String[lastcell-tableTop];
        for(int i = tableTop; i <lastcell; i++){
            String value = getCellValue(row.getCell(i));
            if(StringUtil.isEmpty(value)){
                log.error("有表头为空,请修改后再导入");
            }
            //判断是否有重复值
            value = value.trim();
            boolean isCopy = staffsSet.add(value);
            if(!isCopy){
                log.error(value+"数组列重复,请修改后再导入");
            }
            topName[i-tableTop] = value;
            salaryName.append(",");
            salaryName.append(value);
        }

        //获取文档标题
        String title = getCellValue(sheet.getRow(0).getCell(0));

        //获取工资的发放年份和发放月份
        String salaryYear =getCellValue(sheet.getRow(2).getCell(1));
        if(StringUtil.isEmpty(salaryYear)){
            log.error("工资发放年份为空或者非正确年份，请填写发放年份，例如2021");
        }
        salaryYear = salaryYear.trim();

        String salaryMonth =getCellValue(sheet.getRow(2).getCell(3));
        if(StringUtil.isEmpty(salaryMonth)){
            log.error("资发放月份为空或者非正确月份，请填写发放月份例如：9\"");
        }
        salaryMonth = salaryMonth.trim();

        //数去数据
        int rowNum = sheet.getLastRowNum();
        //从有效数据行开始
        for (int i = dataStartRow; i <= rowNum; i++) {

            row = sheet.getRow(i);
            if (row == null) {
                continue;
            }
            String phone = getCellValue(row.getCell(1));
            String userName =getCellValue(row.getCell(2));
            StringBuffer content = new StringBuffer(userName+"先生(女士)：您好!您"+title+",");
            StringBuffer salaryValue= new StringBuffer();
            salaryValue.append(userName);
            CscpUser getUserdata = cscpUserService.getUserdata(phone);
            try {
                if (StringUtil.isNotEmpty(phone) && StringUtil.isNotEmpty(userName)&& StringUtils.isMobile(phone)&&StringUtils.isNotNull(getUserdata)) {
                    for (int cell = 3; cell < lastcell; cell++) {
                        String value = getCellValue(row.getCell(cell));
                        if (StringUtil.isEmpty(value)) {
                            value = "0";
                        }
                        value = value.trim();
                        content.append(topName[cell - 3]);
                        content.append(":");
                        content.append(value);
                        content.append(";");
                        salaryValue.append(",");
                        salaryValue.append(value);
                    }

                    TSalaryDTO tSalaryDTO = new TSalaryDTO();
                    tSalaryDTO.setSalaryMonth(salaryMonth);
                    tSalaryDTO.setUserId(getUserdata.getId());
                    tSalaryDTO.setSalaryYear(salaryYear);
                    tSalaryDTO.setRealName(userName);
                    tSalaryDTO.setMobile(phone);
                    tSalaryDTO.setSalaryTableName(salaryName.toString());
                    tSalaryDTO.setSalaryTableValue(salaryValue.toString());
                    tSalaryDTOlist.add(tSalaryDTO);

                }else {
                    TSalaryDTO errorDTO = new TSalaryDTO();
                    errorDTO.setRealName(userName);
                    errorDTO.setMobile(phone);
                    errorDTO.setFailedReason("手机号或姓名与系统信息不匹配或重复");
                    failedList.add(errorDTO);
                }
            }catch (BusinessException e) {
                log.error(e.getMessage());
                TSalaryDTO errorDTO = new TSalaryDTO();
                errorDTO.setRealName(userName);
                errorDTO.setMobile(phone);
                errorDTO.setFailedReason(e.getMessage());
                failedList.add(errorDTO);
            }
        }
        TSysImportDTO tSysImportDTO = new TSysImportDTO();

        //失败条数
        tSysImportDTO.setFailedNo(failedList.size());

        //总条数
        tSysImportDTO.setTotalNo(tSalaryDTOlist.size()+failedList.size());

        tSysImportDTO.setSuccessNo(tSalaryDTOlist.size());
        // 导入数据类型
        tSysImportDTO.setType( SysImportTypeUtils.getImportType( FileBasePathName.USER_SALARY_IMPORT));
        tSysImportDTO.setTitle(title);
        // 如果没有失败记录，则直接保存
        if (CollectionUtils.isEmpty(failedList)) {
            TSysImportDTO cg = iSysImportService.create(tSysImportDTO);
            failedList.forEach(a -> a.setImpId(cg.getId()));

        }else{

//        if (CollectionUtils.isNotEmpty(failedList)){
            // 保存导入记录，并上传Excel失败文件
            TSysImportDTO  sbdto = this.saveAndUploadFile(tSysImportDTO, failedList, TSalaryDTO.class, FileBasePathName.USER_SALARY_IMPORT);
            tSalaryDTOlist.forEach(a-> a.setImpId(sbdto.getId()));
            StringBuffer sb =new StringBuffer();

            for (TSalaryDTO tSalaryDTO : failedList) {
                sb.append(tSalaryDTO.getFailedReason() + "; ");
            }
            return tSalaryDTOlist;
//            throw new BusinessException(sb.toString());
        }

            return tSalaryDTOlist;
    }

    private static String getCellValue(HSSFCell cell) {
        if (cell == null) {
            return "";
        }

        switch (cell.getCellType()) {
            case NUMERIC:
                String result = "";
                if (HSSFDateUtil.isCellDateFormatted(cell)) {
                    SimpleDateFormat sdf = null;
                    if (cell.getCellStyle().getDataFormat() == HSSFDataFormat.getBuiltinFormat("h:mm")) {
                        sdf = new SimpleDateFormat("HH:mm");
                    } else {
                        sdf = new SimpleDateFormat("yyyy-MM-dd");
                    }
                    Date date = cell.getDateCellValue();
                    result = sdf.format(date);
                } else if (cell.getCellStyle().getDataFormat() == 58) {
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                    double value = cell.getNumericCellValue();
                    Date date = DateUtil.getJavaDate(value);
                    result = sdf.format(date);
                } else {
                    double value = cell.getNumericCellValue();
                    CellStyle style = cell.getCellStyle();
                    DecimalFormat format = new DecimalFormat();
                    String temp = style.getDataFormatString();
                    if ("General".equals(temp)) {
                        format.applyPattern("#");
                    }
                    result = format.format(value);
                }
                return String.valueOf(result).trim();
            case STRING:
                String str = cell.getStringCellValue().trim();
                return str;
            case FORMULA:
                return cell.getCellFormula();
            case BLANK:
                return "";
            case BOOLEAN:
                return cell.getBooleanCellValue() + "";
            case ERROR:
                return cell.getErrorCellValue() + "";
        }
        return "";
    }

    /**
     * 保存导入记录，并上传Excel失败文件
     * @param sysImportDTO
     * @param failedList
     * @param fileBasePathName
     * @param <T>
     * @return
     */
    @Override
    public <T> TSysImportDTO saveAndUploadFile(TSysImportDTO sysImportDTO, List<T> failedList, Class clazz,
                                         FileBasePathName fileBasePathName) {

        // 将失败记录暂存到本地临时目录
        String fileName = "failedData.xlsx";
        byte[] bytes = iSysImportService.saveFailedToExcel(failedList, fileName, clazz);

        // 生成文件存储的路径
        String filePath = fileStoreTemplateService.createFileUrl(fileBasePathName, ".xlsx");
        sysImportDTO.setFilePath(filePath);

        // 上传文件
        fileStoreTemplateService.uploadFile(filePath, bytes);

        // 保存记录
        TSysImportDTO tSysImportDTO = iSysImportService.create(sysImportDTO);

        // 删除之前暂存的文件
        File file = new File(fileName);
        file.delete();
        return tSysImportDTO;
    }



}
