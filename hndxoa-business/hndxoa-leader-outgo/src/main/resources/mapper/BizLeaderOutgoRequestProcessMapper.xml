<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ctsi.leaderOutgo.mapper.BizLeaderOutgoRequestProcessMapper">

    <select id="getLeaderOutgoRequestProcess" resultType="com.ctsi.leaderOutgo.entity.dto.RequestProcessDTO">
        select b.MODELKEY as processDefinitionKey,b.PROC_INST_ID as processInstanceId,b.root_PROC_INST_ID as rootProcessInstanceId,
        B.FORM_DEF_ID as formId,p.id as formDataId,b.PROC_TYPE_NAME AS procTypeName
        from biz_leader_outgo_request_process p
        left join
        cscp_proc_base b on p.id = b.FORM_DATA_ID
        where p.id = #{id}
    </select>
</mapper>
