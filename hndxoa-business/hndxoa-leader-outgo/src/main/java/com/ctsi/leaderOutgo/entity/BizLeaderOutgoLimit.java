package com.ctsi.leaderOutgo.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.ctsi.hndx.common.BaseEntity;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * <p>
 * 外出领导48小时限制表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-20
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("biz_leader_outgo_limit")
@ApiModel(value="BizLeaderOutgoLimit对象", description="外出领导48小时限制表")
public class BizLeaderOutgoLimit extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 领导用户id
     */
    @ApiModelProperty(value = "领导用户id")
    private Long leaderId;

    /**
     * 领导名称
     */
    @ApiModelProperty(value = "领导名称")
    private String leaderName;

    /**
     * 所属单位名称
     */
    @ApiModelProperty(value = "所属单位名称")
    private String leaderCompanyName;

    /**
     * 所属单位id
     */
    @ApiModelProperty(value = "所属单位id")
    private Long leaderCompanyId;

    /**
     * 领导职务
     */
    @ApiModelProperty(value = "领导职务")
    private String leaderDuties;

    /**
     * 领导电话
     */
    @ApiModelProperty(value = "领导电话")
    private String leaderPhone;

    /**
     * 职务类型
     */
    @ApiModelProperty(value = "职务类型")
    private String dutiesType;

    /**
     * 解除原因
     */
    @ApiModelProperty(value = "解除原因")
    private String releaseReason;

    /**
     * 恢复时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd hh:mm:ss")                    // 表示返回时间类型
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")      // 表示接收时间类型
    @ApiModelProperty(value = "恢复时间")
    private Date restoreTime;

    /**
     * 领导外出配置表id
     */
    @ApiModelProperty(value = "领导外出配置表id")
    private long configId;

}
