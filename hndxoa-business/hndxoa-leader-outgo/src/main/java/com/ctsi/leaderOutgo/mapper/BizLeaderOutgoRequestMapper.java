package com.ctsi.leaderOutgo.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ctsi.leaderOutgo.entity.BizLeaderOutgoInbox;
import com.ctsi.leaderOutgo.entity.BizLeaderOutgoRequest;
import com.ctsi.hndx.common.MybatisBaseMapper;
import com.ctsi.leaderOutgo.entity.dto.BizLeaderOutgoInboxDTO;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 领导外出请示表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-20
 */
public interface BizLeaderOutgoRequestMapper extends MybatisBaseMapper<BizLeaderOutgoRequest> {

    @InterceptorIgnore(tenantLine="true")
    void updateRequestCompany(@Param("leaderId") Long leaderId,@Param("requestCompanyId") Long requestCompanyId,@Param("requestCompanyName") String requestCompanyName);
}
