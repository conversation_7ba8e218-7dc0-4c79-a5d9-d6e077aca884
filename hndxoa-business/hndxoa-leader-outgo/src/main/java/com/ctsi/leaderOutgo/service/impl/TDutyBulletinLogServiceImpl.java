package com.ctsi.leaderOutgo.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ctsi.leaderOutgo.entity.TDutyBulletinLog;
import com.ctsi.leaderOutgo.entity.dto.TDutyBulletinLogDTO;
import com.ctsi.leaderOutgo.mapper.TDutyBulletinLogMapper;
import com.ctsi.leaderOutgo.service.ITDutyBulletinLogService;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.common.SysBaseServiceImpl;
import com.ctsi.hndx.utils.BeanConvertUtils;
import com.ctsi.hndx.utils.ListCopyUtil;
import com.ctsi.hndx.utils.PageHelperUtil;
import com.ctsi.hndx.utils.StringUtils;
import com.ctsi.ssdc.model.PageResult;
import com.ctsi.ssdc.security.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.parameters.P;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-28
 */
@Service
public class TDutyBulletinLogServiceImpl extends SysBaseServiceImpl<TDutyBulletinLogMapper, TDutyBulletinLog> implements ITDutyBulletinLogService {

    @Autowired
    private TDutyBulletinLogMapper tDutyBulletinLogMapper;

    /**
     * 翻页
     *
     * @param entityDTO
     * @param basePageForm
     * @return
     */
    @Override
    public PageResult<TDutyBulletinLogDTO> queryListPage(TDutyBulletinLogDTO entityDTO, BasePageForm basePageForm) {
        Long userid = SecurityUtils.getCurrentUserId();
        //设置条件
        LambdaQueryWrapper<TDutyBulletinLog> queryWrapper = new LambdaQueryWrapper();
//        queryWrapper.eq(TDutyBulletinLog::getUserId,userid);
        queryWrapper.eq(TDutyBulletinLog::getSummaryId,entityDTO.getSummaryId());
        if("1".equals(entityDTO.getIsStart())){
            queryWrapper.ne(TDutyBulletinLog::getIsStart,entityDTO.getIsStart());
        }
        if(StringUtils.isNotEmpty(entityDTO.getState())){
            queryWrapper.eq(TDutyBulletinLog::getState,entityDTO.getState());
        }
        List<TDutyBulletinLog> listdb = selectListNoAdd(queryWrapper);
        List<Long> ids = listdb.stream().map(i -> i.getSummaryId()).collect(Collectors.toList());
        IPage<TDutyBulletinLog> pageData = tDutyBulletinLogMapper.selectPageNoAdd(
                PageHelperUtil.getMPlusPageByBasePage(basePageForm), queryWrapper);
        //返回
        IPage<TDutyBulletinLogDTO> data = pageData.convert(entity -> BeanConvertUtils.copyProperties(entity, TDutyBulletinLogDTO.class));

        return new PageResult<TDutyBulletinLogDTO>(data.getRecords(),
                data.getTotal(), data.getCurrent());
    }

    @Override
    public PageResult<TDutyBulletinLogDTO> getBulletinPage(TDutyBulletinLogDTO entityDTO, BasePageForm basePageForm) {
        Long userid = SecurityUtils.getCurrentUserId();
        IPage<TDutyBulletinLogDTO> pageData = tDutyBulletinLogMapper.getBulletinPage(
                PageHelperUtil.getMPlusPageByBasePage(basePageForm), userid,entityDTO.getTitle(),entityDTO.getState());

        return new PageResult<TDutyBulletinLogDTO>(pageData.getRecords(),
                pageData.getTotal(), pageData.getCurrent());
    }

    /**
     * 列表查询
     *
     * @param entityDTO
     * @return
     */
    @Override
    public List<TDutyBulletinLogDTO> queryList(TDutyBulletinLogDTO entityDTO) {
        LambdaQueryWrapper<TDutyBulletinLog> queryWrapper = new LambdaQueryWrapper();
        List<TDutyBulletinLog> listData = tDutyBulletinLogMapper.selectList(queryWrapper);
        List<TDutyBulletinLogDTO> bizLeadershipEntrustmentDTOList = ListCopyUtil.copy(listData, TDutyBulletinLogDTO.class);
        return bizLeadershipEntrustmentDTOList;
    }

    /**
     * 单个查询
     *
     * @param id the id of the entity
     * @return
     */
    @Override
    public TDutyBulletinLogDTO findOne(Long id) {
        TDutyBulletinLog bizLeadershipEntrustment = tDutyBulletinLogMapper.selectById(id);
        return BeanConvertUtils.copyProperties(bizLeadershipEntrustment, TDutyBulletinLogDTO.class);
    }


    /**
     * 新增
     *
     * @param entityDTO the entity to create
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public TDutyBulletinLogDTO create(TDutyBulletinLogDTO entityDTO) {
        TDutyBulletinLog tDutyBulletinLog = BeanConvertUtils.copyProperties(entityDTO, TDutyBulletinLog.class);
        save(tDutyBulletinLog);
        return BeanConvertUtils.copyProperties(tDutyBulletinLog, TDutyBulletinLogDTO.class);
    }

    @Override
    public void updateState(Long id) {
        TDutyBulletinLog tDutyBulletinLog = tDutyBulletinLogMapper.selectById(id);
        tDutyBulletinLog.setState("1");
        tDutyBulletinLog.setReadTime(new Date());
        tDutyBulletinLogMapper.updateById(tDutyBulletinLog);
    }

    /**
     * 修改
     *
     * @param entity the entity to update
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int update(TDutyBulletinLogDTO entity) {
        TDutyBulletinLog bizLeadershipEntrustment = BeanConvertUtils.copyProperties(entity, TDutyBulletinLog.class);
        return tDutyBulletinLogMapper.updateById(bizLeadershipEntrustment);
    }

    /**
     * 删除
     *
     * @param id the id of the entity
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int delete(Long id) {
        return tDutyBulletinLogMapper.deleteById(id);
    }


}
