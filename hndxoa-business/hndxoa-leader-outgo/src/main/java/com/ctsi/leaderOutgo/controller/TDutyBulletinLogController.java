package com.ctsi.leaderOutgo.controller;


import cn.hutool.core.lang.Assert;
import com.ctsi.leaderOutgo.entity.dto.TDutyBulletinLogDTO;
import com.ctsi.leaderOutgo.service.ITDutyBulletinLogService;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.enums.DBOperation;
import com.ctsi.hndx.result.ResultCode;
import com.ctsi.hndx.result.ResultVO;
import com.ctsi.ssdc.annotation.OperationLog;
import com.ctsi.ssdc.model.PageResult;
import com.ctsi.ssdc.model.ResResult;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-28
 */
@RestController
@RequestMapping("/api/tDutyBulletinLog")
public class TDutyBulletinLogController {

    @Autowired
    private ITDutyBulletinLogService iTDutyBulletinLogService;

    /**
     * 新增数据.
     */
    @PostMapping("/create")
    @ApiOperation(value = "新增(权限code码为：cscp.tLeadershipGrouping.add)", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.ADD, message = "新增领导分组数据")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tLeadershipGrouping.add')")
    public ResultVO<TDutyBulletinLogDTO> create(@RequestBody TDutyBulletinLogDTO tDutyBulletinLogDTO) {
        TDutyBulletinLogDTO result = iTDutyBulletinLogService.create(tDutyBulletinLogDTO);
        return ResultVO.success(result);
    }

    /**
     * 更新存在数据.
     */
    @PostMapping("/update")
    @ApiOperation(value = "更新存在数据(权限code码为：cscp.tLeadershipGrouping.update)", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.UPDATE, message = "更新领导分组数据")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tLeadershipGrouping.update')")
    public ResultVO update(@RequestBody TDutyBulletinLogDTO tDutyBulletinLogDTO) {
        Assert.notNull(tDutyBulletinLogDTO.getId(), "general.IdNotNull");
        int count = iTDutyBulletinLogService.update(tDutyBulletinLogDTO);
        if (count > 0) {
            return ResultVO.success();
        } else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }

    /**
     * 删除存在数据.
     */
    @DeleteMapping("/delete/{id}")
    @OperationLog(dBOperation = DBOperation.DELETE, message = "删除领导分组数据")
    @ApiOperation(value = "删除存在数据(权限code码为：cscp.tLeadershipGrouping.delete)", notes = "传入参数")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tLeadershipGrouping.delete')")
    public ResultVO delete(@PathVariable Long id) {
        int count = iTDutyBulletinLogService.delete(id);
        if (count > 0) {
            return ResultVO.success();
        } else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }

    /**
     * 查询单条数据.
     */
    @GetMapping("/get/{id}")
    @ApiOperation(value = "查询单条数据", notes = "传入参数")
    public ResultVO get(@PathVariable Long id) {
        TDutyBulletinLogDTO tDutyBulletinLogDTO = iTDutyBulletinLogService.findOne(id);
        return ResultVO.success(tDutyBulletinLogDTO);
    }

    /**
     * 分页查询多条数据.
     */
    @GetMapping("/queryTDutyBulletinLogPage")
    @ApiOperation(value = "翻页查询多条数据", notes = "传入参数")
    public ResultVO<PageResult<TDutyBulletinLogDTO>> queryTDutyBulletinLogPage(TDutyBulletinLogDTO tDutyBulletinLogDTO, BasePageForm basePageForm) {
        return ResultVO.success(iTDutyBulletinLogService.queryListPage(tDutyBulletinLogDTO, basePageForm));
    }

    /**
     * 查询多条数据.不分页
     */
    @GetMapping("/queryTDutyBulletinLog")
    @ApiOperation(value = "查询多条数据", notes = "传入参数")
    public ResultVO<ResResult<TDutyBulletinLogDTO>> queryTDutyBulletinLog(TDutyBulletinLogDTO tDutyBulletinLogDTO) {
        List<TDutyBulletinLogDTO> list = iTDutyBulletinLogService.queryList(tDutyBulletinLogDTO);
        return ResultVO.success(new ResResult(list));
    }


    @GetMapping("/getBulletinPage")
    @ApiOperation(value = "翻页查询多条数据", notes = "传入参数")
    public ResultVO<PageResult<TDutyBulletinLogDTO>> getBulletinPage(TDutyBulletinLogDTO tDutyBulletinLogDTO, BasePageForm basePageForm) {
        return ResultVO.success(iTDutyBulletinLogService.getBulletinPage(tDutyBulletinLogDTO, basePageForm));
    }

    @GetMapping("/updateState")
    @OperationLog(dBOperation = DBOperation.UPDATE, message = "修改查阅状态")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tLeadershipGrouping.add')")
    public ResultVO updateState(@RequestParam Long id) {
        iTDutyBulletinLogService.updateState(id);
        return ResultVO.success();
    }

}
