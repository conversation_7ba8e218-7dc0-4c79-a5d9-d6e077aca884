package com.ctsi.leaderOutgo.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.ctsi.hndx.common.BaseEntity;
import java.io.Serializable;
import java.math.BigInteger;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 值班要情汇报表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-18
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("biz_duty_act_report")
@ApiModel(value="BizDutyActReport对象", description="值班要情汇报表")
public class BizDutyActReport extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 标题
     */
    @ApiModelProperty(value = "标题")
    private String title;

    /**
     * 领导总人次
     */
    @ApiModelProperty(value = "领导总人次")
    private String leaderTotalNum;

    /**
     * 领导考察调研招商
     */
    @ApiModelProperty(value = "领导考察调研招商")
    private String leaderInspectNum;

    /**
     * 领导参加会议活动
     */
    @ApiModelProperty(value = "领导参加会议活动")
    private String leaderConferenceNum;

    /**
     * 领导汇报对接工作
     */
    @ApiModelProperty(value = "领导汇报对接工作")
    private String leaderGiveNum;

    /**
     * 参加学习培训
     */
    @ApiModelProperty(value = "参加学习培训")
    private String leaderStudyNum;

    /**
     * 领导因公出国(境)
     */
    @ApiModelProperty(value = "领导因公出国(境)")
    private String leaderAbroadNum;

    /**
     * 省领导总人次
     */
    @ApiModelProperty(value = "省领导总人次")
    private String sleaderTotalNum;

    /**
     * 省领导考察调研招商
     */
    @ApiModelProperty(value = "省领导考察调研招商")
    private String sleaderInspectNum;

    /**
     * 省领导参加会议活动
     */
    @ApiModelProperty(value = "省领导参加会议活动")
    private String sleaderConferenceNum;

    /**
     * 省领导汇报对接工作
     */
    @ApiModelProperty(value = "省领导汇报对接工作")
    private String sleaderGiveNum;

    /**
     * 省参加学习培训
     */
    @ApiModelProperty(value = "省参加学习培训")
    private String sleaderStudyNum;

    /**
     * 省领导因公出国(境)
     */
    @ApiModelProperty(value = "省领导因公出国(境)")
    private String sleaderAbroadNum;

    /**
     * 市州领导总人次
     */
    @ApiModelProperty(value = "市州领导总人次")
    private String cleaderTotalNum;

    /**
     * 市州领导考察调研招商
     */
    @ApiModelProperty(value = "市州领导考察调研招商")
    private String cleaderInspectNum;

    /**
     * 市州领导参加会议活动
     */
    @ApiModelProperty(value = "市州领导参加会议活动")
    private String cleaderConferenceNum;

    /**
     * 市州领导汇报对接工作
     */
    @ApiModelProperty(value = "市州领导汇报对接工作")
    private String cleaderGiveNum;

    /**
     * 市州参加学习培训
     */
    @ApiModelProperty(value = "市州参加学习培训")
    private String cleaderStudyNum;

    /**
     * 市州领导因公出国(境)
     */
    @ApiModelProperty(value = "市州领导因公出国(境)")
    private String cleaderAbroadNum;

    /**
     * 省直领导总人次
     */
    @ApiModelProperty(value = "省直领导总人次")
    private String szleaderTotalNum;

    /**
     * 省直领导考察调研招商
     */
    @ApiModelProperty(value = "省直领导考察调研招商")
    private String szleaderInspectNum;

    /**
     * 省直领导参加会议活动
     */
    @ApiModelProperty(value = "省直领导参加会议活动")
    private String szleaderConferenceNum;

    /**
     * 省直领导汇报对接工作
     */
    @ApiModelProperty(value = "省直领导汇报对接工作")
    private String szleaderGiveNum;

    /**
     * 省直参加学习培训
     */
    @ApiModelProperty(value = "省直参加学习培训")
    private String szleaderStudyNum;

    /**
     * 省直领导因公出国(境)
     */
    @ApiModelProperty(value = "省直领导因公出国(境)")
    private String szleaderAbroadNum;

    /**
     * 申报时间
     */
    @ApiModelProperty(value = "申报时间")
    private String declareTime;

    /**
     * 流程状态
     */
    @ApiModelProperty(value = "流程状态")
    private Integer bpmStatus;

    /**
     * 流程实例ID
     */
    @ApiModelProperty(value = "流程实例ID")
    private BigInteger processInstanceId;

    /**
     * 附件
     */
    @ApiModelProperty(value = "附件")
    private String annex;

    /**
     * 正文
     */
    @ApiModelProperty(value = "正文")
    private String document;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remarks;

    /**
     * 密级期限
     */
    @ApiModelProperty(value = "密级期限")
    private String durationClassification;

    /**
     * 秘钥名称
     */
    @ApiModelProperty(value = "秘钥名称")
    private String durationClassificationName;

    /**
     * 定密依据
     */
    @ApiModelProperty(value = "定密依据")
    private String classificationBasis;

    /**
     * 部门名称
     */
    @ApiModelProperty(value = "部门名称")
    private String departmentName;

    /**
     * 单位名称
     */
    @ApiModelProperty(value = "单位名称")
    private String companyName;


}
