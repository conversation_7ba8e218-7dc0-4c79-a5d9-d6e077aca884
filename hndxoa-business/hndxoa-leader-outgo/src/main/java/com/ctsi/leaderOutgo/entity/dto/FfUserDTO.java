package com.ctsi.leaderOutgo.entity.dto;

import com.ctsi.hndx.common.BaseDtoEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;
import java.util.Date;

/**
 * <p>
 * 来岳客人信息报告单
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-28
 */
@Getter
@Setter
@ApiModel(value = "FfUserDTO对象", description = "分发人")
public class FfUserDTO{

    @ApiModelProperty(value = "分发人id")
    private Long userId;

    @ApiModelProperty("分发人")
    private String userName;

    @ApiModelProperty("来源id")
    private Long fromId;

    @ApiModelProperty("来源类型")
    private String fromType;



}
