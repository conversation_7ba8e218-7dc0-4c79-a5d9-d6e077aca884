package com.ctsi.leaderOutgo.service;

import com.ctsi.leaderOutgo.entity.TDutyBulletinSummary;
import com.ctsi.leaderOutgo.entity.dto.GuestBulletinDTO;
import com.ctsi.leaderOutgo.entity.dto.TDutyBulletinLogDTO;
import com.ctsi.leaderOutgo.entity.dto.TDutyBulletinSummaryDTO;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.common.SysBaseServiceI;
import com.ctsi.leaderOutgo.entity.dto.RequestProcessDTO;
import com.ctsi.ssdc.model.PageResult;
import io.swagger.models.auth.In;

import javax.servlet.http.HttpServletResponse;
import java.text.ParseException;
import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-28
 */
public interface ITDutyBulletinSummaryService extends SysBaseServiceI<TDutyBulletinSummary> {


    /**
     * 分页查询
     *
     * @param entityDTO
     * @param page
     * @return
     */
    PageResult<TDutyBulletinSummaryDTO> queryListPage(TDutyBulletinSummaryDTO entityDTO, BasePageForm page);

    PageResult<TDutyBulletinSummaryDTO> appQueryListPage(TDutyBulletinLogDTO entityDTO, BasePageForm page);

    /**
     * 获取所有不分页
     *
     * @param entity
     * @return
     */
    List<TDutyBulletinSummaryDTO> queryList(TDutyBulletinSummaryDTO entity);

    Integer queryJb();

    /**
     * 根据主键id获取单个对象
     *
     * @param id
     * @return
     */
    TDutyBulletinSummaryDTO findOne(Long id);

    TDutyBulletinSummaryDTO findOneApp(Long id);
    TDutyBulletinSummaryDTO isRead(Long id);

    /**
     * 新增
     *
     * @param entity
     * @return
     */
    TDutyBulletinSummaryDTO create(TDutyBulletinSummaryDTO entity);


    /**
     * 更新
     *
     * @param entity
     * @return
     */
    int update(TDutyBulletinSummaryDTO entity);

    /**
     * 删除
     *
     * @param id
     * @return
     */
    int delete(Long id);

    Integer getPeriods(String year,Long companyId);

    PageResult<GuestBulletinDTO> getAll(GuestBulletinDTO guestBulletinDTO, BasePageForm basePageForm);

    /**
     * 合并操作
     * @param tDutyBulletinLogDTO
     */
    void merge(TDutyBulletinSummaryDTO tDutyBulletinLogDTO);

    /**
     * 合并操作
     */
    void mergeNew() throws ParseException;

    /**
     *
     * @return
     */
    Integer getDutyBulletinSummaryCount();

}
