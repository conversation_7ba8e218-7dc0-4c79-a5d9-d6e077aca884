package com.ctsi.leaderOutgo;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ctsi.hndx.common.ActivitEndDealBiz;
import com.ctsi.hndx.common.ActivitEndDetail;
import com.ctsi.hndx.utils.BeanConvertUtils;
import com.ctsi.leaderOutgo.entity.BizLeaderOutgoReport;
import com.ctsi.leaderOutgo.entity.BizLeaderOutgoRequest;
import com.ctsi.leaderOutgo.entity.BizLeaderOutgoRequestProcess;
import com.ctsi.leaderOutgo.entity.dto.LeaderOutgoSignDTO;
import com.ctsi.leaderOutgo.service.IBizLeaderOutgoInboxService;
import com.ctsi.leaderOutgo.service.IBizLeaderOutgoReportService;
import com.ctsi.leaderOutgo.service.IBizLeaderOutgoRequestService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;

@Component
public class LeaderOutgoInboxActivitiEndBizStrategy implements ActivitEndDealBiz {

    @Autowired
    private IBizLeaderOutgoReportService bizLeaderOutgoReportService;

    @Autowired
    private IBizLeaderOutgoRequestService bizLeaderOutgoRequestService;

    @Autowired
    private IBizLeaderOutgoInboxService bizLeaderOutgoInboxService;

    /**
     * 判断类型是否支持
     * @param
     * @return
     */
    public boolean isSupport(ActivitEndDetail activitEndDetail){
        String tableName = activitEndDetail.getTableName();
        if (StringUtils.isNotEmpty(tableName) && "biz_leader_outgo_sign".equals(tableName)){
            return true;
        }
        return false;
    }


    /**
     * 流程结束的时候处理业务逻辑
     * @param activitEndDetail
     */
    @Override
    public void dealBiz(ActivitEndDetail activitEndDetail) {
        LeaderOutgoSignDTO leaderOutgoSignDTO = bizLeaderOutgoInboxService.selectLeaderOutgoSign(activitEndDetail.getId());

        if(StringUtils.isNotEmpty(leaderOutgoSignDTO.getRequestIds())){
            List<String> requestIds= Arrays.asList(leaderOutgoSignDTO.getRequestIds().split(","));
            LambdaQueryWrapper<BizLeaderOutgoRequest> queryRequestWrapper = new LambdaQueryWrapper();
            queryRequestWrapper.in(BizLeaderOutgoRequest::getId,requestIds);
            List<BizLeaderOutgoRequest> requestList = bizLeaderOutgoRequestService.selectListNoAdd(queryRequestWrapper);
            requestList.forEach(item->{
                item.setState(9);
            });
            bizLeaderOutgoRequestService.updateBatchById(requestList);
        }

        if(StringUtils.isNotEmpty(leaderOutgoSignDTO.getReportIds())){
            List<String> reportIds= Arrays.asList(leaderOutgoSignDTO.getReportIds().split(","));
            LambdaQueryWrapper<BizLeaderOutgoReport> queryRequestWrapper = new LambdaQueryWrapper();
            queryRequestWrapper.in(BizLeaderOutgoReport::getId,reportIds);
            List<BizLeaderOutgoReport> reportList = bizLeaderOutgoReportService.selectListNoAdd(queryRequestWrapper);

            reportList.forEach(item->{
                item.setState(9);
            });
            bizLeaderOutgoReportService.updateBatchById(reportList);
        }
    }
}
