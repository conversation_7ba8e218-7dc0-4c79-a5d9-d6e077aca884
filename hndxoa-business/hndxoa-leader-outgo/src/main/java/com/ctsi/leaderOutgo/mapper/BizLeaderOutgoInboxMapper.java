package com.ctsi.leaderOutgo.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ctsi.leaderOutgo.entity.BizLeaderOutgoInbox;
import com.ctsi.hndx.common.MybatisBaseMapper;
import com.ctsi.leaderOutgo.entity.dto.BizLeaderOutgoInboxByIdsDTO;
import com.ctsi.leaderOutgo.entity.dto.BizLeaderOutgoInboxDTO;
import com.ctsi.leaderOutgo.entity.dto.LeaderOutgoSignDTO;
import com.ctsi.leaderOutgo.entity.dto.StatisticsGooutReasonDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 领导外出收件箱 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-29
 */
public interface BizLeaderOutgoInboxMapper extends MybatisBaseMapper<BizLeaderOutgoInbox> {

    /**
     * 通过查询条件查询领导外出收件箱记录
     *
     * @param dto
     * @return
     */
    @InterceptorIgnore(tenantLine="true")
    IPage<BizLeaderOutgoInbox> pageQueryLeaderOutgoInboxList(IPage iPage, @Param("inbox") BizLeaderOutgoInboxDTO dto);

    @InterceptorIgnore(tenantLine="true")
    IPage<BizLeaderOutgoInbox> pageQueryLeaderOutgoRequesList(IPage iPage, @Param("inbox") BizLeaderOutgoInboxDTO dto);

    @InterceptorIgnore(tenantLine="true")
    IPage<BizLeaderOutgoInbox> pageQueryLeaderOutgoReportList(IPage iPage, @Param("inbox") BizLeaderOutgoInboxDTO dto);

    /**
     * 分页查询批示反馈数据
     * @param iPage
     * @param dto
     * @return
     */
    @InterceptorIgnore(tenantLine="true")
    IPage<BizLeaderOutgoInbox> queryBizLeaderOutgoSignPage(IPage iPage, @Param("inbox") BizLeaderOutgoInboxDTO dto);

    /**
     * 通过id集合查询条件查询领导外出收件箱记录
     *
     * @param dto
     * @return
     */
    @InterceptorIgnore(tenantLine="true")
    IPage<BizLeaderOutgoInbox> pageQueryLeaderOutgoInboxListByIds(IPage iPage, @Param("inbox") BizLeaderOutgoInboxByIdsDTO dto);

    /**
     * 查询领导外出批示信息
     * @param id
     * @return
     */
    LeaderOutgoSignDTO selectLeaderOutgoSign(@Param("id") Long id);

    /**
     * 统计外出事由数量
     *
     * @return
     */
    @InterceptorIgnore(tenantLine="true")
    List<StatisticsGooutReasonDTO> statisticsGooutReason(@Param("dutiesType") String dutiesType,@Param("startTime") String startTime,@Param("endTime") String endTime);

    /**
     * 统计外出地点数量
     *
     * @return
     */
    @InterceptorIgnore(tenantLine="true")
    List<StatisticsGooutReasonDTO> statisticsGooutPlace(@Param("dutiesType") String dutiesType,@Param("startTime") String startTime,@Param("endTime") String endTime);

    @InterceptorIgnore(tenantLine="true")
    List<BizLeaderOutgoInbox> queryBizLeaderOutDuty(@Param("companyId") Long companyId,@Param("yesterday") String yesterday,@Param("dutiesType") String dutiesType);

    @InterceptorIgnore(tenantLine="true")
    Integer queryBizLeaderOutCount();
}
