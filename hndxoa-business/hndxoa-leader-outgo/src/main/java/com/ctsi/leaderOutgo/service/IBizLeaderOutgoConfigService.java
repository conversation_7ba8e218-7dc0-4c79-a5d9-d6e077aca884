package com.ctsi.leaderOutgo.service;

import com.ctsi.leaderOutgo.entity.dto.BizLeaderOutgoConfigDTO;
import com.ctsi.leaderOutgo.entity.BizLeaderOutgoConfig;
import com.ctsi.hndx.common.SysBaseServiceI;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.ssdc.model.PageResult;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <p>
 * 外出领导管理表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-21
 */
public interface IBizLeaderOutgoConfigService extends SysBaseServiceI<BizLeaderOutgoConfig> {


    /**
     * 分页查询
     *
     * @param entityDTO
     * @param page
     * @return
     */
    PageResult<BizLeaderOutgoConfigDTO> queryListPage(BizLeaderOutgoConfigDTO entityDTO, BasePageForm page);

    PageResult<BizLeaderOutgoConfigDTO> queryOutgoConfigByLeaderCompanyIdPage(BizLeaderOutgoConfigDTO entityDTO, BasePageForm page);

    /**
     * 获取所有不分页
     *
     * @param entity
     * @return
     */
    List<BizLeaderOutgoConfigDTO> queryList(BizLeaderOutgoConfigDTO entity);

    List<BizLeaderOutgoConfigDTO> queryBizLeaderOutgoConfigByLeaderCompanyId(BizLeaderOutgoConfigDTO entity);

    /**
     * 根据主键id获取单个对象
     *
     * @param id
     * @return
     */
    BizLeaderOutgoConfigDTO findOne(Long id);

    /**
     * 新增
     *
     * @param entity
     * @return
     */
    BizLeaderOutgoConfigDTO create(BizLeaderOutgoConfigDTO entity);


    /**
     * 更新
     *
     * @param entity
     * @return
     */
    int update(BizLeaderOutgoConfigDTO entity);

    /**
     * 删除
     *
     * @param id
     * @return
     */
    int delete(Long id);

     /**
     * 是否存在
     *
     * existByBizLeaderOutgoConfigId
     * @param code
     * @return
     */
    boolean existByBizLeaderOutgoConfigId(Long code);

    /**
    * 批量新增
    *
    * create batch
    * @param dataList
    * @return
    */
    Boolean insertBatch(List<BizLeaderOutgoConfigDTO> dataList);

    /**
     * 通过领导id修改相应的数据
     *
     * @param entity the entity to update
     * @return
     */
    public int updateLeaderId(BizLeaderOutgoConfigDTO entity);

    /**
     * 通过领导用户ID查询单个信息
     *
     * @param id the id of the entity
     * @return
     */
    BizLeaderOutgoConfigDTO findOneByLeaderId(Long leaderId);

}
