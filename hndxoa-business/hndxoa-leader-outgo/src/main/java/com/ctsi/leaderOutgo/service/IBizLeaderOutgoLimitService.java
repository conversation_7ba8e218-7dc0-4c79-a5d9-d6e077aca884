package com.ctsi.leaderOutgo.service;

import com.ctsi.leaderOutgo.entity.dto.BizLeaderOutgoLimitDTO;
import com.ctsi.leaderOutgo.entity.BizLeaderOutgoLimit;
import com.ctsi.hndx.common.SysBaseServiceI;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.ssdc.model.PageResult;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <p>
 * 外出领导48小时限制表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-20
 */
public interface IBizLeaderOutgoLimitService extends SysBaseServiceI<BizLeaderOutgoLimit> {


    /**
     * 分页查询
     *
     * @param entityDTO
     * @param page
     * @return
     */
    PageResult<BizLeaderOutgoLimitDTO> queryListPage(BizLeaderOutgoLimitDTO entityDTO, BasePageForm page);

    /**
     * 获取所有不分页
     *
     * @param entity
     * @return
     */
    List<BizLeaderOutgoLimitDTO> queryList(BizLeaderOutgoLimitDTO entity);

    /**
     * 根据主键id获取单个对象
     *
     * @param id
     * @return
     */
    BizLeaderOutgoLimitDTO findOne(Long id);

    /**
     * 新增
     *
     * @param entity
     * @return
     */
    BizLeaderOutgoLimitDTO create(BizLeaderOutgoLimitDTO entity);

    /**
     * 更新
     *
     * @param entity
     * @return
     */
    int update(BizLeaderOutgoLimitDTO entity);

    /**
     * 删除
     *
     * @param id
     * @return
     */
    int delete(Long id);

     /**
     * 是否存在
     *
     * existByBizLeaderOutgoLimitId
     * @param code
     * @return
     */
    boolean existByBizLeaderOutgoLimitId(Long code);

    /**
    * 批量新增
    *
    * create batch
    * @param dataList
    * @return
    */
    Boolean insertBatch(List<BizLeaderOutgoLimitDTO> dataList);

    /**
     * 恢复48小时限制
     *
     * @param entity the entity to update
     * @return
     */
    int restoreLimit(BizLeaderOutgoLimitDTO entity);

    /**
     * 解除48小时限制
     *
     * @param entity
     * @return
     */
    BizLeaderOutgoLimitDTO secureLimit(BizLeaderOutgoLimitDTO entity);
}
