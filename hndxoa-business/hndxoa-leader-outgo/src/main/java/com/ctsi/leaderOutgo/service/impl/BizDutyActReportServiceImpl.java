package com.ctsi.leaderOutgo.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ctsi.hndx.utils.BeanConvertUtils;
import com.ctsi.hndx.utils.ListCopyUtil;
import com.ctsi.ssdc.model.PageResult;
import com.ctsi.leaderOutgo.entity.BizDutyActReport;
import com.ctsi.leaderOutgo.entity.dto.BizDutyActReportDTO;
import com.ctsi.leaderOutgo.mapper.BizDutyActReportMapper;
import com.ctsi.leaderOutgo.service.IBizDutyActReportService;
import com.ctsi.hndx.common.SysBaseServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.utils.PageHelperUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.List;
import org.springframework.transaction.annotation.Transactional;

/**
 * <p>
 * 值班要情汇报表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-17
 */
@Slf4j
@Service
public class BizDutyActReportServiceImpl extends SysBaseServiceImpl<BizDutyActReportMapper, BizDutyActReport> implements IBizDutyActReportService {

    @Autowired
    private BizDutyActReportMapper bizDutyActReportMapper;

    /**
     * 翻页
     *
     * @param entityDTO
     * @param basePageForm
     * @return
     */
    @Override
    public PageResult<BizDutyActReportDTO> queryListPage(BizDutyActReportDTO entityDTO, BasePageForm basePageForm) {
        //设置条件
        LambdaQueryWrapper<BizDutyActReport> queryWrapper = new LambdaQueryWrapper();

        IPage<BizDutyActReport> pageData = bizDutyActReportMapper.selectPage(
             PageHelperUtil.getMPlusPageByBasePage(basePageForm), queryWrapper);
        //返回
        IPage<BizDutyActReportDTO> data  = pageData.convert(entity -> BeanConvertUtils.copyProperties(entity,BizDutyActReportDTO.class));

        return new PageResult<BizDutyActReportDTO>(data.getRecords(),
            data.getTotal(), data.getCurrent());
    }

    /**
     * 列表查询
     *
     * @param entityDTO
     * @return
     */
    @Override
    public List<BizDutyActReportDTO> queryList(BizDutyActReportDTO entityDTO) {
        LambdaQueryWrapper<BizDutyActReport> queryWrapper = new LambdaQueryWrapper();
            List<BizDutyActReport> listData = bizDutyActReportMapper.selectList(queryWrapper);
            List<BizDutyActReportDTO> BizDutyActReportDTOList = ListCopyUtil.copy(listData, BizDutyActReportDTO.class);
        return BizDutyActReportDTOList;
    }

    /**
     * 单个查询
     *
     * @param id the id of the entity
     * @return
     */
    @Override
    public BizDutyActReportDTO findOne(Long id) {
        BizDutyActReport  bizDutyActReport =  bizDutyActReportMapper.selectById(id);
        return  BeanConvertUtils.copyProperties(bizDutyActReport,BizDutyActReportDTO.class);
    }


    /**
     * 新增
     *
     * @param entityDTO the entity to create
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public BizDutyActReportDTO create(BizDutyActReportDTO entityDTO) {
       BizDutyActReport bizDutyActReport =  BeanConvertUtils.copyProperties(entityDTO,BizDutyActReport.class);
        save(bizDutyActReport);
        return  BeanConvertUtils.copyProperties(bizDutyActReport,BizDutyActReportDTO.class);
    }

    /**
     * 修改
     *
     * @param entity the entity to update
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int update(BizDutyActReportDTO entity) {
        BizDutyActReport bizDutyActReport = BeanConvertUtils.copyProperties(entity,BizDutyActReport.class);
        return bizDutyActReportMapper.updateById(bizDutyActReport);
    }

    /**
     * 删除
     *
     * @param id the id of the entity
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int delete(Long id) {
        return bizDutyActReportMapper.deleteById(id);
    }


    /**
     * 验证是否存在
     *
     * @param BizDutyActReportId
     * @return
     */
    @Override
    public boolean existByBizDutyActReportId(Long BizDutyActReportId) {
        if (BizDutyActReportId != null) {
            LambdaQueryWrapper<BizDutyActReport> queryWrapper = new LambdaQueryWrapper();
            queryWrapper.eq(BizDutyActReport::getId, BizDutyActReportId);
            List<BizDutyActReport> result = bizDutyActReportMapper.selectList(queryWrapper);
            return result.size() > 0;
        }
        return true;
    }

    /**
    * 批量新增
    *
    */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertBatch(List<BizDutyActReportDTO> dataList) {
        List<BizDutyActReport> result = ListCopyUtil.copy(dataList, BizDutyActReport.class);
        return saveBatch(result);
    }


}
