package com.ctsi.leaderOutgo.entity.dto;

import com.ctsi.hndx.common.BaseDtoEntity;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * <p>
 * 领导外出请示申请表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-18
 */
@Getter
@Setter
@ApiModel(value = "BizLeaderOutgoRequestProcessDTO对象", description = "领导外出请示申请表")
public class BizLeaderOutgoRequestProcessDTO extends BaseDtoEntity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("标题")
    private String title;

    @ApiModelProperty("领导名称")
    private String leaderName;

    @ApiModelProperty("所属单位名称")
    private String leaderCompanyName;

    @ApiModelProperty("所属单位id")
    private Long leaderCompanyId;

    @ApiModelProperty("领导职务")
    private String leaderDuties;

    @ApiModelProperty("领导电话")
    private String leaderPhone;

    @ApiModelProperty("职务类型")
    private String dutiesType;

    @ApiModelProperty("请示单位名称")
    private String requestCompanyName;

    @ApiModelProperty("请示单位id")
    private Long requestCompanyId;

    @ApiModelProperty("报备单位名称")
    private String reportCompanyName;

    @ApiModelProperty("报备单位id")
    private Long reportCompanyId;

    @ApiModelProperty("外出地点")
    private String gooutPlace;

    @ApiModelProperty("外出事由")
    private String gooutReason;

    @DateTimeFormat(pattern = "yyyy-MM-dd hh:mm:ss")                    // 表示返回时间类型
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")      // 表示接收时间类型
    @ApiModelProperty("出发时间")
    private Date gooutTime;

    @DateTimeFormat(pattern = "yyyy-MM-dd hh:mm:ss")                    // 表示返回时间类型
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")      // 表示接收时间类型
    @ApiModelProperty("返回时间")
    private Date returnTime;

    @ApiModelProperty("创建人联系电话")
    private String createPhone;

    @ApiModelProperty("领导用户id")
    private Long leaderId;

    @ApiModelProperty("排序号")
    private Integer sortNumber;

    @ApiModelProperty("流程实例")
    private Long processInstanceId;

    @ApiModelProperty("流程状态")
    private Integer bpmStatus;

    @ApiModelProperty("处理单json")
    private String formJson;

    @ApiModelProperty("拟办意见")
    private String proposedOpinion;

    @ApiModelProperty("正文")
    private String document;

    @ApiModelProperty("附件")
    private String annex;

    /**
     * 拟稿部门
     */
    @ApiModelProperty(value = "拟稿部门")
    private String departmentName;

    /**
     * 单位名称
     */
    @ApiModelProperty(value = "单位名称")
    private String companyName;

    @ApiModelProperty(value = "填报人")
    private String declarePeople;

    @ApiModelProperty(value = "填报时间")
    private String declareTime;

    @ApiModelProperty(value = "主持工作人员及职务")
    private String presideWorkPersonPost;
}
