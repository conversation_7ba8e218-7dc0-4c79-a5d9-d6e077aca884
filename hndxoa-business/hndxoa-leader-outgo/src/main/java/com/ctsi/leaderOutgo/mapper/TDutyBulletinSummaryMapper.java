package com.ctsi.leaderOutgo.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ctsi.leaderOutgo.entity.TDutyBulletinSummary;
import com.ctsi.hndx.common.MybatisBaseMapper;
import com.ctsi.leaderOutgo.entity.dto.GuestBulletinDTO;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-28
 */
public interface TDutyBulletinSummaryMapper extends MybatisBaseMapper<TDutyBulletinSummary> {

    @InterceptorIgnore(tenantLine = "true")
    IPage<GuestBulletinDTO> getLb(IPage iPage, @Param("obj") GuestBulletinDTO guestBulletinDTO);

    @InterceptorIgnore(tenantLine = "true")
    Integer maxPeriods(@Param("year") String year,@Param("companyId") Long companyId);
}
