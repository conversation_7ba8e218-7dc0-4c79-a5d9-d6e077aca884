package com.ctsi.leaderOutgo.entity.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;
import java.util.Date;

@Data
public class BizLeaderOutDutyDTO {

    @ApiModelProperty(value = "领导名称")
    private String leaderName;

    @ApiModelProperty(value = "外出地点")
    private String gooutPlace;

    @ApiModelProperty(value = "外出事由")
    private String gooutReason;

    @ApiModelProperty(value = "出发时间")
    private String gooutTime;

    @ApiModelProperty(value = "返回时间")
    private String returnTime;

    @ApiModelProperty(value = "排序号")
    private Integer sortNumber;

    @ApiModelProperty(value = "签收时间")
    private LocalDateTime qsTime;

}
