package com.ctsi.hndxoa.pull.service;

import com.ctsi.hndxoa.entity.TSyncAppSystemManage;
import com.ctsi.hndxoa.pull.entity.dto.PassiveSyncMessage;

import java.util.List;

/**
 * 被动推送模式
 * <AUTHOR>
 */
public interface ITSyncAppSystemManagePullService {

    /**
     * 被动同步
     * @param passiveSyncMessage passiveSyncMessage
     */
    void passiveSyncToApp(PassiveSyncMessage passiveSyncMessage);

    /**
     * 被动批量同步
     * @param passiveSyncMessages passiveSyncMessages
     */
    void batchPassiveSyncToApp(List<PassiveSyncMessage> passiveSyncMessages);

    /**
     * 单个被动推送-共用
     * @param appId
     * @param type
     * @param businessId
     * @return
     */
    boolean sendPullManages(Long appId, Integer type, Long businessId);

    /**
     * 批量实现
     * @param tSyncAppSystemManage
     * @param appId
     * @param type
     * @param ids
     * @return
     */
    boolean batchSendPullManages(TSyncAppSystemManage tSyncAppSystemManage, Long appId, Integer type, List<Long> ids);

}
