package com.ctsi.hndxoa.entity.dto;

import com.ctsi.hndx.common.BaseDtoEntity;
import java.io.Serializable;
import java.math.BigInteger;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 版主应用管理表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-04
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value="TSyncAppModeratorManageDTO对象", description="版主应用管理表")
public class TSyncAppModeratorManageDTO extends BaseDtoEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 应用ID (关联 t_sync_app_system_manage.id)
     */
    @ApiModelProperty(value = "应用ID (关联 t_sync_app_system_manage.id)")
    private Long appId;

    /**
     * 版主用户ID (关联 cscp_user.id)
     */
    @ApiModelProperty(value = "版主用户ID (关联 cscp_user.id)")
    private Long moderatorId;

}
