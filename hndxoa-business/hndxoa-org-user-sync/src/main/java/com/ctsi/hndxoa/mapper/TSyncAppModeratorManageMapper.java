package com.ctsi.hndxoa.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.ctsi.hndxoa.entity.TSyncAppModeratorManage;
import com.ctsi.hndx.common.MybatisBaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 版主应用管理表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-04
 */
@Mapper
public interface TSyncAppModeratorManageMapper extends MybatisBaseMapper<TSyncAppModeratorManage> {

    /**
     * 根据用户ID和公司ID联合查询应用ID列表
     * @param userId 用户ID
     * @param companyId 单位ID
     * @return 应用ID列表
     */
    @InterceptorIgnore(tenantLine = "true")
    List<Long> selectAppIdsByUserIdAndCompanyId(@Param("userId") Long userId, @Param("companyId") Long companyId);

}
