package com.ctsi.hndxoa.service.job;

import com.ctsi.hndxoa.entity.TSyncUserHistroyRecord;
import com.ctsi.hndxoa.mapper.TSyncUserHistroyRecordMapper;
import com.ctsi.hndxoa.service.ITSyncUserHistroyRecordService;
import com.ctsi.hndxoa.service.constant.OrgUserConstants;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> 15433
 * @date : 2025/03/03/10:40
 * description:
 */

@Service
@Slf4j
@SuppressWarnings("all")
public class SyncUserHistroyRecordJob {

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    @Autowired
    private ITSyncUserHistroyRecordService itSyncUserHistroyRecordService;

    @Autowired
    private TSyncUserHistroyRecordMapper tSyncUserHistroyRecordMapper;

//    @Scheduled(cron ="0 0/5 * * * ? ")
    public void syncUpdateMeetingStatus() {
        log.info("<==============开始处理用户推送结果==============>");
        Map<Object, Object> valueMap = redisTemplate.opsForHash()
                .entries(OrgUserConstants.RedisKey.SYNC_USER_HISTORY_ID);
        if (MapUtils.isNotEmpty(valueMap)) {
            Map<Long, String> keyValueMap = new HashMap<>(valueMap.size());
            for (Map.Entry<Object, Object> entry : valueMap.entrySet()) {
                // 注:强制拆箱会报错
                String key = entry.getKey().toString();
                String value = entry.getValue().toString();
                keyValueMap.putIfAbsent(Long.valueOf(key), value);
            }

            List<TSyncUserHistroyRecord> userRecords = tSyncUserHistroyRecordMapper.selectBatchIds(keyValueMap.keySet());
            if (CollectionUtils.isNotEmpty(userRecords)) {

                for (TSyncUserHistroyRecord userRecord : userRecords) {
                    userRecord.setStrOperaType(keyValueMap.get(userRecord.getId()));
                    userRecord.setSyncSuccess("true");
                    userRecord.setSyncStatus("200");
                    userRecord.setSyncMessage("操作成功");
                }
                itSyncUserHistroyRecordService.saveOrUpdateBatch(userRecords, userRecords.size());

                // 将被执行的key从redis中移除
                for (TSyncUserHistroyRecord userRecord : userRecords) {
                    redisTemplate.opsForHash()
                            .delete(OrgUserConstants.RedisKey.SYNC_USER_HISTORY_ID,
                                    String.valueOf(userRecord.getId()));
                }
            }
        }
        log.info("<==============结束处理用户推送结果==============>");
    }
}
