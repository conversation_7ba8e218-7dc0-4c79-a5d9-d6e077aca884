package com.ctsi.hndxoa.service.constant;

import lombok.Getter;

@Getter
public enum SyncAppSystemEnum {

    WAI_BU("外部", 0),

    NEI_BU("内部", 1);

    String name;

    int code;

    SyncAppSystemEnum(String name, int code) {
        this.name = name;
        this.code = code;
    }

    public static String getNameByCode(Integer code) {
        // 遍历所有枚举值
        for (SyncAppSystemEnum enumItem : SyncAppSystemEnum.values()) {
            // 匹配code值
            if (enumItem.getCode() == code) {
                return enumItem.getName();
            }
        }
        // 未找到匹配项返回null
        return null;
    }

}
