package com.ctsi.hndxoa.mq.consumer;


import com.alibaba.fastjson.JSONObject;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.ctsi.hndx.config.DbConst;
import com.ctsi.hndxoa.entity.TSyncUserHistroyRecord;
import com.ctsi.hndxoa.mapper.TSyncUserHistroyRecordMapper;
import com.ctsi.hndxoa.mq.dto.MqResponseDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.Exchange;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.QueueBinding;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@SuppressWarnings("all")
public class UserConsumer {

    @Autowired
    private TSyncUserHistroyRecordMapper tSyncUserHistroyRecordMapper;

    private static final Logger log = LoggerFactory.getLogger(UserConsumer.class);

    @RabbitListener(bindings = @QueueBinding(
            value = @Queue(value = "userQueue", durable = "true"),
            exchange = @Exchange(value = "userExchange", type = "topic"),
            key = "userResponse"
    ))
    @DS(DbConst.SHARDING)
    public void onMessage(String message, Message amqpMessage) {
        String routingKey = amqpMessage.getMessageProperties().getReceivedRoutingKey();
        log.info("{},{} userResponse: {}",
                this.getClass().getSimpleName(),
                routingKey,
                message);
        MqResponseDTO mqResponseDTO = JSONObject.parseObject(message, MqResponseDTO.class);
        TSyncUserHistroyRecord userRecord = tSyncUserHistroyRecordMapper.selectById(mqResponseDTO.getSyncHistoryId());
        try {
            if ("200".equals(mqResponseDTO.getStatus())) {
                userRecord.setStrOperaType(mqResponseDTO.getStrOperaType());
                userRecord.setSyncSuccess("true");
                userRecord.setSyncStatus("200");
                userRecord.setSyncMessage("操作成功");
            } else {
                userRecord.setStrOperaType(mqResponseDTO.getStrOperaType());
                userRecord.setSyncSuccess("false");
                userRecord.setSyncStatus("500");
                userRecord.setSyncMessage(null != mqResponseDTO.getMessage() ? mqResponseDTO.getMessage() : "应用处理异常");
            }
        } finally {
            tSyncUserHistroyRecordMapper.updateById(userRecord);
        }
    }
}