package com.ctsi.hndxoa.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.common.SysBaseServiceImpl;
import com.ctsi.hndx.utils.PageHelperUtil;
import com.ctsi.hndxoa.entity.TAppAuthHistory;
import com.ctsi.hndxoa.mapper.TAppAuthHistoryMapper;
import com.ctsi.hndxoa.mapper.TSyncAppSystemManageMapper;
import com.ctsi.hndxoa.service.ITAppAuthHistoryService;
import com.ctsi.ssdc.admin.repository.CscpOrgRepository;
import com.ctsi.ssdc.admin.repository.CscpUserRepository;
import com.ctsi.ssdc.model.PageResult;
import com.ctsi.ssdc.security.CscpUserDetail;
import com.ctsi.ssdc.security.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
@SuppressWarnings("all")
public class TAppAuthHistoryServiceImpl extends SysBaseServiceImpl<TAppAuthHistoryMapper, TAppAuthHistory>
        implements ITAppAuthHistoryService {

    @Autowired
    private TAppAuthHistoryMapper tAppAuthHistoryMapper;

    @Autowired
    private TSyncAppSystemManageMapper tSyncAppSystemManageMapper;

    @Autowired
    private CscpUserRepository cscpUserRepository;

    @Autowired
    private CscpOrgRepository cscpOrgRepository;

    @Override
    public PageResult<TAppAuthHistory> pageHistory(TAppAuthHistory record, BasePageForm basePageForm) {

        IPage<TAppAuthHistory> page = PageHelperUtil.getMPlusPageByBasePage(basePageForm);
        LambdaQueryWrapper<TAppAuthHistory> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.like(StringUtils.isNotEmpty(record.getAppName()), TAppAuthHistory::getAppName, record.getAppName());
        queryWrapper.like(StringUtils.isNotEmpty(record.getAppCode()), TAppAuthHistory::getAppCode, record.getAppCode());
        queryWrapper.like(StringUtils.isNotEmpty(record.getUserName()), TAppAuthHistory::getUserName, record.getUserName());
        queryWrapper.like(StringUtils.isNotEmpty(record.getOrgName()), TAppAuthHistory::getOrgName, record.getOrgName());
        queryWrapper.like(StringUtils.isNotEmpty(record.getOptType()), TAppAuthHistory::getOptType, record.getOptType());
        queryWrapper.eq(record.getAppId() != null, TAppAuthHistory::getAppId, record.getAppId());
        queryWrapper.eq(record.getOrgId() != null, TAppAuthHistory::getOrgId, record.getOrgId());
        queryWrapper.eq(record.getUserId() != null, TAppAuthHistory::getUserId, record.getUserId());
        queryWrapper.eq(record.getStatus() != null, TAppAuthHistory::getStatus, record.getStatus());
        queryWrapper.gt(record.getCreateTimeStart() != null, TAppAuthHistory::getCreateTime, record.getCreateTimeStart());
        queryWrapper.lt(record.getCreateTimeEnd() != null, TAppAuthHistory::getCreateTime, record.getCreateTimeEnd());

        IPage<TAppAuthHistory> result = tAppAuthHistoryMapper.selectPageNoAdd(page, queryWrapper);

        return new PageResult<>(result.getRecords(),
                result.getTotal(), result.getCurrent());
    }

    @Override
    public void saveAppAuthByUser(Long userId, String loginName, List<Long> appIds) {

        if (userId == null) {
            return;
        }
        List<TAppAuthHistory> records = tAppAuthHistoryMapper.selectListNoAdd(new LambdaQueryWrapper<TAppAuthHistory>()
                .eq(TAppAuthHistory::getUserId, userId).eq(TAppAuthHistory::getStatus, 1));
        Map<Long, TAppAuthHistory> map = new HashMap<>();
        if (CollectionUtils.isNotEmpty(records)) {
            map = records.stream().collect(
                    Collectors.toMap(
                            TAppAuthHistory::getAppId,
                            history -> history,
                            (existing, replacement) -> existing
                    )
            );
        }
        Map<Long, TAppAuthHistory> finalMap = map;

        List<TAppAuthHistory> dbList = new ArrayList<>();
        List<TAppAuthHistory> addList = appIds.stream().filter(appId -> !finalMap.containsKey(appId)).map(appId -> {
            TAppAuthHistory record = new TAppAuthHistory();
            record.setAppInfo(appId, null, tSyncAppSystemManageMapper);
            record.setUserId(userId);
            record.setUserName(loginName);
            record.setCscpUserDetail(SecurityUtils.getCurrentCscpUserDetail());
            record.setOptType(TAppAuthHistory.OptTypeEnum.ADD.getValue());
            return record;
        }).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(addList)) {
            dbList.addAll(addList);
        }

        List<TAppAuthHistory> deleteList = records.stream().filter(record -> !appIds.contains(record.getAppId())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(deleteList)) {
            for (TAppAuthHistory record : deleteList) {
                record.setOptType(TAppAuthHistory.OptTypeEnum.DELETE.getValue());
                record.setStatus(0);
                record.setCscpUserDetail(SecurityUtils.getCurrentCscpUserDetail());
            }
            dbList.addAll(deleteList);
        }
        if (CollectionUtils.isNotEmpty(dbList)) {
            this.saveOrUpdateBatch(dbList);
        }
    }

    @Override
    public void saveAppAuthByUser2(Long userId, CscpUserDetail cscpUserDetail, List<String> appCodes) {
        if (userId == null) {
            return;
        }
        List<TAppAuthHistory> records = tAppAuthHistoryMapper.selectListNoAdd(new LambdaQueryWrapper<TAppAuthHistory>()
                .eq(TAppAuthHistory::getUserId, userId).eq(TAppAuthHistory::getStatus, 1));
        Map<String, TAppAuthHistory> map = new HashMap<>();
        if (CollectionUtils.isNotEmpty(records)) {
            map = records.stream().collect(
                    Collectors.toMap(
                            TAppAuthHistory::getAppCode,
                            history -> history,
                            (existing, replacement) -> existing
                    )
            );
        }
        Map<String, TAppAuthHistory> finalMap = map;

        List<TAppAuthHistory> dbList = new ArrayList<>();
        List<TAppAuthHistory> addList = appCodes.stream().filter(appCode -> !finalMap.containsKey(appCode)).map(appCode -> {
            TAppAuthHistory record = new TAppAuthHistory();
            record.setAppInfo(null, appCode, tSyncAppSystemManageMapper);
            record.setUserInfo(userId, cscpUserRepository);
            record.setCscpUserDetail(cscpUserDetail);
            record.setOptType(TAppAuthHistory.OptTypeEnum.ROllBACK.getValue());
            record.setDocumentReason(record.getAppName() + "推送失败，取消授权回滚");
            return record;
        }).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(addList)) {
            dbList.addAll(addList);
        }

        List<TAppAuthHistory> deleteList = records.stream().filter(record -> !appCodes.contains(record.getAppCode())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(deleteList)) {
            for (TAppAuthHistory record : deleteList) {
                record.setOptType(TAppAuthHistory.OptTypeEnum.ROllBACK.getValue());
                record.setStatus(0);
                record.setCscpUserDetail(cscpUserDetail);
                record.setDocumentReason(record.getAppName() + "推送失败，新增授权回滚");
            }
            dbList.addAll(deleteList);
        }
        if (CollectionUtils.isNotEmpty(dbList)) {
            this.saveOrUpdateBatch(dbList);
        }
    }

    @Override
    public void saveAppAuthByUser3(Long userId, String loginName, String pushAppCode) {

        if (userId == null) {
            return;
        }
        List<TAppAuthHistory> records = tAppAuthHistoryMapper.selectListNoAdd(new LambdaQueryWrapper<TAppAuthHistory>()
                .eq(TAppAuthHistory::getUserId, userId).eq(TAppAuthHistory::getStatus, 1));
        Map<String, TAppAuthHistory> map = new HashMap<>();
        if (CollectionUtils.isNotEmpty(records)) {
            map = records.stream().collect(
                    Collectors.toMap(
                            TAppAuthHistory::getAppCode,
                            history -> history,
                            (existing, replacement) -> existing
                    )
            );
        }
        Map<String, TAppAuthHistory> finalMap = map;

        List<String> appCodes = Arrays.asList(pushAppCode.split(","));
        List<TAppAuthHistory> dbList = new ArrayList<>();
        List<TAppAuthHistory> addList = appCodes.stream().filter(appCode -> !finalMap.containsKey(appCode)).map(appCode -> {
            TAppAuthHistory record = new TAppAuthHistory();
            record.setAppInfo(null, appCode, tSyncAppSystemManageMapper);
            record.setUserInfo(userId, cscpUserRepository);
            record.setCscpUserDetail(SecurityUtils.getCurrentCscpUserDetail());
            record.setOptType(TAppAuthHistory.OptTypeEnum.ADD.getValue());
            return record;
        }).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(addList)) {
            dbList.addAll(addList);
        }

        List<TAppAuthHistory> deleteList = records.stream().filter(record -> !appCodes.contains(record.getAppCode())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(deleteList)) {
            for (TAppAuthHistory record : deleteList) {
                record.setOptType("取消授权");
                record.setStatus(0);
                record.setCscpUserDetail(SecurityUtils.getCurrentCscpUserDetail());
                record.setOptType(TAppAuthHistory.OptTypeEnum.DELETE.getValue());
            }
            dbList.addAll(deleteList);
        }
        if (CollectionUtils.isNotEmpty(dbList)) {
            this.saveOrUpdateBatch(dbList);
        }
    }

    /** <====================================分割线====================================/> */

    @Override
    public void saveAppAuthByOrg(Long orgId, String orgName, List<Long> appIds) {

        if (orgId == null) {
            return;
        }
        List<TAppAuthHistory> records = tAppAuthHistoryMapper.selectListNoAdd(new LambdaQueryWrapper<TAppAuthHistory>()
                .eq(TAppAuthHistory::getOrgId, orgId).eq(TAppAuthHistory::getStatus, 1));
        Map<Long, TAppAuthHistory> map = new HashMap<>();
        if (CollectionUtils.isNotEmpty(records)) {
            map = records.stream().collect(
                    Collectors.toMap(
                            TAppAuthHistory::getAppId,
                            history -> history,
                            (existing, replacement) -> existing
                    )
            );
        }
        Map<Long, TAppAuthHistory> finalMap = map;

        List<TAppAuthHistory> dbList = new ArrayList<>();
        List<TAppAuthHistory> addList = appIds.stream().filter(appId -> !finalMap.containsKey(appId)).map(appId -> {
            TAppAuthHistory record = new TAppAuthHistory();
            record.setAppInfo(appId, null, tSyncAppSystemManageMapper);
            record.setOrgId(orgId);
            record.setOrgName(orgName);
            record.setCscpUserDetail(SecurityUtils.getCurrentCscpUserDetail());
            record.setOptType(TAppAuthHistory.OptTypeEnum.ADD.getValue());
            return record;
        }).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(addList)) {
            dbList.addAll(addList);
        }

        List<TAppAuthHistory> deleteList = records.stream().filter(record -> !appIds.contains(record.getAppId())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(deleteList)) {
            for (TAppAuthHistory record : deleteList) {
                record.setStatus(0);
                record.setOptType(TAppAuthHistory.OptTypeEnum.DELETE.getValue());
                record.setCscpUserDetail(SecurityUtils.getCurrentCscpUserDetail());
            }
            dbList.addAll(deleteList);
        }
        if (CollectionUtils.isNotEmpty(dbList)) {
            this.saveOrUpdateBatch(dbList);
        }
    }

    @Override
    public void saveAppAuthByOrg2(Long orgId, CscpUserDetail cscpUserDetail, List<String> appCodes) {
        if (orgId == null) {
            return;
        }
        List<TAppAuthHistory> records = tAppAuthHistoryMapper.selectListNoAdd(new LambdaQueryWrapper<TAppAuthHistory>()
                .eq(TAppAuthHistory::getOrgId, orgId).eq(TAppAuthHistory::getStatus, 1));
        Map<String, TAppAuthHistory> map = new HashMap<>();
        if (CollectionUtils.isNotEmpty(records)) {
            map = records.stream().collect(
                    Collectors.toMap(
                            TAppAuthHistory::getAppCode,
                            history -> history,
                            (existing, replacement) -> existing
                    )
            );
        }
        Map<String, TAppAuthHistory> finalMap = map;

        List<TAppAuthHistory> dbList = new ArrayList<>();
        List<TAppAuthHistory> addList = appCodes.stream().filter(appCode -> !finalMap.containsKey(appCode)).map(appCode -> {
            TAppAuthHistory record = new TAppAuthHistory();
            record.setAppInfo(null, appCode, tSyncAppSystemManageMapper);
            record.setOrgInfo(orgId, cscpOrgRepository);
            record.setCscpUserDetail(cscpUserDetail);
            record.setOptType(TAppAuthHistory.OptTypeEnum.ROllBACK.getValue());
            record.setDocumentReason(record.getAppName() + "推送失败，取消授权回滚");
            return record;
        }).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(addList)) {
            dbList.addAll(addList);
        }

        List<TAppAuthHistory> deleteList = records.stream().filter(record -> !appCodes.contains(record.getAppCode())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(deleteList)) {
            for (TAppAuthHistory record : deleteList) {
                record.setOptType(TAppAuthHistory.OptTypeEnum.ROllBACK.getValue());
                record.setCscpUserDetail(cscpUserDetail);
                record.setStatus(0);
                record.setDocumentReason(record.getAppName() + "推送失败，新增授权回滚");
            }
            dbList.addAll(deleteList);
        }
        if (CollectionUtils.isNotEmpty(dbList)) {
            this.saveOrUpdateBatch(dbList);
        }
    }

    @Override
    public void saveAppAuthByOrg3(Long orgId, String orgName, String pushAppCode) {

        if (orgId == null) {
            return;
        }
        List<TAppAuthHistory> records = tAppAuthHistoryMapper.selectListNoAdd(new LambdaQueryWrapper<TAppAuthHistory>()
                .eq(TAppAuthHistory::getOrgId, orgId).eq(TAppAuthHistory::getStatus, 1));
        Map<String, TAppAuthHistory> map = new HashMap<>();
        if (CollectionUtils.isNotEmpty(records)) {
            map = records.stream().collect(
                    Collectors.toMap(
                            TAppAuthHistory::getAppCode,
                            history -> history,
                            (existing, replacement) -> existing
                    )
            );
        }
        Map<String, TAppAuthHistory> finalMap = map;

        List<String> appCodes = Arrays.asList(pushAppCode.split(","));
        List<TAppAuthHistory> dbList = new ArrayList<>();
        List<TAppAuthHistory> addList = appCodes.stream().filter(appCode -> !finalMap.containsKey(appCode)).map(appCode -> {
            TAppAuthHistory record = new TAppAuthHistory();
            record.setAppInfo(null, appCode, tSyncAppSystemManageMapper);
            record.setOrgInfo(orgId, cscpOrgRepository);
            record.setCscpUserDetail(SecurityUtils.getCurrentCscpUserDetail());
            record.setOptType(TAppAuthHistory.OptTypeEnum.ADD.getValue());
            return record;
        }).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(addList)) {
            dbList.addAll(addList);
        }

        List<TAppAuthHistory> deleteList = records.stream().filter(record -> !appCodes.contains(record.getAppCode())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(deleteList)) {
            for (TAppAuthHistory record : deleteList) {
                record.setOptType("取消授权");
                record.setStatus(0);
                record.setCscpUserDetail(SecurityUtils.getCurrentCscpUserDetail());
                record.setOptType(TAppAuthHistory.OptTypeEnum.DELETE.getValue());
            }
            dbList.addAll(deleteList);
        }
        if (CollectionUtils.isNotEmpty(dbList)) {
            this.saveOrUpdateBatch(dbList);
        }
    }

}
