package com.ctsi.hndxoa.entity.dto;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 接收业务系统机构
 */
@Data
@ApiModel(value="SyncReceiveUserDTO对象", description="接收业务系统用户类")
public class SyncReceiveOrgDTO {

    /**
     * id
     */
    private String id;

    /**
     * 组织机构名称
     */
    private String name;

    /**
     * 组织机构编码
     */
    private String code;

    /**
     * 上级机构
     */
    private String parentId;

    private String orderBy;

    /**
     * 失败原因
     */
    @ApiModelProperty(value = "失败原因")
    @ExcelProperty("失败原因")
    private String failedReason;

    @ApiModelProperty(value = "根节点code")
    @ExcelIgnore
    private String rootId;

    @ApiModelProperty(value = "结构数据")
    @ExcelIgnore
    private List<SyncReceiveOrgDTO> dataList;
}
