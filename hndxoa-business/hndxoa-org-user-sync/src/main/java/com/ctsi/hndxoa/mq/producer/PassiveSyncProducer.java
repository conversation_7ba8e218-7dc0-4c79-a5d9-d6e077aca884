package com.ctsi.hndxoa.mq.producer;

import com.ctsi.hndxoa.mq.consumer.PassiveSyncConsumer;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR> 15433
 * @date : 2025/07/23/9:09
 * description:
 */
@Service
public class PassiveSyncProducer {

    @Autowired
    private RabbitTemplate rabbitTemplate;

    public void syncToProject(String exchange, String appCode, String userJson) {
        rabbitTemplate.convertAndSend(exchange, appCode, userJson);
    }

    /**
     * 发送消息
     * @param userJson 单个
     */
    public void sendOneMessage(String userJson) {
        rabbitTemplate.convertAndSend(PassiveSyncConsumer.EXCHANGE, PassiveSyncConsumer.ROUTING_KEY, userJson);
    }


    /**
     * 发送消息
     * @param userJson 批量
     */
    public void sendManyMessage(String userJson) {
        rabbitTemplate.convertAndSend(PassiveSyncConsumer.EXCHANGE2, PassiveSyncConsumer.ROUTING_KEY2, userJson);
    }

}
