package com.ctsi.hndxoa.mq.producer;

import com.alibaba.fastjson.JSONObject;
import com.ctsi.hndxoa.entity.dto.HttpPushOrgDTO;
import com.ctsi.hndxoa.mq.consumer.HttpPushOrgConsumer;
import com.ctsi.hndxoa.service.ITSyncAppSystemManageService;
import com.ctsi.hndxoa.service.constant.SyncAppSystemEnum;
import com.ctsi.ssdc.admin.repository.CscpOrgRepository;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class HttpPushOrgProducer {


    @Autowired
    private RabbitTemplate rabbitTemplate;

    @Autowired
    private ITSyncAppSystemManageService systemManageService;

    @Autowired
    private CscpOrgRepository cscpOrgRepository;


    public void syncToProject(String exchange, String appCode, String userJson) {
        rabbitTemplate.convertAndSend(exchange, appCode, userJson);
    }

    public void httpPushOrgToQueue(String userJson) {
        rabbitTemplate.convertAndSend(HttpPushOrgConsumer.EXCHANGE, HttpPushOrgConsumer.ROUTING_KEY, userJson);
    }

    public void httpPushOrgToQueueWaiBu(String userJson) {
        rabbitTemplate.convertAndSend(HttpPushOrgConsumer.WAIBU_EXCHANGE, HttpPushOrgConsumer.WAIBU_ROUTING_KEY, userJson);
    }

    public void httpPushOrg(HttpPushOrgDTO httpPushOrgDTO) {
        if (httpPushOrgDTO == null || httpPushOrgDTO.getAppSystemManage() == null) {
            return;
        }
        String message = JSONObject.toJSONString(httpPushOrgDTO);
        if (SyncAppSystemEnum.WAI_BU.getCode() == httpPushOrgDTO.getAppSystemManage().getInSystemFlag()) {
            httpPushOrgToQueueWaiBu(message);
        } else {
            httpPushOrgToQueue(message);
        }
    }
}
