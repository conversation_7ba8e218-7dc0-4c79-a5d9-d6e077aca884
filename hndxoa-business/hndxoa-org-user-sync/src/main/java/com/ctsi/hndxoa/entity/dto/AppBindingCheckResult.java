package com.ctsi.hndxoa.entity.dto;

import com.ctsi.hndxoa.entity.TSyncAppSystemManage;
import lombok.Data;

import java.util.List;

/**
 * 应用绑定检查结果
 */
@Data
public class AppBindingCheckResult {
    /** 是否需要直接返回数据 */
    private boolean directReturn = false;
    
    /** 绑定的应用名称 */
    private String bindingAppName;

    /** 绑定的应用id集合 */
    private List<Long> bindingAppIdList;
    
    /** 应用数据 */
    private List<TSyncAppSystemManage> appData;
    
}
