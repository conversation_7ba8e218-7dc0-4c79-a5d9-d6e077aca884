package com.ctsi.hndxoa.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.ctsi.hndx.common.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
* 应用授权市县区单位
* @ TableName t_sync_app_system_manage_company
*/
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("t_sync_app_system_manage_company")
@ApiModel(value = "TSyncAppSystemManageCompany对象", description = "应用授权市县区单位表")
public class TSyncAppSystemManageCompany extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
    * 应用id
    */
    @ApiModelProperty("应用id")
    private Long appId;

    /**
    * 应用名称
    */
    @ApiModelProperty("应用名称")
    private String appName;

    /**
    * 应用编码
    */
    @ApiModelProperty("应用编码")
    private String appCode;

    /**
    * 机构id
    */
    @ApiModelProperty("机构id")
    private Long orgId;

    /**
    * 机构名称
    */
    @ApiModelProperty("机构名称")
    private String orgName;

}
