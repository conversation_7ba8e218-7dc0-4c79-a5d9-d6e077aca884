package com.ctsi.hndxoa.controller;

import com.ctsi.hndx.annotations.ResponseResultVo;
import com.ctsi.hndx.common.BaseController;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.enums.DBOperation;
import com.ctsi.hndx.exception.BusinessException;
import com.ctsi.hndx.result.ResultCode;
import com.ctsi.hndx.result.ResultVO;
import com.ctsi.hndx.westoneuas.util.trace.EnableTraceWatch;
import com.ctsi.hndx.westoneuas.util.trace.TraceLevel;
import com.ctsi.hndxoa.entity.TSyncAppSystemManage;
import com.ctsi.hndxoa.entity.dto.HttpPushUserDTO;
import com.ctsi.hndxoa.entity.dto.SyncOrgUserBatchDTO;
import com.ctsi.hndxoa.entity.dto.SyncOrgUserDTO;
import com.ctsi.hndxoa.entity.dto.TSyncAppSystemManageDTO;
import com.ctsi.hndxoa.mq.producer.HttpPushUserProducer;
import com.ctsi.hndxoa.pull.service.ITSyncAppSystemManagePullService;
import com.ctsi.hndxoa.service.ITSyncAppSystemManageService;
import com.ctsi.hndxoa.service.IWestoneUasManageService;
import com.ctsi.hndxoa.service.constant.OrgUserConstants;
import com.ctsi.hndxoa.westone.queue.SyncBatchStock;
import com.ctsi.hndxoa.westone.queue.SyncType;
import com.ctsi.hndxoa.westone.queue.WestoneSyncBatchQueue;
import com.ctsi.ssdc.admin.domain.dto.CscpOrgDTO;
import com.ctsi.ssdc.admin.domain.dto.CscpUserDTO;
import com.ctsi.ssdc.admin.westoneusa.SyncUserHistroyRecordDTO;
import com.ctsi.ssdc.annotation.OperationLog;
import com.ctsi.ssdc.model.PageResult;
import com.ctsi.ssdc.model.ResResult;
import com.ctsi.ssdc.security.CscpUserDetail;
import com.ctsi.ssdc.security.SecurityUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-27
 *
 */

@Slf4j
@RestController
@ResponseResultVo
@RequestMapping("/api/tSyncAppSystemManage")
@Api(value = "同步应用系统管理表", tags = "同步应用系统管理表接口")
public class TSyncAppSystemManageController extends BaseController {

    private static final String ENTITY_NAME = "tSyncAppSystemManage";

    @Autowired
    private ITSyncAppSystemManageService tSyncAppSystemManageService;

    @Autowired
    private IWestoneUasManageService westoneUasManageService;

    @Autowired
    private HttpPushUserProducer httpPushUserProducer;

    @Autowired
    private ITSyncAppSystemManagePullService itSyncAppSystemManagePullService;

    /**
     *  新增同步应用系统管理表批量数据.
     */
    @PostMapping("/createBatch")
    @ApiOperation(value = "新增批量(权限code码为：cscp.tSyncAppSystemManage.add)", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.ADD,message = "新增同步应用系统管理表批量数据")
    @PreAuthorize("@permissionService.hasPermi('cscp.tSyncAppSystemManage.add')")
    public ResultVO createBatch(@RequestBody List<TSyncAppSystemManageDTO> tSyncAppSystemManageList) {
       Boolean  result = tSyncAppSystemManageService.insertBatch(tSyncAppSystemManageList);
       if(result){
           return ResultVO.success();
       }else {
           return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
       }
    }

     /**
     *  新增数据.
     */
    @PostMapping("/create")
    @ApiOperation(value = "新增(权限code码为：cscp.tSyncAppSystemManage.add)", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.ADD,message = "新增同步应用系统管理表数据")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tSyncAppSystemManage.add')")
    public ResultVO<TSyncAppSystemManageDTO> create(@RequestBody TSyncAppSystemManageDTO tSyncAppSystemManageDTO)  {
        TSyncAppSystemManageDTO result = tSyncAppSystemManageService.create(tSyncAppSystemManageDTO);
        return ResultVO.success(result);
    }

    /**
     *  更新存在数据.
     */
    @PostMapping("/update")
    @ApiOperation(value = "更新存在数据(权限code码为：cscp.tSyncAppSystemManage.update)", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.UPDATE,message = "更新同步应用系统管理表数据")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tSyncAppSystemManage.update')")
    public ResultVO update(@RequestBody TSyncAppSystemManageDTO tSyncAppSystemManageDTO) {
	    Assert.notNull(tSyncAppSystemManageDTO.getId(), "general.IdNotNull");
        int count = tSyncAppSystemManageService.update(tSyncAppSystemManageDTO);
        if(count > 0 ){
            return ResultVO.success();
        }else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }

     /**
     *  删除存在数据.
     */
    @DeleteMapping("/delete/{id}")
    @OperationLog(dBOperation = DBOperation.DELETE,message = "删除同步应用系统管理表数据")
    @ApiOperation(value = "删除存在数据(权限code码为：cscp.tSyncAppSystemManage.delete)", notes = "传入参数")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tSyncAppSystemManage.delete')")
    public ResultVO delete(@PathVariable Long id) {
        int count = tSyncAppSystemManageService.delete(id);
        if(count > 0 ){
            return ResultVO.success();
        }else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }

    /**
     * 查询单条数据.
     */
    @GetMapping("/get/{id}")
    @ApiOperation(value = "查询单条数据", notes = "传入参数")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
    public ResultVO get(@PathVariable Long id) {
        TSyncAppSystemManageDTO tSyncAppSystemManageDTO = tSyncAppSystemManageService.findOne(id);
        return ResultVO.success(tSyncAppSystemManageDTO);
    }

    /**
    *  分页查询多条数据.
    */
    @GetMapping("/queryTSyncAppSystemManagePage")
    @ApiOperation(value = "翻页查询多条数据", notes = "传入参数")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
    public ResultVO<PageResult<TSyncAppSystemManageDTO>> queryTSyncAppSystemManagePage(TSyncAppSystemManageDTO tSyncAppSystemManageDTO, BasePageForm basePageForm) {
        return ResultVO.success(tSyncAppSystemManageService.queryListPage(tSyncAppSystemManageDTO, basePageForm));
    }

    @GetMapping("/unitAdmin/queryAppPage")
    @ApiOperation(value = "单位管理员查询可授权应用列表", notes = "传入参数")
    public ResultVO<PageResult<TSyncAppSystemManageDTO>> unitAdminQueryAppPage(TSyncAppSystemManageDTO tSyncAppSystemManageDTO, BasePageForm basePageForm) {
        return ResultVO.success(tSyncAppSystemManageService.unitAdminQueryAppPage(tSyncAppSystemManageDTO, basePageForm));
    }

   /**
    * 查询多条数据.不分页
    */
   @GetMapping("/queryTSyncAppSystemManage")
   @ApiOperation(value = "查询多条数据", notes = "传入参数")
   //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
   public ResultVO<ResResult<TSyncAppSystemManageDTO>> queryTSyncAppSystemManage(TSyncAppSystemManageDTO tSyncAppSystemManageDTO) {
       List<TSyncAppSystemManageDTO> list = tSyncAppSystemManageService.queryList(tSyncAppSystemManageDTO);
       return ResultVO.success(new ResResult<TSyncAppSystemManageDTO>(list));
   }

    @PostMapping("/syncOrg")
    @ApiOperation(value = "同步机构信息-第三方系统", notes = "传入参数")
    public ResultVO syncOrg(@RequestBody SyncOrgUserDTO syncOrgUserDTO){
        boolean pull = itSyncAppSystemManagePullService.sendPullManages(syncOrgUserDTO.getAppId(), OrgUserConstants.PushType.PUSH_USER, syncOrgUserDTO.getOrgId());
        if (!pull) {
            boolean success = tSyncAppSystemManageService.syncOrg(syncOrgUserDTO);
            if(success){
                return ResultVO.success();
            }else {
                return ResultVO.error(ResultCode.SYNC_DATA_FAIL);
            }
        } else {
            return ResultVO.success();
        }
    }

    @PostMapping("/syncUser")
    @ApiOperation(value = "同步用户信息-第三方系统", notes = "传入参数")
    public ResultVO syncUser(@RequestBody SyncOrgUserDTO syncOrgUserDTO){
        boolean pull = itSyncAppSystemManagePullService.sendPullManages(syncOrgUserDTO.getAppId(), OrgUserConstants.PushType.PUSH_USER, syncOrgUserDTO.getUserId());
        if (!pull) {
            boolean success = tSyncAppSystemManageService.syncUser(syncOrgUserDTO);
            if(success){
                return ResultVO.success();
            }else {
                return ResultVO.error(ResultCode.SYNC_DATA_FAIL);
            }
        } else {
            return ResultVO.success();
        }
    }

    @PostMapping("/syncOrgBatch")
    @ApiOperation(value = "批量同步机构信息", notes = "传入参数")
    public ResultVO syncOrgBatch(@RequestBody SyncOrgUserBatchDTO syncOrgUserBatchDTO){
        List<Long> orgIds = syncOrgUserBatchDTO.getOrgIds();
        TSyncAppSystemManage tSyncAppSystemManage = tSyncAppSystemManageService.getById(syncOrgUserBatchDTO.getAppId());
        // 被动推送前置
        boolean isPull = itSyncAppSystemManagePullService.batchSendPullManages(tSyncAppSystemManage,null, OrgUserConstants.PushType.PUSH_UNIT, orgIds);
        if(!isPull) {
            // 主动推送
            if(tSyncAppSystemManage.getInSystemFlag() == 0){
                orgIds.forEach(e -> {
                    SyncOrgUserDTO syncOrgUserDTO = new SyncOrgUserDTO();
                    syncOrgUserDTO.setOrgId(e);
                    syncOrgUserDTO.setAppId(syncOrgUserBatchDTO.getAppId());
                    tSyncAppSystemManageService.syncOrg(syncOrgUserDTO);
                });
            }else if (tSyncAppSystemManage.getInSystemFlag() == 1){
                orgIds.forEach(e -> {
                    SyncOrgUserDTO syncOrgUserDTO = new SyncOrgUserDTO();
                    syncOrgUserDTO.setOrgId(e);
                    syncOrgUserDTO.setAppId(syncOrgUserBatchDTO.getAppId());
                    tSyncAppSystemManageService.syncOrgInSystem(syncOrgUserDTO);
                });
            }
        }

        return ResultVO.success();
    }

    @PostMapping("/syncUserBatch")
    @ApiOperation(value = "批量同步用户信息", notes = "传入参数")
    public ResultVO syncUserBatch(@RequestBody SyncOrgUserBatchDTO syncOrgUserBatchDTO){
        List<Long> userIds = syncOrgUserBatchDTO.getUserIds();
        TSyncAppSystemManage tSyncAppSystemManage = tSyncAppSystemManageService.getById(syncOrgUserBatchDTO.getAppId());
        // 被动推送前置
        boolean isPull = itSyncAppSystemManagePullService.batchSendPullManages(tSyncAppSystemManage, null, OrgUserConstants.PushType.PUSH_USER, userIds);
        if(!isPull) {
            if(tSyncAppSystemManage.getInSystemFlag() == 0){
                userIds.forEach(e -> {
                    SyncOrgUserDTO syncOrgUserDTO = new SyncOrgUserDTO();
                    syncOrgUserDTO.setUserId(e);
                    syncOrgUserDTO.setAppId(syncOrgUserBatchDTO.getAppId());
                    tSyncAppSystemManageService.syncUser(syncOrgUserDTO);
                });
            }else if (tSyncAppSystemManage.getInSystemFlag() == 1){
                userIds.forEach(e -> {
                    SyncOrgUserDTO syncOrgUserDTO = new SyncOrgUserDTO();
                    syncOrgUserDTO.setUserId(e);
                    syncOrgUserDTO.setAppId(syncOrgUserBatchDTO.getAppId());
                    tSyncAppSystemManageService.syncUserInSystem(syncOrgUserDTO);
                });
            }
        }

        return ResultVO.success();
    }

    // ==============================（卫士通）一键批量同步所有开始==============================
    @PostMapping("/syncWestoneOrgBatch")
    @ApiOperation(value = "一键批量同步所有组织机构信息", notes = "传入参数")
    @EnableTraceWatch(level = TraceLevel.INFO, notes = "一键批量同步所有组织机构信息")
    public ResultVO syncWestoneOrgBatch(@RequestBody SyncOrgUserDTO syncOrgUserDTO) {
        WestoneSyncBatchQueue.getInstance()
                .addBlockingQueueData(new SyncBatchStock(SyncType.ORG, syncOrgUserDTO, westoneUasManageService));
        return ResultVO.success();
    }

    @PostMapping("/syncWestoneUserBatch")
    @ApiOperation(value = "一键批量同步所有用户信息", notes = "传入参数")
    public ResultVO syncWestoneUserBatch(@RequestBody SyncOrgUserDTO syncOrgUserDTO) {
        WestoneSyncBatchQueue.getInstance()
                .addBlockingQueueData(new SyncBatchStock(SyncType.USER, syncOrgUserDTO, westoneUasManageService));
        return ResultVO.success();
    }

    @PostMapping("/syncUnPushedWestoneUserBatch")
    @ApiOperation(value = "一键批量同步所有未推送用户信息", notes = "传入参数")
    public ResultVO syncUnPushedWestoneUserBatch(@RequestBody SyncOrgUserDTO syncOrgUserDTO) {
        WestoneSyncBatchQueue.getInstance()
                .addBlockingQueueData(new SyncBatchStock(SyncType.USER_UN_PUSHED, syncOrgUserDTO, westoneUasManageService));
        return ResultVO.success();
    }

    /**
     * 添加推送用户日志记录
     */
    @PostMapping("/addPushUserLog")
    @ApiOperation(value = "添加推送用户日志记录", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.ADD,message = "添加推送用户日志记录")
    public ResultVO create(@RequestBody SyncUserHistroyRecordDTO syncUserHistroyRecordDTO)  {
        westoneUasManageService.addPushUserLog(syncUserHistroyRecordDTO);
        return ResultVO.success();
    }

    // ==============================（卫士通）一键批量同步所有结束==============================

    @PostMapping("/syncOrgInSystem")
    @ApiOperation(value = "内部系统同步机构信息", notes = "传入参数")
    public ResultVO syncOrgInSystem(@RequestBody SyncOrgUserDTO syncOrgUserDTO){
        boolean pull = itSyncAppSystemManagePullService.sendPullManages(syncOrgUserDTO.getAppId(), OrgUserConstants.PushType.PUSH_UNIT, syncOrgUserDTO.getOrgId());
        if (!pull) {
            Pair<Boolean, String> success = tSyncAppSystemManageService.syncOrgInSystem(syncOrgUserDTO);
            if(success.getKey()){
                return ResultVO.success();
            }else {
                return ResultVO.error(ResultCode.SYNC_DATA_FAIL.message(), success.getValue());
            }
        } else {
            return ResultVO.success();
        }
    }

    @PostMapping("/batch/syncOrgInSystem")
    @ApiOperation(value = "批量-内部系统同步机构信息", notes = "传入参数")
    public ResultVO batchSyncOrgInSystem(@RequestBody List<SyncOrgUserDTO> syncOrgUserDTOList){
        Pair<Boolean, String> success = tSyncAppSystemManageService.batchSyncOrgInSystem(syncOrgUserDTOList);
        if(success.getKey()){
            return ResultVO.success();
        }else {
            return ResultVO.error(ResultCode.SYNC_DATA_FAIL.message(), success.getValue());
        }
    }

    @PostMapping("/syncUserInSystem")
    @ApiOperation(value = "内部系统同步用户信息", notes = "传入参数")
    public ResultVO syncUserInSystem(@RequestBody SyncOrgUserDTO syncOrgUserDTO){
        boolean pull = itSyncAppSystemManagePullService.sendPullManages(syncOrgUserDTO.getAppId(), OrgUserConstants.PushType.PUSH_USER, syncOrgUserDTO.getUserId());
        if (!pull) {
            Pair<Boolean, String> success = tSyncAppSystemManageService.syncUserInSystem(syncOrgUserDTO);
            if(success.getKey()){
                return ResultVO.success();
            }else {
                return ResultVO.error(ResultCode.SYNC_DATA_FAIL.message(), success.getValue());
            }
        } else {
            return ResultVO.success();
        }
    }

    @PostMapping("/batch/syncUserInSystem")
    @ApiOperation(value = "批量-内部系统同步用户信息", notes = "传入参数")
    public ResultVO batchSyncUserInSystem(@RequestBody List<SyncOrgUserDTO> syncOrgUserDTOList){
        Pair<Boolean, String> success = tSyncAppSystemManageService.batchSyncUserInSystem(syncOrgUserDTOList);
        if(success.getKey()){
            return ResultVO.success();
        }else {
            return ResultVO.error(ResultCode.SYNC_DATA_FAIL.message(), success.getValue());
        }
    }

    @PostMapping("/syncUserBusiness")
    @ApiOperation(value = "用户修改、禁用推送业务系统-临时方法", notes = "传入参数")
    public ResultVO syncUserBusiness(@RequestBody SyncOrgUserDTO syncOrgUserDTO){
        syncOrgUserDTO.setIsAutoPushFlag(true);
        tSyncAppSystemManageService.syncUserBusiness(syncOrgUserDTO);
        return ResultVO.success();
    }

    @PostMapping("/syncOrgBusiness")
    @ApiOperation(value = "机构修改、删除推送业务系统-临时方法", notes = "传入参数")
    public ResultVO syncOrgBusiness(@RequestBody SyncOrgUserDTO syncOrgUserDTO){
        syncOrgUserDTO.setIsAutoPushFlag(true);
        tSyncAppSystemManageService.syncOrgBusiness(syncOrgUserDTO);
        return ResultVO.success();
    }

    @PostMapping("/syncOrgBatchInSystem")
    @ApiOperation(value = "内部系统批量同步机构信息", notes = "传入参数")
    public ResultVO syncOrgBatchInSystem(@RequestBody SyncOrgUserBatchDTO syncOrgUserBatchDTO){
        List<Long> orgIds = syncOrgUserBatchDTO.getOrgIds();
        TSyncAppSystemManage tSyncAppSystemManage = tSyncAppSystemManageService.getById(syncOrgUserBatchDTO.getAppId());
        boolean pull = itSyncAppSystemManagePullService.batchSendPullManages(tSyncAppSystemManage, null, OrgUserConstants.PushType.PUSH_UNIT, orgIds);
        if (!pull) {
            if (tSyncAppSystemManage.getInSystemFlag() == 0) {
                orgIds.forEach(e -> {
                    SyncOrgUserDTO syncOrgUserDTO = new SyncOrgUserDTO();
                    syncOrgUserDTO.setOrgId(e);
                    if (null != syncOrgUserBatchDTO.getFlag() && syncOrgUserBatchDTO.getFlag().equals("delete")) {
                        syncOrgUserDTO.setFlag(syncOrgUserBatchDTO.getFlag());
                        syncOrgUserDTO.setIsAutoPushFlag(true);
                    }
                    syncOrgUserDTO.setAppId(syncOrgUserBatchDTO.getAppId());
                    tSyncAppSystemManageService.syncOrg(syncOrgUserDTO);
                });
            } else if (tSyncAppSystemManage.getInSystemFlag() == 1) {
                orgIds.forEach(e -> {
                    SyncOrgUserDTO syncOrgUserDTO = new SyncOrgUserDTO();
                    syncOrgUserDTO.setOrgId(e);
                    if (null != syncOrgUserBatchDTO.getFlag() && syncOrgUserBatchDTO.getFlag().equals("delete")) {
                        syncOrgUserDTO.setFlag(syncOrgUserBatchDTO.getFlag());
                        syncOrgUserDTO.setIsAutoPushFlag(true);
                    }
                    syncOrgUserDTO.setAppId(syncOrgUserBatchDTO.getAppId());
                    tSyncAppSystemManageService.syncOrgInSystem(syncOrgUserDTO);
                });
            }
        }
        return ResultVO.success();
    }

    @PostMapping("/syncUserBatchInSystem")
    @ApiOperation(value = "内部系统批量同步用户信息", notes = "传入参数")
    public ResultVO syncUserBatchInSystem(@RequestBody SyncOrgUserBatchDTO syncOrgUserBatchDTO){
        List<Long> userIds = syncOrgUserBatchDTO.getUserIds();
        TSyncAppSystemManage tSyncAppSystemManage = tSyncAppSystemManageService.getById(syncOrgUserBatchDTO.getAppId());
        boolean pull = itSyncAppSystemManagePullService.batchSendPullManages(tSyncAppSystemManage, null, OrgUserConstants.PushType.PUSH_USER, userIds);
        if (!pull) {
            if (tSyncAppSystemManage.getInSystemFlag() == 0) {
                userIds.forEach(e -> {
                    SyncOrgUserDTO syncOrgUserDTO = new SyncOrgUserDTO();
                    syncOrgUserDTO.setUserId(e);
                    if (null != syncOrgUserBatchDTO.getFlag() && syncOrgUserBatchDTO.getFlag().equals("delete")) {
                        syncOrgUserDTO.setFlag(syncOrgUserBatchDTO.getFlag());
                        syncOrgUserDTO.setIsAutoPushFlag(true);
                    }
                    syncOrgUserDTO.setAppId(syncOrgUserBatchDTO.getAppId());
                    tSyncAppSystemManageService.syncUser(syncOrgUserDTO);
                });
            } else if (tSyncAppSystemManage.getInSystemFlag() == 1) {
                userIds.forEach(e -> {
                    SyncOrgUserDTO syncOrgUserDTO = new SyncOrgUserDTO();
                    syncOrgUserDTO.setUserId(e);
                    if (null != syncOrgUserBatchDTO.getFlag() && syncOrgUserBatchDTO.getFlag().equals("delete")) {
                        syncOrgUserDTO.setFlag(syncOrgUserBatchDTO.getFlag());
                        syncOrgUserDTO.setIsAutoPushFlag(true);
                    }
                    syncOrgUserDTO.setAppId(syncOrgUserBatchDTO.getAppId());
                    tSyncAppSystemManageService.syncUserInSystem(syncOrgUserDTO);
                });
            }
        }
        return ResultVO.success();
    }

    @PostMapping("/http/syncUserBatchToApp")
    @ApiOperation(value = "批量推送用户信息http", notes = "传入参数")
    public ResultVO<String> syncUserBatchToApp(@RequestBody SyncOrgUserBatchDTO syncOrgUserBatchDTO){
        List<Long> userIds = syncOrgUserBatchDTO.getUserIds();
        if (CollectionUtils.isEmpty(userIds)) {
            throw new BusinessException(ResultCode.PARAM_IS_INVALID);
        }
        if (syncOrgUserBatchDTO.getAppId() == null) {
            throw new BusinessException(ResultCode.PARAM_IS_INVALID);
        }
        TSyncAppSystemManage tSyncAppSystemManage = tSyncAppSystemManageService.getById(syncOrgUserBatchDTO.getAppId());
        if (tSyncAppSystemManage == null) {
            throw new BusinessException("应用不存在或未启用");
        }
        CscpUserDetail cscpUserDetail = SecurityUtils.getCurrentCscpUserDetail();
        CompletableFuture.runAsync(() -> userIds.forEach(e -> {
            HttpPushUserDTO httpPushUserDTO = new HttpPushUserDTO();
            httpPushUserDTO.setAppSystemManage(tSyncAppSystemManage);
            httpPushUserDTO.setUserId(e);
            httpPushUserDTO.setCscpUserDetail(cscpUserDetail);
            httpPushUserProducer.httpPushUser(httpPushUserDTO);
        }));

        return ResultVO.success();
    }

    @ApiOperation(value = "编辑用户-授权")
    @PutMapping("/updateCscpUser")
    @ResponseResultVo
    public ResultVO<String> autoUserApp(@RequestBody CscpUserDTO cscpUserDTO) {
        tSyncAppSystemManageService.autoUserApp(cscpUserDTO, false);
        return ResultVO.success();
    }

    @ApiOperation(value = "编辑机构-授权")
    @PutMapping("/autoOrgApp")
    @ResponseResultVo
    public ResultVO<String> autoOrgApp(@RequestBody CscpOrgDTO cscpOrgDTO) {
        tSyncAppSystemManageService.autoOrgApp(cscpOrgDTO, false);
        return ResultVO.success();
    }

    @PostMapping("/syncUserBatchByCompany")
    @ApiOperation(value = "同步指定单位下所有用户信息", notes = "传入companyId和appId")
    public ResultVO syncCompanyUsers(@RequestBody SyncOrgUserDTO syncOrgUserDTO) {
        List<Long> userIds = tSyncAppSystemManageService.getAllUserByCompanyId(syncOrgUserDTO);
        SyncOrgUserBatchDTO userBatchDTO = new SyncOrgUserBatchDTO();
        userBatchDTO.setAppId(syncOrgUserDTO.getAppId());
        userBatchDTO.setUserIds(userIds);
        return this.syncUserBatchInSystem(userBatchDTO);
    }
}
