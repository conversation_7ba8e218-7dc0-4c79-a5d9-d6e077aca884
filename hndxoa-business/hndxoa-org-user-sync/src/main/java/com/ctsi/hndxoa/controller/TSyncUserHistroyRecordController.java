package com.ctsi.hndxoa.controller;

import com.ctsi.hndx.annotations.ResponseResultVo;
import com.ctsi.hndx.common.BaseController;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.enums.DBOperation;
import com.ctsi.hndx.result.ResultCode;
import com.ctsi.hndx.result.ResultVO;
import com.ctsi.hndxoa.entity.dto.TSyncUserHistoryRecordBackDTO;
import com.ctsi.hndxoa.entity.dto.TSyncUserHistroyRecordDTO;
import com.ctsi.hndxoa.service.ITSyncUserHistroyRecordService;
import com.ctsi.ssdc.annotation.OperationLog;
import com.ctsi.ssdc.model.PageResult;
import com.ctsi.ssdc.model.ResResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-27
 *
 */

@Slf4j
@RestController
@ResponseResultVo
@RequestMapping("/api/tSyncUserHistroyRecord")
@Api(value = "同步机构历史记录表", tags = "同步机构历史记录表接口")
public class TSyncUserHistroyRecordController extends BaseController {

    private static final String ENTITY_NAME = "tSyncUserHistroyRecord";

    @Autowired
    private ITSyncUserHistroyRecordService tSyncUserHistroyRecordService;



    /**
     *  新增同步机构历史记录表批量数据.
     */
    @PostMapping("/createBatch")
    @ApiOperation(value = "新增批量(权限code码为：cscp.tSyncUserHistroyRecord.add)", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.ADD,message = "新增同步机构历史记录表批量数据")
    @PreAuthorize("@permissionService.hasPermi('cscp.tSyncUserHistroyRecord.add')")
    public ResultVO createBatch(@RequestBody List<TSyncUserHistroyRecordDTO> tSyncUserHistroyRecordList) {
       Boolean  result = tSyncUserHistroyRecordService.insertBatch(tSyncUserHistroyRecordList);
       if(result){
           return ResultVO.success();
       }else {
           return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
       }
    }

     /**
     *  新增数据.
     */
    @PostMapping("/create")
    @ApiOperation(value = "新增(权限code码为：cscp.tSyncUserHistroyRecord.add)", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.ADD,message = "新增同步机构历史记录表数据")
    @PreAuthorize("@permissionService.hasPermi('cscp.tSyncUserHistroyRecord.add')")
    public ResultVO<TSyncUserHistroyRecordDTO> create(@RequestBody TSyncUserHistroyRecordDTO tSyncUserHistroyRecordDTO)  {
        TSyncUserHistroyRecordDTO result = tSyncUserHistroyRecordService.create(tSyncUserHistroyRecordDTO);
        return ResultVO.success(result);
    }

    /**
     *  更新存在数据.
     */
    @PostMapping("/update")
    @ApiOperation(value = "更新存在数据(权限code码为：cscp.tSyncUserHistroyRecord.update)", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.UPDATE,message = "更新同步机构历史记录表数据")
    @PreAuthorize("@permissionService.hasPermi('cscp.tSyncUserHistroyRecord.update')")
    public ResultVO update(@RequestBody TSyncUserHistroyRecordDTO tSyncUserHistroyRecordDTO) {
	    Assert.notNull(tSyncUserHistroyRecordDTO.getId(), "general.IdNotNull");
        int count = tSyncUserHistroyRecordService.update(tSyncUserHistroyRecordDTO);
        if(count > 0 ){
            return ResultVO.success();
        }else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }

     /**
     *  删除存在数据.
     */
    @DeleteMapping("/delete/{id}")
    @OperationLog(dBOperation = DBOperation.DELETE,message = "删除同步机构历史记录表数据")
    @ApiOperation(value = "删除存在数据(权限code码为：cscp.tSyncUserHistroyRecord.delete)", notes = "传入参数")
    public ResultVO delete(@PathVariable Long id) {
        int count = tSyncUserHistroyRecordService.delete(id);
        if(count > 0 ){
            return ResultVO.success();
        }else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }

    /**
     * 查询单条数据.
     */
    @GetMapping("/get/{id}")
    @ApiOperation(value = "查询单条数据", notes = "传入参数")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
    public ResultVO get(@PathVariable Long id) {
        TSyncUserHistroyRecordDTO tSyncUserHistroyRecordDTO = tSyncUserHistroyRecordService.findOne(id);
        return ResultVO.success(tSyncUserHistroyRecordDTO);
    }

    /**
    *  分页查询多条数据.
    */
    @GetMapping("/queryTSyncUserHistroyRecordPage")
    @ApiOperation(value = "翻页查询多条数据", notes = "传入参数")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
    public ResultVO<PageResult<TSyncUserHistroyRecordDTO>> queryTSyncUserHistroyRecordPage(TSyncUserHistroyRecordDTO tSyncUserHistroyRecordDTO, BasePageForm basePageForm) {
        return ResultVO.success(tSyncUserHistroyRecordService.queryListPage(tSyncUserHistroyRecordDTO, basePageForm));
    }

    /**
     *  分页查询多条数据.
     */
    @GetMapping("/queryTSyncUserHistroyRecordPageByRole")
    @ApiOperation(value = "翻页查询多条数据", notes = "传入参数")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
    public ResultVO<PageResult<TSyncUserHistroyRecordDTO>> queryTSyncUserHistroyRecordPageByRole(TSyncUserHistroyRecordDTO tSyncUserHistroyRecordDTO, BasePageForm basePageForm) {
        return ResultVO.success(tSyncUserHistroyRecordService.queryListPageByRole(tSyncUserHistroyRecordDTO, basePageForm));
    }

   /**
    * 查询多条数据.不分页
    */
   @GetMapping("/queryTSyncUserHistroyRecord")
   @ApiOperation(value = "查询多条数据", notes = "传入参数")
   //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
   public ResultVO<ResResult<TSyncUserHistroyRecordDTO>> queryTSyncUserHistroyRecord(TSyncUserHistroyRecordDTO tSyncUserHistroyRecordDTO) {
       List<TSyncUserHistroyRecordDTO> list = tSyncUserHistroyRecordService.queryList(tSyncUserHistroyRecordDTO);
       return ResultVO.success(new ResResult<TSyncUserHistroyRecordDTO>(list));
   }


    @PostMapping("/sync/callback")
    @ApiOperation(value = "同步回调", notes = "传入参数")
    public ResultVO<TSyncUserHistoryRecordBackDTO> syncCallback(@RequestBody TSyncUserHistoryRecordBackDTO dto) {
        try {
            Assert.notNull(dto.getSourceKey(), "sourceKey Not Null");
            Assert.notNull(dto.getEventId(), "eventId Not Null");
            tSyncUserHistroyRecordService.syncCallback(dto);
            return ResultVO.success(dto);
        } catch (Exception e) {
            return ResultVO.error(e.toString());
        }
    }

}
