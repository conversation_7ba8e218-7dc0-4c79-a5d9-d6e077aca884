package com.ctsi.hndxoa.mq.consumer;


import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ctsi.hndxoa.entity.dto.TMqDlx;
import com.ctsi.hndxoa.pull.entity.dto.PassiveSyncMessage;
import com.ctsi.hndxoa.pull.service.ITSyncAppSystemManagePullService;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.Exchange;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.QueueBinding;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 */
@Component
public class PassiveSyncConsumer {

    /**
     * 单个同步
     */
    public static final String EXCHANGE = "PassiveSyncExchange";
    public static final String QUEUE = "PassiveSyncQueue";
    public static final String ROUTING_KEY = "Passive";


    /**
     * 批量同步
     */
    public static final String EXCHANGE2 = "BatchPassiveSyncExchange";
    public static final String QUEUE2 = "BatchPassiveSyncQueue";
    public static final String ROUTING_KEY2 = "BatchPassive";

    @Autowired
    private ITSyncAppSystemManagePullService itSyncAppSystemManagePullService;
    @Autowired
    private DlxMessageQueue dlxMessageQueue;

    private static final Logger log = LoggerFactory.getLogger(PassiveSyncConsumer.class);

    @RabbitListener(bindings = @QueueBinding(
            value = @Queue(value = QUEUE, durable = "true"),
            exchange = @Exchange(value = EXCHANGE, type = "topic"),
            key = ROUTING_KEY
    ))
    public void onMessage(String message, Message amqpMessage) {
        try {
            JSONObject json = JSONObject.parseObject(message);
            PassiveSyncMessage passiveSyncMessage = json.toJavaObject(PassiveSyncMessage.class);
            itSyncAppSystemManagePullService.passiveSyncToApp(passiveSyncMessage);
        } catch (Exception e) {
            handleMessageException(QUEUE, ROUTING_KEY, message, e);
        }
    }

    @RabbitListener(bindings = @QueueBinding(
            value = @Queue(value = QUEUE2, durable = "true"),
            exchange = @Exchange(value = EXCHANGE2, type = "topic"),
            key = ROUTING_KEY2
    ))
    public void onMessage3(String message, Message amqpMessage) {
        try {
            JSONArray json = JSONObject.parseArray(message);
            List<PassiveSyncMessage> passiveSyncMessages = json.toJavaList(PassiveSyncMessage.class);
            itSyncAppSystemManagePullService.batchPassiveSyncToApp(passiveSyncMessages);
        } catch (Exception e) {
            handleMessageException(QUEUE2, ROUTING_KEY2, message, e);
        }
    }

    /**
     * 封装重复的异常处理逻辑
     */
    private void handleMessageException(String queue, String routingKey, String message, Exception e) {
        try {
            TMqDlx tMqDlx = new TMqDlx();
            tMqDlx.setBusinessQueue(queue);
            tMqDlx.setMessageId(routingKey);
            tMqDlx.setMessageBody(message);
            tMqDlx.setFailReason(StringUtils.substring(e.toString(), 0, 500));
            tMqDlx.setStatus("0");
            tMqDlx.setCreateTime(LocalDateTime.now());
            tMqDlx.setRetryCount(1);
            dlxMessageQueue.add(tMqDlx);
        } catch (Exception ex) {
            log.error("记录消息消费失败时发生异常, message={}, error={}", message, ex.toString());
        }
    }

}
