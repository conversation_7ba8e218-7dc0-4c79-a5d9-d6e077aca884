package com.ctsi.hndxoa.service;

import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.common.SysBaseServiceI;
import com.ctsi.hndxoa.entity.TSyncAppSystemManage;
import com.ctsi.hndxoa.entity.TSyncOrgHistroyRecord;
import com.ctsi.hndxoa.entity.dto.TSyncOrgHistroyRecordDTO;
import com.ctsi.ssdc.model.PageResult;

import java.util.List;

/**
 * <p>
 * 同步机构历史记录表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-27
 */
public interface ITSyncOrgHistroyRecordService extends SysBaseServiceI<TSyncOrgHistroyRecord> {


    /**
     * 分页查询
     *
     * @param entityDTO
     * @param page
     * @return
     */
    PageResult<TSyncOrgHistroyRecordDTO> queryListPage(TSyncOrgHistroyRecordDTO entityDTO, BasePageForm page);

    /**
     * 分页查询
     *
     * @param entityDTO
     * @param page
     * @return
     */
    PageResult<TSyncOrgHistroyRecordDTO> queryListPageByRole(TSyncOrgHistroyRecordDTO entityDTO, BasePageForm page);

    /**
     * 获取所有不分页
     *
     * @param entity
     * @return
     */
    List<TSyncOrgHistroyRecordDTO> queryList(TSyncOrgHistroyRecordDTO entity);

    /**
     * 根据主键id获取单个对象
     *
     * @param id
     * @return
     */
    TSyncOrgHistroyRecordDTO findOne(Long id);

    /**
     * 新增
     *
     * @param entity
     * @return
     */
    TSyncOrgHistroyRecordDTO create(TSyncOrgHistroyRecordDTO entity);


    /**
     * 更新
     *
     * @param entity
     * @return
     */
    int update(TSyncOrgHistroyRecordDTO entity);

    /**
     * 删除
     *
     * @param id
     * @return
     */
    int delete(Long id);

     /**
     * 是否存在
     *
     * existByTSyncOrgHistroyRecordId
     * @param code
     * @return
     */
    boolean existByTSyncOrgHistroyRecordId(Long code);

    /**
    * 批量新增
    *
    * create batch
    * @param dataList
    * @return
    */
    Boolean insertBatch(List<TSyncOrgHistroyRecordDTO> dataList);

    /**
     * 根据应用信息、机构信息-生成一条推送记录
     * @param orgId
     * @param appSystemManage
     * @return
     */
    TSyncOrgHistroyRecordDTO generateOrgHistoryRecord(Long orgId, TSyncAppSystemManage appSystemManage);


}
