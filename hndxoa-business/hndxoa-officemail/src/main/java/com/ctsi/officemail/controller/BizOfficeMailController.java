package com.ctsi.officemail.controller;
import cn.hutool.core.collection.CollectionUtil;
import com.ctsi.hndx.utils.BeanConvertUtils;
import com.ctsi.hndx.utils.SnowflakeIdUtil;
import com.ctsi.hndx.utils.StringUtils;
import com.ctsi.officemail.entity.BizOfficeMailInbox;
import com.ctsi.officemail.entity.dto.BizOfficeMailPostDTO;
import com.ctsi.officemail.service.IBizOfficeMailInboxService;
import com.ctsi.sms.smssend.SmsSendEnum;
import com.ctsi.sms.smssend.SmsSendUtil;
import com.ctsi.ssdc.admin.service.CscpUserService;
import com.ctsi.ssdc.model.PageResult;

import java.util.*;

import com.ctsi.officemail.entity.dto.BizOfficeMailDTO;
import com.ctsi.officemail.service.IBizOfficeMailService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.ctsi.ssdc.model.ResResult;
import org.springframework.security.access.prepost.PreAuthorize;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import com.ctsi.hndx.common.BasePageForm;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.util.Assert;
import com.ctsi.hndx.common.BaseController;
import com.ctsi.hndx.annotations.ResponseResultVo;
import com.ctsi.hndx.result.ResultCode;
import com.ctsi.hndx.result.ResultVO;
import com.ctsi.ssdc.annotation.OperationLog;
import com.ctsi.hndx.enums.DBOperation;


/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-15
 *
 */

@Slf4j
@RestController
@ResponseResultVo
@RequestMapping("/api/bizOfficeMail")
@Api(value = "办公邮件", tags = "办公邮件接口")
public class BizOfficeMailController extends BaseController {

    private static final String ENTITY_NAME = "bizOfficeMail";

    @Autowired
    private IBizOfficeMailService bizOfficeMailService;

    @Autowired
    private CscpUserService cscpUserService;

    @Autowired
    private IBizOfficeMailInboxService bizOfficeMailInboxService;



    /**
     *  新增办公邮件批量数据.
     */
    @PostMapping("/createBatch")
    @ApiOperation(value = "新增批量(权限code码为：cscp.bizOfficeMail.add)", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.ADD,message = "新增办公邮件批量数据")
    //@PreAuthorize("@permissionService.hasPermi('cscp.bizOfficeMail.add')")
    public ResultVO createBatch(@RequestBody List<BizOfficeMailDTO> bizOfficeMailList) {
       Boolean  result = bizOfficeMailService.insertBatch(bizOfficeMailList);
       if(result){
           return ResultVO.success();
       }else {
           return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
       }
    }

     /**
     *  新增/暂存数据.
     */
    @PostMapping("/create")
    @ApiOperation(value = "新增(权限code码为：cscp.bizOfficeMail.add)", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.ADD,message = "新增办公邮件数据")
    //@PreAuthorize("@permissionService.hasPermi('cscp.bizOfficeMail.add')")
    public ResultVO<BizOfficeMailDTO> create(@RequestBody BizOfficeMailPostDTO bizOfficeMailPostDTO)  {
        BizOfficeMailDTO bizOfficeMailDTO = BeanConvertUtils.copyProperties(bizOfficeMailPostDTO,BizOfficeMailDTO.class);
        String mailState = bizOfficeMailPostDTO.getMailState();
        //如果邮件状态为发送
        if(null!=mailState && "1".equals(mailState)){
            String[] readUserids = bizOfficeMailDTO.getReadUserids().split(",");
            String[] readUsernames = bizOfficeMailDTO.getReadUsernames().split(",");
            StringBuffer readUseridsBf=new StringBuffer();
            StringBuffer readUsernamesBf=new StringBuffer();
            //短信发送手机号
            Set<String> phoneList = new HashSet<>();
            String snowFlakeId = SnowflakeIdUtil.getSnowFlakeId();
            if(null==bizOfficeMailDTO.getId()||bizOfficeMailDTO.getId()==0){
                bizOfficeMailDTO.setId(Long.valueOf(snowFlakeId));
            }
            List<BizOfficeMailInbox> bizOfficeMailInboxList = new ArrayList<>();
            //判断人员是否重复
            Map<Long,String> isUserMap=new HashMap();
            for (int i=0;i<readUserids.length;i++){
                String readUserid = readUserids[i];
                String readUsername = readUsernames[i];
                if(!isUserMap.containsKey(readUserid)){
                    BizOfficeMailInbox bizOfficeMailInbox = new BizOfficeMailInbox();
                    readUseridsBf.append(readUserid+",");
                    readUsernamesBf.append(readUsername+",");
                    bizOfficeMailInbox.setOfficeMailId(bizOfficeMailDTO.getId());
                    bizOfficeMailInbox.setReadUserid(readUserid);
                    bizOfficeMailInbox.setReadUsername(readUsername);
                    bizOfficeMailInbox.setTitle(bizOfficeMailDTO.getTitle());
                    bizOfficeMailInbox.setMainContent(bizOfficeMailDTO.getMainContent());
                    bizOfficeMailInbox.setIsReply("0");
                    bizOfficeMailInbox.setIsLabel("0");
                    bizOfficeMailInbox.setIsAnnex(bizOfficeMailDTO.getIsAnnex());
                    bizOfficeMailInbox.setIsRead("0");
                    bizOfficeMailInboxList.add(bizOfficeMailInbox);
                    // 查询个人电话用于短信提醒
                    String mobile = cscpUserService.getMobilePhoneById(Long.valueOf(readUserid));
                    phoneList.add(mobile);
                }
            }
            String endStr="";
            if(StringUtils.isNotEmpty(readUseridsBf.toString())){
                endStr=readUseridsBf.toString().substring(readUseridsBf.length()-1,readUseridsBf.length());
                if(endStr.equals(",")){
                    bizOfficeMailDTO.setReadUserids(readUseridsBf.toString().substring(0,readUseridsBf.length()-1));
                    bizOfficeMailDTO.setReadUsernames(readUsernamesBf.toString().substring(0,readUsernamesBf.length()-1));
                }
            }

            //保存到公文邮件收件箱
            bizOfficeMailInboxService.saveBatch(bizOfficeMailInboxList);
            //批量发送短信提醒
            if (null!=bizOfficeMailPostDTO.getSmsReminder()&&bizOfficeMailPostDTO.getSmsReminder()==1&&StringUtils.isNotEmpty(bizOfficeMailPostDTO.getTitle()) && CollectionUtil.isNotEmpty(phoneList)) {
                SmsSendUtil.batchSendSms(phoneList, bizOfficeMailPostDTO.getTitle(), SmsSendEnum.DAILY_REPORT_SMS);
            }
        }
        //保存公文邮件
        BizOfficeMailDTO result = bizOfficeMailService.create(bizOfficeMailDTO);
        return ResultVO.success(result);
    }

    /**
     *  发送邮件
     */
    @PostMapping("/sendmail")
    @ApiOperation(value = "新增(权限code码为：cscp.bizOfficeMail.sendmail)", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.ADD,message = "新增办公邮件数据")
    //@PreAuthorize("@permissionService.hasPermi('cscp.bizOfficeMail.add')")
    public ResultVO<BizOfficeMailDTO> sendmail(@RequestBody BizOfficeMailPostDTO bizOfficeMailPostDTO)  {
        BizOfficeMailDTO bizOfficeMailDTO = BeanConvertUtils.copyProperties(bizOfficeMailPostDTO,BizOfficeMailDTO.class);
        String mailState = bizOfficeMailPostDTO.getMailState();
        //如果邮件状态为发送
        if(null!=mailState && "1".equals(mailState)){
            String[] readUserids = bizOfficeMailDTO.getReadUserids().split(",");
            String[] readUsernames = bizOfficeMailDTO.getReadUsernames().split(",");
            StringBuffer readUseridsBf=new StringBuffer();
            StringBuffer readUsernamesBf=new StringBuffer();
            //短信发送手机号
            Set<String> phoneList = new HashSet<>();
            String snowFlakeId = SnowflakeIdUtil.getSnowFlakeId();
            if(null==bizOfficeMailDTO.getId()||bizOfficeMailDTO.getId()==0){
                bizOfficeMailDTO.setId(Long.valueOf(snowFlakeId));
            }
            List<BizOfficeMailInbox> bizOfficeMailInboxList = new ArrayList<>();
            //判断人员是否重复
            Map<Long,String> isUserMap=new HashMap();
            for (int i=0;i<readUserids.length;i++){
                String readUserid = readUserids[i];
                String readUsername = readUsernames[i];
                if(!isUserMap.containsKey(readUserid)){
                    BizOfficeMailInbox bizOfficeMailInbox = new BizOfficeMailInbox();
                    readUseridsBf.append(readUserid+",");
                    readUsernamesBf.append(readUsername+",");
                    bizOfficeMailInbox.setOfficeMailId(bizOfficeMailDTO.getId());
                    bizOfficeMailInbox.setReadUserid(readUserid);
                    bizOfficeMailInbox.setReadUsername(readUsername);
                    bizOfficeMailInbox.setTitle(bizOfficeMailDTO.getTitle());
                    bizOfficeMailInbox.setMainContent(bizOfficeMailDTO.getMainContent());
                    bizOfficeMailInbox.setIsReply("0");
                    bizOfficeMailInbox.setIsLabel("0");
                    bizOfficeMailInbox.setIsAnnex(bizOfficeMailDTO.getIsAnnex());
                    bizOfficeMailInbox.setIsRead("0");
                    bizOfficeMailInboxList.add(bizOfficeMailInbox);
                    // 查询个人电话用于短信提醒
                    String mobile = cscpUserService.getMobilePhoneById(Long.valueOf(readUserid));
                    phoneList.add(mobile);
                }
            }
            String endStr="";
            if(StringUtils.isNotEmpty(readUseridsBf.toString())){
                endStr=readUseridsBf.toString().substring(readUseridsBf.length()-1,readUseridsBf.length());
                if(endStr.equals(",")){
                    bizOfficeMailDTO.setReadUserids(readUseridsBf.toString().substring(0,readUseridsBf.length()-1));
                    bizOfficeMailDTO.setReadUsernames(readUsernamesBf.toString().substring(0,readUsernamesBf.length()-1));
                }
            }

            //保存到公文邮件收件箱
            bizOfficeMailInboxService.saveBatch(bizOfficeMailInboxList);
            //批量发送短信提醒
            if (null!=bizOfficeMailPostDTO.getSmsReminder()&&bizOfficeMailPostDTO.getSmsReminder()==1&&StringUtils.isNotEmpty(bizOfficeMailPostDTO.getTitle()) && CollectionUtil.isNotEmpty(phoneList)) {
                SmsSendUtil.batchSendSms(phoneList, bizOfficeMailPostDTO.getTitle(), SmsSendEnum.DAILY_REPORT_SMS);
            }
        }
        //修改为发送状态
        bizOfficeMailDTO.setMailState("1");
        //保存公文邮件
        int result = bizOfficeMailService.update(bizOfficeMailDTO);
        return ResultVO.success("发送成功！");
    }

    /**
     *  更新存在数据.
     */
    @PostMapping("/update")
    @ApiOperation(value = "更新存在数据(权限code码为：cscp.bizOfficeMail.update)", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.UPDATE,message = "更新办公邮件数据")
    //@PreAuthorize("@permissionService.hasPermi('cscp.bizOfficeMail.update')")
    public ResultVO update(@RequestBody BizOfficeMailDTO bizOfficeMailDTO) {
	    Assert.notNull(bizOfficeMailDTO.getId(), "general.IdNotNull");
        int count = bizOfficeMailService.update(bizOfficeMailDTO);
        if(count > 0 ){
            return ResultVO.success();
        }else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }

     /**
     *  删除存在数据.
     */
    @DeleteMapping("/delete/{id}")
    @OperationLog(dBOperation = DBOperation.DELETE,message = "删除办公邮件数据")
    @ApiOperation(value = "删除存在数据(权限code码为：cscp.bizOfficeMail.delete)", notes = "传入参数")
    //@PreAuthorize("@permissionService.hasPermi('cscp.bizOfficeMail.delete')")
    public ResultVO delete(@PathVariable Long id) {
        int count = bizOfficeMailService.delete(id);
        if(count > 0 ){
            return ResultVO.success();
        }else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }

    /**
     * 查询单条数据.
     */
    @GetMapping("/get/{id}")
    @ApiOperation(value = "查询单条数据", notes = "传入参数")
    ////@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
    public ResultVO get(@PathVariable Long id) {
        BizOfficeMailDTO bizOfficeMailDTO = bizOfficeMailService.findOne(id);
        return ResultVO.success(bizOfficeMailDTO);
    }

    /**
    *  分页查询多条数据.
    */
    @GetMapping("/queryBizOfficeMailPage")
    @ApiOperation(value = "翻页查询多条数据", notes = "传入参数")
    ////@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
    public ResultVO<PageResult<BizOfficeMailDTO>> queryBizOfficeMailPage(BizOfficeMailDTO bizOfficeMailDTO, BasePageForm basePageForm) {
        return ResultVO.success(bizOfficeMailService.queryListPage(bizOfficeMailDTO, basePageForm));
    }

   /**
    * 查询多条数据.不分页
    */
   @GetMapping("/queryBizOfficeMail")
   @ApiOperation(value = "查询多条数据", notes = "传入参数")
   ////@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
   public ResultVO<ResResult<BizOfficeMailDTO>> queryBizOfficeMail(BizOfficeMailDTO bizOfficeMailDTO) {
       List<BizOfficeMailDTO> list = bizOfficeMailService.queryList(bizOfficeMailDTO);
       return ResultVO.success(new ResResult<BizOfficeMailDTO>(list));
   }

}
