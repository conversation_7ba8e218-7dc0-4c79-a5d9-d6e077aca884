package com.ctsi.officemail.entity.dto;

import com.ctsi.hndx.common.BaseDtoEntity;
import java.io.Serializable;
import java.time.LocalDateTime;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 办公邮件收件箱
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-15
 */
@Getter
@Setter
@ApiModel(value = "BizOfficeMailInboxDTO对象", description = "办公邮件收件箱")
public class BizOfficeMailInboxDTO extends BaseDtoEntity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("标题")
    private String title;

    @ApiModelProperty("办公邮件ID")
    private Long officeMailId;

    @ApiModelProperty("收件人id")
    private String readUserid;

    @ApiModelProperty("收件人名称")
    private String readUsername;

    @ApiModelProperty("是否回复（0:未回复，1：已回复）")
    private String isReply;

    @ApiModelProperty("正文内容")
    private String mainContent;

    @ApiModelProperty("是否标星：0:未标星，1:已标星")
    private String isLabel;

    @ApiModelProperty("是否有附件：0:没附件，1:有附件")
    private String isAnnex;

    @ApiModelProperty("是否已阅（0:未阅，1：已阅）")
    private String isRead;

    @ApiModelProperty("创建人")
    private Long createBy;

    @ApiModelProperty("创建人姓名")
    private String createName;

    @ApiModelProperty("创建人时间")
    private LocalDateTime createTime;

}
