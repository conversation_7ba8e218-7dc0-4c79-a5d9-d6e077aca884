package com.ctsi.gwhd.controller;
import com.ctsi.gwhd.entity.dto.TUniversalResearchActivityUserDTO;
import com.ctsi.gwhd.entity.vo.TUniversalResearchActivityCountVo;
import com.ctsi.gwhd.service.ITUniversalResearchActivityUserService;
import com.ctsi.ssdc.model.PageResult;
import java.util.List;
import java.util.Optional;
import com.ctsi.gwhd.entity.TUniversalResearchActivity;
import com.ctsi.gwhd.entity.dto.TUniversalResearchActivityDTO;
import com.ctsi.gwhd.service.ITUniversalResearchActivityService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.ctsi.ssdc.model.ResResult;
import org.springframework.security.access.prepost.PreAuthorize;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import com.ctsi.hndx.common.BasePageForm;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.InitBinder;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.util.Assert;
import com.ctsi.hndx.common.BaseController;
import com.ctsi.hndx.annotations.ResponseResultVo;
import com.ctsi.hndx.result.ResultCode;
import com.ctsi.hndx.result.ResultVO;
import com.ctsi.ssdc.annotation.OperationLog;
import com.ctsi.hndx.enums.DBOperation;


/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-06
 *
 */

@Slf4j
@RestController
@ResponseResultVo
@RequestMapping("/api/tUniversalResearchActivity")
@Api(value = "通用调研活动表", tags = "通用调研活动表接口")
public class TUniversalResearchActivityController extends BaseController {

    private static final String ENTITY_NAME = "tUniversalResearchActivity";

    @Autowired
    private ITUniversalResearchActivityService tUniversalResearchActivityService;

    @Autowired
    private ITUniversalResearchActivityUserService tUniversalResearchActivityUserService;

    /**
     *  分页查询多条数据.
     */
    @GetMapping("/queryTUniversalResearchActivityPage")
    @ApiOperation(value = "翻页查询多条数据", notes = "传入参数")
    public ResultVO<PageResult<TUniversalResearchActivityDTO>> queryTUniversalResearchActivityPage(TUniversalResearchActivityDTO tUniversalResearchActivityDTO, BasePageForm basePageForm) {
        return ResultVO.success(tUniversalResearchActivityService.queryListPage(tUniversalResearchActivityDTO, basePageForm));
    }

    /**
     *  新增发布调研活动.
     */
    @PostMapping("/create")
    @ApiOperation(value = "新增发布调研活动", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.ADD,message = "新增发布调研活动")
    public ResultVO<TUniversalResearchActivityDTO> create(@RequestBody TUniversalResearchActivityDTO tUniversalResearchActivityDTO)  {
        TUniversalResearchActivityDTO result = tUniversalResearchActivityService.create(tUniversalResearchActivityDTO);
        return ResultVO.success(result);
    }

    /**
     * 查询单条数据.
     */
    @GetMapping("/get/{id}")
    @ApiOperation(value = "查询单条数据", notes = "传入参数")
    public ResultVO get(@PathVariable Long id) {
        TUniversalResearchActivityDTO tUniversalResearchActivityDTO = tUniversalResearchActivityService.findOne(id);
        if (tUniversalResearchActivityDTO != null) {
            tUniversalResearchActivityDTO.setUserCount(tUniversalResearchActivityUserService.qurCountCheckUser(null,id));
            tUniversalResearchActivityDTO.setReadingCount(tUniversalResearchActivityUserService.qurCountCheckUser(1,id));
            tUniversalResearchActivityDTO.setNoReadingCount(tUniversalResearchActivityUserService.qurCountCheckUser(0,id));
        }
        return ResultVO.success(tUniversalResearchActivityDTO);
    }

    /**
     *  删除存在数据.
     */
    @DeleteMapping("/delete/{id}")
    @OperationLog(dBOperation = DBOperation.DELETE,message = "删除通用调研活动表数据")
    @ApiOperation(value = "删除通用调研活动表数据", notes = "传入参数")
    public ResultVO delete(@PathVariable Long id) {
        int count = tUniversalResearchActivityService.delete(id);
        if(count > 0 ){
            return ResultVO.success();
        }else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }

    /**
     *  分页查询调研查收数据.
     */
    @GetMapping("/queryTUniversalResearchActivityCheckPage")
    @ApiOperation(value = "分页查询调研查收数据", notes = "传入参数")
    public ResultVO<PageResult<TUniversalResearchActivityDTO>> queryTUniversalResearchActivityCheckPage(TUniversalResearchActivityDTO tUniversalResearchActivityDTO, BasePageForm basePageForm) {
        return ResultVO.success(tUniversalResearchActivityService.queryCheckListPage(tUniversalResearchActivityDTO, basePageForm));
    }

    /**
     *  签收调研活动
     * @param tUniversalResearchActivityUserDTO
     * @return
     */
    @PostMapping("/signResearchActivity")
    @ApiOperation(value = "签收调研活动", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.ADD,message = "签收调研活动")
    public ResultVO signResearchActivity(@RequestBody TUniversalResearchActivityUserDTO tUniversalResearchActivityUserDTO)  {
        Boolean result = tUniversalResearchActivityService.signResearchActivity(tUniversalResearchActivityUserDTO);
        if (result){
            return ResultVO.success();
        }else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }

    /**
     * 查询调研活动各类型统计数据
     * @return
     */
    @GetMapping("/queryResearchActivityCount")
    @ApiOperation(value = "查询调研活动各类型统计数据", notes = "传入参数")
    public ResultVO<List<TUniversalResearchActivityCountVo>> queryResearchActivityCount() {
        return ResultVO.success(tUniversalResearchActivityService.queryResearchActivityCount());
    }

    @GetMapping("/queryNoReadingCount")
    @ApiOperation(value = "查询待签收数据", notes = "传入参数")
    public ResultVO<Integer> queryNoReadingCount(String strId) {
        return ResultVO.success(tUniversalResearchActivityService.queryNoReadingCount(strId));
    }


    /**
     *  新增通用调研活动表批量数据.
     */
    /*@PostMapping("/createBatch")
    @ApiOperation(value = "新增批量(权限code码为：cscp.tUniversalResearchActivity.add)", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.ADD,message = "新增通用调研活动表批量数据")
    @PreAuthorize("@permissionService.hasPermi('cscp.tUniversalResearchActivity.add')")
    public ResultVO createBatch(@RequestBody List<TUniversalResearchActivityDTO> tUniversalResearchActivityList) {
       Boolean  result = tUniversalResearchActivityService.insertBatch(tUniversalResearchActivityList);
       if(result){
           return ResultVO.success();
       }else {
           return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
       }
    }*/



    /**
     *  更新存在数据.
     */
    /*@PostMapping("/update")
    @ApiOperation(value = "更新存在数据(权限code码为：cscp.tUniversalResearchActivity.update)", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.UPDATE,message = "更新通用调研活动表数据")
    @PreAuthorize("@permissionService.hasPermi('cscp.tUniversalResearchActivity.update')")
    public ResultVO update(@RequestBody TUniversalResearchActivityDTO tUniversalResearchActivityDTO) {
	    Assert.notNull(tUniversalResearchActivityDTO.getId(), "general.IdNotNull");
        int count = tUniversalResearchActivityService.update(tUniversalResearchActivityDTO);
        if(count > 0 ){
            return ResultVO.success();
        }else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }*/


   /**
    * 查询多条数据.不分页
    */
   /*@GetMapping("/queryTUniversalResearchActivity")
   @ApiOperation(value = "查询多条数据", notes = "传入参数")
   //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
   public ResultVO<ResResult<TUniversalResearchActivityDTO>> queryTUniversalResearchActivity(TUniversalResearchActivityDTO tUniversalResearchActivityDTO) {
       List<TUniversalResearchActivityDTO> list = tUniversalResearchActivityService.queryList(tUniversalResearchActivityDTO);
       return ResultVO.success(new ResResult<TUniversalResearchActivityDTO>(list));
   }*/

}
