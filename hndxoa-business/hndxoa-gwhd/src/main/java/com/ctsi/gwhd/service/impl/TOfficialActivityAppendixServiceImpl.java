package com.ctsi.gwhd.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ctsi.hndx.utils.BeanConvertUtils;
import com.ctsi.hndx.utils.ListCopyUtil;
import com.ctsi.ssdc.model.PageResult;
import com.ctsi.gwhd.entity.TOfficialActivityAppendix;
import com.ctsi.gwhd.entity.dto.TOfficialActivityAppendixDTO;
import com.ctsi.gwhd.mapper.TOfficialActivityAppendixMapper;
import com.ctsi.gwhd.service.ITOfficialActivityAppendixService;
import com.ctsi.hndx.common.SysBaseServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.utils.PageHelperUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.List;
import org.springframework.transaction.annotation.Transactional;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-10
 */
@Slf4j
@Service
public class TOfficialActivityAppendixServiceImpl extends SysBaseServiceImpl<TOfficialActivityAppendixMapper, TOfficialActivityAppendix> implements ITOfficialActivityAppendixService {

    @Autowired
    private TOfficialActivityAppendixMapper tOfficialActivityAppendixMapper;

    /**
     * 翻页
     *
     * @param entityDTO
     * @param basePageForm
     * @return
     */
    @Override
    public PageResult<TOfficialActivityAppendixDTO> queryListPage(TOfficialActivityAppendixDTO entityDTO, BasePageForm basePageForm) {
        //设置条件
        LambdaQueryWrapper<TOfficialActivityAppendix> queryWrapper = new LambdaQueryWrapper();

        IPage<TOfficialActivityAppendix> pageData = tOfficialActivityAppendixMapper.selectPage(
             PageHelperUtil.getMPlusPageByBasePage(basePageForm), queryWrapper);
        //返回
        IPage<TOfficialActivityAppendixDTO> data  = pageData.convert(entity -> BeanConvertUtils.copyProperties(entity,TOfficialActivityAppendixDTO.class));

        return new PageResult<TOfficialActivityAppendixDTO>(data.getRecords(),
            data.getTotal(), data.getCurrent());
    }

    /**
     * 列表查询
     *
     * @param entityDTO
     * @return
     */
    @Override
    public List<TOfficialActivityAppendixDTO> queryList(TOfficialActivityAppendixDTO entityDTO) {
        LambdaQueryWrapper<TOfficialActivityAppendix> queryWrapper = new LambdaQueryWrapper();
            List<TOfficialActivityAppendix> listData = tOfficialActivityAppendixMapper.selectList(queryWrapper);
            List<TOfficialActivityAppendixDTO> TOfficialActivityAppendixDTOList = ListCopyUtil.copy(listData, TOfficialActivityAppendixDTO.class);
        return TOfficialActivityAppendixDTOList;
    }

    /**
     * 单个查询
     *
     * @param id the id of the entity
     * @return
     */
    @Override
    public TOfficialActivityAppendixDTO findOne(Long id) {
        TOfficialActivityAppendix  tOfficialActivityAppendix =  tOfficialActivityAppendixMapper.selectById(id);
        return  BeanConvertUtils.copyProperties(tOfficialActivityAppendix,TOfficialActivityAppendixDTO.class);
    }


    /**
     * 新增
     *
     * @param entityDTO the entity to create
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public TOfficialActivityAppendixDTO create(TOfficialActivityAppendixDTO entityDTO) {
       TOfficialActivityAppendix tOfficialActivityAppendix =  BeanConvertUtils.copyProperties(entityDTO,TOfficialActivityAppendix.class);
        save(tOfficialActivityAppendix);
        return  BeanConvertUtils.copyProperties(tOfficialActivityAppendix,TOfficialActivityAppendixDTO.class);
    }

    /**
     * 修改
     *
     * @param entity the entity to update
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int update(TOfficialActivityAppendixDTO entity) {
        TOfficialActivityAppendix tOfficialActivityAppendix = BeanConvertUtils.copyProperties(entity,TOfficialActivityAppendix.class);
        return tOfficialActivityAppendixMapper.updateById(tOfficialActivityAppendix);
    }

    /**
     * 删除
     *
     * @param id the id of the entity
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int delete(Long id) {
        return tOfficialActivityAppendixMapper.deleteById(id);
    }


    /**
     * 验证是否存在
     *
     * @param TOfficialActivityAppendixId
     * @return
     */
    @Override
    public boolean existByTOfficialActivityAppendixId(Long TOfficialActivityAppendixId) {
        if (TOfficialActivityAppendixId != null) {
            LambdaQueryWrapper<TOfficialActivityAppendix> queryWrapper = new LambdaQueryWrapper();
            queryWrapper.eq(TOfficialActivityAppendix::getId, TOfficialActivityAppendixId);
            List<TOfficialActivityAppendix> result = tOfficialActivityAppendixMapper.selectList(queryWrapper);
            return result.size() > 0;
        }
        return true;
    }

    /**
    * 批量新增
    *
    */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertBatch(List<TOfficialActivityAppendixDTO> dataList) {
        List<TOfficialActivityAppendix> result = ListCopyUtil.copy(dataList, TOfficialActivityAppendix.class);
        return saveBatch(result);
    }


}
