package com.ctsi.gwhd.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.ctsi.hndx.common.BaseEntity;
import java.io.Serializable;
import java.math.BigInteger;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 活动通知表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-09
 */
@Getter
@Setter
@TableName("t_official_activity_notify")
@ApiModel(value = "TOfficialActivityNotify对象", description = "活动通知表")
public class TOfficialActivityNotify extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("活动id")
    private Long activityId;

    @ApiModelProperty("用户id")
    private Long userId;

    @ApiModelProperty("姓名")
    private String userName;

    @ApiModelProperty("人员类型")
    private String type;

    @ApiModelProperty("手机号")
    private String mobile;

    @ApiModelProperty("单位id")
    private Long userCompanyId;

    @ApiModelProperty("单位名称")
    private String userCompanyName;

    @ApiModelProperty("部门id")
    private Long userDepartmentId;

    @ApiModelProperty("部门名称")
    private String userDepartmentName;

    @ApiModelProperty("通知内容")
    private String content;


}
