package com.ctsi.gwhd.mapper;

import com.ctsi.gwhd.entity.TOfficialActivityNotify;
import com.ctsi.hndx.common.MybatisBaseMapper;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 活动通知表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-09
 */
public interface TOfficialActivityNotifyMapper extends MybatisBaseMapper<TOfficialActivityNotify> {

    Integer selectDeletedCount(@Param("activityId") Long activityId);
}
