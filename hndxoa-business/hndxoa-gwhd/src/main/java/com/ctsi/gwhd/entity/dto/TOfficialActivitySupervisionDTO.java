package com.ctsi.gwhd.entity.dto;

import com.ctsi.hndx.common.BaseDtoEntity;
import java.io.Serializable;
import java.math.BigInteger;
import java.util.Date;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 活动督办事项
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-15
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value="TOfficialActivitySupervisionDTO对象", description="活动督办事项")
public class TOfficialActivitySupervisionDTO extends BaseDtoEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 活动id
     */
    @ApiModelProperty(value = "活动id")
    private Long activityId;

    /**
     * 督察事项id
     */
    @ApiModelProperty(value = "督察事项id")
    private Long supervisionId;

    /**
     * 责任单位
     */
    @ApiModelProperty(value = "责任单位")
    private String reponseCompany;

    /**
     * 督办单位
     */
    @ApiModelProperty(value = "督办单位")
    private String supervisionCompany;

    /**
     * 责任单位id
     */
    @ApiModelProperty(value = "责任单位id")
    private Long reponseCompanyId;

    /**
     * 督办单位id
     */
    @ApiModelProperty(value = "督办单位id")
    private Long supervisionCompanyId;

    /**
     * 完成时间
     */
    @ApiModelProperty(value = "完成时间")
    private Date completeTime;

    /**
     * 0未转督办系统1.已转督办系统2.督办任务完结
     */
    @ApiModelProperty(value = "0未转督办系统1.已转督办系统2.督办任务完结")
    private Integer status;

    /**
     * 活动指示事项
     */
    @ApiModelProperty(value = "活动指示事项")
    private String title;


}
