package com.ctsi.gwhd.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.ctsi.hndx.common.BaseEntity;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 通用调研活动表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-06
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("t_universal_research_activity")
@ApiModel(value="TUniversalResearchActivity对象", description="通用调研活动表")
public class TUniversalResearchActivity extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 活动名称
     */
    @ApiModelProperty(value = "活动名称")
    private String activityName;

    /**
     * 活动来源(1省领导联点 2省领导指示批示)
     */
    @ApiModelProperty(value = "活动来源(1省领导联点 2省领导指示批示)")
    private Integer source;

    /**
     * 活动类型(1.走访慰问,2.宣讲,3.现场考察,4.调研座谈,5.暗访,6.督察指导)
     */
    @ApiModelProperty(value = "活动类型(1.走访慰问,2.宣讲,3.现场考察,4.调研座谈,5.暗访,6.督察指导)")
    private Integer type;

    /**
     * 开始时间
     */
    @ApiModelProperty(value = "开始时间")
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    @ApiModelProperty(value = "结束时间")
    private LocalDateTime endTime;

    /**
     * 活动省份
     */
    @ApiModelProperty(value = "活动省份")
    private String province;

    /**
     * 活动城市
     */
    @ApiModelProperty(value = "活动城市")
    private String city;

    /**
     * 活动区县
     */
    @ApiModelProperty(value = "活动区县")
    private String district;

    /**
     * 活动详细地址
     */
    @ApiModelProperty(value = "活动详细地址")
    private String adrress;

    /**
     * 调研活动安排
     */
    @ApiModelProperty(value = "调研活动安排")
    private String planContent;

    /**
     * 密级级别
     */
    @ApiModelProperty(value = "密级级别")
    private String secretLevel;

    /**
     * 密级级别名称
     */
    @ApiModelProperty(value = "密级级别名称")
    private String secretLevelName;


}
