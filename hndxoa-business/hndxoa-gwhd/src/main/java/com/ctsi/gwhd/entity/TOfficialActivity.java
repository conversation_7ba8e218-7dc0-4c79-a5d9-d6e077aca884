package com.ctsi.gwhd.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.ctsi.hndx.common.BaseEntity;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * <p>
 * 公务活动表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-09
 */
@Getter
@Setter
@TableName("t_official_activity")
@ApiModel(value = "TOfficialActivity对象", description = "公务活动表")
public class TOfficialActivity extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("活动名称")
    private String activityName;

    @ApiModelProperty("来源(1省领导联点 2省领导指示批示)")
    private Integer source;

    @ApiModelProperty("活动开始时间")
    private LocalDateTime startTime;

    @ApiModelProperty("活动结束时间")
    private LocalDateTime endTime;

    @ApiModelProperty("活动地点")
    private String adrress;

    @ApiModelProperty("省")
    private String province;

    @ApiModelProperty("市")
    private String city;

    @ApiModelProperty("区")
    private String district;

    @ApiModelProperty("活动类型(1.走访慰问,2.宣讲,3.现场考察,4.调研座谈,5.暗访,6.督察指导)")
    private Integer type;

    @ApiModelProperty("上传相关附件(,分开)")
    private String fileIds;

    @ApiModelProperty("有关事宜")
    private String descption;

    @ApiModelProperty("媒体文章(,分开)")
    private String mediaFileIds;

    @ApiModelProperty("状态(1.待提交2.待审批.3已通过4.已驳回5.已归档)")
    private Integer status;

    @ApiModelProperty("驳回原因")
    private String rejectReason;

    @ApiModelProperty("媒体报道状态(1.待导入2.待提交3.待审批4.已驳回5已通过)")
    private Integer mediaReportStatus;

    @ApiModelProperty("媒体报道驳回原因")
    private String mediaReportRejectReason;

    @ApiModelProperty("活动方案文件")
    private String activityPlanFiles;

    @ApiModelProperty("现场资料文件")
    private String feedbackMaterialFiles;

    @ApiModelProperty("督查反馈文件")
    private String supervisionFiles;

    @ApiModelProperty("复盘分析")
    private String reviewAnalysis;
}
