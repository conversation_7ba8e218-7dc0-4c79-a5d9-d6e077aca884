package com.ctsi.gwhd.controller;

import com.ctsi.gwhd.entity.dto.TOfficialActivityNotifyDTO;
import com.ctsi.gwhd.entity.vo.TOfficialActivityNotifyListVo;
import com.ctsi.gwhd.service.ITOfficialActivityNotifyService;
import com.ctsi.hndx.result.ResultVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <p>
 * 活动通知表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-09
 */
@RestController
@RequestMapping("/api/officialActivityNotify")
@Api(value = "公务活动通知 ", tags = "公务活动通知 接口")
public class TOfficialActivityNotifyController {

    @Autowired
    private ITOfficialActivityNotifyService officialActivityNotifyService;

    @PostMapping("/createOrUpdate")
    @ApiOperation(value = "公务活动通知-新增/修改", notes = "传入参数")
    public ResultVO createOrUpdate(@RequestBody TOfficialActivityNotifyListVo officialActivityNotifyListVo ){
        officialActivityNotifyService.createOrUpdate(officialActivityNotifyListVo);
        return ResultVO.success();
    }

    @DeleteMapping("/deleteActivityNotify")
    @ApiOperation(value = "公务活动-删除", notes = "传入参数")
    public ResultVO deleteActivityNotify(Long id) {
        Assert.notNull(id, "id不能为空");
        officialActivityNotifyService.deleteActivityNotify(id);
        return ResultVO.success();
    }

    @GetMapping("/list")
    @ApiOperation(value = "公务活动通知-列表", notes = "不传参数")
    public ResultVO<List<TOfficialActivityNotifyDTO>> list(@Valid @NotNull(message = "id不能为空") Long activityId){
        List<TOfficialActivityNotifyDTO> list = officialActivityNotifyService.list(activityId);
        return ResultVO.success(list);
    }

    /**
     * 短信模板导出
     */
    @GetMapping("/exportSmsTemplate")
    @ApiOperation(value = "短信模板导出", notes = "传入参数")
    public void exportSmsTemplate(@Valid @NotNull(message = "id不能为空") Long activityId,HttpServletResponse response) {
        officialActivityNotifyService.exportSmsTemplate(activityId,response);
    }

}
