package com.ctsi.third.entity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections.map.LinkedMap;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * @description
 * @author: Tan<PERSON><PERSON>
 * @create: 2024-06-19
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MeetingReportVO implements Serializable {
	private static final long serialVersionUID = 1L;

	@ApiModelProperty("各状态会议占比")
	private SfwMeetingCountVO meetingCount = new SfwMeetingCountVO();

	@ApiModelProperty("各部门会议对比")
	private List<SfwMeetingCountVO> allOrgCount = new ArrayList<>();


	@ApiModelProperty("近6个月会议变化<创建会议>")
	private Map<String,Integer> near6MonthCreatCount = new LinkedMap();
	@ApiModelProperty("近6个月会议变化<已开会议>")
	private Map<String,Integer> near6MonthCompletedCount = new LinkedMap();






}
