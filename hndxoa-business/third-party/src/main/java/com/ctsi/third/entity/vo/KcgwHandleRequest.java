package com.ctsi.third.entity.vo;

import com.alibaba.fastjson.JSON;
import com.ctsi.hndx.utils.BeanConvertUtils;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * @description
 * @author: TanJie
 * @create: 2024-06-19
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class KcgwHandleRequest implements Serializable {
	private static final long serialVersionUID = 1L;



	/**
	 *  第三方 唯一id
	 */
	private  String recordId;
	/**
	 * 拟办意见
	 */
	private  String nbyj;
	/**
	 * 批示信息
	 * http://ip:port/gwcl/swrequesthandle/swrequestrecord/receiveOACallBackJson.do 省领导批示
	 * http://ip:port/gwcl/swrequesthandle/swrequestrecord/receiveOACallBackJsonStringAndFiles.do 领导批示
	 */
	private List<PsInfo> ldps;
	/**
	 *
	 * http://ip:port/gwcl/swrequesthandle/swrequestrecord/receiveOACallBackJson.do 厅领导批示
	 */
	private List<PsInfo> t_ldps;


	/**
	 * 批示信息
	 */
	@Data
	public static class PsInfo{
		@ApiModelProperty("审核人")
		private String leaderName;
		@ApiModelProperty("审核内容")
		private String content;

		@ApiModelProperty("审核时间")
		private String time;

		@ApiModelProperty("盖章号信息")
		private String no;
	}


	// public static void main(String[] args) {
	// 	KcgwHandleRequest data = new KcgwHandleRequest();
	// 	List<KcgwHandleRequest.PsInfo>  info = new ArrayList<>();
	//
	// 	KcgwHandleRequest.PsInfo ps = new PsInfo();
	// 	ps.setNo("123");
	// 	ps.setLeaderName("测试123");
	//
	// 	info.add(ps);
	//
	// 	data.setT_ldps(info);
	//
	// 	List<KcgwHandleRequest.PsInfo>  info2 = new ArrayList<>();
	// 	PsInfo psInfo = BeanConvertUtils.copyProperties(info , KcgwHandleRequest.PsInfo.class);
	// 	psInfo.setNo("1233");
	// 	psInfo.setLeaderName("测试1233");
	// 	info2.add(psInfo);
	//
	// 	data.setLdps(info2);
	// 	System.out.println(JSON.toJSONString(data));
	// }

}
