package com.ctsi.third.controller;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ctsi.activiti.core.model.TaskQueryParam;
import com.ctsi.activiti.core.service.ActivitiQueryService;
import com.ctsi.activiti.core.vo.TaskVO;
import com.ctsi.biaodan.entity.dto.PageTcirculateDTO;
import com.ctsi.biaodan.mapper.TCirculateMapper;
import com.ctsi.biaodan.service.ITCirculateUserService;
import com.ctsi.hndx.annotations.ResponseResultVo;
import com.ctsi.hndx.common.BaseController;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.exception.BusinessException;
import com.ctsi.hndx.result.ResultVO;
import com.ctsi.hndx.utils.PageHelperUtil;
import com.ctsi.hndx.westone.WestoneEncryptService;
import com.ctsi.notic.service.ITNoticeService;
import com.ctsi.regulations.service.ITRegulationsExaminationService;
import com.ctsi.ssdc.admin.domain.CscpOrg;
import com.ctsi.ssdc.admin.domain.CscpUser;
import com.ctsi.ssdc.admin.domain.dto.CscpOrgDTO;
import com.ctsi.ssdc.admin.repository.CscpOrgRepository;
import com.ctsi.ssdc.admin.service.CscpOrgService;
import com.ctsi.ssdc.admin.service.CscpUserService;
import com.ctsi.ssdc.model.PageResult;
import com.ctsi.ssdc.service.BizService;
import com.ctsi.third.entity.dto.ExternalCornerDTO;
import com.ctsi.third.service.IExternalService;
import com.ctsi.third.util.EncryptionUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;


/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-15
 */

@Slf4j
@RestController
@ResponseResultVo
@RequestMapping("/api/external/Test")
@Api(value = "外部接口对接", tags = "外部接口对接")
public class ExternalTestController extends BaseController {

	private static final String ENTITY_NAME = "external";


	@Autowired
	private ActivitiQueryService activitiQueryService;
	@Autowired
	private IExternalService externalService;
	@Autowired
	private CscpUserService cscpUserService;
	@Autowired
	private ITNoticeService tNoticeService;

	@Autowired
	private ITCirculateUserService tCirculateUserService;


	@Autowired
	private TCirculateMapper tCirculateMapper;


	@Autowired
	private BizService bizService;
	@Autowired
	private CscpOrgService cscpOrgService;

	@Autowired
	private WestoneEncryptService westoneEncryptService;

	@Autowired
	private com.ctsi.ssdc.admin.repository.CscpOrgRepository cscpOrgRepository;

	@Autowired
	private ITRegulationsExaminationService regulationsExaminationService;
	@Autowired
	private com.ctsi.regulations.service.ITRegulationsProblemDescriptionService regulationsProblemDescriptionService;


	@Autowired
	private com.ctsi.regulations.service.ITRegulationsConfirmSaveService regulationsConfirmSaveService;



	// -------------法规室相关:-----------------------------------------------------------end

	@ApiOperation(value = "测试接口")
	@PostMapping(value = "/testDocFile", headers = "content-type=multipart/form-data")
	public ResultVO<String> testDocFile(@RequestParam("annex") MultipartFile[] annex ,
									 @RequestParam("doc") MultipartFile[] doc ,
									 String paramJsonStr
	) {
		log.info(paramJsonStr);
		System.out.println("接收参数-------------:" + paramJsonStr);
		int length = annex.length;
		int length1 = doc.length;
		String data = "正文数:"+ length1 +"  附件数:"+length;
		return ResultVO.success(paramJsonStr +"---------- "+data);
	}


	@GetMapping("/get/{id}")
	public ResultVO get(@PathVariable Long id, String type, String replyType) {
		ArrayList<Long> objects = new ArrayList<>();
		objects.add(id);
		if(StrUtil.equals("1",type) ){
			// 问题表id
			bizService.pushProblemFgs(id);
		}
		if(StrUtil.equals("2",type) ){
			bizService.pushCheckResultFgs(objects);
		}else{
			if(StrUtil.isEmpty(replyType)){
				replyType="10";
			}
			bizService.pushCheckResultReplyFgs(id,replyType,"20202测试");
		}



		return ResultVO.success();
	}



	@GetMapping("/initOrgCodeParent0")
	public ResultVO initOrgCodeParent0() {
		// 430000000000   湖南省编码
		LambdaQueryWrapper<CscpOrg> query = Wrappers.lambdaQuery();
		query.eq(CscpOrg::getParentId, 0L)
				.select(CscpOrg::getId,CscpOrg::getOrgCode)
				.orderByAsc(CscpOrg::getOrderBy)
		;
		List<CscpOrg> cscpOrgs = cscpOrgService.selectListNoAdd(query);
		for (int i = 0 ; i < cscpOrgs.size() ; i++) {
			String curOrgPathCode = "430000000000" + com.ctsi.hndx.utils.StringUtils.addZeroForNum( String.valueOf(i+ 1),
					4);
			CscpOrg org = cscpOrgs.get(i);
			org.setOrgCode(curOrgPathCode);

			if (westoneEncryptService.isCipherMachine()) {
				if (null != org.getOrgCode() && !"".equals(org.getOrgCode())) {
					// TODO 计算SM3HMAC
					org.setHmacOrgCode(westoneEncryptService.calculateSM3HMAC(org.getOrgName() + org.getOrgCode()));
				}
			}

			cscpOrgRepository.updateById(org);
		}
		return ResultVO.success();
	}

	/**
	 * 初始化机构编码
	 * @return
	 */
	@GetMapping("/initOrgCode")
	public ResultVO initOrgCode() {
		// 430000000000   湖南省编码

		LambdaQueryWrapper<CscpOrg> query = Wrappers.lambdaQuery();
		query.eq(CscpOrg::getParentId, 0L)
				.select(CscpOrg::getId,CscpOrg::getOrgCode)
				.orderByAsc(CscpOrg::getOrderBy)
		;
		List<CscpOrg> cscpOrgs = cscpOrgService.selectListNoAdd(query);

		forEachOrgCode(cscpOrgs);


		return ResultVO.success();
	}

	private void forEachOrgCode(List<CscpOrg> cscpOrgs_) {

		for (int k = 0 ; k < cscpOrgs_.size() ; k++) {

			CscpOrg cscpOrg = cscpOrgs_.get(k);
			LambdaQueryWrapper<CscpOrg> query = Wrappers.lambdaQuery();
			query.eq(CscpOrg::getParentId, cscpOrg.getId())
					// .isNull(CscpOrg::getOrgCode)
					.select(CscpOrg::getId,CscpOrg::getOrgCode)
					.orderByAsc(CscpOrg::getOrderBy)
			;
			List<CscpOrg> cscpOrgs1 = cscpOrgService.selectListNoAdd(query);

			if(cscpOrgs1 == null ||cscpOrgs1.isEmpty()){
				continue;
			}
			for (int i = 0 ; i < cscpOrgs1.size() ; i++) {
				CscpOrg org = cscpOrgs1.get(i);
				if(StrUtil.isEmpty(org.getOrgCode())){
					String curOrgPathCode = cscpOrg.getOrgCode() + com.ctsi.hndx.utils.StringUtils.addZeroForNum( String.valueOf(i+ 1),
							4);
					org.setOrgCode(curOrgPathCode);

					if (westoneEncryptService.isCipherMachine()) {
						if (null != org.getOrgCode() && !"".equals(org.getOrgCode())) {
							// TODO 计算SM3HMAC
							org.setHmacOrgCode(westoneEncryptService.calculateSM3HMAC(org.getOrgName() + org.getOrgCode()));
						}
					}

					cscpOrgRepository.updateById(org);
				}

			}
			forEachOrgCode(cscpOrgs1);
		}

	}

}
