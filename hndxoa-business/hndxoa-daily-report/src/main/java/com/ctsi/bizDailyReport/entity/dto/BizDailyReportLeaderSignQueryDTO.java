package com.ctsi.bizDailyReport.entity.dto;

import com.ctsi.hndx.common.BaseDtoEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 每日汇报子表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-13
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value="BizDailyReportSubQueryDTO查询对象", description="每日汇报子表")
public class BizDailyReportLeaderSignQueryDTO extends BaseDtoEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 标题
     */
    @ApiModelProperty(value = "标题")
    private String title;


    /**
     * 领导名称
     */
    @ApiModelProperty(value = "领导名称")
    private String readUsername;

    /**
     * 拟稿人
     */
    @ApiModelProperty(value = "拟稿人")
    private String createName;

    /**
     * 拟稿部门
     */
    @ApiModelProperty(value = "拟稿部门")
    private String departmentName;

    @ApiModelProperty(value = "开始时间")
    private String startTime;

    @ApiModelProperty(value = "结束时间")
    private String endTime;

    /**
     * 报送类型（1：涉岳舆情、2：每日汇报）
     */
    @ApiModelProperty(value = "报送类型（1：涉岳舆情、2：每日汇报）")
    private String reportType;

    /**
     * 阅读人ID集合
     */
    @ApiModelProperty(value = "阅读人ID集合")
    private List<Long> readUserIdList;
}
