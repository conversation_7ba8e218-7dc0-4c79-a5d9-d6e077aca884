package com.ctsi.bizDailyReport.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.Month;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ctsi.bizDailyReport.entity.BizDailyReportSub;
import com.ctsi.bizDailyReport.entity.dto.*;
import com.ctsi.bizDailyReport.mapper.BizDailyReportSubMapper;
import com.ctsi.bizDailyReport.service.IBizDailyReportSubService;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.common.SysBaseServiceImpl;
import com.ctsi.hndx.leadershipEntrustment.entity.BizLeadershipEntrustment;
import com.ctsi.hndx.leadershipEntrustment.service.IBizLeadershipEntrustmentService;
import com.ctsi.hndx.utils.BeanConvertUtils;
import com.ctsi.hndx.utils.ListCopyUtil;
import com.ctsi.hndx.utils.PageHelperUtil;
import com.ctsi.hndx.utils.StringUtils;
import com.ctsi.ssdc.model.PageResult;
import com.ctsi.ssdc.security.SecurityUtils;
import com.ctsi.system.entity.dto.TSysDictRecordDTO;
import com.ctsi.system.service.ITSysDictRecordService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 每日汇报子表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-22
 */
@Slf4j
@Service
public class BizDailyReportSubServiceImpl extends SysBaseServiceImpl<BizDailyReportSubMapper, BizDailyReportSub> implements IBizDailyReportSubService {

    @Autowired
    private BizDailyReportSubMapper bizDailyReportSubMapper;

    @Autowired
    private IBizLeadershipEntrustmentService bigLeadershipEntrustmentService;
    @Autowired
    private ITSysDictRecordService itSysDictRecordService;

    /**
     * 未阅
     * */
    private static final String unRead= "0";

    /**
     * 已阅
     * */
    private static final String hasRead= "1";

    /**
     * 翻页
     *
     * @param entityDTO
     * @param basePageForm
     * @return
     */
    @Override
    public PageResult<BizDailyReportSubRowDTO> queryListPage(BizDailyReportSubQueryDTO entityDTO, BasePageForm basePageForm) {
        //设置条件
        LambdaQueryWrapper<BizDailyReportSub> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.like(StringUtils.isNotEmpty(entityDTO.getTitle()), BizDailyReportSub::getTitle, entityDTO.getTitle());
        queryWrapper.eq(StringUtils.isNotEmpty(entityDTO.getReportType()), BizDailyReportSub::getReportType, entityDTO.getReportType());
        queryWrapper.ge(StringUtils.isNotEmpty(entityDTO.getStartTime()), BizDailyReportSub::getCreateTime, entityDTO.getStartTime());
        queryWrapper.le(StringUtils.isNotEmpty(entityDTO.getEndTime()), BizDailyReportSub::getCreateTime, entityDTO.getEndTime());
        queryWrapper.eq(StringUtils.isNotEmpty(entityDTO.getDepartmentName()), BizDailyReportSub::getDepartmentName, entityDTO.getDepartmentName());
        queryWrapper.eq(StringUtils.isNotEmpty(entityDTO.getReadUsername()), BizDailyReportSub::getReadUsername, entityDTO.getReadUsername());
        queryWrapper.eq(StringUtils.isNotEmpty(entityDTO.getDailyReportId()+""), BizDailyReportSub::getDailyReportId, entityDTO.getDailyReportId());
        queryWrapper.eq(StringUtils.isNotEmpty(entityDTO.getSignId()), BizDailyReportSub::getSignId, entityDTO.getSignId());
        queryWrapper.orderByDesc(BizDailyReportSub::getCreateTime);
        queryWrapper.eq(StringUtils.isNotEmpty(entityDTO.getReadUserid()+""), BizDailyReportSub::getReadUserid, SecurityUtils.getCurrentUserId());
        queryWrapper.eq(StringUtils.isNotEmpty(entityDTO.getReadStatus()),BizDailyReportSub::getReadStatus, entityDTO.getReadStatus());
        queryWrapper.eq(StringUtils.isNotEmpty(entityDTO.getStaffOrCompany()),BizDailyReportSub::getStaffOrCompany, entityDTO.getStaffOrCompany());
        queryWrapper.eq(StringUtils.isNotEmpty(entityDTO.getReadDepartmentId()+""),BizDailyReportSub::getReadDepartmentId, entityDTO.getReadDepartmentId());
        queryWrapper.eq(StringUtils.isNotEmpty(entityDTO.getReadDepartment()),BizDailyReportSub::getReadDepartment, entityDTO.getReadDepartment());
        IPage<BizDailyReportSub> pageData = bizDailyReportSubMapper.selectPageNoAdd(
             PageHelperUtil.getMPlusPageByBasePage(basePageForm), queryWrapper);
        //返回
        IPage<BizDailyReportSubRowDTO> data  = pageData.convert(entity -> BeanConvertUtils.copyProperties(entity,BizDailyReportSubRowDTO.class));

        return new PageResult<BizDailyReportSubRowDTO>(data.getRecords(),
            data.getTotal(), data.getCurrent());
    }

    /**
     * 通过查询条件查询领导批示相关信息报送信息翻页
     *
     * @param entityDTO
     * @param basePageForm
     * @return
     */
    @Override
    public PageResult<BizDailyReportLeaderSignDTO> queryDailyReportLeaderSignListPage(BizDailyReportLeaderSignQueryDTO entityDTO, BasePageForm basePageForm) {
        //查询当前登录人的领导ID
        LambdaQueryWrapper<BizLeadershipEntrustment> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.eq(BizLeadershipEntrustment:: getLiaisonManId, SecurityUtils.getCurrentUserId());
        List<BizLeadershipEntrustment> bizLeadershipEntrustmentList = bigLeadershipEntrustmentService.selectListNoAdd(queryWrapper);
        List<Long> LeaderIdList = new ArrayList<>();
        if(null!=bizLeadershipEntrustmentList&&bizLeadershipEntrustmentList.size()>0){
            //设置查询当前登录人对应领导的信息报送签批信息
            for (BizLeadershipEntrustment bizLeadershipEntrustment:bizLeadershipEntrustmentList){
                LeaderIdList.add(bizLeadershipEntrustment.getLeaderId());
            }
            entityDTO.setReadUserIdList(LeaderIdList);
            //设置条件
            IPage<BizDailyReportSub> pageData = bizDailyReportSubMapper.pageQueryDailyReportLeaderSignList(
                    PageHelperUtil.getMPlusPageByBasePage(basePageForm), entityDTO);
            //返回
            IPage<BizDailyReportLeaderSignDTO> data  = pageData.convert(entity -> BeanConvertUtils.copyProperties(entity,BizDailyReportLeaderSignDTO.class));
            List<BizDailyReportLeaderSignDTO> BizDailyReportLeaderSignList = data.getRecords();
            for (BizDailyReportLeaderSignDTO bizDailyReportLeaderSignDTO:BizDailyReportLeaderSignList){
                List<String> readUsernameList = new ArrayList<>();
                LambdaQueryWrapper<BizDailyReportSub> queryWrapperDailyReportSub = new LambdaQueryWrapper();
                queryWrapperDailyReportSub.eq(BizDailyReportSub::getDailyReportId,bizDailyReportLeaderSignDTO.getDailyReportId());
                queryWrapperDailyReportSub.in(BizDailyReportSub::getReadUserid,LeaderIdList);
                queryWrapperDailyReportSub.eq(BizDailyReportSub::getIsSign,"1");
                List<BizDailyReportSub> listData = bizDailyReportSubMapper.selectListNoAdd(queryWrapperDailyReportSub);
                if (null!=listData&&listData.size()>0){
                    for (BizDailyReportSub bizDailyReportSub:listData){
                        readUsernameList.add(bizDailyReportSub.getReadUsername()+"已批示");
                    }
                    bizDailyReportLeaderSignDTO.setReadUsernameList(readUsernameList);
                }
            }
            return new PageResult<BizDailyReportLeaderSignDTO>(data.getRecords(),
                    data.getTotal(), data.getCurrent());
        }else{
            return null;
        }

    }

    /**
     * 获取数据条数
     *
     * @param entityDTO
     * @param basePageForm
     * @return
     */
    @Override
    public int queryBizDailyReportSubCount(BizDailyReportSubQueryDTO entityDTO) {
        //设置条件
        LambdaQueryWrapper<BizDailyReportSub> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.like(StringUtils.isNotEmpty(entityDTO.getTitle()), BizDailyReportSub::getTitle, entityDTO.getTitle());
        queryWrapper.eq(StringUtils.isNotEmpty(entityDTO.getReportType()), BizDailyReportSub::getReportType, entityDTO.getReportType());
        queryWrapper.ge(StringUtils.isNotEmpty(entityDTO.getStartTime()), BizDailyReportSub::getCreateTime, entityDTO.getStartTime());
        queryWrapper.le(StringUtils.isNotEmpty(entityDTO.getEndTime()), BizDailyReportSub::getCreateTime, entityDTO.getEndTime());
        queryWrapper.eq(StringUtils.isNotEmpty(entityDTO.getDepartmentName()), BizDailyReportSub::getDepartmentName, entityDTO.getDepartmentName());
        queryWrapper.eq(StringUtils.isNotEmpty(entityDTO.getReadUsername()), BizDailyReportSub::getReadUsername, entityDTO.getReadUsername());
        queryWrapper.eq(StringUtils.isNotEmpty(entityDTO.getDailyReportId()+""), BizDailyReportSub::getDailyReportId, entityDTO.getDailyReportId());
        queryWrapper.eq(BizDailyReportSub::getReadUserid, SecurityUtils.getCurrentUserId());
        queryWrapper.eq(StringUtils.isNotEmpty(entityDTO.getReadStatus()),BizDailyReportSub::getReadStatus, entityDTO.getReadStatus());
        int bizDailyReportSubCount=this.selectCountNoAdd(queryWrapper);
        return bizDailyReportSubCount;
    }

    /**
     * 阅文统计
     *
     * @param year  年份
     * @return 数据条数
     */
    public Map<String, Map<String,Object>> queryBizDailyReportStatistics(Integer year){
        // 计算月份
        LocalDateTime endDate;
        if (year == LocalDate.now().getYear()){
            endDate = LocalDateTime.now();
        }else {
            // 往年查询，截止月份为12月
            endDate = LocalDateTime.of(year, 12, 31, 23, 59, 59);
        }

        LambdaQueryWrapper<BizDailyReportSub> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.ge(BizDailyReportSub::getCreateTime, LocalDateTime.of(year, 1, 1, 0, 0, 0));
        queryWrapper.le(BizDailyReportSub::getCreateTime, endDate);
        List<BizDailyReportSub> listData = bizDailyReportSubMapper.selectList(queryWrapper);

        // 统计月份的已阅和未阅
        Map<String, Object> monthMap = new LinkedHashMap<>();
        for (int i = 0; i < endDate.getMonthValue(); i++) {
            int finalI = i;
            Map<String, Long> readStatusMap = listData.stream().filter(x -> ObjectUtil.equals(x.getCreateTime().getMonthValue(), finalI+1))
                    .collect(Collectors.groupingBy(BizDailyReportSub::getReadStatus, Collectors.counting()));
            if (CollectionUtil.isEmpty(readStatusMap)){
                monthMap.put(Month.of(i).name(),"0,0");
            }else {
                monthMap.put(Month.of(i).name(), readStatusMap.get(unRead) + "," + readStatusMap.get(hasRead));
            }
        }

        // 各类阅文统计
        Map<String, Long> reportTypeMap = listData.stream().collect(Collectors.groupingBy(BizDailyReportSub::getReportType, Collectors.counting()));
        List<TSysDictRecordDTO> dicList = itSysDictRecordService.getDictRecordListByDictCode("reportType", SecurityUtils.getCurrentCompanyId());
        Map<String, String> dictMap = dicList.stream().collect(Collectors.toMap(TSysDictRecordDTO::getCode, TSysDictRecordDTO::getName, (v1, v2) -> v1));
        int totalCount = listData.size();
        HashMap<String, Object> typeMap = new HashMap<>();
        ArrayList<String> typeNameList = new ArrayList<>();
        ArrayList<Integer> typePercentList = new ArrayList<>();
        for (Map.Entry<String, Long> entry : reportTypeMap.entrySet()) {
            int percent;
            if (entry.getValue() != null) {
                percent = (int) Math.round((double) entry.getValue() / totalCount * 100);
            } else {
                percent = 0;
            }
            typeNameList.add(dictMap.get(entry.getKey()));
            typePercentList.add(percent);
        }

        int percent = 100;
        for (int i = 0; i < typePercentList.size(); i++) {

            if (i == typePercentList.size()-1){
                typePercentList.set(i,percent);
            }else {
                percent = percent - typePercentList.get(i);
            }
        }

        //总收文
        typeMap.put("reportTotal", Convert.toStr(totalCount));
        typeMap.put("typeNameList", typeNameList);
        typeMap.put("typePercentList", typePercentList);

        HashMap<String, Map<String, Object>> resultMap = new HashMap<>();
        //各月阅文统计
        resultMap.put("monthCount", monthMap);
        //各类阅文统计
        resultMap.put("typeCount", typeMap);

        return resultMap;
    }

    /**
     * 列表查询
     *
     * @param entityDTO
     * @return
     */
    @Override
    public List<BizDailyReportSubRowDTO> queryList(BizDailyReportSubQueryDTO entityDTO) {
        //设置条件
        LambdaQueryWrapper<BizDailyReportSub> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.like(StringUtils.isNotEmpty(entityDTO.getTitle()), BizDailyReportSub::getTitle, entityDTO.getTitle());
        queryWrapper.eq(StringUtils.isNotEmpty(entityDTO.getReportType()), BizDailyReportSub::getReportType, entityDTO.getReportType());
        queryWrapper.ge(StringUtils.isNotEmpty(entityDTO.getStartTime()), BizDailyReportSub::getCreateTime, entityDTO.getStartTime());
        queryWrapper.le(StringUtils.isNotEmpty(entityDTO.getEndTime()), BizDailyReportSub::getCreateTime, entityDTO.getEndTime());
        queryWrapper.eq(StringUtils.isNotEmpty(entityDTO.getDepartmentName()), BizDailyReportSub::getDepartmentName, entityDTO.getDepartmentName());
        queryWrapper.eq(StringUtils.isNotEmpty(entityDTO.getReadUsername()), BizDailyReportSub::getReadUsername, entityDTO.getReadUsername());
        queryWrapper.eq(StringUtils.isNotEmpty(entityDTO.getDailyReportId()+""), BizDailyReportSub::getDailyReportId, entityDTO.getDailyReportId());
        queryWrapper.orderByDesc(BizDailyReportSub::getCreateTime);
        queryWrapper.eq(BizDailyReportSub::getReadUserid, SecurityUtils.getCurrentUserId());
        queryWrapper.eq(StringUtils.isNotEmpty(entityDTO.getReadStatus()),BizDailyReportSub::getReadStatus, entityDTO.getReadStatus());
            List<BizDailyReportSub> listData = bizDailyReportSubMapper.selectList(queryWrapper);
            List<BizDailyReportSubRowDTO> BizDailyReportSubDTOList = ListCopyUtil.copy(listData, BizDailyReportSubRowDTO.class);
        return BizDailyReportSubDTOList;
    }

    /**
     * 列表查询不传当前登录人,不加租户条件
     *
     * @param entityDTO
     * @return
     */
    @Override
    public List<BizDailyReportSubRowDTO> queryListNoUser(BizDailyReportSubQueryDTO entityDTO) {
        //设置条件
        LambdaQueryWrapper<BizDailyReportSub> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.like(StringUtils.isNotEmpty(entityDTO.getTitle()), BizDailyReportSub::getTitle, entityDTO.getTitle());
        queryWrapper.eq(StringUtils.isNotEmpty(entityDTO.getReportType()), BizDailyReportSub::getReportType, entityDTO.getReportType());
        queryWrapper.ge(StringUtils.isNotEmpty(entityDTO.getStartTime()), BizDailyReportSub::getCreateTime, entityDTO.getStartTime());
        queryWrapper.le(StringUtils.isNotEmpty(entityDTO.getEndTime()), BizDailyReportSub::getCreateTime, entityDTO.getEndTime());
        queryWrapper.eq(StringUtils.isNotEmpty(entityDTO.getDepartmentName()), BizDailyReportSub::getDepartmentName, entityDTO.getDepartmentName());
        queryWrapper.eq(StringUtils.isNotEmpty(entityDTO.getReadUsername()), BizDailyReportSub::getReadUsername, entityDTO.getReadUsername());
        queryWrapper.eq(StringUtils.isNotEmpty(entityDTO.getDailyReportId()+""), BizDailyReportSub::getDailyReportId, entityDTO.getDailyReportId());
        queryWrapper.eq(StringUtils.isNotEmpty(entityDTO.getReadStatus()),BizDailyReportSub::getReadStatus, entityDTO.getReadStatus());
        queryWrapper.eq(BizDailyReportSub::getDeleted,"0");
        queryWrapper.orderByDesc(BizDailyReportSub::getCreateTime);

        List<BizDailyReportSub> listData = bizDailyReportSubMapper.selectListNoAdd(queryWrapper);
        List<BizDailyReportSubRowDTO> BizDailyReportSubDTOList = ListCopyUtil.copy(listData, BizDailyReportSubRowDTO.class);
        return BizDailyReportSubDTOList;
    }

    /**
     * 通过业务id获取对应的数据
     *
     * @param entityDTO
     * @return
     */
    @Override
    public List<BizDailyReportSubRowDTO> queryListByIds(List<Long> ids,BizDailyReportSubQueryDTO entityDTO) {
        List<BizDailyReportSubRowDTO> BizDailyReportSubDTOList = new ArrayList<>();
        //设置条件
        LambdaQueryWrapper<BizDailyReportSub> queryWrapper = new LambdaQueryWrapper();
        if(null!=ids&&StringUtils.isNotEmpty(ids)){
            queryWrapper.in(BizDailyReportSub::getId, ids);
        }
        queryWrapper.orderByDesc(BizDailyReportSub::getCreateTime);
        queryWrapper.eq(StringUtils.isNotEmpty(entityDTO.getReadStatus()),BizDailyReportSub::getReadStatus, entityDTO.getReadStatus());
        List<BizDailyReportSub> listData = bizDailyReportSubMapper.selectListNoAdd(queryWrapper);
        BizDailyReportSubDTOList = ListCopyUtil.copy(listData, BizDailyReportSubRowDTO.class);
        return BizDailyReportSubDTOList;
    }
    /**
     * 单个查询
     *
     * @param id the id of the entity
     * @return
     */
    @Override
    public BizDailyReportSubDTO findOne(Long id) {
        BizDailyReportSub  bizDailyReportSub =  bizDailyReportSubMapper.selectById(id);
        return  BeanConvertUtils.copyProperties(bizDailyReportSub,BizDailyReportSubDTO.class);
    }


    /**
     * 新增
     *
     * @param entityDTO the entity to create
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public BizDailyReportSubDTO create(BizDailyReportSubDTO entityDTO) {
       BizDailyReportSub bizDailyReportSub =  BeanConvertUtils.copyProperties(entityDTO,BizDailyReportSub.class);
        save(bizDailyReportSub);
        return  BeanConvertUtils.copyProperties(bizDailyReportSub,BizDailyReportSubDTO.class);
    }

    /**
     * 修改
     *
     * @param entity the entity to update
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int update(BizDailyReportSubDTO entity) {
        BizDailyReportSub bizDailyReportSub = BeanConvertUtils.copyProperties(entity,BizDailyReportSub.class);
        return bizDailyReportSubMapper.updateById(bizDailyReportSub);
    }

    /**
     * 删除
     *
     * @param id the id of the entity
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int delete(Long id) {
        return bizDailyReportSubMapper.deleteById(id);
    }


    /**
     * 验证是否存在
     *
     * @param BizDailyReportSubId
     * @return
     */
    @Override
    public boolean existByBizDailyReportSubId(Long BizDailyReportSubId) {
        if (BizDailyReportSubId != null) {
            LambdaQueryWrapper<BizDailyReportSub> queryWrapper = new LambdaQueryWrapper();
            queryWrapper.eq(BizDailyReportSub::getId, BizDailyReportSubId);
            List<BizDailyReportSub> result = bizDailyReportSubMapper.selectList(queryWrapper);
            return result.size() > 0;
        }
        return true;
    }

    /**
    * 批量新增
    *
    */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertBatch(List<BizDailyReportSubDTO> dataList) {
        List<BizDailyReportSub> result = ListCopyUtil.copy(dataList, BizDailyReportSub.class);
        return saveBatch(result);
    }

    /**
     * 通过主表业务id删除所有符合条件的记录
     *
     * @param entityDTO
     * @return
     */
    @Override
    public boolean deleteByDailyReportId(Long dailyReportId) {
        //设置条件
        LambdaQueryWrapper<BizDailyReportSub> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.eq(BizDailyReportSub::getDailyReportId, dailyReportId);
        boolean b = this.remove(queryWrapper);
        return b;
    }
}
