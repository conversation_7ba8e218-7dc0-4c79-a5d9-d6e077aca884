package com.ctsi.bizDailyReport.entity.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.ctsi.hndx.common.BaseAllDtoEntity;
import com.ctsi.hndx.common.BaseDtoEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * <p>
 * 每日汇报
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-22
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value="BizDailyReportDTO对象所有字段", description="每日汇报")
public class BizDailyReportAllDTO extends BaseAllDtoEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 标题
     */
    @ApiModelProperty(value = "标题")
    private String title;

    /**
     * 是否正文
     */
    @ApiModelProperty(value = "是否正文")
    private String document;

    /**
     * 分阅人id
     */
    @ApiModelProperty(value = "分阅人id")
    private String readUserids;

    /**
     * 分阅人名称
     */
    @ApiModelProperty(value = "分阅人名称")
    private String readUsernames;

    /**
     * 拟稿部门
     */
    @ApiModelProperty(value = "拟稿部门")
    private String departmentName;

    /**
     * 分阅单位id
     */
    @ApiModelProperty(value = "分阅单位id")
    private String readCompanyids;

    /**
     * 分阅单位名称
     */
    @ApiModelProperty(value = "分阅单位名称")
    private String readCompanynames;

    /**
     * 手写签批ID
     */
    @ApiModelProperty(value = "手写签批ID")
    private String signId;

    /**
     * 报送类型（1：涉岳舆情、2：每日汇报）
     */
    @ApiModelProperty(value = "报送类型（1：涉岳舆情、2：每日汇报）")
    private String reportType;

    /**
     * 报送类型名称
     */
    @ApiModelProperty(value = "报送类型名称")
    private String reportTypeName;

}
