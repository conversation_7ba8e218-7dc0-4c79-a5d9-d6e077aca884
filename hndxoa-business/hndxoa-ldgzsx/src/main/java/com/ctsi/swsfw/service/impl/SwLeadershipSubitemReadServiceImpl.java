package com.ctsi.swsfw.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ctsi.hndx.utils.BeanConvertUtils;
import com.ctsi.hndx.utils.ListCopyUtil;
import com.ctsi.ssdc.model.PageResult;
import com.ctsi.swsfw.entity.SwLeadershipSubitemRead;
import com.ctsi.swsfw.entity.dto.SwLeadershipSubitemReadDTO;
import com.ctsi.swsfw.mapper.SwLeadershipSubitemReadMapper;
import com.ctsi.swsfw.service.ISwLeadershipSubitemReadService;
import com.ctsi.hndx.common.SysBaseServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.utils.PageHelperUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.List;
import org.springframework.transaction.annotation.Transactional;

/**
 * <p>
 * 子事项领导评价表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-05
 */
@Slf4j
@Service
public class SwLeadershipSubitemReadServiceImpl extends SysBaseServiceImpl<SwLeadershipSubitemReadMapper, SwLeadershipSubitemRead> implements ISwLeadershipSubitemReadService {

    @Autowired
    private SwLeadershipSubitemReadMapper swLeadershipSubitemReadMapper;

    /**
     * 翻页
     *
     * @param entityDTO
     * @param basePageForm
     * @return
     */
    @Override
    public PageResult<SwLeadershipSubitemReadDTO> queryListPage(SwLeadershipSubitemReadDTO entityDTO, BasePageForm basePageForm) {
        //设置条件
        LambdaQueryWrapper<SwLeadershipSubitemRead> queryWrapper = new LambdaQueryWrapper();

        IPage<SwLeadershipSubitemRead> pageData = swLeadershipSubitemReadMapper.selectPage(
             PageHelperUtil.getMPlusPageByBasePage(basePageForm), queryWrapper);
        //返回
        IPage<SwLeadershipSubitemReadDTO> data  = pageData.convert(entity -> BeanConvertUtils.copyProperties(entity,SwLeadershipSubitemReadDTO.class));

        return new PageResult<SwLeadershipSubitemReadDTO>(data.getRecords(),
            data.getTotal(), data.getCurrent());
    }

    /**
     * 列表查询
     *
     * @param entityDTO
     * @return
     */
    @Override
    public List<SwLeadershipSubitemReadDTO> queryList(SwLeadershipSubitemReadDTO entityDTO) {
        LambdaQueryWrapper<SwLeadershipSubitemRead> queryWrapper = new LambdaQueryWrapper();
            List<SwLeadershipSubitemRead> listData = swLeadershipSubitemReadMapper.selectList(queryWrapper);
            List<SwLeadershipSubitemReadDTO> SwLeadershipSubitemReadDTOList = ListCopyUtil.copy(listData, SwLeadershipSubitemReadDTO.class);
        return SwLeadershipSubitemReadDTOList;
    }

    /**
     * 单个查询
     *
     * @param id the id of the entity
     * @return
     */
    @Override
    public SwLeadershipSubitemReadDTO findOne(Long id) {
        SwLeadershipSubitemRead  swLeadershipSubitemRead =  swLeadershipSubitemReadMapper.selectById(id);
        return  BeanConvertUtils.copyProperties(swLeadershipSubitemRead,SwLeadershipSubitemReadDTO.class);
    }


    /**
     * 新增
     *
     * @param entityDTO the entity to create
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public SwLeadershipSubitemReadDTO create(SwLeadershipSubitemReadDTO entityDTO) {
       SwLeadershipSubitemRead swLeadershipSubitemRead =  BeanConvertUtils.copyProperties(entityDTO,SwLeadershipSubitemRead.class);
        save(swLeadershipSubitemRead);
        return  BeanConvertUtils.copyProperties(swLeadershipSubitemRead,SwLeadershipSubitemReadDTO.class);
    }

    /**
     * 修改
     *
     * @param entity the entity to update
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int update(SwLeadershipSubitemReadDTO entity) {
        SwLeadershipSubitemRead swLeadershipSubitemRead = BeanConvertUtils.copyProperties(entity,SwLeadershipSubitemRead.class);
        return swLeadershipSubitemReadMapper.updateById(swLeadershipSubitemRead);
    }

    /**
     * 删除
     *
     * @param id the id of the entity
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int delete(Long id) {
        return swLeadershipSubitemReadMapper.deleteById(id);
    }


    /**
     * 验证是否存在
     *
     * @param SwLeadershipSubitemReadId
     * @return
     */
    @Override
    public boolean existBySwLeadershipSubitemReadId(Long SwLeadershipSubitemReadId) {
        if (SwLeadershipSubitemReadId != null) {
            LambdaQueryWrapper<SwLeadershipSubitemRead> queryWrapper = new LambdaQueryWrapper();
            queryWrapper.eq(SwLeadershipSubitemRead::getId, SwLeadershipSubitemReadId);
            List<SwLeadershipSubitemRead> result = swLeadershipSubitemReadMapper.selectList(queryWrapper);
            return result.size() > 0;
        }
        return true;
    }

    /**
    * 批量新增
    *
    */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertBatch(List<SwLeadershipSubitemReadDTO> dataList) {
        List<SwLeadershipSubitemRead> result = ListCopyUtil.copy(dataList, SwLeadershipSubitemRead.class);
        return saveBatch(result);
    }


}
