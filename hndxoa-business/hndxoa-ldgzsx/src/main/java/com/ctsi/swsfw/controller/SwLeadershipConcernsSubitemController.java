package com.ctsi.swsfw.controller;
import com.ctsi.ssdc.model.PageResult;
import java.util.List;
import java.util.Optional;
import com.ctsi.swsfw.entity.SwLeadershipConcernsSubitem;
import com.ctsi.swsfw.entity.dto.SwLeadershipConcernsSubitemDTO;
import com.ctsi.swsfw.service.ISwLeadershipConcernsSubitemService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.ctsi.ssdc.model.ResResult;
import org.springframework.security.access.prepost.PreAuthorize;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import com.ctsi.hndx.common.BasePageForm;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.InitBinder;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.util.Assert;
import com.ctsi.hndx.common.BaseController;
import com.ctsi.hndx.annotations.ResponseResultVo;
import com.ctsi.hndx.result.ResultCode;
import com.ctsi.hndx.result.ResultVO;
import com.ctsi.ssdc.annotation.OperationLog;
import com.ctsi.hndx.enums.DBOperation;


/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-09
 *
 */

@Slf4j
@RestController
@ResponseResultVo
@RequestMapping("/api/swLeadershipConcernsSubitem")
@Api(value = "子事项信息表", tags = "子事项信息表接口")
public class SwLeadershipConcernsSubitemController extends BaseController {

    private static final String ENTITY_NAME = "swLeadershipConcernsSubitem";

    @Autowired
    private ISwLeadershipConcernsSubitemService swLeadershipConcernsSubitemService;



    /**
     *  新增子事项信息表批量数据.
     */
    @PostMapping("/createBatch")
    @ApiOperation(value = "新增批量(权限code码为：cscp.swLeadershipConcernsSubitem.add)", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.ADD,message = "新增子事项信息表批量数据")
//    @PreAuthorize("@permissionService.hasPermi('cscp.swLeadershipConcernsSubitem.add')")
    public ResultVO createBatch(@RequestBody List<SwLeadershipConcernsSubitemDTO> swLeadershipConcernsSubitemList) {
       Boolean  result = swLeadershipConcernsSubitemService.insertBatch(swLeadershipConcernsSubitemList);
       if(result){
           return ResultVO.success();
       }else {
           return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
       }
    }

     /**
     *  新增数据.
     */
    @PostMapping("/create")
    @ApiOperation(value = "新增(权限code码为：cscp.swLeadershipConcernsSubitem.add)", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.ADD,message = "新增子事项信息表数据")
//    @PreAuthorize("@permissionService.hasPermi('cscp.swLeadershipConcernsSubitem.add')")
    public ResultVO<SwLeadershipConcernsSubitemDTO> create(@RequestBody SwLeadershipConcernsSubitemDTO swLeadershipConcernsSubitemDTO)  {
        SwLeadershipConcernsSubitemDTO result = swLeadershipConcernsSubitemService.create(swLeadershipConcernsSubitemDTO);
        return ResultVO.success(result);
    }

    /**
     *  更新存在数据.
     */
    @PostMapping("/update")
    @ApiOperation(value = "更新存在数据(权限code码为：cscp.swLeadershipConcernsSubitem.update)", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.UPDATE,message = "更新子事项信息表数据")
//    @PreAuthorize("@permissionService.hasPermi('cscp.swLeadershipConcernsSubitem.update')")
    public ResultVO update(@RequestBody SwLeadershipConcernsSubitemDTO swLeadershipConcernsSubitemDTO) {
	    Assert.notNull(swLeadershipConcernsSubitemDTO.getId(), "general.IdNotNull");
        int count = swLeadershipConcernsSubitemService.update(swLeadershipConcernsSubitemDTO);
        if(count > 0 ){
            return ResultVO.success();
        }else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }

     /**
     *  删除存在数据.
     */
    @DeleteMapping("/delete/{id}")
    @OperationLog(dBOperation = DBOperation.DELETE,message = "删除子事项信息表数据")
    @ApiOperation(value = "删除存在数据(权限code码为：cscp.swLeadershipConcernsSubitem.delete)", notes = "传入参数")
//    @PreAuthorize("@permissionService.hasPermi('cscp.swLeadershipConcernsSubitem.delete')")
    public ResultVO delete(@PathVariable Long id) {
        int count = swLeadershipConcernsSubitemService.delete(id);
        if(count > 0 ){
            return ResultVO.success();
        }else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }

    /**
     * 查询单条数据.
     */
    @GetMapping("/get/{id}")
    @ApiOperation(value = "查询单条数据", notes = "传入参数")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
    public ResultVO get(@PathVariable Long id) {
        SwLeadershipConcernsSubitemDTO swLeadershipConcernsSubitemDTO = swLeadershipConcernsSubitemService.findOne(id);
        return ResultVO.success(swLeadershipConcernsSubitemDTO);
    }

    /**
    *  分页查询多条数据.
    */
    @GetMapping("/querySwLeadershipConcernsSubitemPage")
    @ApiOperation(value = "翻页查询多条数据", notes = "传入参数")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
    public ResultVO<PageResult<SwLeadershipConcernsSubitemDTO>> querySwLeadershipConcernsSubitemPage(SwLeadershipConcernsSubitemDTO swLeadershipConcernsSubitemDTO, BasePageForm basePageForm) {
        return ResultVO.success(swLeadershipConcernsSubitemService.queryListPage(swLeadershipConcernsSubitemDTO, basePageForm));
    }

   /**
    * 查询多条数据.不分页
    */
   @GetMapping("/querySwLeadershipConcernsSubitem")
   @ApiOperation(value = "查询多条数据", notes = "传入参数")
   //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
   public ResultVO<ResResult<SwLeadershipConcernsSubitemDTO>> querySwLeadershipConcernsSubitem(SwLeadershipConcernsSubitemDTO swLeadershipConcernsSubitemDTO) {
       List<SwLeadershipConcernsSubitemDTO> list = swLeadershipConcernsSubitemService.queryList(swLeadershipConcernsSubitemDTO);
       return ResultVO.success(new ResResult<SwLeadershipConcernsSubitemDTO>(list));
   }

}
