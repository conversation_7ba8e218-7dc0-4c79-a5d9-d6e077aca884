package com.ctsi.swsfw.controller;
import com.ctsi.ssdc.model.PageResult;
import java.util.List;
import java.util.Optional;
import com.ctsi.swsfw.entity.SwLeadershipConcernsSort;
import com.ctsi.swsfw.entity.dto.SwLeadershipConcernsSortDTO;
import com.ctsi.swsfw.service.ISwLeadershipConcernsSortService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.ctsi.ssdc.model.ResResult;
import org.springframework.security.access.prepost.PreAuthorize;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import com.ctsi.hndx.common.BasePageForm;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.InitBinder;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.util.Assert;
import com.ctsi.hndx.common.BaseController;
import com.ctsi.hndx.annotations.ResponseResultVo;
import com.ctsi.hndx.result.ResultCode;
import com.ctsi.hndx.result.ResultVO;
import com.ctsi.ssdc.annotation.OperationLog;
import com.ctsi.hndx.enums.DBOperation;


/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-11
 *
 */

@Slf4j
@RestController
@ResponseResultVo
@RequestMapping("/api/swLeadershipConcernsSort")
@Api(value = "事项个人排序表", tags = "事项个人排序表接口")
public class SwLeadershipConcernsSortController extends BaseController {

    private static final String ENTITY_NAME = "swLeadershipConcernsSort";

    @Autowired
    private ISwLeadershipConcernsSortService swLeadershipConcernsSortService;



    /**
     *  新增事项个人排序表批量数据.
     */
    @PostMapping("/createBatch")
    @ApiOperation(value = "新增批量(权限code码为：cscp.swLeadershipConcernsSort.add)", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.ADD,message = "新增事项个人排序表批量数据")
    @PreAuthorize("@permissionService.hasPermi('cscp.swLeadershipConcernsSort.add')")
    public ResultVO createBatch(@RequestBody List<SwLeadershipConcernsSortDTO> swLeadershipConcernsSortList) {
       Boolean  result = swLeadershipConcernsSortService.insertBatch(swLeadershipConcernsSortList);
       if(result){
           return ResultVO.success();
       }else {
           return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
       }
    }

     /**
     *  置顶或修改排序.
     */
    @PostMapping("/create")
    @ApiOperation(value = "置顶或修改排序", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.ADD,message = "置顶")
    public ResultVO<List<SwLeadershipConcernsSortDTO>> create(@RequestBody List<SwLeadershipConcernsSortDTO> dtoList)  {
        List<SwLeadershipConcernsSortDTO> result = swLeadershipConcernsSortService.create(dtoList);
        return ResultVO.success(result);
    }

     /**
     *  删除存在数据.
     */
    @DeleteMapping("/delete/{id}")
    @OperationLog(dBOperation = DBOperation.DELETE,message = "删除事项个人排序表数据")
    @ApiOperation(value = "删除存在数据(权限code码为：cscp.swLeadershipConcernsSort.delete)", notes = "传入参数")
    @PreAuthorize("@permissionService.hasPermi('cscp.swLeadershipConcernsSort.delete')")
    public ResultVO delete(@PathVariable Long id) {
        int count = swLeadershipConcernsSortService.delete(id);
        if(count > 0 ){
            return ResultVO.success();
        }else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }

    /**
     * 查询单条数据.
     */
    @GetMapping("/get/{id}")
    @ApiOperation(value = "查询单条数据", notes = "传入参数")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
    public ResultVO get(@PathVariable Long id) {
        SwLeadershipConcernsSortDTO swLeadershipConcernsSortDTO = swLeadershipConcernsSortService.findOne(id);
        return ResultVO.success(swLeadershipConcernsSortDTO);
    }

    /**
    *  分页查询多条数据.
    */
    @GetMapping("/querySwLeadershipConcernsSortPage")
    @ApiOperation(value = "翻页查询多条数据", notes = "传入参数")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
    public ResultVO<PageResult<SwLeadershipConcernsSortDTO>> querySwLeadershipConcernsSortPage(SwLeadershipConcernsSortDTO swLeadershipConcernsSortDTO, BasePageForm basePageForm) {
        return ResultVO.success(swLeadershipConcernsSortService.queryListPage(swLeadershipConcernsSortDTO, basePageForm));
    }

   /**
    * 查询多条数据.不分页
    */
   @GetMapping("/querySwLeadershipConcernsSort")
   @ApiOperation(value = "查询多条数据", notes = "传入参数")
   //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
   public ResultVO<ResResult<SwLeadershipConcernsSortDTO>> querySwLeadershipConcernsSort(SwLeadershipConcernsSortDTO swLeadershipConcernsSortDTO) {
       List<SwLeadershipConcernsSortDTO> list = swLeadershipConcernsSortService.queryList(swLeadershipConcernsSortDTO);
       return ResultVO.success(new ResResult<SwLeadershipConcernsSortDTO>(list));
   }



    /**
     * 取消置顶
     */
    @GetMapping("/cancelTop")
    @ApiOperation(value = "取消置顶", notes = "传入参数")
    public ResultVO cancelTop(SwLeadershipConcernsSortDTO swLeadershipConcernsSortDTO) {
       swLeadershipConcernsSortService.cancelTop(swLeadershipConcernsSortDTO);
        return ResultVO.success();
    }
}
