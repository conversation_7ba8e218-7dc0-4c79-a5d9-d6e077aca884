package com.ctsi.swsfw.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ctsi.hndx.utils.BeanConvertUtils;
import com.ctsi.hndx.utils.ListCopyUtil;
import com.ctsi.ssdc.model.PageResult;
import com.ctsi.swsfw.entity.SwLeadershipSubitemEvaluation;
import com.ctsi.swsfw.entity.dto.SwLeadershipSubitemEvaluationDTO;
import com.ctsi.swsfw.mapper.SwLeadershipSubitemEvaluationMapper;
import com.ctsi.swsfw.service.ISwLeadershipSubitemEvaluationService;
import com.ctsi.hndx.common.SysBaseServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.utils.PageHelperUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.List;
import org.springframework.transaction.annotation.Transactional;

/**
 * <p>
 * 子事项领导评价表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-30
 */
@Slf4j
@Service
public class SwLeadershipSubitemEvaluationServiceImpl extends SysBaseServiceImpl<SwLeadershipSubitemEvaluationMapper, SwLeadershipSubitemEvaluation> implements ISwLeadershipSubitemEvaluationService {

    @Autowired
    private SwLeadershipSubitemEvaluationMapper swLeadershipSubitemEvaluationMapper;

    /**
     * 翻页
     *
     * @param entityDTO
     * @param basePageForm
     * @return
     */
    @Override
    public PageResult<SwLeadershipSubitemEvaluationDTO> queryListPage(SwLeadershipSubitemEvaluationDTO entityDTO, BasePageForm basePageForm) {
        //设置条件
        LambdaQueryWrapper<SwLeadershipSubitemEvaluation> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.eq(SwLeadershipSubitemEvaluation::getSubitemId,entityDTO.getSubitemId());
        IPage<SwLeadershipSubitemEvaluation> pageData = swLeadershipSubitemEvaluationMapper.selectPageNoAdd(
             PageHelperUtil.getMPlusPageByBasePage(basePageForm), queryWrapper);
        //返回
        IPage<SwLeadershipSubitemEvaluationDTO> data  = pageData.convert(entity -> BeanConvertUtils.copyProperties(entity,SwLeadershipSubitemEvaluationDTO.class));

        return new PageResult<SwLeadershipSubitemEvaluationDTO>(data.getRecords(),
            data.getTotal(), data.getCurrent());
    }

    /**
     * 列表查询
     *
     * @param entityDTO
     * @return
     */
    @Override
    public List<SwLeadershipSubitemEvaluationDTO> queryList(SwLeadershipSubitemEvaluationDTO entityDTO) {
        LambdaQueryWrapper<SwLeadershipSubitemEvaluation> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.eq(SwLeadershipSubitemEvaluation::getSubitemId,entityDTO.getSubitemId());
            List<SwLeadershipSubitemEvaluation> listData = swLeadershipSubitemEvaluationMapper.selectListNoAdd(queryWrapper);
            List<SwLeadershipSubitemEvaluationDTO> SwLeadershipSubitemEvaluationDTOList = ListCopyUtil.copy(listData, SwLeadershipSubitemEvaluationDTO.class);
        return SwLeadershipSubitemEvaluationDTOList;
    }

    /**
     * 单个查询
     *
     * @param id the id of the entity
     * @return
     */
    @Override
    public SwLeadershipSubitemEvaluationDTO findOne(Long id) {
        SwLeadershipSubitemEvaluation  swLeadershipSubitemEvaluation =  swLeadershipSubitemEvaluationMapper.selectById(id);
        return  BeanConvertUtils.copyProperties(swLeadershipSubitemEvaluation,SwLeadershipSubitemEvaluationDTO.class);
    }


    /**
     * 新增
     *
     * @param entityDTO the entity to create
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public SwLeadershipSubitemEvaluationDTO create(SwLeadershipSubitemEvaluationDTO entityDTO) {
       SwLeadershipSubitemEvaluation swLeadershipSubitemEvaluation =  BeanConvertUtils.copyProperties(entityDTO,SwLeadershipSubitemEvaluation.class);
        save(swLeadershipSubitemEvaluation);
        return  BeanConvertUtils.copyProperties(swLeadershipSubitemEvaluation,SwLeadershipSubitemEvaluationDTO.class);
    }

    /**
     * 修改
     *
     * @param entity the entity to update
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int update(SwLeadershipSubitemEvaluationDTO entity) {
        SwLeadershipSubitemEvaluation swLeadershipSubitemEvaluation = BeanConvertUtils.copyProperties(entity,SwLeadershipSubitemEvaluation.class);
        return swLeadershipSubitemEvaluationMapper.updateById(swLeadershipSubitemEvaluation);
    }

    /**
     * 删除
     *
     * @param id the id of the entity
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int delete(Long id) {
        return swLeadershipSubitemEvaluationMapper.deleteById(id);
    }


    /**
     * 验证是否存在
     *
     * @param SwLeadershipSubitemEvaluationId
     * @return
     */
    @Override
    public boolean existBySwLeadershipSubitemEvaluationId(Long SwLeadershipSubitemEvaluationId) {
        if (SwLeadershipSubitemEvaluationId != null) {
            LambdaQueryWrapper<SwLeadershipSubitemEvaluation> queryWrapper = new LambdaQueryWrapper();
            queryWrapper.eq(SwLeadershipSubitemEvaluation::getId, SwLeadershipSubitemEvaluationId);
            List<SwLeadershipSubitemEvaluation> result = swLeadershipSubitemEvaluationMapper.selectListNoAdd(queryWrapper);
            return result.size() > 0;
        }
        return true;
    }

    /**
    * 批量新增
    *
    */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertBatch(List<SwLeadershipSubitemEvaluationDTO> dataList) {
        List<SwLeadershipSubitemEvaluation> result = ListCopyUtil.copy(dataList, SwLeadershipSubitemEvaluation.class);
        return saveBatch(result);
    }


}
