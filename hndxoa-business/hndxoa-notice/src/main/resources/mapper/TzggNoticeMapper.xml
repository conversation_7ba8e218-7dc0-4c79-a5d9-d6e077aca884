<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ctsi.notic.mapper.TNoticeMapper">
    <resultMap id="resTnotice" type="com.ctsi.notic.entity.TNotice">
        <result column="id" property="id"></result>
        <result column="TITLE" property="title"></result>
        <result column="OUTLINE" property="outline"></result>
        <result column="create_name" property="createName"></result>
        <result column="create_time" property="createTime"></result>
        <result column="NOTICE_STATE" property="noticeState"></result>
        <result column="NOTIFICATION_TYPE_NAME" property="notificationTypeName"></result>
        <result column="source_unit_name" property="sourceUnitName"></result>
        <result column="source_unit_id" property="sourceUnitId"></result>
    </resultMap>


    <select id="queryCondition" parameterType="com.ctsi.notic.entity.dto.QueryTzggNoticeDTO"
            resultType="com.ctsi.notic.entity.dto.ResTnoticeDTO">
        SELECT tn.ID,tn.TITLE, tn.OUTLINE, tnu.create_name, tnu.create_time,
        tnu.IS_READING,tn.NOTIFICATION_TYPE_NAME,tn.source_unit_name,tn.source_department_name,
        tn.source_unit_id,tnu.id as tunid,tnu.reply_time as replyTime,tnu.first_open_time,tn.secret_level,
        CASE
        WHEN first_open_time IS NOT NULL THEN 1
        ELSE 0
        END AS signInStatus
        FROM t_notice tn
        LEFT JOIN t_notice_user tnu on tn.ID = tnu.NOTICE_NOTICE_ID
        <where>
            and tnu.user_id = #{param.userId} and tn.DELETED = 0 and (tn.NOTICE_STATE = 0 or tn.NOTICE_STATE = 3)
            <if test="param.id != null and param.id != ''">
                and tn.id = #{param.id}
            </if>

            <if test="param.title != null and param.title != ''">
                and tn.TITLE like concat('%',#{param.title},'%')
            </if>


            <if test="param.notificationTypeId != null and param.notificationTypeId != ''">
                and tn.NOTIFICATION_TYPE_ID = #{param.notificationTypeId}
            </if>
            <if test="param.startTime != null and param.startTime != ''">
                and tn.CREATE_TIME >= #{param.startTime}
            </if>
            <if test="param.endTime != null and param.endTime != ''">
                and tn.CREATE_TIME &lt;= #{param.endTime}
            </if>
            <if test="param.isRead !=null ">
                and tnu.IS_READING = #{param.isRead}
            </if>
            <if test="param.securityClassificationCode == 0">
                and (tn.secret_level_name is null or tn.secret_level_name like '%内部%' or tn.secret_level_name like '%秘密%')
            </if>
            <choose>
                <when test="param.isRead ==1">
                    order by tnu.REPLY_TIME desc
                </when>
                <otherwise>
                    order by tnu.CREATE_TIME desc
                </otherwise>
            </choose>

        </where>
    </select>


    <!--三服务首页-通知公告消息列表-->
    <select id="selectSFWNoticePage" parameterType="com.ctsi.notic.entity.dto.QueryTzggNoticeDTO"
            resultType="com.ctsi.notic.entity.dto.ResTnoticeDTO">
        SELECT
            tn.id, tn.title, tn.notification_type_name, tn.create_name, tn.create_time, tn.notice_state,
            tn.source_unit_name, tn.source_unit_id, tn.source_department_name, tn.secret_level,tn.secret_level_name,
            CASE WHEN tnu.first_open_time IS NULL THEN 0 ELSE 1 END AS sign_in_status
        FROM t_notice tn
        LEFT JOIN t_notice_user tnu on tn.ID = tnu.NOTICE_NOTICE_ID
        <where>
            and tnu.user_id = #{param.userId} and tn.DELETED = 0 and (tn.NOTICE_STATE = 0 or tn.NOTICE_STATE = 3)
            <if test="param.id != null and param.id != ''">
                and tn.id = #{param.id}
            </if>

            <if test="param.title != null and param.title != ''">
                and tn.TITLE like concat('%',#{param.title},'%')
            </if>


            <if test="param.notificationTypeId != null and param.notificationTypeId != ''">
                and tn.NOTIFICATION_TYPE_ID = #{param.notificationTypeId}
            </if>
            <if test="param.startTime != null and param.startTime != ''">
                and tn.CREATE_TIME >= #{param.startTime}
            </if>
            <if test="param.endTime != null and param.endTime != ''">
                and tn.CREATE_TIME &lt;= #{param.endTime}
            </if>
            <if test="param.isRead !=null ">
                and tnu.IS_READING = #{param.isRead}
            </if>
            <if test="param.securityClassificationCode == 0">
                and (tn.secret_level_name is null or tn.secret_level_name like '%内部%' or tn.secret_level_name like '%秘密%')
            </if>
            <choose>
                <when test="param.isRead ==1">
                    order by tnu.REPLY_TIME desc
                </when>
                <otherwise>
                    order by tnu.CREATE_TIME desc
                </otherwise>
            </choose>

        </where>
    </select>

    <select id="getNoticeList" parameterType="com.ctsi.notic.entity.TNotice"
            resultMap="resTnotice">
        SELECT tn.ID, tn.TITLE, tn.OUTLINE, tn.create_name, tn.create_time, tn.NOTICE_STATE,tn.NOTIFICATION_TYPE_NAME
        FROM t_notice tn
        <where>
            <if test="Notice.createBy != null and Notice.createBy != ''">
                and tn.CREATE_BY = #{Notice.createBy}
            </if>
            <if test="Notice.title != null and Notice.title != ''">
                and tn.TITLE = #{Notice.title}
            </if>
            <if test="Notice.notificationTypeId != null and Notice.notificationTypeId != ''">
                and tn.NOTIFICATION_TYPE_ID = #{Notice.notificationTypeId}
            </if>
            <if test="Notice.startTime != null and Notice.startTime != ''">
                and tn.CREATE_TIME >= #{Notice.startTime}
            </if>
            <if test="Notice.endTime != null and Notice.endTime != ''">
                and tn.CREATE_TIME &lt;= #{Notice.endTime}
            </if>
            and tn.DELETED = 0
            order by tn.CREATE_TIME desc
        </where>
    </select>

    <select id="querUnreadCirculateCount" parameterType="com.ctsi.notic.entity.dto.QueryTzggNoticeDTO"
            resultType="java.lang.Long">
        SELECT tn.ID,tn.TITLE, tn.OUTLINE, tn.create_name, tn.create_time,
        tnu.IS_READING,tn.NOTIFICATION_TYPE_NAME,tn.source_unit_name,tn.source_unit_id
        FROM t_notice tn
        RIGHT JOIN t_notice_user tnu on tn.ID = tnu.NOTICE_NOTICE_ID
        WHERE tnu.user_id = #{param.userId}
        and tn.DELETED = 0
        and (tn.NOTICE_STATE = 0 or tn.NOTICE_STATE = 3)
        and tnu.IS_READING = #{param.isRead}

          -- 未阅
        <if test="param.signInStatus == 0 ">
            and tnu.first_open_time is null
        </if>
        <if test="param.signInStatus == 1 ">
            and tnu.first_open_time is not null
        </if>

        order by tn.CREATE_TIME desc
    </select>

    <!--    呈批件业务数据查询-->
    <select id="selectCpjById" resultType="java.util.Map">
        select title as
                   titleCld,is_print,process_instance_id,telegraph,secret,duration_classification,duration_classification_name
        from   biz_approval_management a where 1=1
                                           and id =#{id}
    </select>


</mapper>
