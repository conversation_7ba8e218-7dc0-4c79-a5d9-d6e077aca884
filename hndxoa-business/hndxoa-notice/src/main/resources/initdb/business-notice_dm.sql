CREATE TABLE "myapp"."t_notice"
(
"ID" BIGINT DEFAULT 0 NOT NULL,
"TITLE" VARCHAR(255),
"OUTLINE" VARCHAR(255),
"NOTIFICATION_TYPE_ID" BIGINT,
"ENCLOSURE_ID" BIGINT,
"CREATE_TIME" TIMESTAMP(0),
"CREATE_BY" BIGINT,
"CREATE_NAME" VARCHAR(32),
"UPDATE_TIME" TIMESTAMP(0),
"UPDATE_BY" BIGINT,
"UPDATE_NAME" VARCHAR(32),
"DEPARTMENT_ID" BIGINT,
"COMPANY_ID" BIGINT,
"TENANT_ID" BIGINT,
"DELETED" INT,
"NOTICE_STATE" INT DEFAULT 0,
"NOTIFICATION_TYPE_NAME" VARCHAR(50),
"FROM_DATA_ID" BI, GINT,
"source_unit_name" VARCHAR(100),
"source_unit_id" BIGINT,
"hits" INT,
"selection_method" INT,
"sms_reminder" INT,
NOT CLUSTER PRIMARY KEY("ID")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE "myapp"."t_notice" IS '通知公告 ';COMMENT ON COLUMN "myapp"."t_notice"."ID" IS '主键id';
COMMENT ON COLUMN "myapp"."t_notice"."TITLE" IS '标题';
COMMENT ON COLUMN "myapp"."t_notice"."OUTLINE" IS '概要';
COMMENT ON COLUMN "myapp"."t_notice"."NOTIFICATION_TYPE_ID" IS '通知公告类型ID';
COMMENT ON COLUMN "myapp"."t_notice"."ENCLOSURE_ID" IS '附件id';
COMMENT ON COLUMN "myapp"."t_notice"."CREATE_TIME" IS '创建时间';
COMMENT ON COLUMN "myapp"."t_notice"."CREATE_BY" IS '创建人id';
COMMENT ON COLUMN "myapp"."t_notice"."CREATE_NAME" IS '创建人姓名';
COMMENT ON COLUMN "myapp"."t_notice"."UPDATE_TIME" IS '修改时间';
COMMENT ON COLUMN "myapp"."t_notice"."UPDATE_BY" IS '修改人id';
COMMENT ON COLUMN "myapp"."t_notice"."UPDATE_NAME" IS '更新人';
COMMENT ON COLUMN "myapp"."t_notice"."DEPARTMENT_ID" IS '部门id';
COMMENT ON COLUMN "myapp"."t_notice"."COMPANY_ID" IS '单位id';
COMMENT ON COLUMN "myapp"."t_notice"."TENANT_ID" IS '租户id';
COMMENT ON COLUMN "myapp"."t_notice"."DELETED" IS '逻辑删除 0：未删除 1：删除';
COMMENT ON COLUMN "myapp"."t_notice"."NOTICE_STATE" IS '通知公告状态  0:已发布 1:审批中 2：审批不通过 3：审批通过  ';
COMMENT ON COLUMN "myapp"."t_notice"."NOTIFICATION_TYPE_NAME" IS '通知公告类型名称';
COMMENT ON COLUMN "myapp"."t_notice"."FROM_DATA_ID" IS '业务id';
COMMENT ON COLUMN "myapp"."t_notice"."source_unit_name" IS '来源单位名称';
COMMENT ON COLUMN "myapp"."t_notice"."source_unit_id" IS '来源单位id';
COMMENT ON COLUMN "myapp"."t_notice"."hits" IS '点击率';
COMMENT ON COLUMN "myapp"."t_notice"."selection_method" IS '选人方式（0：本单位 1：自定义 2：外单位）';
COMMENT ON COLUMN "myapp"."t_notice"."sms_reminder" IS '是否发送短信（0：否  1：是）';




CREATE TABLE "myapp"."t_notice_opinion_history"
(
"id" BIGINT NOT NULL,
"notice_id" BIGINT,
"opinion" VARCHAR(255),
"audit_status" INT,
"create_by" BIGINT,
"create_name" VARCHAR(32),
"create_time" TIMESTAMP(0),
"update_by" BIGINT,
"update_name" VARCHAR(32),
"update_time" TIMESTAMP(0),
"department_id" BIGINT,
"company_id" BIGINT,
"tenant_id" BIGINT,
"deleted" INT,
NOT CLUSTER PRIMARY KEY("id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE "myapp"."t_notice_opinion_history" IS '公告意见审核表';COMMENT ON COLUMN "myapp"."t_notice_opinion_history"."id" IS '主键';
COMMENT ON COLUMN "myapp"."t_notice_opinion_history"."notice_id" IS '公告id';
COMMENT ON COLUMN "myapp"."t_notice_opinion_history"."opinion" IS '审批意见';
COMMENT ON COLUMN "myapp"."t_notice_opinion_history"."audit_status" IS '审核状态（2：审批不通过 3：审批通过 ）';
COMMENT ON COLUMN "myapp"."t_notice_opinion_history"."create_by" IS '创建人ID';
COMMENT ON COLUMN "myapp"."t_notice_opinion_history"."create_name" IS '创建人姓名';
COMMENT ON COLUMN "myapp"."t_notice_opinion_history"."create_time" IS '创建时间';
COMMENT ON COLUMN "myapp"."t_notice_opinion_history"."update_by" IS '修改人ID';
COMMENT ON COLUMN "myapp"."t_notice_opinion_history"."update_name" IS '修改人姓名';
COMMENT ON COLUMN "myapp"."t_notice_opinion_history"."update_time" IS '修改时间';
COMMENT ON COLUMN "myapp"."t_notice_opinion_history"."department_id" IS '部门ID';
COMMENT ON COLUMN "myapp"."t_notice_opinion_history"."company_id" IS '单位ID';
COMMENT ON COLUMN "myapp"."t_notice_opinion_history"."tenant_id" IS '租户ID';
COMMENT ON COLUMN "myapp"."t_notice_opinion_history"."deleted" IS '逻辑删除字段';




CREATE TABLE "myapp"."t_notice_type"
(
"ID" BIGINT NOT NULL,
"NAME_TYPE" VARCHAR(128),
"MESSAGE_AUDIT" INT,
"ALLOW_COMMENTS" VARCHAR(32),
"PRIORITY" INT,
"WATERMARK" INT,
"DESCRIBES" VARCHAR(550),
"STATE" INT,
"CREATE_TIME" TIMESTAMP(0),
"CREATE_BY" BIGINT,
"CREATE_NAME" VARCHAR(32),
"UPDATE_TIME" TIMESTAMP(0),
"UPDATE_BY" BIGINT,
"UPDATE_NAME" VARCHAR(32),
"DEPARTMENT_ID" BIGINT,
"COMPANY_ID" BIGINT,
"TENANT_ID" BIGINT,
"DELETED" BIGINT,
"REVIEWER_NAME" VARCHAR(50),
"REVIEW, ER_ID" BIGINT,
NOT CLUSTER PRIMARY KEY("ID")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE "myapp"."t_notice_type" IS '通知公告类型表 ';COMMENT ON COLUMN "myapp"."t_notice_type"."ID" IS '主键id';
COMMENT ON COLUMN "myapp"."t_notice_type"."NAME_TYPE" IS '类型名称';
COMMENT ON COLUMN "myapp"."t_notice_type"."MESSAGE_AUDIT" IS '消息审核 1:审核 0 ：不需审核';
COMMENT ON COLUMN "myapp"."t_notice_type"."ALLOW_COMMENTS" IS '是否允许评论 1：允许2：不允许';
COMMENT ON COLUMN "myapp"."t_notice_type"."PRIORITY" IS '优先级';
COMMENT ON COLUMN "myapp"."t_notice_type"."WATERMARK" IS '是否需要水印 1：是2：否';
COMMENT ON COLUMN "myapp"."t_notice_type"."DESCRIBES" IS '描述';
COMMENT ON COLUMN "myapp"."t_notice_type"."STATE" IS '状态是否有效 1：是2：否';
COMMENT ON COLUMN "myapp"."t_notice_type"."CREATE_TIME" IS '创建时间';
COMMENT ON COLUMN "myapp"."t_notice_type"."CREATE_BY" IS '创建人id';
COMMENT ON COLUMN "myapp"."t_notice_type"."CREATE_NAME" IS '创建人姓名';
COMMENT ON COLUMN "myapp"."t_notice_type"."UPDATE_TIME" IS '修改时间';
COMMENT ON COLUMN "myapp"."t_notice_type"."UPDATE_BY" IS '修改人id';
COMMENT ON COLUMN "myapp"."t_notice_type"."UPDATE_NAME" IS '更新人';
COMMENT ON COLUMN "myapp"."t_notice_type"."DEPARTMENT_ID" IS '部门id';
COMMENT ON COLUMN "myapp"."t_notice_type"."COMPANY_ID" IS '单位id';
COMMENT ON COLUMN "myapp"."t_notice_type"."TENANT_ID" IS '租户id';
COMMENT ON COLUMN "myapp"."t_notice_type"."DELETED" IS '逻辑删除 0：未删除 1：删除';
COMMENT ON COLUMN "myapp"."t_notice_type"."REVIEWER_NAME" IS '审核人名称';
COMMENT ON COLUMN "myapp"."t_notice_type"."REVIEWER_ID" IS '审核人id';




CREATE TABLE "myapp"."t_noticetype_user"
(
"ID" BIGINT NOT NULL,
"NOTICE_NOTICE_TYPE_ID" BIGINT,
"USER_ID" BIGINT,
"USER_NAME" VARCHAR(50),
"CREATE_TIME" TIMESTAMP(0),
"CREATE_BY" BIGINT,
"CREATE_NAME" VARCHAR(32),
"UPDATE_TIME" TIMESTAMP(0),
"UPDATE_BY" BIGINT,
"UPDATE_NAME" VARCHAR(32),
"DEPARTMENT_ID" BIGINT,
"COMPANY_ID" BIGINT,
"TENANT_ID" BIGINT,
"DELETED" BIGINT,
NOT CLUSTER PRIMARY KEY("ID")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE "myapp"."t_noticetype_user" IS '权限和用户中间表 ';COMMENT ON COLUMN "myapp"."t_noticetype_user"."ID" IS '主键id';
COMMENT ON COLUMN "myapp"."t_noticetype_user"."NOTICE_NOTICE_TYPE_ID" IS '类型id';
COMMENT ON COLUMN "myapp"."t_noticetype_user"."USER_ID" IS '用户id';
COMMENT ON COLUMN "myapp"."t_noticetype_user"."USER_NAME" IS '用户名称';
COMMENT ON COLUMN "myapp"."t_noticetype_user"."CREATE_TIME" IS '创建时间';
COMMENT ON COLUMN "myapp"."t_noticetype_user"."CREATE_BY" IS '创建人id';
COMMENT ON COLUMN "myapp"."t_noticetype_user"."CREATE_NAME" IS '创建人姓名';
COMMENT ON COLUMN "myapp"."t_noticetype_user"."UPDATE_TIME" IS '修改时间';
COMMENT ON COLUMN "myapp"."t_noticetype_user"."UPDATE_BY" IS '修改人id';
COMMENT ON COLUMN "myapp"."t_noticetype_user"."UPDATE_NAME" IS '更新人';
COMMENT ON COLUMN "myapp"."t_noticetype_user"."DEPARTMENT_ID" IS '部门id';
COMMENT ON COLUMN "myapp"."t_noticetype_user"."COMPANY_ID" IS '单位id';
COMMENT ON COLUMN "myapp"."t_noticetype_user"."TENANT_ID" IS '租户id';
COMMENT ON COLUMN "myapp"."t_noticetype_user"."DELETED" IS '逻辑删除 0：未删除 1：删除';




CREATE TABLE "myapp"."t_notice_user"
(
"ID" BIGINT NOT NULL,
"NOTICE_NOTICE_ID" BIGINT,
"USER_ID" BIGINT,
"IS_READING" INT DEFAULT 2,
"USER_NAME" VARCHAR(50),
"CREATE_TIME" TIMESTAMP(0),
"CREATE_BY" BIGINT,
"CREATE_NAME" VARCHAR(32),
"UPDATE_TIME" TIMESTAMP(0),
"UPDATE_BY" BIGINT,
"UPDATE_NAME" VARCHAR(32),
"DEPARTMENT_ID" BIGINT,
"COMPANY_ID" BIGINT,
"TENANT_ID" BIGINT,
"REPLY_CONTENT" VARCHAR(255),
"REPLY_TIME" TIMESTAMP(0),
"DELETED" BIGINT,
"branch_id" BIGINT,
"branch_nam, e" VARCHAR(100),
"unit_id" BIGINT,
"unit_name" VARCHAR(100),
"mobile" VARCHAR(20),
NOT CLUSTER PRIMARY KEY("ID")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE "myapp"."t_notice_user" IS '通知公告和用户表中间表 ';COMMENT ON COLUMN "myapp"."t_notice_user"."ID" IS '主键id';
COMMENT ON COLUMN "myapp"."t_notice_user"."NOTICE_NOTICE_ID" IS '通知公告id';
COMMENT ON COLUMN "myapp"."t_notice_user"."USER_ID" IS '用户id';
COMMENT ON COLUMN "myapp"."t_notice_user"."IS_READING" IS '是否已阅 1：已阅 0：没有阅览';
COMMENT ON COLUMN "myapp"."t_notice_user"."USER_NAME" IS '用户名称';
COMMENT ON COLUMN "myapp"."t_notice_user"."CREATE_TIME" IS '创建时间';
COMMENT ON COLUMN "myapp"."t_notice_user"."CREATE_BY" IS '创建人id';
COMMENT ON COLUMN "myapp"."t_notice_user"."CREATE_NAME" IS '创建人姓名';
COMMENT ON COLUMN "myapp"."t_notice_user"."UPDATE_TIME" IS '修改时间';
COMMENT ON COLUMN "myapp"."t_notice_user"."UPDATE_BY" IS '修改人id';
COMMENT ON COLUMN "myapp"."t_notice_user"."UPDATE_NAME" IS '更新人';
COMMENT ON COLUMN "myapp"."t_notice_user"."DEPARTMENT_ID" IS '部门id';
COMMENT ON COLUMN "myapp"."t_notice_user"."COMPANY_ID" IS '单位id';
COMMENT ON COLUMN "myapp"."t_notice_user"."TENANT_ID" IS '租户id';
COMMENT ON COLUMN "myapp"."t_notice_user"."REPLY_CONTENT" IS '回复内容';
COMMENT ON COLUMN "myapp"."t_notice_user"."REPLY_TIME" IS '回复时间';
COMMENT ON COLUMN "myapp"."t_notice_user"."DELETED" IS '逻辑删除 0：未删除 1：删除';
COMMENT ON COLUMN "myapp"."t_notice_user"."branch_id" IS '部门id';
COMMENT ON COLUMN "myapp"."t_notice_user"."branch_name" IS '部门名称';
COMMENT ON COLUMN "myapp"."t_notice_user"."unit_id" IS '单位id';
COMMENT ON COLUMN "myapp"."t_notice_user"."unit_name" IS '单位名称';
COMMENT ON COLUMN "myapp"."t_notice_user"."mobile" IS '电话号码';




CREATE TABLE "myapp"."t_text_data"
(
"ID" BIGINT NOT NULL,
"TITLE" VARCHAR(32),
"DATA" VARCHAR(1000),
"NOTICE_NOTICE_ID" BIGINT,
"CREATE_TIME" TIMESTAMP(0),
"CREATE_BY" BIGINT,
"CREATE_NAME" VARCHAR(32),
"UPDATE_TIME" TIMESTAMP(0),
"UPDATE_BY" BIGINT,
"UPDATE_NAME" VARCHAR(32),
"DEPARTMENT_ID" BIGINT,
"COMPANY_ID" BIGINT,
"TENANT_ID" BIGINT,
"DELETED" BIGINT,
NOT CLUSTER PRIMARY KEY("ID")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE "myapp"."t_text_data" IS '通知公告的正文数据 ';COMMENT ON COLUMN "myapp"."t_text_data"."ID" IS '主键id';
COMMENT ON COLUMN "myapp"."t_text_data"."TITLE" IS '标题';
COMMENT ON COLUMN "myapp"."t_text_data"."DATA" IS '内容';
COMMENT ON COLUMN "myapp"."t_text_data"."NOTICE_NOTICE_ID" IS '通知公告id';
COMMENT ON COLUMN "myapp"."t_text_data"."CREATE_TIME" IS '创建时间';
COMMENT ON COLUMN "myapp"."t_text_data"."CREATE_BY" IS '创建人id';
COMMENT ON COLUMN "myapp"."t_text_data"."CREATE_NAME" IS '创建人姓名';
COMMENT ON COLUMN "myapp"."t_text_data"."UPDATE_TIME" IS '修改时间';
COMMENT ON COLUMN "myapp"."t_text_data"."UPDATE_BY" IS '修改人id';
COMMENT ON COLUMN "myapp"."t_text_data"."UPDATE_NAME" IS '更新人';
COMMENT ON COLUMN "myapp"."t_text_data"."DEPARTMENT_ID" IS '部门id';
COMMENT ON COLUMN "myapp"."t_text_data"."COMPANY_ID" IS '单位id';
COMMENT ON COLUMN "myapp"."t_text_data"."TENANT_ID" IS '租户id';
COMMENT ON COLUMN "myapp"."t_text_data"."DELETED" IS '逻辑删除 0：未删除 1：删除';




