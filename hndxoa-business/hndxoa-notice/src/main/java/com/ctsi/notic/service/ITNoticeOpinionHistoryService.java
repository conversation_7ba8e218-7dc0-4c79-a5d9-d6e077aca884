package com.ctsi.notic.service;

import com.ctsi.notic.entity.dto.TNoticeOpinionHistoryDTO;
import com.ctsi.notic.entity.TNoticeOpinionHistory;
import com.ctsi.hndx.common.SysBaseServiceI;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.ssdc.model.PageResult;
import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-31
 */
public interface ITNoticeOpinionHistoryService extends SysBaseServiceI<TNoticeOpinionHistory> {


    /**
     * 分页查询
     */
    PageResult<TNoticeOpinionHistoryDTO> queryListPage(TNoticeOpinionHistoryDTO entityDTO, BasePageForm page);

    /**
     * 获取所有不分页
     */
    List<TNoticeOpinionHistoryDTO> queryList(TNoticeOpinionHistoryDTO entity);

    /**
     * 根据主键id获取单个对象
     */
    TNoticeOpinionHistoryDTO findOne(Long id);

    /**
     * 新增
     */
    TNoticeOpinionHistoryDTO create(TNoticeOpinionHistoryDTO entity);


    /**
     * 更新
     */
    int update(TNoticeOpinionHistoryDTO entity);

    /**
     * 删除
     */
    int delete(Long id);

     /**
     * 是否存在
     * existByTNoticeOpinionHistoryId
     */
    boolean existByTNoticeOpinionHistoryId(Long code);

    /**
    * 批量新增
    * create batch
    */
    Boolean insertBatch(List<TNoticeOpinionHistoryDTO> dataList);


}
