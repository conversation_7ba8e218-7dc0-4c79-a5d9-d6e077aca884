package com.ctsi.notic.entity;

import com.ctsi.hndx.common.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-31
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "TNoticeOpinionHistory对象", description = "")
public class TNoticeOpinionHistory extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 公告id
     */
    @ApiModelProperty(value = "公告id")
    private Long noticeId;

    /**
     * 审批意见
     */
    @ApiModelProperty(value = "审批意见")
    private String opinion;

    /**
     * 审核状态（2：审批不通过 3：审批通过 ）
     */
    @ApiModelProperty(value = "审核状态（2：审批不通过 3：审批通过 ）", required = true)
    @NotNull(message = "审核状态不能为空")
    private Integer auditStatus;
}
