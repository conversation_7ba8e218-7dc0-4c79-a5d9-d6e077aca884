package com.ctsi.notic.entity.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.ctsi.hndx.common.BaseDtoEntity;
import com.ctsi.operation.domain.CscpDocumentFile;
import com.ctsi.operation.domain.CscpEnclosureFile;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;
import java.util.List;


@Data
@EqualsAndHashCode(callSuper = true)
public class TNoticeDTO extends BaseDtoEntity {

    /**
     * 标题
     */
    @ApiModelProperty(value = "标题")
    private String title;

    /**
     * 概要
     */
    @ApiModelProperty(value = "概要")
    private String outline;

    /**
     * 通知公告类型
     */
    @ApiModelProperty(value = "通知公告类型id")
    private Long notificationTypeId;

    /**
     * 通知公告类型名称
     */
    @ApiModelProperty(value = "通知公告类型名称")
    @TableField("NOTIFICATION_TYPE_NAME")
    private String notificationTypeName;

    /**
     * 正文数据
     */
    @ApiModelProperty(value = "正文数据")
    private TTextDataDTO tTextDataDTO;


    /**
     * 发布范围
     */
    @ApiModelProperty(value = "发布范围")
    private List<TNoticeUserDTO> TNoticeUserDTO;

    /**
     * 通知公告状态
     */
    @ApiModelProperty(value = "通知公告状态")
    private Integer noticeState;

    /**
     * 业务id
     */
    @ApiModelProperty(value = "业务id")
    private Long formDataId;


    /**
     * 来源单位名称
     */
    @ApiModelProperty(value = "来源单位名称")
    private String sourceUnitName;


    /**
     * 来源单位id
     */
    @ApiModelProperty(value = "来源单位id")
    private String sourceUnitId;


    /**
     * 部门id
     */
    @ApiModelProperty(value = "部门id")
    private Long branchId;


    /**
     * 部门名称
     */
    @ApiModelProperty(value = "部门名称")
    private String branchName;


    /**
     * 单位id
     */
    @ApiModelProperty(value = "单位id")
    private Long unitId;


    /**
     * 单位名称
     */
    @ApiModelProperty(value = "单位名称")
    private String unitName;


    /**
     * 点击量
     */
    @ApiModelProperty(value = "点击量")
    private Integer hits;


    /**
     * 附件文件
     */
    @ApiModelProperty(value = "附件文件")
    private List<CscpEnclosureFile> cscpEnclosureFileList;

    /**
     * 已阅回复内容
     */
    @ApiModelProperty(value = "已阅回复内容")
    private String replyContent;

    /**
     * 回复的附件文件
     */
    @ApiModelProperty(value = "回复的附件文件")
    private List<CscpEnclosureFile> replyEnclosureFileList;


    /**
     * 正文数据
     */
    @ApiModelProperty(value = "正文数据")
    private List<CscpDocumentFile> cscpDocumentFiles;


    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String createName;


    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    /**
     * 选人方式（0：本单位 1：自定义 2：外单位）
     */
    @ApiModelProperty(value = "选人方式（0：本单位 1：自定义 2：外单位）")
    private Integer selectionMethod;


    /**
     * 是否发送短信（0：否  1：是）
     */
    @ApiModelProperty(value = "是否发送短信（0：否  1：是）")
    private Integer smsReminder;


    /**
     * 送达人数
     */
    @ApiModelProperty(value = "送达人数")
    private Integer countSend;

    /**
     * 已打开人数
     */
    @ApiModelProperty(value = "已打开人数")
    private Integer countOpen;

    /**
     * 未打开人数
     */
    @ApiModelProperty(value = "未打开人数")
    private Integer countNotOpen;

    /**
     * 未确认人数
     */
    @ApiModelProperty(value = "未确认人数")
    private Integer countNotConfirm;

    /**
     * 已确认人数
     */
    @ApiModelProperty(value = "已确认人数")
    private Integer countConfirm;

    /**
     * 密级期限
     */
    @ApiModelProperty(value = "密级期限")
    private String secretLevel;

    /**
     * 密级期限
     */
    @ApiModelProperty(value = "密级期限")
    private String secretLevelName;

    @ApiModelProperty(value = "打印/下载:  0-禁止打印和下载 1-能打印和下载")
    private String isPrint;

    @ApiModelProperty(value = "1 已经关注, null 没关注")
    private String focus;
}
