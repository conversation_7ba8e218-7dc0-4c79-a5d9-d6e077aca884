package com.ctsi.notic.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.notic.entity.TNoticeType;
import com.ctsi.notic.entity.dto.TNoticeTypeDTO;
import com.ctsi.ssdc.model.PageResult;

import java.util.List;

/**
 * <p>
 * 通知公告类型表  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-26
 */
public interface ITNoticeTypeService extends IService<TNoticeType> {


    /**
     * 分页查询
     */
    IPage<TNoticeType> pageTzggNoticeNoticeType(TNoticeType entity, BasePageForm page);

    /**
     * 获取所有不分页
     */
    PageResult<TNoticeTypeDTO> findList(BasePageForm basePageForm);

    /**
     * 根据主键id获取单个对象
     */
    TNoticeType findOne(Long id);

    /**
     * 新增
     */
    Boolean create(TNoticeTypeDTO tzggNoticeNoticeType);


    /**
     * 更新
     */
    int update( TNoticeTypeDTO tzggNoticeNoticeType);

    /**
     * 删除
     */
    int delete(Long id);

     /**
     * 是否存在
     * existByTzggNoticeNoticeTypeId
     */
    boolean existByTzggNoticeNoticeTypeId(Long code);

    /**
    * 批量新增
    * create batch
    */
    Boolean insertBatch(List<TNoticeType> dataList);


    List<TNoticeType> userNoticeType(long uid);
}
