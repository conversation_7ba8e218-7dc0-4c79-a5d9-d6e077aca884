package com.ctsi.notic.controller;

import com.ctsi.hndx.common.BaseController;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.enums.DBOperation;
import com.ctsi.hndx.result.ResultCode;
import com.ctsi.hndx.result.ResultVO;
import com.ctsi.notic.entity.TNotice;
import com.ctsi.notic.entity.dto.*;
import com.ctsi.notic.service.ITNoticeService;
import com.ctsi.sms.smssend.SmsSendEnum;
import com.ctsi.sms.smssend.SmsSendUtil;
import com.ctsi.sms.syssms.entity.dto.TSendSmsMoileDto;
import com.ctsi.ssdc.annotation.OperationLog;
import com.ctsi.ssdc.model.PageResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-23
 */

@Slf4j
@RestController
@RequestMapping("/api/tzggNotice")
@Api(value = "通知公告 ", tags = "通知公告 接口")
public class TNoticeController extends BaseController {

    private static final String ENTITY_NAME = "tzggNotice";

    @Autowired
    private ITNoticeService tzggNoticeService;


    /**
     * 新增通知公告数据
     */
    @PostMapping("/create")
    @OperationLog(dBOperation = DBOperation.ADD, message = "新增通知公告数据")
    @ApiOperation(value = "新增通知公告数据", notes = "传入参数")
    public ResultVO create(@RequestBody TNoticeDTO tNoticeDTO) {
        boolean create = tzggNoticeService.create(tNoticeDTO);
        if (create) {
            return ResultVO.success(tNoticeDTO);
        }
        return ResultVO.error("新增失败");
    }

    /**
     * 修改通知公告数据.
     */
    @PutMapping("/update")
    @OperationLog(dBOperation = DBOperation.UPDATE, message = "修改通知公告数据")
    @ApiOperation(value = "修改通知公告数据", notes = "传入参数")
    public ResultVO update(@RequestBody TNoticeDTO tNoticeDTO) {
        Assert.notNull(tNoticeDTO.getId(), "general.IdNotNull");
        int count = tzggNoticeService.update(tNoticeDTO);
        if (count > 0) {
            return ResultVO.success(tNoticeDTO);
        } else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }

    /**
     * 删除指定通知公告数据.
     */
    @DeleteMapping("/delete/{id}")
    @OperationLog(dBOperation = DBOperation.DELETE, message = "删除指定通知公告数据")
    @ApiOperation(value = "删除指定通知公告数据", notes = "传入参数")
    public ResultVO delete(@PathVariable Long id) {
        log.debug("REST request to delete tzggNotice : {}", id);
        int count = tzggNoticeService.delete(id);
        if (count > 0) {
            return ResultVO.success();
        } else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }

    /**
     * 查询指定通知公告基本信息
     */
    @GetMapping("/get/{id}")
    @ApiOperation(value = "查询指定通知公告基本信息", notes = "传入参数")
    @ApiImplicitParams({
            @ApiImplicitParam(name="id",value="通知id",required=true),
            @ApiImplicitParam(name="ownPost",value="2-已确认，1-我发起的，0-未确认",required=false)
    })
    public ResultVO<TNoticeDTO> get(@PathVariable Long id, @RequestParam(required = false) Integer ownPost) {
        return ResultVO.success(tzggNoticeService.findOne(id, ownPost));
    }


    /**
     * 查询指定公告(公告送达多少人 已阅多少人 多少人未阅)
     */
    @GetMapping("/details")
    @ApiOperation(value = "查询指定公告(公告送达多少人 已阅多少人 多少人未阅)", notes = "传入参数")
    public ResultVO<TNoticeCountDTO> details(DetailsDTO detailsDTO, BasePageForm basePageForm) {
        return ResultVO.success(tzggNoticeService.details(detailsDTO, basePageForm));
    }

    /**
     * 查询指定公告的意见历史
     */
    @GetMapping("/opinionHistory/{id}")
    @ApiOperation(value = "查询指定公告的意见历史", notes = "传入参数")
    public ResultVO<List<TNoticeOpinionHistoryDTO>> opinionHistory(@PathVariable Long id) {
        List<TNoticeOpinionHistoryDTO> historyList = tzggNoticeService.opinionHistory(id);
        return ResultVO.success(historyList);
    }

    /**
     * 查询该呈批件转发通知公告历史接收人
     * */
    @GetMapping("/getNoticeDataByBizId/{id}")
    @ApiOperation(value = "查询该呈批件转发通知公告历史接收人", notes = "id 业务id")
    public ResultVO<ResDetailsDTO> getNoticeDataByBizId(@PathVariable Long id) {
        Assert.notNull(id, "general.IdNotNull");
        ResDetailsDTO data = tzggNoticeService.getNoticeDataByBizId(id);
        return ResultVO.success(data);

    }

    /**
     * 查询指定通知公告数据
     */
    @GetMapping("/getTNotice/{id}")
    @ApiOperation(value = "查询指定通知公告数据", notes = "传入参数")
    public ResultVO<ResDetailsDTO> getTNotice(@PathVariable Long id) {
        ResDetailsDTO tNotice = tzggNoticeService.getTNotice(id);
        return ResultVO.success(tNotice);
    }


    /**
     * 查询多条数据.不分页
     */
    @GetMapping("/list")
    @ApiOperation(value = "查询多条数据", notes = "传入参数")
    public ResultVO list(TNotice tNotice) {
        log.debug("REST request to get tzggNotice list: {}", tNotice);
        List<TNotice> list = tzggNoticeService.findList(tNotice);
        return ResultVO.success(list);
    }

    /**
     * 查询用户是否打开过当前通知公告, 0-未打开过，1-打开过
     */
    @GetMapping("/getNoticeUserStatus")
    @ApiOperation(value = "查询用户是否打开过当前通知公告, 0-未打开过，1-打开过", notes = "传入参数")
    public ResultVO getNoticeUserStatus(Long noticeId, Long userId) {
        return ResultVO.success(tzggNoticeService.getNoticeUserStatus(noticeId, userId));
    }


    /**
     * 动态条件查询通知公告数据分页(已收)
     *
     * @param queryTzggNoticeDTO
     * @param basePageForm
     * @return
     */
    @GetMapping("/querycondition")
    @ApiOperation(value = "动态条件查询通知公告数据分页(已收)", notes = "传入参数")
    public ResultVO<PageResult<ResTnoticeDTO>> queryCondition(QueryTzggNoticeDTO queryTzggNoticeDTO, BasePageForm basePageForm) {
        PageResult<ResTnoticeDTO> querycondition = tzggNoticeService.queryCondition(queryTzggNoticeDTO, basePageForm);
        return ResultVO.success(querycondition);
    }


    /**
     * 查询指定用户待审批的公告
     *
     * @param queryTzggNoticeDTO
     * @param basePageForm
     * @return
     */
    @GetMapping("/queryToExamine")
    @ApiOperation(value = "查询指定用户待审批的公告", notes = "传入参数")
    public ResultVO<PageResult<ResTnoticeDTO>> queryToExamine(QueryTzggNoticeDTO queryTzggNoticeDTO, BasePageForm basePageForm) {
        PageResult<ResTnoticeDTO> resTnoticeDTOPageResult = tzggNoticeService.queryToExamine(queryTzggNoticeDTO, basePageForm);
        return ResultVO.success(resTnoticeDTOPageResult);
    }
    /**
     * 查询指定用户待审批的公告数量
     *
     * @param queryTzggNoticeDTO
     * @return
     */
    @GetMapping("/queryToExamineCount")
    @ApiOperation(value = "查询指定用户待审批的公告数量", notes = "传入参数")
    public ResultVO<Integer> queryToExamineCount(QueryTzggNoticeDTO queryTzggNoticeDTO) {
        Integer count = tzggNoticeService.queryToExamineCount(queryTzggNoticeDTO);
        return ResultVO.success(count);
    }

    /**
     * 审核通知公告
     *
     * @param toExamineDTO
     * @return
     */
    @PostMapping("/queryToExamine")
    @OperationLog(dBOperation = DBOperation.UPDATE, message = "审核通知公告")
    @ApiOperation(value = "审核通知公告", notes = "传入参数")
    public ResultVO toExamine(@RequestBody ToExamineDTO toExamineDTO) {
        Integer count = tzggNoticeService.toExamine(toExamineDTO);
        if (count > 0) {
            return ResultVO.success();
        } else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }


    /**
     * 获取自己发布的通知公告数据分页
     *
     * @param queryTzggNoticeDTO
     * @param basePageForm
     * @return
     */
    @GetMapping("/getNoticeList")
    @ApiOperation(value = "获取自己发布的通知公告数据分页")
    public ResultVO<PageResult<ResTnoticeDTO>> getNoticeList(QueryTzggNoticeDTO queryTzggNoticeDTO, BasePageForm basePageForm) {
        return ResultVO.success(tzggNoticeService.getNoticeList(queryTzggNoticeDTO, basePageForm));
    }

    /**
     *
     *  三服务首页-通知公告消息列表
     *
     * @param strId
     * @param basePageForm
     * @return
     */
    @GetMapping("/getSFWNoticePage/{strId}")
    @ApiOperation(value = "三服务首页-通知公告消息列表")
    public ResultVO<PageResult<ResTnoticeDTO>> getSFWNoticePage(@PathVariable("strId") String strId,  BasePageForm basePageForm) {
        return ResultVO.success(tzggNoticeService.getSFWNoticePage(strId,  basePageForm));
    }


    /**
     * 管理员获取全部的通知公告数据
     *
     * @param queryTzggNoticeDTO
     * @param basePageForm
     * @return
     */
    @GetMapping("/getAdminNoticeList")
    @ApiOperation(value = "管理员获取全部的通知公告数据")
    public ResultVO<PageResult<ResTnoticeDTO>> getAdminNoticeList(QueryTzggNoticeDTO queryTzggNoticeDTO, BasePageForm basePageForm) {
        return ResultVO.success(tzggNoticeService.admingetNoticeList(queryTzggNoticeDTO, basePageForm));
    }


    /**
     * 通知公告确认已阅
     *
     * @param readReplyDTO
     * @return
     */
    @PostMapping("/confirRead")
    @OperationLog(dBOperation = DBOperation.UPDATE, message = "通知公告确认已阅")
    @ApiOperation(value = "通知公告确认已阅")
    public ResultVO<PageResult<ResTnoticeDTO>> confirRead(@RequestBody ReadReplyDTO readReplyDTO) {
        Assert.notNull(readReplyDTO.getId(), "general.IdNotNull");
        Integer count = tzggNoticeService.confirRead(readReplyDTO);
        if (count > 0) {
            return ResultVO.success();
        } else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }


    /**
     * app查询角标（未看公告）
     *
     * @return
     */
    @GetMapping("/getAngleMark")
    @ApiOperation(value = "app查询角标（未看公告）")
    public ResultVO<Integer> getAngleMark() {
        return ResultVO.success(tzggNoticeService.getAngleMark());
    }

    @PostMapping("/sendSmsByPhone")
    @ApiOperation(value = "根据手机号码和内容发送短信")
    public ResultVO sendSmsByPhone(@Validated @RequestBody TSendSmsMoileDto tSendSmsDto) {
        SmsSendUtil.sendSms(tSendSmsDto.getReceivePhone(), tSendSmsDto.getTitile(), SmsSendEnum.URGE_SMS);
        return ResultVO.success(true);
    }

    /**
     * 查询最近当前用户最近发送的10个人
     */
    @GetMapping("/getRecentPeople")
    @ApiOperation(value = "查询最近当前用户最近发送的10个人", notes = "无传入参数")
    public ResultVO get() {
        return ResultVO.success(tzggNoticeService.getRecentPeople());
    }
}
