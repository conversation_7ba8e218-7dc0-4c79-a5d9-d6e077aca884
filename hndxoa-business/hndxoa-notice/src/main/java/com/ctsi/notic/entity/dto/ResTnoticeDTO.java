package com.ctsi.notic.entity.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
public class ResTnoticeDTO {

    /**
     * id
     */
    @ApiModelProperty(value = "通知公告id")
    private Long id;

    /**
     * 标题
     */
    @ApiModelProperty(value = "标题")
    @TableField("TITLE")
    private String title;

    /**
     * 通知公告类型名称
     */
    @ApiModelProperty(value = "通知公告类型名称")
    private String notificationTypeName;

    /**
     * 创建人名称
     */
    @ApiModelProperty(value = "创建人名称")
    private String createName;

    /**
     * 创建人日期
     */
    @ApiModelProperty(value = "创建人日期")
    private LocalDateTime createTime;

    /**
     * 通知公告审批状态
     */
    @ApiModelProperty(value = "通知公告审批状态")
    private Integer noticeState;

    /**
     * 是否预览
     */
    @ApiModelProperty(value = "是否预览")
    private Integer isReading;

    /**
     * 是否打开 0:未打开 1:已打开
     */
    @ApiModelProperty(value = "是否打开 0:未打开 1:已打开")
    private Integer signInStatus;

    /**
     * 来源单位名称
     */
    @ApiModelProperty(value = "来源单位名称")
    private String sourceUnitName;
    /**
     * 来源部门名称
     */
    @ApiModelProperty(value = "来源部门名称")
    private String sourceDepartmentName;


    /**
     * 来源单位id
     */
    @ApiModelProperty(value = "来源单位id")
    private Long sourceUnitId;

    /**
     * 来源单位id
     */
    @ApiModelProperty(value = "子表id")
    private Long tunid;

    /**
     * 来源单位id
     */
    @ApiModelProperty(value = "回复时间")
    private LocalDateTime replyTime;

    /**
     * 接收通知的所有人
     */
    @ApiModelProperty(value = "接收通知的所有人")
    private List<String> noticeUserNameList;

    /**
     * 关注  false 没有关注
     */
    @ApiModelProperty(value = "关注 ;false 没有关注; true 已关注")
    private Boolean isFocus =false;


    /**
     * 密级期限
     */
    @ApiModelProperty(value = "密级期限")
    private String secretLevel;
    /**
     * 密级期限
     */
    @ApiModelProperty(value = "密级期限")
    private String secretLevelName;

}
