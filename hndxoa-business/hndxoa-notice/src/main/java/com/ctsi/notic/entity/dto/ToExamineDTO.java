package com.ctsi.notic.entity.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Null;

/**
 * <AUTHOR>
 * @Classname ToExamineDTO
 * @Description
 * @Date 2021/12/31 14:53
 */
@Data
@ApiModel(value = "公告审核", description = "通知公告 ")
public class ToExamineDTO {

    /**
     * 公告id
     */
    @ApiModelProperty(value = "公告id", required = true)
    @NotNull(message = "公告id不能为空")
    private Long id;


    /**
     * 是否审核通过
     */
    @ApiModelProperty(value = "是否审核通过（2：审批不通过 3：审批通过 ）", required = true)
    @NotNull(message = ("是否审核通过不能为空"))
    private Integer isAdopt;


    /**
     * 审核意见
     */
    @ApiModelProperty(value = "审核意见", required = true)
    @NotNull(message = "审核意见不能为空")
    private String reviewComments;


}
