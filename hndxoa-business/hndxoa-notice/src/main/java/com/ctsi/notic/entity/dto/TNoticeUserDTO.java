package com.ctsi.notic.entity.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.ctsi.operation.domain.CscpEnclosureFile;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

@Data
public class TNoticeUserDTO implements Serializable {

    /**
     * 用户接收通知公告id
     */
    @ApiModelProperty(value = "用户接收通知公告id")
    @TableField("ID")
    private Long id;

    /**
     * 通知公告id
     */
    @ApiModelProperty(value = "通知公告id")
    @TableField("NOTICE_NOTICE_ID")
    private Long noticeNoticeId;

    /**
     * 用户id
     */
    @ApiModelProperty(value = "用户id")
    private Long userId;

    /**
     * 是否已阅 1：已阅 2：没有阅览
     */
    @ApiModelProperty(value = "是否已阅 1：已阅 2：没有阅览")
    @TableField("IS_READING")
    private Integer isReading;

    /**
     * 用户名称
     */
    @ApiModelProperty(value = "用户名称")
    private String userName;

    /**
     * 回复内容
     */
    @ApiModelProperty(value = "回复内容")
    private String replyContent;

    /**
     * 回复时间
     */
    @ApiModelProperty(value = "回复时间")
    private LocalDateTime replyTime;


    /**
     * 首次打开时间
     */
    @ApiModelProperty(value = "首次打开时间")
    private LocalDateTime firstOpenTime;


    /**
     * 部门id
     */
    @ApiModelProperty(value = "部门id")
    private Long branchId;


    /**
     * 部门名称
     */
    @ApiModelProperty(value = "部门名称")
    private String branchName;


    /**
     * 单位id
     */
    @ApiModelProperty(value = "单位id")
    private Long unitId;


    /**
     * 单位名称
     */
    @ApiModelProperty(value = "单位名称")
    private String unitName;


    /**
     * 电话号码
     */
    @ApiModelProperty(value = "电话号码")
    private String mobile;


    /**
     * 回复的附件文件
     */
    @ApiModelProperty(value = "回复的附件文件")
    private List<CscpEnclosureFile> replyEnclosureFileList;
}
