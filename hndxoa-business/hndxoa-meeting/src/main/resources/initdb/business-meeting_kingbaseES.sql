CREATE TABLE "public"."biz_meeting_summary" (
                                                "id" int8 NOT NULL,
                                                "title" varchar(128 char) NULL,
	"create_by" int8 NULL,
	"create_name" varchar(32 char) NULL,
	"department_id" int8 NULL,
	"department_name" varchar(32 char) NULL,
	"mobile" varchar(32 char) NULL,
	"create_time" timestamp(6) NULL,
	"document" varchar(1 char) NULL DEFAULT '0'::varchar,
	"annex" varchar(1 char) NULL DEFAULT '0'::varchar,
	"deleted" int4 NULL,
	"update_by" int8 NULL,
	"update_name" varchar(32 char) NULL,
	"update_time" timestamp(6) NULL,
	"tenant_id" int8 NULL,
	"process_instance_id" int8 NULL,
	"bpm_status" int4 NULL,
	"company_id" int8 NULL,
	"company_name" varchar(100 char) NULL,
	"reference_number" varchar(100 char) NULL,
	"urgency" varchar(255 char) NULL,
	"meet_notic" varchar(255 char) NULL,
	"meet_start_time" varchar(20 char) NULL,
	"meet_end_time" varchar(20 char) NULL,
	CONSTRAINT "biz_meeting_summary_PRIMARY" PRIMARY KEY ("id") ENABLE VALIDATE
);
COMMENT ON COLUMN "public"."biz_meeting_summary"."meet_end_time" IS '会议结束时间';
COMMENT ON COLUMN "public"."biz_meeting_summary"."meet_start_time" IS '会议开始时间';
COMMENT ON COLUMN "public"."biz_meeting_summary"."meet_notic" IS '会议通知';
COMMENT ON COLUMN "public"."biz_meeting_summary"."urgency" IS '缓急';
COMMENT ON COLUMN "public"."biz_meeting_summary"."reference_number" IS '文号';
COMMENT ON COLUMN "public"."biz_meeting_summary"."company_name" IS '拟稿单位名称';
COMMENT ON COLUMN "public"."biz_meeting_summary"."company_id" IS '单位ID';
COMMENT ON COLUMN "public"."biz_meeting_summary"."bpm_status" IS '流程状态';
COMMENT ON COLUMN "public"."biz_meeting_summary"."process_instance_id" IS '流程实例';
COMMENT ON COLUMN "public"."biz_meeting_summary"."tenant_id" IS '租户ID';
COMMENT ON COLUMN "public"."biz_meeting_summary"."update_time" IS '修改时间';
COMMENT ON COLUMN "public"."biz_meeting_summary"."update_name" IS '修改用户姓名';
COMMENT ON COLUMN "public"."biz_meeting_summary"."update_by" IS '修改用户ID';
COMMENT ON COLUMN "public"."biz_meeting_summary"."deleted" IS '逻辑删除 0：未删除 1：删除';
COMMENT ON COLUMN "public"."biz_meeting_summary"."annex" IS '是否有附件';
COMMENT ON COLUMN "public"."biz_meeting_summary"."document" IS '是否有正文';
COMMENT ON COLUMN "public"."biz_meeting_summary"."create_time" IS '创建日期';
COMMENT ON COLUMN "public"."biz_meeting_summary"."mobile" IS '联系电话';
COMMENT ON COLUMN "public"."biz_meeting_summary"."department_name" IS '部门名称';
COMMENT ON COLUMN "public"."biz_meeting_summary"."department_id" IS '部门ID';
COMMENT ON COLUMN "public"."biz_meeting_summary"."create_name" IS '拟稿人名称';
COMMENT ON COLUMN "public"."biz_meeting_summary"."create_by" IS '拟稿人ID';
COMMENT ON COLUMN "public"."biz_meeting_summary"."title" IS '会议标题';
COMMENT ON COLUMN "public"."biz_meeting_summary"."id" IS '主键ID';
COMMENT ON TABLE "public"."biz_meeting_summary" IS '会议纪要';


CREATE TABLE "public"."t_conference_room" (
                                              "id" int8 NOT NULL,
                                              "conference_room_name" varchar(100 char) NULL,
	"conference_room_describe" varchar(200 char) NULL,
	"administrators_id" int8 NULL,
	"administrators_name" varchar(100 char) NULL,
	"contact_number" character(12 char) NULL,
	"use_org_id" int8 NULL,
	"use_org_name" varchar(100 char) NULL,
	"conference_room_equipment" varchar(255 char) NULL,
	"create_by" int8 NULL,
	"create_name" varchar(32 char) NULL,
	"create_time" timestamp(6) NULL,
	"update_by" int8 NULL,
	"update_name" varchar(32 char) NULL,
	"update_time" timestamp(6) NULL,
	"department_id" int8 NULL,
	"company_id" int8 NULL,
	"tenant_id" int8 NULL,
	"deleted" int4 NULL,
	"sort" int4 NULL,
	CONSTRAINT "t_conference_room_PRIMARY" PRIMARY KEY ("id") ENABLE VALIDATE
);
COMMENT ON COLUMN "public"."t_conference_room"."sort" IS '排序';
COMMENT ON COLUMN "public"."t_conference_room"."deleted" IS '逻辑删除字段';
COMMENT ON COLUMN "public"."t_conference_room"."tenant_id" IS '租户ID';
COMMENT ON COLUMN "public"."t_conference_room"."company_id" IS '单位ID';
COMMENT ON COLUMN "public"."t_conference_room"."department_id" IS '部门ID';
COMMENT ON COLUMN "public"."t_conference_room"."update_time" IS '修改时间';
COMMENT ON COLUMN "public"."t_conference_room"."update_name" IS '修改人姓名';
COMMENT ON COLUMN "public"."t_conference_room"."update_by" IS '修改人ID';
COMMENT ON COLUMN "public"."t_conference_room"."create_time" IS '创建时间';
COMMENT ON COLUMN "public"."t_conference_room"."create_name" IS '创建人姓名';
COMMENT ON COLUMN "public"."t_conference_room"."create_by" IS '创建人ID';
COMMENT ON COLUMN "public"."t_conference_room"."conference_room_equipment" IS '会议室设备';
COMMENT ON COLUMN "public"."t_conference_room"."use_org_name" IS '使用部门名称';
COMMENT ON COLUMN "public"."t_conference_room"."use_org_id" IS '使用部门id';
COMMENT ON COLUMN "public"."t_conference_room"."contact_number" IS '负责人手机号码';
COMMENT ON COLUMN "public"."t_conference_room"."administrators_name" IS '会议室负责人';
COMMENT ON COLUMN "public"."t_conference_room"."administrators_id" IS '会议室负责人id';
COMMENT ON COLUMN "public"."t_conference_room"."conference_room_describe" IS '会议室描述';
COMMENT ON COLUMN "public"."t_conference_room"."conference_room_name" IS '会议室名称';
COMMENT ON COLUMN "public"."t_conference_room"."id" IS '主键';
COMMENT ON TABLE "public"."t_conference_room" IS '会议室管理接口';


CREATE TABLE "public"."t_meeting_agenda" (
                                             "id" int8 NOT NULL,
                                             "title" varchar(128 char) NULL,
	"create_by" int8 NULL,
	"create_name" varchar(32 char) NULL,
	"department_id" int8 NULL,
	"department_name" varchar(32 char) NULL,
	"mobile" varchar(32 char) NULL,
	"create_time" timestamp(6) NULL,
	"document" varchar(1 char) NULL DEFAULT '0'::varchar,
	"deleted" int4 NULL,
	"update_by" int8 NULL,
	"update_name" varchar(32 char) NULL,
	"update_time" timestamp(6) NULL,
	"tenant_id" int8 NULL,
	"company_id" int8 NULL,
	"company_name" varchar(32 char) NULL,
	"notic_choose" int4 NULL DEFAULT 0,
	"topics" varchar(1000 char) NULL,
	"meeting_date" date NULL,
	CONSTRAINT "t_meeting_agenda_PRIMARY" PRIMARY KEY ("id") ENABLE VALIDATE
);
COMMENT ON COLUMN "public"."t_meeting_agenda"."meeting_date" IS '会议时间';
COMMENT ON COLUMN "public"."t_meeting_agenda"."topics" IS '所有议题对象组';
COMMENT ON COLUMN "public"."t_meeting_agenda"."notic_choose" IS '是否会议通知选择1表示选择，其他不选择';
COMMENT ON COLUMN "public"."t_meeting_agenda"."company_name" IS '拟稿单位名称';
COMMENT ON COLUMN "public"."t_meeting_agenda"."company_id" IS '单位ID';
COMMENT ON COLUMN "public"."t_meeting_agenda"."tenant_id" IS '租户ID';
COMMENT ON COLUMN "public"."t_meeting_agenda"."update_time" IS '修改时间';
COMMENT ON COLUMN "public"."t_meeting_agenda"."update_name" IS '修改用户姓名';
COMMENT ON COLUMN "public"."t_meeting_agenda"."update_by" IS '修改用户ID';
COMMENT ON COLUMN "public"."t_meeting_agenda"."deleted" IS '逻辑删除 0：未删除 1：删除';
COMMENT ON COLUMN "public"."t_meeting_agenda"."document" IS '是否有正文';
COMMENT ON COLUMN "public"."t_meeting_agenda"."create_time" IS '创建日期';
COMMENT ON COLUMN "public"."t_meeting_agenda"."mobile" IS '联系电话';
COMMENT ON COLUMN "public"."t_meeting_agenda"."department_name" IS '汇报部门名称';
COMMENT ON COLUMN "public"."t_meeting_agenda"."department_id" IS '部门ID';
COMMENT ON COLUMN "public"."t_meeting_agenda"."create_name" IS '拟稿人汇报人名称';
COMMENT ON COLUMN "public"."t_meeting_agenda"."create_by" IS '拟稿人ID';
COMMENT ON COLUMN "public"."t_meeting_agenda"."title" IS '议程标题';
COMMENT ON COLUMN "public"."t_meeting_agenda"."id" IS '主键ID';
COMMENT ON TABLE "public"."t_meeting_agenda" IS '会议议程';


CREATE TABLE "public"."t_meeting" (
                                      "id" int8 NOT NULL,
                                      "conference_title" varchar(100 char) NULL,
	"start_time" timestamp(6) NULL,
	"end_time" timestamp(6) NULL,
	"conference_room_id" int8 NULL,
	"conference_room_name" varchar(100 char) NULL,
	"contacts_id" int8 NULL,
	"contacts_name" varchar(100 char) NULL,
	"contact_number" character(12 char) NULL,
	"attendants" varchar(255 char) NULL,
	"meeting_content" varchar(255 char) NULL,
	"conference_state" int4 NULL,
	"create_by" int8 NULL,
	"create_name" varchar(32 char) NULL,
	"create_time" timestamp(6) NULL,
	"update_by" int8 NULL,
	"update_name" varchar(32 char) NULL,
	"update_time" timestamp(6) NULL,
	"department_id" int8 NULL,
	"company_id" int8 NULL,
	"tenant_id" int8 NULL,
	"deleted" int4 NULL,
	"reject_reason" varchar(255 char) NULL,
	"sort" int4 NULL,
	"administrators_id" int8 NULL,
	"administrators_name" varchar(100 char) NULL,
	CONSTRAINT "t_meeting_PRIMARY" PRIMARY KEY ("id") ENABLE VALIDATE
);
COMMENT ON COLUMN "public"."t_meeting"."administrators_name" IS '会议室负责人';
COMMENT ON COLUMN "public"."t_meeting"."administrators_id" IS '会议室负责人id';
COMMENT ON COLUMN "public"."t_meeting"."sort" IS '排序';
COMMENT ON COLUMN "public"."t_meeting"."reject_reason" IS '驳回原因';
COMMENT ON COLUMN "public"."t_meeting"."deleted" IS '逻辑删除字段';
COMMENT ON COLUMN "public"."t_meeting"."tenant_id" IS '租户ID';
COMMENT ON COLUMN "public"."t_meeting"."company_id" IS '单位ID';
COMMENT ON COLUMN "public"."t_meeting"."department_id" IS '部门ID';
COMMENT ON COLUMN "public"."t_meeting"."update_time" IS '修改时间';
COMMENT ON COLUMN "public"."t_meeting"."update_name" IS '修改人姓名';
COMMENT ON COLUMN "public"."t_meeting"."update_by" IS '修改人ID';
COMMENT ON COLUMN "public"."t_meeting"."create_time" IS '创建时间';
COMMENT ON COLUMN "public"."t_meeting"."create_name" IS '创建人姓名';
COMMENT ON COLUMN "public"."t_meeting"."create_by" IS '创建人ID';
COMMENT ON COLUMN "public"."t_meeting"."conference_state" IS '会议室状态 0：待审批 1：已通过 2：已驳回 3：占用 4.取消';
COMMENT ON COLUMN "public"."t_meeting"."meeting_content" IS '会议内容';
COMMENT ON COLUMN "public"."t_meeting"."attendants" IS '出席人员';
COMMENT ON COLUMN "public"."t_meeting"."contact_number" IS '联系号码';
COMMENT ON COLUMN "public"."t_meeting"."contacts_name" IS '联系人名称';
COMMENT ON COLUMN "public"."t_meeting"."contacts_id" IS '联系人id';
COMMENT ON COLUMN "public"."t_meeting"."conference_room_name" IS '会议室名称';
COMMENT ON COLUMN "public"."t_meeting"."conference_room_id" IS '会议室id';
COMMENT ON COLUMN "public"."t_meeting"."end_time" IS '结束时间';
COMMENT ON COLUMN "public"."t_meeting"."start_time" IS '开始时间';
COMMENT ON COLUMN "public"."t_meeting"."conference_title" IS '会议标题';
COMMENT ON COLUMN "public"."t_meeting"."id" IS '主键';
COMMENT ON TABLE "public"."t_meeting" IS '申请会议表';


CREATE TABLE "public"."t_meeting_notic" (
                                            "id" int8 NOT NULL,
                                            "title" varchar(128 char) NULL,
	"create_by" int8 NULL,
	"create_name" varchar(32 char) NULL,
	"department_id" int8 NULL,
	"department_name" varchar(32 char) NULL,
	"mobile" varchar(32 char) NULL,
	"create_time" timestamp(6) NULL,
	"document" varchar(1 char) NULL DEFAULT '0'::varchar,
	"annex" varchar(1 char) NULL DEFAULT '0'::varchar,
	"deleted" int4 NULL,
	"update_by" int8 NULL,
	"update_name" varchar(32 char) NULL,
	"update_time" timestamp(6) NULL,
	"tenant_id" int8 NULL,
	"process_instance_id" int8 NULL,
	"bpm_status" int4 NULL,
	"company_id" int8 NULL,
	"company_name" varchar(32 char) NULL,
	"meet_start_time" timestamp(6) NULL,
	"meet_end_time" timestamp(6) NULL,
	"meet_place" varchar(255 char) NULL,
	"meet_compere" varchar(255 char) NULL,
	"attendance_people" text NULL,
	"present_people" text NULL,
	"topic_count" int4 NULL,
	"agenda_id" int8 NULL,
	"urgency" varchar(255 char) NULL,
	"generate_summary" int4 NULL DEFAULT 0,
	CONSTRAINT "t_meeting_notic_PRIMARY" PRIMARY KEY ("id") ENABLE VALIDATE
);
COMMENT ON COLUMN "public"."t_meeting_notic"."generate_summary" IS '1表示生产会议纪要，0表示没有会议纪要';
COMMENT ON COLUMN "public"."t_meeting_notic"."urgency" IS '紧急程度';
COMMENT ON COLUMN "public"."t_meeting_notic"."agenda_id" IS '议程的id';
COMMENT ON COLUMN "public"."t_meeting_notic"."topic_count" IS '议题数量';
COMMENT ON COLUMN "public"."t_meeting_notic"."present_people" IS '出席人员';
COMMENT ON COLUMN "public"."t_meeting_notic"."attendance_people" IS '列席人员';
COMMENT ON COLUMN "public"."t_meeting_notic"."meet_compere" IS '会议主持人';
COMMENT ON COLUMN "public"."t_meeting_notic"."meet_place" IS '会议地点';
COMMENT ON COLUMN "public"."t_meeting_notic"."meet_end_time" IS '会议结束时间';
COMMENT ON COLUMN "public"."t_meeting_notic"."meet_start_time" IS '会议开始时间';
COMMENT ON COLUMN "public"."t_meeting_notic"."company_name" IS '拟稿单位名称';
COMMENT ON COLUMN "public"."t_meeting_notic"."company_id" IS '单位ID';
COMMENT ON COLUMN "public"."t_meeting_notic"."bpm_status" IS '流程状态';
COMMENT ON COLUMN "public"."t_meeting_notic"."process_instance_id" IS '流程实例';
COMMENT ON COLUMN "public"."t_meeting_notic"."tenant_id" IS '租户ID';
COMMENT ON COLUMN "public"."t_meeting_notic"."update_time" IS '修改时间';
COMMENT ON COLUMN "public"."t_meeting_notic"."update_name" IS '修改用户姓名';
COMMENT ON COLUMN "public"."t_meeting_notic"."update_by" IS '修改用户ID';
COMMENT ON COLUMN "public"."t_meeting_notic"."deleted" IS '逻辑删除 0：未删除 1：删除';
COMMENT ON COLUMN "public"."t_meeting_notic"."annex" IS '是否有附件';
COMMENT ON COLUMN "public"."t_meeting_notic"."document" IS '是否有正文';
COMMENT ON COLUMN "public"."t_meeting_notic"."create_time" IS '创建日期';
COMMENT ON COLUMN "public"."t_meeting_notic"."mobile" IS '联系电话';
COMMENT ON COLUMN "public"."t_meeting_notic"."department_name" IS '汇报部门名称';
COMMENT ON COLUMN "public"."t_meeting_notic"."department_id" IS '部门ID';
COMMENT ON COLUMN "public"."t_meeting_notic"."create_name" IS '拟稿人汇报人名称';
COMMENT ON COLUMN "public"."t_meeting_notic"."create_by" IS '拟稿人ID';
COMMENT ON COLUMN "public"."t_meeting_notic"."title" IS '会议标题';
COMMENT ON COLUMN "public"."t_meeting_notic"."id" IS '主键ID';
COMMENT ON TABLE "public"."t_meeting_notic" IS '会议通知';


CREATE TABLE "public"."t_meeting_topic" (
                                            "id" int8 NOT NULL,
                                            "title" varchar(100 char) NULL,
	"create_by" int8 NULL,
	"create_name" varchar(32 char) NULL,
	"department_id" int8 NULL,
	"department_name" varchar(32 char) NULL,
	"mobile" varchar(32 char) NULL,
	"create_time" timestamp(6) NULL,
	"document" varchar(1 char) NULL DEFAULT '0'::varchar,
	"annex" varchar(1 char) NULL DEFAULT '0'::varchar,
	"deleted" int4 NULL,
	"update_by" int8 NULL,
	"update_name" varchar(32 char) NULL,
	"update_time" timestamp(6) NULL,
	"tenant_id" int8 NULL,
	"process_instance_id" int8 NULL,
	"bpm_status" int4 NULL,
	"company_id" int8 NULL,
	"company_name" varchar(32 char) NULL,
	"report_people_id" varchar(500 char) NULL,
	"report_people_name" varchar(1000 char) NULL,
	"material" varchar(1 char) NULL DEFAULT '0'::varchar,
	"report_time" int4 NULL,
	"remark" varchar(500 char) NULL,
	"up_meet" varchar(1 char) NULL DEFAULT '0'::varchar,
	"agenda_id" int8 NULL,
	"submit_time" timestamp(6) NULL,
	"topic_summary" varchar(500 char) NULL,
	"report_deapart_name" varchar(255 char) NULL,
	CONSTRAINT "t_meeting_topic_PRIMARY" PRIMARY KEY ("id") ENABLE VALIDATE,
	CONSTRAINT "t_meeting_topic_indexTopic_instanceId" UNIQUE ("process_instance_id") ENABLE VALIDATE
);
COMMENT ON COLUMN "public"."t_meeting_topic"."report_deapart_name" IS '汇报部门名称';
COMMENT ON COLUMN "public"."t_meeting_topic"."topic_summary" IS '议题概要';
COMMENT ON COLUMN "public"."t_meeting_topic"."submit_time" IS '提交到议题库的时间';
COMMENT ON COLUMN "public"."t_meeting_topic"."agenda_id" IS '会议议程的id，被会议通知选择后，没有被选择为空';
COMMENT ON COLUMN "public"."t_meeting_topic"."up_meet" IS '0 表示没有提交议题库,1表示提交议题库没有上会，2表示已上会';
COMMENT ON COLUMN "public"."t_meeting_topic"."remark" IS '备注';
COMMENT ON COLUMN "public"."t_meeting_topic"."report_time" IS '汇报时长，单位分钟';
COMMENT ON COLUMN "public"."t_meeting_topic"."material" IS '1表示有会议材料，0表示没有';
COMMENT ON COLUMN "public"."t_meeting_topic"."report_people_name" IS '汇报人员的姓名，多个逗号隔开';
COMMENT ON COLUMN "public"."t_meeting_topic"."report_people_id" IS '汇报人员的id，多个逗号隔开';
COMMENT ON COLUMN "public"."t_meeting_topic"."company_name" IS '拟稿单位名称';
COMMENT ON COLUMN "public"."t_meeting_topic"."company_id" IS '单位ID';
COMMENT ON COLUMN "public"."t_meeting_topic"."bpm_status" IS '流程状态';
COMMENT ON COLUMN "public"."t_meeting_topic"."process_instance_id" IS '流程实例';
COMMENT ON COLUMN "public"."t_meeting_topic"."tenant_id" IS '租户ID';
COMMENT ON COLUMN "public"."t_meeting_topic"."update_time" IS '修改时间';
COMMENT ON COLUMN "public"."t_meeting_topic"."update_name" IS '修改用户姓名';
COMMENT ON COLUMN "public"."t_meeting_topic"."update_by" IS '修改用户ID';
COMMENT ON COLUMN "public"."t_meeting_topic"."deleted" IS '逻辑删除 0：未删除 1：删除';
COMMENT ON COLUMN "public"."t_meeting_topic"."annex" IS '是否有附件';
COMMENT ON COLUMN "public"."t_meeting_topic"."document" IS '是否有正文';
COMMENT ON COLUMN "public"."t_meeting_topic"."create_time" IS '创建日期';
COMMENT ON COLUMN "public"."t_meeting_topic"."mobile" IS '联系电话';
COMMENT ON COLUMN "public"."t_meeting_topic"."department_name" IS '拟稿部门名称';
COMMENT ON COLUMN "public"."t_meeting_topic"."department_id" IS '部门ID';
COMMENT ON COLUMN "public"."t_meeting_topic"."create_name" IS '拟稿人经办人名称';
COMMENT ON COLUMN "public"."t_meeting_topic"."create_by" IS '拟稿人ID';
COMMENT ON COLUMN "public"."t_meeting_topic"."title" IS '议题标题';
COMMENT ON COLUMN "public"."t_meeting_topic"."id" IS '主键ID';
COMMENT ON TABLE "public"."t_meeting_topic" IS '会议议题';
