package com.ctsi.hndx.meeting.controller;
import com.ctsi.ssdc.model.PageResult;
import java.util.List;
import java.util.Optional;
import com.ctsi.hndx.meeting.entity.TMeetingNotic;
import com.ctsi.hndx.meeting.entity.dto.TMeetingNoticDTO;
import com.ctsi.hndx.meeting.service.ITMeetingNoticService;
import com.ctsi.ssdc.model.ResResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import com.ctsi.hndx.common.BasePageForm;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.InitBinder;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.util.Assert;
import com.ctsi.hndx.common.BaseController;
import com.ctsi.hndx.annotations.ResponseResultVo;
import com.ctsi.hndx.result.ResultCode;
import com.ctsi.hndx.result.ResultVO;
import com.ctsi.ssdc.annotation.OperationLog;
import com.ctsi.hndx.enums.DBOperation;


/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-01-11
 *
 */

@Slf4j
@RestController
@ResponseResultVo
@RequestMapping("/api/tMeetingNotic")
@Api(value = "会议通知", tags = "会议通知接口")
public class TMeetingNoticController extends BaseController {

    private static final String ENTITY_NAME = "tMeetingNotic";

    @Autowired
    private ITMeetingNoticService tMeetingNoticService;



    /**
     *  新增会议通知批量数据.
     */
    @PostMapping("/createBatch")
    @ApiOperation(value = "新增批量(权限code码为：cscp.tMeetingNotic.add)", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.ADD,message = "新增会议通知批量数据")
    public ResultVO createBatch(@RequestBody List<TMeetingNoticDTO> tMeetingNoticList) {
       Boolean  result = tMeetingNoticService.insertBatch(tMeetingNoticList);
       if(result){
           return ResultVO.success();
       }else {
           return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
       }
    }

     /**
     *  新增数据.
     */
    @PostMapping("/create")
    @ApiOperation(value = "新增(权限code码为：cscp.tMeetingNotic.add)", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.ADD,message = "新增会议通知数据")
    public ResultVO<TMeetingNoticDTO> create(@RequestBody TMeetingNoticDTO tMeetingNoticDTO)  {
        TMeetingNoticDTO result = tMeetingNoticService.create(tMeetingNoticDTO);
        return ResultVO.success(result);
    }

    /**
     *  更新存在数据.
     */
    @PostMapping("/update")
    @ApiOperation(value = "更新存在数据(权限code码为：cscp.tMeetingNotic.update)", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.UPDATE,message = "更新会议通知数据")
    public ResultVO update(@RequestBody TMeetingNoticDTO tMeetingNoticDTO) {
	    Assert.notNull(tMeetingNoticDTO.getId(), "general.IdNotNull");
        int count = tMeetingNoticService.update(tMeetingNoticDTO);
        if(count > 0 ){
            return ResultVO.success();
        }else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }

     /**
     *  删除存在数据.
     */
    @DeleteMapping("/delete/{id}")
    @OperationLog(dBOperation = DBOperation.DELETE,message = "删除会议通知数据")
    @ApiOperation(value = "删除存在数据(权限code码为：cscp.tMeetingNotic.delete)", notes = "传入参数")
    public ResultVO delete(@PathVariable Long id) {
        int count = tMeetingNoticService.delete(id);
        if(count > 0 ){
            return ResultVO.success();
        }else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }

    /**
     * 查询单条数据.
     */
    @GetMapping("/get/{id}")
    @ApiOperation(value = "查询单条数据", notes = "传入参数")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
    public ResultVO get(@PathVariable Long id) {
        TMeetingNoticDTO tMeetingNoticDTO = tMeetingNoticService.findOne(id);
        return ResultVO.success(tMeetingNoticDTO);
    }

    /**
    *  分页查询多条数据.
    */
    @GetMapping("/queryTMeetingNoticPage")
    @ApiOperation(value = "翻页查询多条数据", notes = "传入参数")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
    public ResultVO<PageResult<TMeetingNoticDTO>> queryTMeetingNoticPage(TMeetingNoticDTO tMeetingNoticDTO, BasePageForm basePageForm) {
        return ResultVO.success(tMeetingNoticService.queryListPage(tMeetingNoticDTO, basePageForm));
    }

   /**
    * 查询多条数据.不分页
    */
   @GetMapping("/queryTMeetingNotic")
   @ApiOperation(value = "查询多条数据", notes = "传入参数")
   //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
   public ResultVO<ResResult<TMeetingNoticDTO>> queryTMeetingNotic(TMeetingNoticDTO tMeetingNoticDTO) {
       List<TMeetingNoticDTO> list = tMeetingNoticService.queryList(tMeetingNoticDTO);
       return ResultVO.success(new ResResult<TMeetingNoticDTO>(list));
   }

}
