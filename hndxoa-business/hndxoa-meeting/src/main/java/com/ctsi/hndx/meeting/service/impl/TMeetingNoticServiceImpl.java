package com.ctsi.hndx.meeting.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ctsi.hndx.meeting.entity.TMeetingAgenda;
import com.ctsi.hndx.meeting.entity.dto.TMeetingAgendaDTO;
import com.ctsi.hndx.meeting.entity.dto.TMeetingAgendaTopicDTO;
import com.ctsi.hndx.meeting.service.ITMeetingAgendaService;
import com.ctsi.hndx.utils.BeanConvertUtils;
import com.ctsi.hndx.utils.ListCopyUtil;
import com.ctsi.ssdc.model.PageResult;
import com.ctsi.hndx.meeting.entity.TMeetingNotic;
import com.ctsi.hndx.meeting.entity.dto.TMeetingNoticDTO;
import com.ctsi.hndx.meeting.mapper.TMeetingNoticMapper;
import com.ctsi.hndx.meeting.service.ITMeetingNoticService;
import com.ctsi.hndx.common.SysBaseServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.utils.PageHelperUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.List;
import org.springframework.transaction.annotation.Transactional;
/**
 * <p>
 * 会议通知 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-01-11
 */

@Slf4j
@Service
public class TMeetingNoticServiceImpl extends SysBaseServiceImpl<TMeetingNoticMapper, TMeetingNotic> implements ITMeetingNoticService {

    @Autowired
    private TMeetingNoticMapper tMeetingNoticMapper;

    @Autowired
    private ITMeetingAgendaService tMeetingAgendaService;

    /**
     * 翻页
     *
     * @param entityDTO
     * @param basePageForm
     * @return
     */
    @Override
    public PageResult<TMeetingNoticDTO> queryListPage(TMeetingNoticDTO entityDTO, BasePageForm basePageForm) {
        //设置条件
        LambdaQueryWrapper<TMeetingNotic> queryWrapper = new LambdaQueryWrapper();

        IPage<TMeetingNotic> pageData = tMeetingNoticMapper.selectPage(
             PageHelperUtil.getMPlusPageByBasePage(basePageForm), queryWrapper);
        //返回
        IPage<TMeetingNoticDTO> data  = pageData.convert(entity -> BeanConvertUtils.copyProperties(entity,TMeetingNoticDTO.class));

        return new PageResult<TMeetingNoticDTO>(data.getRecords(),
            data.getTotal(), data.getCurrent());
    }

    /**
     * 列表查询
     *
     * @param entityDTO
     * @return
     */
    @Override
    public List<TMeetingNoticDTO> queryList(TMeetingNoticDTO entityDTO) {
        LambdaQueryWrapper<TMeetingNotic> queryWrapper = new LambdaQueryWrapper();
            List<TMeetingNotic> listData = tMeetingNoticMapper.selectList(queryWrapper);
            List<TMeetingNoticDTO> TMeetingNoticDTOList = ListCopyUtil.copy(listData, TMeetingNoticDTO.class);
        return TMeetingNoticDTOList;
    }

    /**
     * 单个查询
     *
     * @param id the id of the entity
     * @return
     */
    @Override
    public TMeetingNoticDTO findOne(Long id) {
        TMeetingNotic  tMeetingNotic =  tMeetingNoticMapper.selectById(id);
        return  BeanConvertUtils.copyProperties(tMeetingNotic,TMeetingNoticDTO.class);
    }


    /**
     * 新增
     *
     * @param entityDTO the entity to create
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public TMeetingNoticDTO create(TMeetingNoticDTO entityDTO) {
        String agendaId = entityDTO.getAgendaId();
        if (StringUtils.isNotEmpty(agendaId) ){
            if (StringUtils.isNumeric(agendaId)){
                //  修改会议议程
                TMeetingAgenda tMeetingAgenda = tMeetingAgendaService.getById(agendaId);
                if (tMeetingAgenda != null){
                    tMeetingAgendaService.agendaUpMeet(Long.valueOf(agendaId));
                    TMeetingAgendaDTO meetingAgendaDTO = tMeetingAgendaService.findOne(Long.valueOf(agendaId));
                    List<TMeetingAgendaTopicDTO> topicDTOList = meetingAgendaDTO.getTopicDTOList();
                    if (CollectionUtil.isNotEmpty(topicDTOList)){
                        entityDTO.setTopicCount(topicDTOList.size());
                    }
                }

            }

        }
        TMeetingNotic tMeetingNotic =  BeanConvertUtils.copyProperties(entityDTO,TMeetingNotic.class);
        save(tMeetingNotic);
        return  BeanConvertUtils.copyProperties(tMeetingNotic,TMeetingNoticDTO.class);
    }

    /**
     * 修改
     *
     * @param entity the entity to update
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int update(TMeetingNoticDTO entity) {
        TMeetingNotic tMeetingNotic = BeanConvertUtils.copyProperties(entity,TMeetingNotic.class);
        return tMeetingNoticMapper.updateById(tMeetingNotic);
    }

    /**
     * 删除
     *
     * @param id the id of the entity
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int delete(Long id) {
        return tMeetingNoticMapper.deleteById(id);
    }


    /**
     * 验证是否存在
     *
     * @param TMeetingNoticId
     * @return
     */
    @Override
    public boolean existByTMeetingNoticId(Long TMeetingNoticId) {
        if (TMeetingNoticId != null) {
            LambdaQueryWrapper<TMeetingNotic> queryWrapper = new LambdaQueryWrapper();
            queryWrapper.eq(TMeetingNotic::getId, TMeetingNoticId);
            List<TMeetingNotic> result = tMeetingNoticMapper.selectList(queryWrapper);
            return result.size() > 0;
        }
        return true;
    }

    /**
    * 批量新增
    *
    */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertBatch(List<TMeetingNoticDTO> dataList) {
        List<TMeetingNotic> result = ListCopyUtil.copy(dataList, TMeetingNotic.class);
        return saveBatch(result);
    }


}
