package com.ctsi.hndx.meeting.entity.dto;

import com.ctsi.hndx.common.ProcessBusinessBaseDtoEntity;
import com.ctsi.hndx.mybatisplus.query.QueryCondition;
import com.ctsi.hndx.mybatisplus.query.QueryConditionEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * <p>
 * 会议议题
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-27
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value="TMeetingTopicDTO对象", description="会议议题")
public class TMeetingTopicDTO extends ProcessBusinessBaseDtoEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 汇报部门名称
     */
    @ApiModelProperty(value = "拟稿部门名称")
    @QueryCondition(value = QueryConditionEnum.LIKE)
    private String departmentName;

    /**
     * 联系电话
     */
    @ApiModelProperty(value = "联系电话")
    private String mobile;

    /**
     * 流程实例
     */
    @ApiModelProperty(value = "流程实例")
    private Long processInstanceId;

    /**
     * 拟稿单位名称
     */
    @ApiModelProperty(value = "拟稿单位名称")
    private String companyName;

    /**
     * 汇报人员的id，多个逗号隔开
     */
    @ApiModelProperty(value = "汇报人员的id，多个逗号隔开")
    private String reportPeopleId;

    /**
     * 汇报人员的姓名，多个逗号隔开
     */
    @ApiModelProperty(value = "汇报人员的姓名，多个逗号隔开")
    private String reportPeopleName;


    @ApiModelProperty(value = "汇报部门名称")
    private String reportDeapartName;
    /**
     * 1表示有会议材料，0表示没有
     */
    @ApiModelProperty(value = "1表示有会议材料，0表示没有")
    private String material;

    /**
     * 汇报时长，单位分钟
     */
    @ApiModelProperty(value = "汇报时长，单位分钟")
    private Integer reportTime;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;



    /**
     * 0 表示没有提交议题库,1表示提交议题库没有上会，2表示已上会
     */
    @ApiModelProperty(value = "0 表示没有提交议题库,1表示提交议题库没有上会，2表示已上会")
    private String upMeet;


    @ApiModelProperty(value = "提交到议题库的时间")
    private LocalDateTime submitTime;


    @ApiModelProperty(value = "议题概要")
    private String topicSummary;
}
