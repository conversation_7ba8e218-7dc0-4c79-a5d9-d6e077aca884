package com.ctsi.hndx.meeting.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ctsi.hndx.utils.BeanConvertUtils;
import com.ctsi.hndx.utils.ListCopyUtil;
import com.ctsi.ssdc.model.PageResult;
import com.ctsi.hndx.meeting.entity.BizMeetingSummary;
import com.ctsi.hndx.meeting.entity.dto.BizMeetingSummaryDTO;
import com.ctsi.hndx.meeting.mapper.BizMeetingSummaryMapper;
import com.ctsi.hndx.meeting.service.IBizMeetingSummaryService;
import com.ctsi.hndx.common.SysBaseServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.utils.PageHelperUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.List;
import org.springframework.transaction.annotation.Transactional;
/**
 * <p>
 * 会议纪要 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-01-13
 */

@Slf4j
@Service
public class BizMeetingSummaryServiceImpl extends SysBaseServiceImpl<BizMeetingSummaryMapper, BizMeetingSummary> implements IBizMeetingSummaryService {

    @Autowired
    private BizMeetingSummaryMapper bizMeetingSummaryMapper;

    /**
     * 翻页
     *
     * @param entityDTO
     * @param basePageForm
     * @return
     */
    @Override
    public PageResult<BizMeetingSummaryDTO> queryListPage(BizMeetingSummaryDTO entityDTO, BasePageForm basePageForm) {
        //设置条件
        LambdaQueryWrapper<BizMeetingSummary> queryWrapper = new LambdaQueryWrapper();

        IPage<BizMeetingSummary> pageData = bizMeetingSummaryMapper.selectPage(
             PageHelperUtil.getMPlusPageByBasePage(basePageForm), queryWrapper);
        //返回
        IPage<BizMeetingSummaryDTO> data  = pageData.convert(entity -> BeanConvertUtils.copyProperties(entity,BizMeetingSummaryDTO.class));

        return new PageResult<BizMeetingSummaryDTO>(data.getRecords(),
            data.getTotal(), data.getCurrent());
    }

    /**
     * 列表查询
     *
     * @param entityDTO
     * @return
     */
    @Override
    public List<BizMeetingSummaryDTO> queryList(BizMeetingSummaryDTO entityDTO) {
        LambdaQueryWrapper<BizMeetingSummary> queryWrapper = new LambdaQueryWrapper();
            List<BizMeetingSummary> listData = bizMeetingSummaryMapper.selectList(queryWrapper);
            List<BizMeetingSummaryDTO> BizMeetingSummaryDTOList = ListCopyUtil.copy(listData, BizMeetingSummaryDTO.class);
        return BizMeetingSummaryDTOList;
    }

    /**
     * 单个查询
     *
     * @param id the id of the entity
     * @return
     */
    @Override
    public BizMeetingSummaryDTO findOne(Long id) {
        BizMeetingSummary  bizMeetingSummary =  bizMeetingSummaryMapper.selectById(id);
        return  BeanConvertUtils.copyProperties(bizMeetingSummary,BizMeetingSummaryDTO.class);
    }


    /**
     * 新增
     *
     * @param entityDTO the entity to create
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public BizMeetingSummaryDTO create(BizMeetingSummaryDTO entityDTO) {
       BizMeetingSummary bizMeetingSummary =  BeanConvertUtils.copyProperties(entityDTO,BizMeetingSummary.class);
        save(bizMeetingSummary);
        return  BeanConvertUtils.copyProperties(bizMeetingSummary,BizMeetingSummaryDTO.class);
    }

    /**
     * 修改
     *
     * @param entity the entity to update
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int update(BizMeetingSummaryDTO entity) {
        BizMeetingSummary bizMeetingSummary = BeanConvertUtils.copyProperties(entity,BizMeetingSummary.class);
        return bizMeetingSummaryMapper.updateById(bizMeetingSummary);
    }

    /**
     * 删除
     *
     * @param id the id of the entity
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int delete(Long id) {
        return bizMeetingSummaryMapper.deleteById(id);
    }


    /**
     * 验证是否存在
     *
     * @param BizMeetingSummaryId
     * @return
     */
    @Override
    public boolean existByBizMeetingSummaryId(Long BizMeetingSummaryId) {
        if (BizMeetingSummaryId != null) {
            LambdaQueryWrapper<BizMeetingSummary> queryWrapper = new LambdaQueryWrapper();
            queryWrapper.eq(BizMeetingSummary::getId, BizMeetingSummaryId);
            List<BizMeetingSummary> result = bizMeetingSummaryMapper.selectList(queryWrapper);
            return result.size() > 0;
        }
        return true;
    }

    /**
    * 批量新增
    *
    */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertBatch(List<BizMeetingSummaryDTO> dataList) {
        List<BizMeetingSummary> result = ListCopyUtil.copy(dataList, BizMeetingSummary.class);
        return saveBatch(result);
    }


}
