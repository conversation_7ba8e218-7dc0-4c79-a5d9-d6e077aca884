package com.ctsi.hndx.meeting.entity;

import java.time.LocalDateTime;
import com.ctsi.hndx.common.ProcessBusinessBaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 会议纪要
 * </p>
 *
 * <AUTHOR>
 * @since 2022-01-13
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value="BizMeetingSummary对象", description="会议纪要")
public class BizMeetingSummary extends ProcessBusinessBaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 部门名称
     */
    @ApiModelProperty(value = "部门名称")
    private String departmentName;

    /**
     * 联系电话
     */
    @ApiModelProperty(value = "联系电话")
    private String mobile;

    /**
     * 流程实例
     */
    @ApiModelProperty(value = "流程实例")
    private Long processInstanceId;

    /**
     * 拟稿单位名称
     */
    @ApiModelProperty(value = "拟稿单位名称")
    private String companyName;

    /**
     * 文号
     */
    @ApiModelProperty(value = "文号")
    private String referenceNumber;

    /**
     * 缓急
     */
    @ApiModelProperty(value = "缓急")
    private String urgency;

    /**
     * 会议通知id
     */
    @ApiModelProperty(value = "会议通知")
    private String meetNotic;

    /**
     * 会议开始时间
     */
    @ApiModelProperty(value = "会议开始时间")
    private LocalDateTime meetStartTime;

    /**
     * 会议结束时间
     */
    @ApiModelProperty(value = "会议结束时间")
    private LocalDateTime meetEndTime;


}
