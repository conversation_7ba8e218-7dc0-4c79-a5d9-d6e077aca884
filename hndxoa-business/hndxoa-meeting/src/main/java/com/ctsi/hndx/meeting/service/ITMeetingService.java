package com.ctsi.hndx.meeting.service;

import com.ctsi.hndx.meeting.entity.dto.*;
import com.ctsi.hndx.meeting.entity.TMeeting;
import com.ctsi.hndx.common.SysBaseServiceI;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.ssdc.model.PageResult;

import java.util.List;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-22
 */
public interface ITMeetingService extends SysBaseServiceI<TMeeting> {


    /**
     * 会议管理员查询需要审批的申请（分页）.
     *
     * @param condition
     * @param page
     * @return
     */
    PageResult<TMeetingDTO> queryToExamineMeetingPage(QueryToExamineMeetingPageDTO condition, BasePageForm page);

    /**
     * 获取所有不分页
     */
    List<TMeetingDTO> queryList(TMeetingDTO entity);

    /**
     * 查询指定数据的详情信息
     *
     * @param id
     * @return
     */
    TMeetingDTO findOne(Long id);

    /**
     * 会议室申请
     *
     * @param entity
     * @return
     * @throws Exception
     */
    TMeetingDTO createApply(TMeetingDTO entity) throws Exception;


    /**
     * 更新
     */
    int update(TMeetingDTO entity);

    /**
     * 删除
     */
    int delete(Long id);


    /**
     * 查询自己提交的申请（分页）
     *
     * @param condition
     * @param basePageForm
     * @return
     */
    PageResult<TMeetingDTO> queryTMeetingPage(QueryToExamineMeetingPageDTO condition, BasePageForm basePageForm);

    /**
     * 查询当前会议室的使用情况（app）
     *
     * @param queryConferenceRoomDetailsDTO
     * @return
     */
    List<TMeetingDTO> conferenceRoomDetailsApp(QueryConferenceRoomDetailsDTO queryConferenceRoomDetailsDTO);

    /**
     * 取消申请
     *
     * @param id
     * @return
     */
    int updateWithdraw(Long id);

    /**
     * 会议室管理员审批
     *
     * @param copyProperties
     * @return
     */
    int updateToExamine(TMeetingDTO copyProperties);

    /**
     * 查询当前会议室的使用情况(pc)
     *
     * @param queryConferenceRoomDetailsDTO
     * @return
     */
    List<ConferenceRoomDetailsPcDTO> conferenceRoomDetailsPc(QueryConferenceRoomDetailsDTO queryConferenceRoomDetailsDTO);
}
