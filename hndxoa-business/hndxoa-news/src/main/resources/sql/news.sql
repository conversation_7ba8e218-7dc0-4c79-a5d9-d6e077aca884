-- ----------------------------
-- Table structure for t_news
-- ----------------------------
DROP TABLE IF EXISTS `t_news`;
CREATE TABLE `t_news`  (
  `ID` bigint NOT NULL DEFAULT 0 COMMENT '主键id',
  `TITLE` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '标题',
  `OUTLINE` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '概要',
  `CREATE_TIME` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `CREATE_BY` bigint NULL DEFAULT NULL COMMENT '创建人id',
  `CREATE_NAME` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建人姓名',
  `UPDATE_TIME` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `UPDATE_BY` bigint NULL DEFAULT NULL COMMENT '修改人id',
  `UPDATE_NAME` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '更新人',
  `DEPARTMENT_ID` bigint NULL DEFAULT NULL COMMENT '部门id',
  `COMPANY_ID` bigint NULL DEFAULT NULL COMMENT '单位id',
  `TENANT_ID` bigint NULL DEFAULT NULL COMMENT '租户id',
  `DELETED` int NULL DEFAULT NULL COMMENT '逻辑删除 0：未删除 1：删除',
  `source_unit_name` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '来源单位名称',
  `source_unit_id` bigint NULL DEFAULT NULL COMMENT '来源单位id',
  `hits` int NULL DEFAULT NULL COMMENT '点击率',
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '新闻 ' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_news_content
-- ----------------------------

CREATE TABLE `t_news_content` (
  `ID` bigint NOT NULL COMMENT '主键id',
  `TITLE` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '标题',
  `DATA` varchar(1000) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '内容',
  `NOTICE_NOTICE_ID` bigint DEFAULT NULL COMMENT '新闻id',
  `CREATE_TIME` datetime DEFAULT NULL COMMENT '创建时间',
  `CREATE_BY` bigint DEFAULT NULL COMMENT '创建人id',
  `CREATE_NAME` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人姓名',
  `UPDATE_TIME` datetime DEFAULT NULL COMMENT '修改时间',
  `UPDATE_BY` bigint DEFAULT NULL COMMENT '修改人id',
  `UPDATE_NAME` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '更新人',
  `DEPARTMENT_ID` bigint DEFAULT NULL COMMENT '部门id',
  `COMPANY_ID` bigint DEFAULT NULL COMMENT '单位id',
  `TENANT_ID` bigint DEFAULT NULL COMMENT '租户id',
  `DELETED` bigint DEFAULT NULL COMMENT '逻辑删除 0：未删除 1：删除',
  `BANNER_IMG_PATH` varchar(500) DEFAULT NULL COMMENT '缩略图（轮播图）图片路径',
  `BANNER_IMG_URL` varchar(500) DEFAULT NULL COMMENT '缩略图（轮播图）图片跳转地址',
  `PX` int(0) DEFAULT 0 COMMENT '排序',
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='新闻的正文数据 ';