CREATE TABLE "t_news"
(
"ID" BIGINT DEFAULT 0 NOT NULL,
"TITLE" VARCHAR(255),
"OUTLINE" VARCHAR(255),
"NOTIFICATION_TYPE_ID" BIGINT,
"ENCLOSURE_ID" BIGINT,
"CREATE_TIME" TIMESTAMP(0),
"CREATE_BY" BIGINT,
"CREATE_NAME" VARCHAR(32),
"UPDATE_TIME" TIMESTAMP(0),
"UPDATE_BY" BIGINT,
"UPDATE_NAME" VARCHAR(32),
"DEPARTMENT_ID" BIGINT,
"COMPANY_ID" BIGINT,
"TENANT_ID" BIGINT,
"DELETED" INT,
"NOTICE_STATE" INT DEFAULT 0,
"NOTIFICATION_TYPE_NAME" VARCHAR(50),
"FROM_DATA_ID" BI, GINT,
"source_unit_name" VARCHAR(100),
"source_unit_id" BIGINT,
"hits" INT,
"selection_method" INT,
"sms_reminder" INT,
NOT CLUSTER PRIMARY KEY("ID")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE "t_news" IS '新闻 ';COMMENT ON COLUMN "t_news"."ID" IS '主键id';
COMMENT ON COLUMN "t_news"."TITLE" IS '标题';
COMMENT ON COLUMN "t_news"."OUTLINE" IS '概要';
COMMENT ON COLUMN "t_news"."CREATE_TIME" IS '创建时间';
COMMENT ON COLUMN "t_news"."CREATE_BY" IS '创建人id';
COMMENT ON COLUMN "t_news"."CREATE_NAME" IS '创建人姓名';
COMMENT ON COLUMN "t_news"."UPDATE_TIME" IS '修改时间';
COMMENT ON COLUMN "t_news"."UPDATE_BY" IS '修改人id';
COMMENT ON COLUMN "t_news"."UPDATE_NAME" IS '更新人';
COMMENT ON COLUMN "t_news"."DEPARTMENT_ID" IS '部门id';
COMMENT ON COLUMN "t_news"."COMPANY_ID" IS '单位id';
COMMENT ON COLUMN "t_news"."TENANT_ID" IS '租户id';
COMMENT ON COLUMN "t_news"."DELETED" IS '逻辑删除 0：未删除 1：删除';
COMMENT ON COLUMN "t_news"."source_unit_name" IS '来源单位名称';
COMMENT ON COLUMN "t_news"."source_unit_id" IS '来源单位id';
COMMENT ON COLUMN "t_news"."hits" IS '点击率';



CREATE TABLE "t_news_content"
(
"ID" BIGINT NOT NULL,
"TITLE" VARCHAR(32),
"DATA" VARCHAR(1000),
"NOTICE_NOTICE_ID" BIGINT,
"CREATE_TIME" TIMESTAMP(0),
"CREATE_BY" BIGINT,
"CREATE_NAME" VARCHAR(32),
"UPDATE_TIME" TIMESTAMP(0),
"UPDATE_BY" BIGINT,
"UPDATE_NAME" VARCHAR(32),
"DEPARTMENT_ID" BIGINT,
"COMPANY_ID" BIGINT,
"TENANT_ID" BIGINT,
"DELETED" BIGINT,
"BANNER_IMG_PATH" VARCHAR(500),
"BANNER_IMG_URL" VARCHAR(500),
"PX" int(0),
NOT CLUSTER PRIMARY KEY("ID")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE "t_news_content" IS '新闻的正文数据 ';COMMENT ON COLUMN "t_news_content"."ID" IS '主键id';
COMMENT ON COLUMN "t_news_content"."TITLE" IS '标题';
COMMENT ON COLUMN "t_news_content"."DATA" IS '内容';
COMMENT ON COLUMN "t_news_content"."NOTICE_NOTICE_ID" IS '新闻id';
COMMENT ON COLUMN "t_news_content"."CREATE_TIME" IS '创建时间';
COMMENT ON COLUMN "t_news_content"."CREATE_BY" IS '创建人id';
COMMENT ON COLUMN "t_news_content"."CREATE_NAME" IS '创建人姓名';
COMMENT ON COLUMN "t_news_content"."UPDATE_TIME" IS '修改时间';
COMMENT ON COLUMN "t_news_content"."UPDATE_BY" IS '修改人id';
COMMENT ON COLUMN "t_news_content"."UPDATE_NAME" IS '更新人';
COMMENT ON COLUMN "t_news_content"."DEPARTMENT_ID" IS '部门id';
COMMENT ON COLUMN "t_news_content"."COMPANY_ID" IS '单位id';
COMMENT ON COLUMN "t_news_content"."TENANT_ID" IS '租户id';
COMMENT ON COLUMN "t_news_content"."DELETED" IS '逻辑删除 0：未删除 1：删除';
COMMENT ON COLUMN "t_news_content"."BANNER_IMG_PATH" IS '缩略图（轮播图）图片路径';
COMMENT ON COLUMN "t_news_content"."BANNER_IMG_URL" IS '缩略图（轮播图）图片跳转地址';
COMMENT ON COLUMN "t_news_content"."PX" IS '排序';




