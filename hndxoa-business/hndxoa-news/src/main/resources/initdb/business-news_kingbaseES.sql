CREATE TABLE "public"."t_news" (
                                     ID int8 NOT NULL DEFAULT 0,
                                     TITLE varchar(255 char) NULL,
	OUTLINE varchar(255 char) NULL,
	CREATE_TIME timestamp(6) NULL,
	CREATE_BY int8 NULL,
	CREATE_NAME varchar(32 char) NULL,
	UPDATE_TIME timestamp(6) NULL,
	UPDATE_BY int8 NULL,
	UPDATE_NAME varchar(32 char) NULL,
	DEPARTMENT_ID int8 NULL,
	COMPANY_ID int8 NULL,
	TENANT_ID int8 NULL,
	DELETED int4 NULL,
	"source_unit_name" varchar(100 char) NULL,
	"source_unit_id" int8 NULL,
	"hits" int4 NULL,
	CONSTRAINT "t_news_PRIMARY" PRIMARY KEY (ID) ENABLE VALIDATE
);
COMMENT ON COLUMN "public"."t_news"."hits" IS '点击率';
COMMENT ON COLUMN "public"."t_news"."source_unit_id" IS '来源单位id';
COMMENT ON COLUMN "public"."t_news"."source_unit_name" IS '来源单位名称';
COMMENT ON COLUMN "public"."t_news"."DELETED" IS '逻辑删除 0：未删除 1：删除';
COMMENT ON COLUMN "public"."t_news"."TENANT_ID" IS '租户id';
COMMENT ON COLUMN "public"."t_news"."COMPANY_ID" IS '单位id';
COMMENT ON COLUMN "public"."t_news"."DEPARTMENT_ID" IS '部门id';
COMMENT ON COLUMN "public"."t_news"."UPDATE_NAME" IS '更新人';
COMMENT ON COLUMN "public"."t_news"."UPDATE_BY" IS '修改人id';
COMMENT ON COLUMN "public"."t_news"."UPDATE_TIME" IS '修改时间';
COMMENT ON COLUMN "public"."t_news"."CREATE_NAME" IS '创建人姓名';
COMMENT ON COLUMN "public"."t_news"."CREATE_BY" IS '创建人id';
COMMENT ON COLUMN "public"."t_news"."CREATE_TIME" IS '创建时间';
COMMENT ON COLUMN "public"."t_news"."OUTLINE" IS '概要';
COMMENT ON COLUMN "public"."t_news"."TITLE" IS '标题';
COMMENT ON COLUMN "public"."t_news"."ID" IS '主键id';
COMMENT ON TABLE "public"."t_news" IS '新闻 ';

CREATE TABLE "public"."t_news_content" (
                                        ID int8 NOT NULL,
                                        TITLE varchar(32 char) NULL,
	"DATA" varchar(1000 char) NULL,
	NOTICE_NOTICE_ID int8 NULL,
	CREATE_TIME timestamp NULL,
	CREATE_BY int8 NULL,
	CREATE_NAME varchar(32 char) NULL,
	UPDATE_TIME timestamp NULL,
	UPDATE_BY int8 NULL,
	UPDATE_NAME varchar(32 char) NULL,
	DEPARTMENT_ID int8 NULL,
	COMPANY_ID int8 NULL,
	TENANT_ID int8 NULL,
	DELETED int8 NULL,
	BANNER_IMG_PATH varchar(500 char) NULL,
    BANNER_IMG_URL varchar(500 char) NULL,
    PX int8 NULL,
	CONSTRAINT "t_text_data_PRIMARY" PRIMARY KEY (ID) ENABLE VALIDATE
);
COMMENT ON COLUMN "public"."t_news_content"."PX" IS '排序';
COMMENT ON COLUMN "public"."t_news_content"."BANNER_IMG_URL" IS '缩略图（轮播图）图片路径';
COMMENT ON COLUMN "public"."t_news_content"."BANNER_IMG_PATH" IS '缩略图（轮播图）图片跳转地址';
COMMENT ON COLUMN "public"."t_news_content"."DELETED" IS '逻辑删除 0：未删除 1：删除';
COMMENT ON COLUMN "public"."t_news_content"."TENANT_ID" IS '租户id';
COMMENT ON COLUMN "public"."t_news_content"."COMPANY_ID" IS '单位id';
COMMENT ON COLUMN "public"."t_news_content"."DEPARTMENT_ID" IS '部门id';
COMMENT ON COLUMN "public"."t_news_content"."UPDATE_NAME" IS '更新人';
COMMENT ON COLUMN "public"."t_news_content"."UPDATE_BY" IS '修改人id';
COMMENT ON COLUMN "public"."t_news_content"."UPDATE_TIME" IS '修改时间';
COMMENT ON COLUMN "public"."t_news_content"."CREATE_NAME" IS '创建人姓名';
COMMENT ON COLUMN "public"."t_news_content"."CREATE_BY" IS '创建人id';
COMMENT ON COLUMN "public"."t_news_content"."CREATE_TIME" IS '创建时间';
COMMENT ON COLUMN "public"."t_news_content"."NOTICE_NOTICE_ID" IS '新闻id';
COMMENT ON COLUMN "public"."t_news_content"."DATA" IS '内容';
COMMENT ON COLUMN "public"."t_news_content"."TITLE" IS '标题';
COMMENT ON COLUMN "public"."t_news_content"."ID" IS '主键id';
COMMENT ON TABLE "public"."t_news_content" IS '新闻的正文数据 ';
