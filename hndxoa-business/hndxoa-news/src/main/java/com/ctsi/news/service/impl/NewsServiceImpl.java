package com.ctsi.news.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.enums.FileBasePathName;
import com.ctsi.hndx.filestore.FileStoreTemplateService;
import com.ctsi.hndx.tsysconfig.service.ISysConfigService;
import com.ctsi.hndx.utils.BeanConvertUtils;
import com.ctsi.hndx.utils.PageHelperUtil;
import com.ctsi.hndx.wps.WpsConst;
import com.ctsi.news.entity.News;
import com.ctsi.news.entity.NewsContent;
import com.ctsi.news.entity.dto.NewsDTO;
import com.ctsi.news.entity.dto.QueryNewsDTO;
import com.ctsi.news.entity.dto.ResDetailsDTO;
import com.ctsi.news.entity.dto.ResNewsDTO;
import com.ctsi.news.mapper.NewsContentMapper;
import com.ctsi.news.mapper.NewsMapper;
import com.ctsi.news.service.INewsContentService;
import com.ctsi.news.service.INewsService;
import com.ctsi.operation.domain.CscpDocumentFile;
import com.ctsi.operation.domain.CscpEnclosureFile;
import com.ctsi.operation.mapper.CscpDocumentFileMapper;
import com.ctsi.operation.mapper.CscpEnclosureFileMapper;
import com.ctsi.ssdc.model.PageResult;
import com.ctsi.ssdc.security.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.MessageFormat;
import java.util.Base64;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <p>
 * 新闻  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-23
 */

@Slf4j
@Service
public class NewsServiceImpl extends ServiceImpl<NewsMapper, News> implements INewsService {

    @Autowired
    private NewsMapper newsMapper;

    @Autowired
    private INewsContentService iNewsContentService;

    @Autowired
    private NewsContentMapper newsContentMapper;

    @Autowired
    private CscpDocumentFileMapper cscpDocumentFileMapper;

    @Autowired
    private CscpEnclosureFileMapper cscpEnclosureFileRepository;

    @Autowired
    private FileStoreTemplateService fileStoreTemplateService;

    @Autowired
    private ISysConfigService sysConfigService;

    /**
     * 翻页
     *
     * @param entity
     * @param
     * @return
     */
    @Override
    public IPage<News> pageTzggNotice(News entity, BasePageForm basePageForm) {
        //设置条件
        LambdaQueryWrapper<News> queryWrapper = new LambdaQueryWrapper<>();

        IPage<News> pageData = newsMapper.selectPage(
                PageHelperUtil.getMPlusPageByBasePage(basePageForm), queryWrapper);
        //返回
        return pageData;
    }

    /**
     * 列表查询
     *
     * @param entity
     * @return
     */
    @Override
    public List<News> findList(News entity) {
        LambdaQueryWrapper<News> queryWrapper = new LambdaQueryWrapper<>();
        return newsMapper.selectList(queryWrapper);
    }

    /**
     * 单个查询
     *
     * @param id the id of the entity
     * @return
     */
    @Override
    public NewsDTO findOne(Long id) {

        //新闻
        NewsDTO tNoticeDTO = BeanConvertUtils.copyProperties(newsMapper.selectOneNoAdd(new LambdaQueryWrapper<News>().eq(News::getId, id)), NewsDTO.class);

        //增加点击率
        synchronized (this) {
            News tNoticeHits = new News();
            if (null == tNoticeDTO.getHits()) {
                tNoticeHits.setHits(new Integer(1));
            } else {
                tNoticeHits.setHits(new Integer(tNoticeDTO.getHits() + 1));
            }
            newsMapper.updateTenantId(tNoticeHits, new LambdaQueryWrapper<News>().eq(News::getId, id));
            tNoticeDTO.setHits(tNoticeHits.getHits());
        }


        //对应正文数据
        LambdaQueryWrapper<NewsContent> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(NewsContent::getNoticeNoticeId, id);
        tNoticeDTO.setNewsContent(BeanConvertUtils.copyProperties(newsContentMapper.selectOneNoAdd(queryWrapper), NewsContent.class));


        //附件文件
        tNoticeDTO.setCscpEnclosureFileList(cscpEnclosureFileRepository.selectListNoAdd(
                new LambdaQueryWrapper<CscpEnclosureFile>()
                        .eq(CscpEnclosureFile::getFormDataId, id)));


        //正文文件
        tNoticeDTO.setCscpDocumentFiles(cscpDocumentFileMapper
                .selectListNoAdd(new LambdaQueryWrapper<CscpDocumentFile>()
                        .eq(CscpDocumentFile::getFormDataId, id)));


        return tNoticeDTO;
    }


    /**
     * 新增
     *
     * @param tNoticeDTO the entity to create
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean create(NewsDTO tNoticeDTO) {
        boolean save = false;
        // 接收写入之后的主表数据
        News newNotice = new News();

        // 转换
        News entity = BeanConvertUtils.copyProperties(tNoticeDTO, News.class);
        entity.setSourceUnitId(SecurityUtils.getCurrentCscpUserDetail().getCompanyId());
        entity.setSourceUnitName(SecurityUtils.getCurrentCscpUserDetail().getCompanyName());

        // 增加新闻基本数据
        save = save(entity);

        if (null != tNoticeDTO.getNewsContent()) {
            String content = tNoticeDTO.getNewsContent().getData();
            if (null != content && !"".equals(content)) {
                content = convertNewsContent(content);
                tNoticeDTO.getNewsContent().setData(content);
            }
        }

        // 正文数据
        NewsContent tTextData = BeanConvertUtils.copyProperties(tNoticeDTO.getNewsContent(), NewsContent.class);
        if (null != tTextData) {
            tTextData.setNoticeNoticeId(entity.getId());
            iNewsContentService.create(tTextData);
        }

        return save ? true : false;
    }

    /**
     * 修改
     *
     * @param tNoticeDTO the entity to update
     * @return-
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int update(NewsDTO tNoticeDTO) {
        News entity = BeanConvertUtils.copyProperties(tNoticeDTO, News.class);

        LambdaQueryWrapper<NewsContent> queryWrapper1 = new LambdaQueryWrapper<>();
        queryWrapper1.eq(NewsContent::getNoticeNoticeId, entity.getId());

        if (null != tNoticeDTO.getNewsContent()) {
            String content = tNoticeDTO.getNewsContent().getData();
            if (null != content && !"".equals(content)) {
                content = convertNewsContent(content);
                tNoticeDTO.getNewsContent().setData(content);
            }
        }

        newsContentMapper.update(BeanConvertUtils.copyProperties(tNoticeDTO.getNewsContent(), NewsContent.class), queryWrapper1);

        //修改新闻数据
        int i = newsMapper.updateById(entity);

        return i > 0 ? 1 : 0;
    }

    /**
     * 删除
     *
     * @param id the id of the entity
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int delete(Long id) {

        //删除正文数据表数据
        LambdaQueryWrapper<NewsContent> qwTTextData = new LambdaQueryWrapper<>();
        qwTTextData.eq(NewsContent::getNoticeNoticeId, id);
        newsContentMapper.delete(qwTTextData);

        int noticeCount = newsMapper.deleteById(id);

        return noticeCount;

    }


    /**
     * 查询这个用户待阅已阅
     *
     * @param queryTzggNoticeDTO
     * @param basePageForm
     * @return
     */
    @Override
    public PageResult<ResNewsDTO> queryCondition(QueryNewsDTO queryTzggNoticeDTO, BasePageForm basePageForm) {
        //当前用户id
        queryTzggNoticeDTO.setUserId(SecurityUtils.getCurrentUserId());

        //查询待阅已阅
        IPage<ResNewsDTO> queryCondition = newsMapper.queryCondition(PageHelperUtil.getMPlusPageByBasePage(basePageForm), queryTzggNoticeDTO);

        return new PageResult<>(queryCondition.getRecords(), queryCondition.getTotal(), queryCondition.getCurrent());
    }

    /**
     * 查询这个用户待阅已阅
     *
     * @param queryTzggNoticeDTO
     * @param basePageForm
     * @return
     */
    @Override
    public PageResult<ResNewsDTO> queryConditionByApp(QueryNewsDTO queryTzggNoticeDTO, BasePageForm basePageForm) {
        IPage<ResNewsDTO> queryCondition = newsMapper.queryConditionByApp(PageHelperUtil.getMPlusPageByBasePage(basePageForm), queryTzggNoticeDTO);
        //返回
        return new PageResult<>(queryCondition.getRecords(), queryCondition.getTotal(), queryCondition.getCurrent());
    }

    /**
     * 获取自己发布的新闻数据分页
     *
     * @param queryTzggNoticeDTO
     * @param basePageForm
     * @return
     */
    @Override
    public PageResult<ResNewsDTO> getNewsList(QueryNewsDTO queryTzggNoticeDTO, BasePageForm basePageForm) {
        //设置条件
        LambdaQueryWrapper<News> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.like(StringUtils.isNotEmpty(queryTzggNoticeDTO.getTitle()), News::getTitle, queryTzggNoticeDTO.getTitle());
        queryWrapper.ge(StringUtils.isNotEmpty(queryTzggNoticeDTO.getStartTime()), News::getCreateTime, queryTzggNoticeDTO.getStartTime());
        queryWrapper.le(StringUtils.isNotEmpty(queryTzggNoticeDTO.getEndTime()), News::getCreateTime, queryTzggNoticeDTO.getEndTime());
        queryWrapper.orderByDesc(News::getPx);
        queryWrapper.orderByDesc(News::getCreateTime);
        // 查询个人所有
        queryWrapper.eq(News::getCreateBy, SecurityUtils.getCurrentUserId());
        IPage<News> noticeList = newsMapper.selectPage(PageHelperUtil.getMPlusPageByBasePage(basePageForm), queryWrapper);
        IPage<ResNewsDTO> convert = noticeList.convert(i -> BeanConvertUtils.copyProperties(i, ResNewsDTO.class));
        return new PageResult<>(convert.getRecords(), noticeList.getTotal(), noticeList.getCurrent());
    }

    /**
     * 查询指定新闻数据
     *
     * @param id
     * @return
     */
    @Override
    public ResDetailsDTO getNews(Long id) {
        //新闻数据
        News tNotice = newsMapper.selectById(id);

        //正文数据
        LambdaQueryWrapper<NewsContent> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(NewsContent::getNoticeNoticeId, id);
        List<NewsContent> tTextData = newsContentMapper.selectList(lambdaQueryWrapper);

        ResDetailsDTO resDetailsDTO = BeanConvertUtils.copyProperties(tNotice, ResDetailsDTO.class);

        NewsContent newsContent = new NewsContent();
        if (null != tTextData && tTextData.size() > 0) {
            newsContent = tTextData.get(0);
            resDetailsDTO.setDocumentData(newsContent.getData());
            resDetailsDTO.setBannerImgPath(newsContent.getBannerImgPath());
            resDetailsDTO.setBannerImgUrl(newsContent.getBannerImgUrl());
            resDetailsDTO.setContentShowThumbnail(newsContent.getContentShowThumbnail());
            // 排序
            resDetailsDTO.setPx(tNotice.getPx());
        }

        return resDetailsDTO;
    }

    /**
     * 管理员获取全部的新闻数据
     *
     * @param queryTzggNoticeDTO
     * @param basePageForm
     * @return
     */
    @Override
    public PageResult<ResNewsDTO> admingetNewsList(QueryNewsDTO queryTzggNoticeDTO, BasePageForm basePageForm) {
        IPage<News> objectPage = new Page<>(basePageForm.getCurrentPage(), basePageForm.getPageSize());
        LambdaQueryWrapper<News> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.like(StringUtils.isNotEmpty(queryTzggNoticeDTO.getTitle()), News::getTitle, queryTzggNoticeDTO.getTitle());
        queryWrapper.ge(StringUtils.isNotEmpty(queryTzggNoticeDTO.getStartTime()), News::getCreateTime, queryTzggNoticeDTO.getStartTime());
        queryWrapper.le(StringUtils.isNotEmpty(queryTzggNoticeDTO.getEndTime()), News::getCreateTime, queryTzggNoticeDTO.getEndTime());
        queryWrapper.orderByDesc(News::getPx);
        queryWrapper.orderByDesc(News::getCreateTime);
        // 查询单位所有
        queryWrapper.eq(News::getCompanyId, SecurityUtils.getCurrentCompanyId());
        IPage<News> noticeList = newsMapper.selectPage(objectPage, queryWrapper);
        List<ResNewsDTO> collect = noticeList.getRecords().stream().map(i -> BeanConvertUtils.copyProperties(i, ResNewsDTO.class)).collect(Collectors.toList());
        return new PageResult<ResNewsDTO>(collect, objectPage.getTotal(), objectPage.getCurrent());
    }

    /**
     * 查询新闻的最大排序号
     *
     * @param companyId
     * @return
     */
    @Override
    public Integer getMaxPx(long companyId) {
        Integer maxPx = newsMapper.getMaxPx(companyId);
        if (null == maxPx) {
            maxPx = 0;
        }
        return maxPx + 1;
    }

    public String base64ToImageUrl(String base64ImgStr, String format) {
        String imgPath;
        try {
            String imgBase64 = base64ImgStr.substring(base64ImgStr.indexOf(",") + 1);
            byte[] imgData = Base64.getDecoder().decode(imgBase64);
            if (null == format || "".equals(format)) {
                format = "jpg";
            }
            imgPath = fileStoreTemplateService.createFileUrl(FileBasePathName.WPS_IMPORT, "." + format);
            fileStoreTemplateService.uploadFile(imgPath, imgData);
            imgPath = new StringBuffer()
                    .append(sysConfigService.getSysConfigValueByCode(WpsConst.OAWEB_SERVER_URL
                            .replace(".local", "")
                    )).append(MessageFormat.format(WpsConst.OAWEB_DOWNLOAD_URL, imgPath))
                    .toString();
        } catch (Exception e) {
            imgPath = base64ImgStr;
            e.printStackTrace();
        }
        return imgPath;
    }

    public String convertNewsContent(String strHtml) {
        StringBuffer sb = new StringBuffer();
        Pattern p_img = Pattern.compile("<(img|IMG)(.*?)(/>|></img>|>)");
        Matcher m_img = p_img.matcher(strHtml);
        boolean result_img = m_img.find();
        if (result_img) {
            while (result_img) {
                StringBuffer sbSrc = new StringBuffer();
                // 获取到匹配的<img />标签中的内容
                String str_img = m_img.group(2);
                // 开始匹配<img />标签中的src
                Pattern p_src = Pattern
                        .compile("(src|SRC)=(\"|\')(.*?)(\"|\')");
                Matcher m_src = p_src.matcher(str_img);
                if (m_src.find()) {
                    String str_src = m_src.group(3);
                    String format = StringUtils.substringBetween(str_src, "data:image/", ";base64,");
                    String imgUrl = base64ToImageUrl(str_src, format);
                    m_src.appendReplacement(sbSrc, " src=\"" + str_src + "\" ");
                    m_img.appendReplacement(sb, "<img src='" + imgUrl + "'/>");
                }
                // 结束匹配<img />标签中的src
                // 匹配strHtml中是否存在下一个<img />标签，有则继续以上步骤匹配<img />标签中的src
                result_img = m_img.find();
            }
            m_img.appendTail(sb);
            return sb.toString();
        } else {
            return strHtml;
        }

    }

}
