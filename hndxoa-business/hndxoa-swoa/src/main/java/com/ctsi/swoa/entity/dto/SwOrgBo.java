package com.ctsi.swoa.entity.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * <p>
 * 区域信息
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-28
 */
@Getter
@Setter
@ApiModel(value = "SwLmdcOrgBo对象", description = "区域信息")
public class SwOrgBo implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "地址id")
    private Long addressId;

    @ApiModelProperty("调研开始时间")
    @TableField(exist = false)
    private String startTime;

    @ApiModelProperty("调研结束时间")
    @TableField(exist = false)
    private String endTime;


}
