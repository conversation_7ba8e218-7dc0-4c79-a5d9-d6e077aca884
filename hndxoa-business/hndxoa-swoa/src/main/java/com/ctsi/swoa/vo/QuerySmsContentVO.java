//package com.ctsi.swoa.vo;
//
//import com.ctsi.activiti.core.vo.TaskVO;
//import io.swagger.annotations.ApiModelProperty;
//import lombok.AllArgsConstructor;
//import lombok.Builder;
//import lombok.Data;
//import lombok.NoArgsConstructor;
//
///**
// * <AUTHOR>
// * @date 2023/12/28
// * @apiNote
// */
//@Data
//@Builder
//@AllArgsConstructor
//@NoArgsConstructor
//public class QuerySmsContentVO {
//
//    @ApiModelProperty(value = "通知记录ID")
//    private Long id;
//
//    @ApiModelProperty(value = "任务ID")
//    private Long taskId;
//
//    @ApiModelProperty(value = "流程实例ID")
//    private Long processInstanceId;
//
//    @ApiModelProperty(value = "任务VO")
//    private TaskVO taskVO;
//}
