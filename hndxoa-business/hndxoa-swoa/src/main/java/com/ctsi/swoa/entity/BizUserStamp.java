package com.ctsi.swoa.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.ctsi.hndx.common.BaseEntity;
import java.io.Serializable;
import java.math.BigInteger;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-28
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("biz_user_stamp")
@ApiModel(value="BizUserStamp对象", description="")
public class BizUserStamp extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 业务id
     */
    @ApiModelProperty(value = "业务id")
    private String formDataId;

    /**
     * 编号
     */
    @ApiModelProperty(value = "编号")
    private Integer number;

    /**
     * 排序
     */
    @ApiModelProperty(value = "排序")
    private Integer sorti;

    /**
     * 年份
     */
    @ApiModelProperty(value = "年份")
    private String year;

    /**
     * 月份
     */
    @ApiModelProperty(value = "月份")
    private String month;

    /**
     * 日
     */
    @ApiModelProperty(value = "日")
    private String day;

    /**
     * 用户id
     */
    @ApiModelProperty(value = "用户id")
    private Long userId;

    /**
     * 单位名称
     */
    @ApiModelProperty(value = "单位名称")
    private String companyName;

    /**
     * 部门名称
     */
    @ApiModelProperty(value = "部门名称")
    private String departmentName;


}
