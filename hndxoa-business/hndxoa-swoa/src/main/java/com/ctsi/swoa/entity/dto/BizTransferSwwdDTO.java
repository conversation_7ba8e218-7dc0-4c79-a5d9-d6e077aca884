package com.ctsi.swoa.entity.dto;

import com.ctsi.hndx.common.BaseDtoEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 办文办件转办表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value="BizTransferSwwdDTO对象", description="办文办件转办表")
public class BizTransferSwwdDTO extends BaseDtoEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 标题
     */
    @ApiModelProperty(value = "标题")
    private String title;

    /**
     * 文件类别
     */
    @ApiModelProperty(value = "文件类别")
    private String fileType;

    /**
     * 是否需要反馈：0-需要，1-不需要
     */
    @ApiModelProperty(value = "是否需要反馈：0-需要，1-不需要")
    private Integer feedbackRequired;

    /**
     * 转办类型：0-取自线上呈批件,1-手动上传
     */
    @ApiModelProperty(value = "转办类型：0-取自线上呈批件,1-手动上传")
    private Integer transferType;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remarks;

    /**
     * 业务表ID=BID，cscp_proc_base
     */
    @ApiModelProperty(value = "业务表ID,选择线上呈批件时，需要给该参数赋值")
    private Long formDataId;

    /**
     * 密级期限code
     */
    @ApiModelProperty(value = "密级期限code")
    private String durationClassification;

    /**
     * 密级期限名称
     */
    @ApiModelProperty(value = "密级期限名称")
    private String durationClassificationName;

    /**
     * 接收单位和人员集合
     */
    @ApiModelProperty(value = "接收单位和人员集合")
    private List<BizTransferUserOrgDTO> bizTransferUserOrgDTOList;

    /**
     * 接收单位名称集合
     */
    @ApiModelProperty(value = "接收单位名称集合")
    private List<String> receiptUnitNameList;

    /**
     * 接收人员集合
     */
    @ApiModelProperty(value = "接收人员集合")
    private List<String> receiptUserNameList;

    @ApiModelProperty(value = "转办时间")
    private LocalDateTime createTime;
}
