package com.ctsi.swoa.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.ctsi.hndx.common.BaseEntity;
import java.io.Serializable;
import java.time.LocalDateTime;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 新增签章历史记录
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-21
 */
@Getter
@Setter
@TableName("t_sign_history")
@ApiModel(value = "TSignHistory对象", description = "新增签章历史记录")
public class TSignHistory extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("签章名称")
    private String signatureName;

    @ApiModelProperty("事项内容,标题")
    private String title;

    @ApiModelProperty("呈批件拟稿人-经办人")
    private String handledBy;

    @ApiModelProperty("请示件,拟稿人部门")
    private String deptName;

    @ApiModelProperty("呈批件业务id")
    private String bid;

    @ApiModelProperty("业务类别")
    private String btype;

    @ApiModelProperty("流程id")
    private String procInstId;


}
