package com.ctsi.general.context;


import com.ctsi.hndx.emergency.service.impl.BizUserEmergencyObserverServiceImpl;
import com.ctsi.hndx.emergency.service.impl.BizUserEmergencySubjectServiceImpl;
import com.ctsi.huaihua.service.impl.BizScoreUserConfigObserverServiceImpl;
import com.ctsi.huaihua.service.impl.BizScoreUserConfigSubjectServiceImpl;
import com.ctsi.ssdc.admin.domain.dto.CscpUserDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Classname UserInfoChangeContext
 * @Description
 * @Date 2022/3/9/0009 9:59
 */
@Component
public class UserInfoChangeContext {

    @Autowired
    private BizUserEmergencySubjectServiceImpl bizUserEmergencySubjectService;

    @Autowired
    private BizUserEmergencyObserverServiceImpl bizUserEmergencyObserverService;

    @Autowired
    private BizScoreUserConfigSubjectServiceImpl bizScoreUserConfigSubjectService;
    @Autowired
    private BizScoreUserConfigObserverServiceImpl bizScoreUserConfigObserverService;


    public void update(CscpUserDTO userDTO) {

        // 注册观察者
        bizUserEmergencySubjectService.registerObserver(bizUserEmergencyObserverService);
        bizScoreUserConfigSubjectService.registerObserver(bizScoreUserConfigObserverService);

        // 通知注册的观察者
        bizUserEmergencySubjectService.update(userDTO);
        bizScoreUserConfigSubjectService.update(userDTO);
    }

}
