package com.ctsi.question.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.ctsi.hndx.common.BaseEntity;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 问卷投票题目填报信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-11
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("biz_question_response")
@ApiModel(value="BizQuestionResponse对象", description="问卷投票题目填报信息表")
public class BizQuestionResponse extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 问卷投票题目表ID
     */
    @ApiModelProperty(value = "问卷投票题目表ID")
    private Long questionTopicId;

    /**
     * 题目已选选项
     */
    @ApiModelProperty(value = "题目已选选项")
    private String responseOption;

    /**
     * 题目回答内容
     */
    @ApiModelProperty(value = "题目回答内容")
    private String responseContext;

    @ApiModelProperty(value = "是否删除")
    private Integer deleted;

    /**
     * 问卷投票主表ID
     */
    @ApiModelProperty(value = "问卷投票主表ID")
    private Long questionSurveyId;

    /**
     * 问卷投票填报人信息ID
     */
    @ApiModelProperty(value = "问卷投票填报人信息ID")
    private Long questionPeopleId;

}
