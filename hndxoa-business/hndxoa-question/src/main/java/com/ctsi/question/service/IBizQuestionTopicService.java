package com.ctsi.question.service;

import com.ctsi.question.entity.dto.BizQuestionTopicDTO;
import com.ctsi.question.entity.BizQuestionTopic;
import com.ctsi.hndx.common.SysBaseServiceI;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.ssdc.model.PageResult;
import java.util.List;

/**
 * <p>
 * 问卷投票题目表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-11
 */
public interface IBizQuestionTopicService extends SysBaseServiceI<BizQuestionTopic> {


    /**
     * 分页查询
     *
     * @param entityDTO
     * @param page
     * @return
     */
    PageResult<BizQuestionTopicDTO> queryListPage(BizQuestionTopicDTO entityDTO, BasePageForm page);

    /**
     * 获取所有不分页
     *
     * @param entity
     * @return
     */
    List<BizQuestionTopicDTO> queryList(BizQuestionTopicDTO entity);

    /**
     * 根据主键id获取单个对象
     *
     * @param id
     * @return
     */
    BizQuestionTopicDTO findOne(Long id);

    /**
     * 新增
     *
     * @param entity
     * @return
     */
    BizQuestionTopicDTO create(BizQuestionTopicDTO entity);


    /**
     * 更新
     *
     * @param entity
     * @return
     */
    int update(BizQuestionTopicDTO entity);

    /**
     * 删除
     *
     * @param id
     * @return
     */
    int delete(Long id);

    /**
     * 通过查询条件删除
     *
     * @param entityDTO
     * @return
     */
    int deleteByEntity(BizQuestionTopicDTO entityDTO);

     /**
     * 是否存在
     *
     * existByBizQuestionTopicId
     * @param code
     * @return
     */
    boolean existByBizQuestionTopicId(Long code);

    /**
    * 批量新增
    *
    * create batch
    * @param dataList
    * @return
    */
    Boolean insertBatch(List<BizQuestionTopicDTO> dataList);


}
