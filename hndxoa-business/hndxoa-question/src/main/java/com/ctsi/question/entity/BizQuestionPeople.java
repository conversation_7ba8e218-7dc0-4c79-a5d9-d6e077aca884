package com.ctsi.question.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ctsi.hndx.common.BaseEntity;
import java.io.Serializable;
import java.time.LocalDateTime;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 问卷投票填报人员表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-11
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("biz_question_people")
@ApiModel(value="BizQuestionPeople对象", description="问卷投票填报人员表")
public class BizQuestionPeople extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 部门名称
     */
    @ApiModelProperty(value = "部门名称")
    private String departmentName;

    /**
     * 单位名称
     */
    @ApiModelProperty(value = "单位名称")
    private String companyName;

    /**
     * 填报人id
     */
    @ApiModelProperty(value = "填报人id")
    private Long userId;

    /**
     * 填报人姓名
     */
    @ApiModelProperty(value = "填报人姓名")
    private String realName;

    /**
     * 问卷投票主表ID
     */
    @ApiModelProperty(value = "问卷投票主表ID")
    private Long questionSurveyId;

    /**
     * 是否填报
     */
    @ApiModelProperty(value = "是否填报")
    private String response;

    /**
     * 填报时间
     */
    @ApiModelProperty(value = "填报时间")
    private LocalDateTime responseTime;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String tip;


    @ApiModelProperty(value = "是否删除")
    private Integer deleted;


}
