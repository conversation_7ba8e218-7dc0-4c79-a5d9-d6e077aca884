package com.ctsi.question.entity.dto;

import com.ctsi.hndx.common.BaseDtoEntity;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 问卷投票题目选项表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-11
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value="BizQuestionOptionDTO对象", description="问卷投票题目选项表")
public class BizQuestionOptionDTO extends BaseDtoEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 序号
     */
    @ApiModelProperty(value = "序号")
    private Integer sort;

    /**
     * 内容
     */
    @ApiModelProperty(value = "内容")
    private String content;

    /**
     * 问卷投票主表ID
     */
    @ApiModelProperty(value = "问卷投票主表ID")
    private Long questionSurveyId;

    /**
     * 问卷投票题目ID
     */
    @ApiModelProperty(value = "问卷投票题目ID")
    private Long questionTopicId;

    /**
     * 是否删除（0:不删除，1：删除）
     */
    @ApiModelProperty(value = "是否删除（0:不删除，1：删除）")
    private int deleted;


}
