package com.ctsi.question.entity.dto;

import com.ctsi.hndx.common.BaseDtoEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 问卷投票表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-11
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value="BizQuestionSurveyDTO对象", description="问卷投票表")
public class BizQuestionSurveyCountDTO extends BaseDtoEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 问卷标题
     */
    @ApiModelProperty(value = "问卷标题")
    private String title;

    /**
     * 调查说明
     */
    @ApiModelProperty(value = "调查说明")
    private String surveyTip;

    /**
     * 截止时间
     */
    @ApiModelProperty(value = "截止时间")
    private LocalDateTime lastTime;

    /**
     * 是否发布
     */
    @ApiModelProperty(value = "是否发布")
    private String isRelease;

    /**
     * 发布时间
     */
    @ApiModelProperty(value = "发布时间")
    private LocalDateTime releaseDate;

    /**
     * 0:问卷;1:投票
     */
    @ApiModelProperty(value = "0:问卷;1:投票")
    private Integer type;

    /**
     * 已填人数
     */
    @ApiModelProperty(value = "已填人数")
    private Integer completedCount;

    /**
     * 未填人数
     */
    @ApiModelProperty(value = "未填人数")
    private Integer notCompletedCount;

    /**
     * 投票问卷题目
     * */
    @ApiModelProperty(value = "投票问卷题目集合")
    private List<BizQuestionTopicCountDTO> bizQuestionTopicCountList = new ArrayList<BizQuestionTopicCountDTO>();

}
