package com.ctsi.question.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ctsi.hndx.utils.BeanConvertUtils;
import com.ctsi.hndx.utils.DateUtils;
import com.ctsi.hndx.utils.ListCopyUtil;
import com.ctsi.question.entity.BizQuestionPeople;
import com.ctsi.question.entity.BizQuestionTopic;
import com.ctsi.question.entity.dto.BizQuestionPeopleDTO;
import com.ctsi.question.entity.dto.BizQuestionTopicDTO;
import com.ctsi.question.service.IBizQuestionPeopleService;
import com.ctsi.ssdc.model.PageResult;
import com.ctsi.question.entity.BizQuestionResponse;
import com.ctsi.question.entity.dto.BizQuestionResponseDTO;
import com.ctsi.question.mapper.BizQuestionResponseMapper;
import com.ctsi.question.service.IBizQuestionResponseService;
import com.ctsi.hndx.common.SysBaseServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.utils.PageHelperUtil;
import com.ctsi.ssdc.security.CscpUserDetail;
import com.ctsi.ssdc.security.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import org.springframework.transaction.annotation.Transactional;

/**
 * <p>
 * 问卷投票题目填报信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-11
 */
@Slf4j
@Service
public class BizQuestionResponseServiceImpl extends SysBaseServiceImpl<BizQuestionResponseMapper, BizQuestionResponse> implements IBizQuestionResponseService {

    @Autowired
    private BizQuestionResponseMapper bizQuestionResponseMapper;
    @Autowired
    private IBizQuestionPeopleService bizQuestionPeopleService;
    /**
     * 翻页
     *
     * @param entityDTO
     * @param basePageForm
     * @return
     */
    @Override
    public PageResult<BizQuestionResponseDTO> queryListPage(BizQuestionResponseDTO entityDTO, BasePageForm basePageForm) {
        //设置条件
        LambdaQueryWrapper<BizQuestionResponse> queryWrapper = new LambdaQueryWrapper();

        IPage<BizQuestionResponse> pageData = bizQuestionResponseMapper.selectPage(
             PageHelperUtil.getMPlusPageByBasePage(basePageForm), queryWrapper);
        //返回
        IPage<BizQuestionResponseDTO> data  = pageData.convert(entity -> BeanConvertUtils.copyProperties(entity,BizQuestionResponseDTO.class));

        return new PageResult<BizQuestionResponseDTO>(data.getRecords(),
            data.getTotal(), data.getCurrent());
    }

    /**
     * 列表查询
     *
     * @param entityDTO
     * @return
     */
    @Override
    public List<BizQuestionResponseDTO> queryList(BizQuestionResponseDTO entityDTO) {
        LambdaQueryWrapper<BizQuestionResponse> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.orderByDesc(BizQuestionResponse::getCreateTime);
        if(entityDTO.getCreateBy()!=null){
            queryWrapper.eq(BizQuestionResponse::getCreateBy, entityDTO.getCreateBy());
        }
        if(null!=entityDTO.getQuestionTopicId()){
            queryWrapper.eq(BizQuestionResponse::getQuestionTopicId, entityDTO.getQuestionTopicId());
        }
        if(null!=entityDTO.getResponseOption()){
            queryWrapper.eq(BizQuestionResponse::getResponseOption, entityDTO.getResponseOption());
        }
        if(null!=entityDTO.getQuestionSurveyId()){
            queryWrapper.eq(BizQuestionResponse::getQuestionSurveyId, entityDTO.getQuestionSurveyId());
        }
        List<BizQuestionResponse> listData = bizQuestionResponseMapper.selectList(queryWrapper);
        List<BizQuestionResponseDTO> BizQuestionResponseDTOList = ListCopyUtil.copy(listData, BizQuestionResponseDTO.class);
        return BizQuestionResponseDTOList;
    }

    /**
     * 通过条件删除
     *
     * @param entityDTO
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteByEntity(BizQuestionResponseDTO entityDTO) {
        LambdaQueryWrapper<BizQuestionResponse> queryWrapper = new LambdaQueryWrapper();
        if(null!=entityDTO.getQuestionTopicId()){
            queryWrapper.eq(BizQuestionResponse::getQuestionTopicId, entityDTO.getQuestionTopicId());
        }
        if(null!=entityDTO.getResponseOption()){
            queryWrapper.eq(BizQuestionResponse::getResponseOption, entityDTO.getResponseOption());
        }
        if(null!=entityDTO.getQuestionSurveyId()){
            queryWrapper.eq(BizQuestionResponse::getQuestionSurveyId, entityDTO.getQuestionSurveyId());
        }
        if(null!=entityDTO.getId()){
            queryWrapper.eq(BizQuestionResponse::getId, entityDTO.getId());
        }
        return bizQuestionResponseMapper.delete(queryWrapper);
    }

    /**
     * 单个查询
     *
     * @param id the id of the entity
     * @return
     */
    @Override
    public BizQuestionResponseDTO findOne(Long id) {
        BizQuestionResponse  bizQuestionResponse =  bizQuestionResponseMapper.selectById(id);
        return  BeanConvertUtils.copyProperties(bizQuestionResponse,BizQuestionResponseDTO.class);
    }


    /**
     * 新增
     *
     * @param entityDTO the entity to create
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public BizQuestionResponseDTO create(BizQuestionResponseDTO entityDTO) {
       BizQuestionResponse bizQuestionResponse =  BeanConvertUtils.copyProperties(entityDTO,BizQuestionResponse.class);
        save(bizQuestionResponse);
        return  BeanConvertUtils.copyProperties(bizQuestionResponse,BizQuestionResponseDTO.class);
    }

    /**
     * 修改
     *
     * @param entity the entity to update
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int update(BizQuestionResponseDTO entity) {
        BizQuestionResponse bizQuestionResponse = BeanConvertUtils.copyProperties(entity,BizQuestionResponse.class);
        return bizQuestionResponseMapper.updateById(bizQuestionResponse);
    }

    /**
     * 删除
     *
     * @param id the id of the entity
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int delete(Long id) {
        return bizQuestionResponseMapper.deleteById(id);
    }


    /**
     * 验证是否存在
     *
     * @param BizQuestionResponseId
     * @return
     */
    @Override
    public boolean existByBizQuestionResponseId(Long BizQuestionResponseId) {
        if (BizQuestionResponseId != null) {
            LambdaQueryWrapper<BizQuestionResponse> queryWrapper = new LambdaQueryWrapper();
            queryWrapper.eq(BizQuestionResponse::getId, BizQuestionResponseId);
            List<BizQuestionResponse> result = bizQuestionResponseMapper.selectList(queryWrapper);
            return result.size() > 0;
        }
        return true;
    }

    /**
    * 批量新增
    *
    */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertBatch(List<BizQuestionResponseDTO> dataList) {
        if(null!=dataList&&dataList.size()>0){
            BizQuestionResponseDTO bizQuestionResponseDTO = dataList.get(0);
            CscpUserDetail cscpUserDetail = SecurityUtils.getCurrentCscpUserDetail();
            Long questionSurveyId = bizQuestionResponseDTO.getQuestionSurveyId();
            BizQuestionPeopleDTO bizQuestionPeopleDTO = new BizQuestionPeopleDTO();
            bizQuestionPeopleDTO.setQuestionSurveyId(questionSurveyId);
            bizQuestionPeopleDTO.setUserId(cscpUserDetail.getId());
            //修改调查问卷填报人员表中，填报时间和填报状态。
            List<BizQuestionPeopleDTO> bizQuestionPeopleDTOList = bizQuestionPeopleService.queryList(bizQuestionPeopleDTO);
            if(null!=bizQuestionPeopleDTOList&&bizQuestionPeopleDTOList.size()>0){
                bizQuestionPeopleDTO = bizQuestionPeopleDTOList.get(0);
                bizQuestionPeopleDTO.setResponseTime(LocalDateTime.now());
                bizQuestionPeopleDTO.setResponse("1");
                bizQuestionPeopleService.update(bizQuestionPeopleDTO);
            }
        }
        List<BizQuestionResponse> result = ListCopyUtil.copy(dataList, BizQuestionResponse.class);
        return saveBatch(result);
    }

    /**
     * 查询填报选项条数
     *
     * @param entityDTO
     * @return
     */
    @Override
    public int queryCount(BizQuestionResponseDTO entityDTO) {
        LambdaQueryWrapper<BizQuestionResponse> queryWrapper = new LambdaQueryWrapper();
        if(entityDTO.getQuestionTopicId()!=null){
            queryWrapper.eq(BizQuestionResponse::getQuestionTopicId, entityDTO.getQuestionTopicId());
        }
        if(entityDTO.getResponseOption()!=null){
            queryWrapper.eq(BizQuestionResponse::getResponseOption, entityDTO.getResponseOption());
        }
        if(entityDTO.getQuestionSurveyId()!=null){
            queryWrapper.eq(BizQuestionResponse::getQuestionSurveyId, entityDTO.getQuestionSurveyId());
        }
        return bizQuestionResponseMapper.selectCountNoAdd(queryWrapper);
    }

    /**
     * 查询填报选项条数
     *
     * @param entityDTO
     * @return
     */
    @Override
    public List<String> queryResponseContext(BizQuestionResponseDTO entityDTO) {
        List<String> responseContextList = new ArrayList<>();
        LambdaQueryWrapper<BizQuestionResponse> queryWrapper = new LambdaQueryWrapper();
        if(entityDTO.getQuestionTopicId()!=null){
            queryWrapper.eq(BizQuestionResponse::getQuestionTopicId, entityDTO.getQuestionTopicId());
        }
        if(entityDTO.getQuestionSurveyId()!=null){
            queryWrapper.eq(BizQuestionResponse::getQuestionSurveyId, entityDTO.getQuestionSurveyId());
        }
        responseContextList =  bizQuestionResponseMapper.selectListNoAdd(queryWrapper).stream().map(i -> {
            return i.getResponseContext();
        }).distinct().collect(Collectors.toList());
        return responseContextList;
    }

}
