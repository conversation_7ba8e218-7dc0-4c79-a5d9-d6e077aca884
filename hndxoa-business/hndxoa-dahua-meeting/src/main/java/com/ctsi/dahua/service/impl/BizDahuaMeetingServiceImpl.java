package com.ctsi.dahua.service.impl;

import cn.hutool.core.map.MapBuilder;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ctsi.dahua.entity.dto.BizUserDTO;
import com.ctsi.hndx.exception.BusinessException;
import com.ctsi.hndx.tsysconfig.service.ISysConfigService;
import com.ctsi.hndx.utils.BeanConvertUtils;
import com.ctsi.dahua.entity.dto.BizUpdatePersonnelDTO;
import com.ctsi.ssdc.model.PageResult;
import com.ctsi.dahua.entity.BizDahuaMeeting;
import com.ctsi.dahua.entity.dto.BizDahuaMeetingDTO;
import com.ctsi.dahua.mapper.BizDahuaMeetingMapper;
import com.ctsi.dahua.service.IBizDahuaMeetingService;
import com.ctsi.hndx.common.SysBaseServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.utils.PageHelperUtil;
import com.ctsi.ssdc.security.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.*;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

/**
 * <p>
 * 大华会议 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-18
 */
@Slf4j
@Service
public class BizDahuaMeetingServiceImpl extends SysBaseServiceImpl<BizDahuaMeetingMapper, BizDahuaMeeting> implements IBizDahuaMeetingService {

    @Autowired
    private BizDahuaMeetingMapper bizDahuaMeetingMapper;

    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private ISysConfigService sysConfigService;

    /**
     * 创建会议
     *
     * @param bizDahuaMeetingDTO
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int createMeeting(BizDahuaMeetingDTO bizDahuaMeetingDTO) {
        //获取token
        String token = this.getToken();
        //获取创建会议接口
        String createMeetingUrl = getMeetingUrl();
        //请求头设置
        HttpHeaders httpHeaders = setHeader(token);

        //请求body
        String format = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss").format(LocalDateTime.now().plusSeconds(bizDahuaMeetingDTO.getMeetingDuration()));
        Map<String, String> cid = MapBuilder
                .create(new HashMap<String, String>(2)).put("name", bizDahuaMeetingDTO.getMeetingName()).put("endTime", format).build();
        Map<String, Object> bodyMap = MapBuilder
                .create(new HashMap<String, Object>(2)).put("method", sysConfigService.getSysConfigByCode("dahua:meeting").getValue()).put("params", cid).build();

        HttpEntity<MultiValueMap<String, String>> requestEntity = new HttpEntity(bodyMap, httpHeaders);

        //请求创建会议室接口
        ResponseEntity<String> exchange = restTemplate.exchange(createMeetingUrl, HttpMethod.POST, requestEntity, String.class);

        //获取会议id
        String result = JSON.parseObject(exchange.getBody()).getString("result");
        String uId = JSON.parseObject(result).getString("nid");

        BizDahuaMeeting bizDahuaMeeting = BeanConvertUtils.copyProperties(bizDahuaMeetingDTO, BizDahuaMeeting.class);
        //会议id
        bizDahuaMeeting.setMeetingId(uId);
        //会议结束时间
        bizDahuaMeeting.setMeetingEndTime(LocalDateTime.parse(format, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        if (Objects.isNull(bizDahuaMeeting.getMeetingStartTime())) {
            //会议开始时间
            bizDahuaMeeting.setMeetingStartTime(LocalDateTime.now());
        }

        int insert = bizDahuaMeetingMapper.insert(bizDahuaMeeting);

        return insert;
    }

    /**
     * 获取当前用户的会议
     *
     * @param basePageForm
     * @return
     */
    @Override
    public PageResult<BizDahuaMeetingDTO> queryMeeting(BasePageForm basePageForm) {

        //分页查询指定用户的会议列表
        IPage<BizDahuaMeetingDTO> page = bizDahuaMeetingMapper.selectPageNoAdd(PageHelperUtil.getMPlusPageByBasePage(basePageForm), new LambdaQueryWrapper<BizDahuaMeeting>()
                .select(BizDahuaMeeting::getRemarks, BizDahuaMeeting::getCreateName, BizDahuaMeeting::getId, BizDahuaMeeting::getMeetingName, BizDahuaMeeting::getAttendee, BizDahuaMeeting::getMeetingStartTime, BizDahuaMeeting::getCreateName, BizDahuaMeeting::getMeetingDuration, BizDahuaMeeting::getMeetingType)
                .and(i -> i.like(BizDahuaMeeting::getAttendee, SecurityUtils.getCurrentUserId()).or().eq(BizDahuaMeeting::getCreateBy, SecurityUtils.getCurrentUserId()))
                .and(i -> i.ge(BizDahuaMeeting::getMeetingEndTime, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss").format(LocalDateTime.now())))
                .orderByDesc(BizDahuaMeeting::getMeetingStartTime))
                .convert(i -> BeanConvertUtils.copyProperties(i, BizDahuaMeetingDTO.class));

        //将参会人组装成用户对象
        List<BizDahuaMeetingDTO> daHuaRecords = page.getRecords().stream().map(i -> {
            //获取到所有参会人，并分割
            String[] attendee = i.getAttendee().split(",");
            List<BizUserDTO> bizUserList = new LinkedList<>();
            //将参会人组装成对象
            Arrays.stream(attendee).forEach(v -> {
                String[] split = v.split(":");
                if (split.length != 2) {
                    throw new BusinessException("数据错误!");
                }
                BizUserDTO build = BizUserDTO.builder().id(Long.valueOf(split[0])).realName(split[1]).build();
                bizUserList.add(build);
            });
            //设置参会人对象
            i.setUserList(bizUserList);
            return i;
        }).collect(Collectors.toList());

        //放回
        return new PageResult(daHuaRecords, page.getTotal(), page.getCurrent());
    }

    /**
     * 查询单条会议数据，获取会议id
     *
     * @param id
     * @return
     */
    @Override
    public String getMeeting(Long id) {
        BizDahuaMeeting bizDahuaMeeting1 = bizDahuaMeetingMapper.selectOneNoAdd(new LambdaQueryWrapper<BizDahuaMeeting>()
                .select(BizDahuaMeeting::getId)
                .and(i -> i.like(BizDahuaMeeting::getAttendee, SecurityUtils.getCurrentUserId())
                        .or().eq(BizDahuaMeeting::getCreateBy, SecurityUtils.getCurrentUserId())).eq(BizDahuaMeeting::getId, id));
        if (Objects.isNull(bizDahuaMeeting1)) {
            throw new BusinessException("您无法进入该会议!");
        }

        //查询对应的会议信息
        BizDahuaMeeting bizDahuaMeeting = bizDahuaMeetingMapper.selectOneNoAdd(
                new LambdaQueryWrapper<BizDahuaMeeting>()
                        .select(BizDahuaMeeting::getId, BizDahuaMeeting::getMeetingId)
                        .eq(BizDahuaMeeting::getId, id)
                        .ge(BizDahuaMeeting::getMeetingEndTime, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss").format(LocalDateTime.now())));
        //判断会议是否过期
        if (Objects.isNull(bizDahuaMeeting)) {
            throw new BusinessException("该会议已过期!");
        }

        return bizDahuaMeeting.getMeetingId();
    }

    /**
     * 取消会议
     *
     * @param id
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer cancelMeeting(Long id) {
        BizDahuaMeeting bizDahuaMeeting = bizDahuaMeetingMapper.selectOneNoAdd(
                new LambdaQueryWrapper<BizDahuaMeeting>().eq(BizDahuaMeeting::getId, id)
                        .eq(BizDahuaMeeting::getCreateBy, SecurityUtils.getCurrentUserId()));

        if (Objects.isNull(bizDahuaMeeting)) {
            throw new BusinessException("你不是主持人,无法取消会议!");
        }

        //获取token
        String token = this.getToken();
        //获取创建会议接口
        String createMeetingUrl = getMeetingUrl();
        //请求头设置
        HttpHeaders httpHeaders = setHeader(token);
        //请求body
        Map<String, String> cid = MapBuilder
                .create(new HashMap<String, String>(2)).put("cid", getMeeting(id)).build();
        Map<String, Object> bodyMap = MapBuilder
                .create(new HashMap<String, Object>(2)).put("method", "call.delete").put("params", cid).build();

        HttpEntity<MultiValueMap<String, String>> requestEntity = new HttpEntity(bodyMap, httpHeaders);

        //请求删除会议室接口
        restTemplate.exchange(createMeetingUrl, HttpMethod.POST, requestEntity, String.class);

        return bizDahuaMeetingMapper.deleteById(id);
    }

    /**
     * 会议人员调整
     *
     * @param bizUpdatePersonnelDTO
     * @return
     */
    @Override
    public int updatePersonnel(BizUpdatePersonnelDTO bizUpdatePersonnelDTO) {
        //判断改会议是否结束
        this.getMeeting(bizUpdatePersonnelDTO.getId());
        BizDahuaMeeting bizDahuaMeeting = new BizDahuaMeeting();
        bizDahuaMeeting.setAttendee(bizUpdatePersonnelDTO.getAttendee());
        //修改人员
        int i = bizDahuaMeetingMapper.updateTenantId(bizDahuaMeeting,
                new LambdaQueryWrapper<BizDahuaMeeting>().eq(BizDahuaMeeting::getId, bizUpdatePersonnelDTO.getId()));
        return i;
    }

    /**
     * 获取大华token
     *
     * @return
     */
    public String getToken() {
        //获取token地址
        String tokenUrl = new StringBuilder()
                .append(sysConfigService.getSysConfigByCode("dahua:url").getValue())
                .append(sysConfigService.getSysConfigByCode("dahua:token:url").getValue()).toString();

        //在请求头信息中携带Basic认证信息(这里才是实际Basic认证传递用户名密码的方式)
        HttpHeaders headers = new HttpHeaders();
        //大华账号密码
        String accountNumber = new StringBuffer(sysConfigService.getSysConfigByCode("dahua:userName").getValue()).append(":").append(sysConfigService.getSysConfigByCode("dahua:password").getValue()).toString();
        headers.set("authorization", "Basic "
                + Base64.getEncoder().encodeToString(accountNumber.getBytes()));

        //使用get请求
        ResponseEntity<String> exchange = restTemplate.exchange(tokenUrl, HttpMethod.GET, new HttpEntity<>(null, headers), String.class);

        JSONObject jsonObject = JSON.parseObject(exchange.getBody());
        String token = jsonObject.getString("token");

        return token;
    }

    /**
     * 获取大华地址
     *
     * @return
     */
    public String getMeetingUrl() {
        //获取创建会议接口
        String createMeetingUrl = new StringBuilder()
                .append(sysConfigService.getSysConfigByCode("dahua:url").getValue())
                .append(sysConfigService.getSysConfigByCode("dahua:common").getValue()).toString();
        return createMeetingUrl;
    }

    /**
     * 设置请求头
     *
     * @param token
     * @return
     */
    public HttpHeaders setHeader(String token) {
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setBearerAuth(token);
        httpHeaders.setContentType(MediaType.parseMediaType("application/json;charset=UTF-8"));
        httpHeaders.add("Accept", MediaType.APPLICATION_JSON.toString());
        httpHeaders.add("Accept-Charset", "UTF-8");
        return httpHeaders;
    }
}
