package com.ctsi.dahua.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.ctsi.hndx.common.BaseEntity;

import java.time.LocalDateTime;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

/**
 * <p>
 * 大华会议
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-18
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@TableName("biz_dahua_meeting")
@ApiModel(value = "BizDahuaMeeting对象", description = "大华会议")
public class BizDahuaMeeting extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 会议id
     */
    @ApiModelProperty(value = "会议id")
    private String meetingId;

    /**
     * 结束时间
     */
    @ApiModelProperty(value = "结束时间")
    private LocalDateTime meetingEndTime;

    /**
     * 会议开始时间
     */
    @ApiModelProperty(value = "会议开始时间")
    private LocalDateTime meetingStartTime;

    /**
     * 会议时长
     */
    @ApiModelProperty(value = "会议时长")
    private Integer meetingDuration;

    /**
     * 会议名称
     */
    @ApiModelProperty(value = "会议名称")
    private String meetingName;

    /**
     * 参会人
     */
    @ApiModelProperty(value = "参会人")
    private String attendee;

    /**
     * 会议类型 0:立即开会 1:预约会议
     */
    @ApiModelProperty(value = "会议类型 0:立即开会 1:预约会议")
    private Integer meetingType;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remarks;
}
