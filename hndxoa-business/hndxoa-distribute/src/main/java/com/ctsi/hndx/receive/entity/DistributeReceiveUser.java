package com.ctsi.hndx.receive.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.ctsi.hndx.common.BaseEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-25
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName(value = "t_distribute_receive_user")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "TDistributeReceiveUser对象", description = "")
public class DistributeReceiveUser extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 收文id
     */
    @ApiModelProperty(value = "收文分发id")
    private Long formDataId;

    /**
     * 正文id
     */
    @ApiModelProperty(value = "正文id")
    private Long documentId;


    /**
     * 附件id
     */
    @ApiModelProperty(value = "附件id")
    private Long enclosureId;

    /**
     * 处理单id
     */
    @ApiModelProperty(value = "处理单id")
    private Long processingSheetId;


    /**
     * 是否已阅（0：已阅 1：未阅）
     */
    @ApiModelProperty(value = "是否已阅（0：已阅 1：未阅 2:已批示）")
    private String readNotRead;


    /**
     * 用户id
     */
    @ApiModelProperty(value = "用户id")
    private Long userId;


    /**
     * 用户名称
     */
    @ApiModelProperty(value = "用户名称")
    private String userName;

    /**
     * 来源业务表名
     */
    @ApiModelProperty(value = "来源业务表名")
    private String sourceBusinessTable;

    /**
     * 来源业务名称
     */
    @ApiModelProperty(value = "来源业务名称")
    private String sourceBusinessName;

    /**
     * 来源单位id
     */
    @ApiModelProperty(value = "来源单位id")
    private Long sourceCompanyId;

    /**
     * 来源单位名称
     */
    @ApiModelProperty(value = "来源单位名称")
    private String sourceCompanyName;

    /**
     * 公文类型（0：收文 1：发文）
     */
    @ApiModelProperty(value = "公文类型（0：收文 1：发文）")
    private String type;

    /**
     * 标题
     */
    @ApiModelProperty(value = "标题")
    private String title;

    @ApiModelProperty(value = "批示状态  0-未批示  1-已批示")
    private Integer instructionStatus;

    @ApiModelProperty(value = "批示时间")
    private LocalDateTime instructionTime;

    @ApiModelProperty(value = "批示文件")
    private String instructionFile;

    @ApiModelProperty(value = "是否分发处理单  0-不是 1-是")
    private Integer isProcessingSheet;
}
