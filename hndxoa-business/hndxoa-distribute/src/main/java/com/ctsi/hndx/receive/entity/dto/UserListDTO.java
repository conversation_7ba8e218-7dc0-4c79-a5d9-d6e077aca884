package com.ctsi.hndx.receive.entity.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Classname UserListDTO
 * @Description
 * @Date 2021/12/16 17:14
 */

@Data
@ApiModel(value = "分发接收用户集合", description = "")
public class UserListDTO {

    /**
     * 用户id
     */
    @ApiModelProperty(value = "用户id")
    private Long templateBusinessId;


    /**
     * 用户名称
     */
    @ApiModelProperty(value = "用户名称")
    private String templateBusinessName;

    /**
     * 用户单位
     */
    @ApiModelProperty(value = "用户单位")
    private String unitName;

    /**
     * 用户电话
     */
    @ApiModelProperty(value = "用户电话")
    private String mobile;



}
