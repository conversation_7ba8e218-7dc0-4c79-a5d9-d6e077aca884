package com.ctsi.hndx.receive.service;

import com.ctsi.hndx.common.SysBaseServiceI;
import com.ctsi.hndx.leadershipEntrustment.entity.BizLeadershipEntrustment;
import com.ctsi.hndx.leadershipEntrustment.entity.dto.BizLeadershipEntrustmentDTO;
import com.ctsi.hndx.receive.entity.dto.AuthorizationGroupDTO;
import com.ctsi.hndx.receive.entity.dto.BizQueryHhClientDTO;

import java.util.List;

/**
 * <p>
 * 领导委托 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-02-09
 */
public interface IBizLeaderLiaisonService extends SysBaseServiceI<BizLeadershipEntrustment> {


    /**
     * 查询领导和委托人.
     *
     * @param modelForm
     * @param leaderName
     * @param leaderId
     * @param condition
     * @return
     */
    AuthorizationGroupDTO queryClient(String modelForm, String leaderName, Long leaderId, Boolean condition);

    /**
     * 怀化查询责任人和对应的联络员
     *
     * @param bizQueryHhClientDTO
     * @return
     */
    AuthorizationGroupDTO queryHhClient(BizQueryHhClientDTO bizQueryHhClientDTO);

    /**
     * 怀化查询责任人 对应的联络员
     *
     * @param dutyPeopleId 责任人id
     * @return
     */
    List<BizLeadershipEntrustmentDTO> queryContactPeoplesByDutyPeopleId(Long dutyPeopleId);
}
