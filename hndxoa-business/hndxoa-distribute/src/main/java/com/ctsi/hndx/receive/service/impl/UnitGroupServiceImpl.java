package com.ctsi.hndx.receive.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ctsi.hndx.exception.BusinessException;
import com.ctsi.hndx.mybatisplus.sort.SortEnum;
import com.ctsi.hndx.receive.entity.EmpowermentCompanyGroup;
import com.ctsi.hndx.receive.entity.GroupUnit;
import com.ctsi.hndx.receive.entity.TemplateGroup;
import com.ctsi.hndx.receive.entity.dto.*;
import com.ctsi.hndx.receive.mapper.EmpowermentCompanyGroupMapper;
import com.ctsi.hndx.receive.mapper.GroupUnitMapper;
import com.ctsi.hndx.receive.mapper.TemplateGroupMapper;
import com.ctsi.hndx.receive.service.IEmpowermentCompanyGroupService;
import com.ctsi.hndx.utils.BeanConvertUtils;
import com.ctsi.hndx.utils.ListCopyUtil;
import com.ctsi.ssdc.model.PageResult;
import com.ctsi.hndx.receive.entity.UnitGroup;
import com.ctsi.hndx.receive.mapper.UnitGroupMapper;
import com.ctsi.hndx.receive.service.IUnitGroupService;
import com.ctsi.hndx.common.SysBaseServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.utils.PageHelperUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.LinkedList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import org.springframework.transaction.annotation.Transactional;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-02
 */

@Slf4j
@Service
public class UnitGroupServiceImpl extends SysBaseServiceImpl<UnitGroupMapper, UnitGroup> implements IUnitGroupService {

    @Autowired
    private UnitGroupMapper unitGroupMapper;

    @Autowired
    private GroupUnitServiceImpl groupUnitService;

    @Autowired
    private GroupUnitMapper groupUnitMapper;

    @Autowired
    private IEmpowermentCompanyGroupService empowermentCompanyGroupService;

    @Autowired
    private EmpowermentCompanyGroupMapper empowermentCompanyGroupMapper;

    @Autowired
    private TemplateGroupMapper templateGroupMapper;

    /**
     * 翻页
     *
     * @param entityDTO
     * @param basePageForm
     * @return
     */
    @Override
    public PageResult<UnitGroupDTO> queryListPage(UnitGroupDTO entityDTO, BasePageForm basePageForm) {
        //设置条件,查询单位群组基本信息
        LambdaQueryWrapper<UnitGroup> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.orderByAsc(UnitGroup::getGroupSort);
        IPage<UnitGroup> pageData = unitGroupMapper.selectPageOnlyAddTenantId(
                PageHelperUtil.getMPlusPageByBasePage(basePageForm), queryWrapper);
        //返回
        IPage<UnitGroupDTO> data = pageData.convert(entity -> BeanConvertUtils.copyProperties(entity, UnitGroupDTO.class));

        //根据群组id查询对应的用户
        //获取所有的人群组id
        List<Long> collect = data.getRecords().stream().map(i -> i.getId()).collect(Collectors.toList());
        List<UnitGroupDTO> unitGroupDTOS = null;
        if (!collect.isEmpty()) {
            //查询到对应的单位中间表信息
            List<GroupUnit> groupUnits = groupUnitMapper.selectListOnlyAddTenantId(new LambdaQueryWrapper<GroupUnit>().in(GroupUnit::getGroupId, collect));
            //查询单位群组对应的权限信息
            List<EmpowermentCompanyGroup> empowermentCompanyGroups = empowermentCompanyGroupMapper.selectListOnlyAddTenantId(new LambdaQueryWrapper<EmpowermentCompanyGroup>().in(EmpowermentCompanyGroup::getUnitGroupId, collect));
            //将单位群组和对应的单位中间表关联
            unitGroupDTOS = data.getRecords().stream().map(y -> {
                //用于存放单位群组对应的单位中间表
                List<GroupUnitDTO> groupUserList = new LinkedList();
                //用于存放单位群组对应的授权信息
                List<EmpowermentCompanyGroupDTO> empowermentCompanyGroupList = new LinkedList<>();
                //循环判断单位组，
                groupUnits.stream().forEach(s -> {
                    GroupUnitDTO groupUnitDTO = BeanConvertUtils.copyProperties(s, GroupUnitDTO.class);
                    if (y.getId().longValue() == groupUnitDTO.getGroupId().longValue()) {
                        groupUserList.add(groupUnitDTO);
                    }
                });
                //循环判断组装单位群组授权的单位
                empowermentCompanyGroups.stream().forEach(t -> {
                    EmpowermentCompanyGroupDTO empowermentCompanyGroup = BeanConvertUtils.copyProperties(t, EmpowermentCompanyGroupDTO.class);
                    if (y.getId().longValue() == empowermentCompanyGroup.getUnitGroupId().longValue()) {
                        empowermentCompanyGroupList.add(empowermentCompanyGroup);
                    }
                });

                y.setEmpowermentGroups(empowermentCompanyGroupList);
                y.setGroupCompanies(groupUserList);
                return y;
            }).collect(Collectors.toList());
        }

        return new PageResult<UnitGroupDTO>(unitGroupDTOS,
                data.getTotal(), data.getCurrent());
    }

    /**
     * 列表查询
     *
     * @param entityDTO
     * @return
     */
    @Override
    public List<UnitGroupDTO> queryList(UnitGroupDTO entityDTO) {
        LambdaQueryWrapper<UnitGroup> queryWrapper = new LambdaQueryWrapper();
        List<UnitGroup> listData = unitGroupMapper.selectList(queryWrapper);
        List<UnitGroupDTO> UnitGroupDTOList = ListCopyUtil.copy(listData, UnitGroupDTO.class);
        return UnitGroupDTOList;
    }

    /**
     * 单个查询
     *
     * @param id the id of the entity
     * @return
     */
    @Override
    public UnitGroupDTO findOne(Long id) {
        UnitGroup unitGroup = unitGroupMapper.selectById(id);
        return BeanConvertUtils.copyProperties(unitGroup, UnitGroupDTO.class);
    }


    /**
     * 新增
     *
     * @param entityDTO the entity to create
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public UnitGroupDTO create(UnitGroupDTO entityDTO) {
        //处理排序
        unitGroupMapper.updataSort(SortEnum.builder().sort(entityDTO.getGroupSort()).tableName("t_unit_group").sortName("sort").additionOrsubtraction("+").build());

        //新增单位群组基本信息
        UnitGroup unitGroup = BeanConvertUtils.copyProperties(entityDTO, UnitGroup.class);
        save(unitGroup);

        //新增单位群组对应的单位
        groupUnitService.insertBatchGroup(entityDTO, unitGroup);

        //授权
        if (!entityDTO.getEmpowermentGroups().isEmpty()) {
            empowermentCompanyGroupService.insertBatchGroup(unitGroup, entityDTO);
        }

        UnitGroupDTO unitGroupDTO = BeanConvertUtils.copyProperties(unitGroup, UnitGroupDTO.class);
        unitGroupDTO.setGroupCompanies(entityDTO.getGroupCompanies());
        return unitGroupDTO;
    }

    /**
     * 修改
     *
     * @param entity the entity to update
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int update(UnitGroupDTO entity) {
        //处理排序
        unitGroupMapper.updataSort(SortEnum.builder().sort(entity.getGroupSort()).id(entity.getId()).tableName("t_unit_group").sortName("sort").additionOrsubtraction("+").build());

        //更新单位群组基本信息
        UnitGroup unitGroup = BeanConvertUtils.copyProperties(entity, UnitGroup.class);
        int updateById = unitGroupMapper.updateById(unitGroup);

        //删除单位群组对应的单位
        groupUnitService.remove(new LambdaQueryWrapper<GroupUnit>().eq(GroupUnit::getGroupId, entity.getId()));
        int groupUnitCount = groupUnitService.insertBatchGroup(entity, unitGroup);

        //删除单位群组对应的授权信息
        empowermentCompanyGroupService.remove(new LambdaQueryWrapper<EmpowermentCompanyGroup>().eq(EmpowermentCompanyGroup::getUnitGroupId, entity.getId()));
        //新增单位群组对应的权限信息
        empowermentCompanyGroupService.insertBatchGroup(unitGroup, entity);

        return updateById > 0 && groupUnitCount > 0 ? 1 : 0;
    }

    /**
     * 删除
     *
     * @param id the id of the entity
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int delete(Long id) {

        //用于判断这个群组是否被别的模板所引用
        List<TemplateGroup> templateGroups = templateGroupMapper.selectList(new LambdaQueryWrapper<TemplateGroup>().eq(TemplateGroup::getGroupId, id));
        if (!templateGroups.isEmpty()) {
            throw new BusinessException("该群组已经被模板所引用,无法删除");
        }

        //删除单位群组基本信息
        int unitGroupCount = unitGroupMapper.deleteById(id);

        //删除单位群组对应的单位
        groupUnitService.remove(new LambdaQueryWrapper<GroupUnit>().eq(GroupUnit::getGroupId, id));
        //删除单位群组对应的授权权限
        empowermentCompanyGroupService.remove(new LambdaQueryWrapper<EmpowermentCompanyGroup>().eq(EmpowermentCompanyGroup::getUnitGroupId, id));

        return unitGroupCount > 0 ? 1 : 0;
    }


    /**
     * 验证是否存在
     *
     * @param UnitGroupId
     * @return
     */
    @Override
    public boolean existByUnitGroupId(Long UnitGroupId) {
        if (UnitGroupId != null) {
            LambdaQueryWrapper<UnitGroup> queryWrapper = new LambdaQueryWrapper();
            queryWrapper.eq(UnitGroup::getId, UnitGroupId);
            List<UnitGroup> result = unitGroupMapper.selectList(queryWrapper);
            return result.size() > 0;
        }
        return true;
    }

    /**
     * 批量新增
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertBatch(List<UnitGroupDTO> dataList) {
        List<UnitGroup> result = ListCopyUtil.copy(dataList, UnitGroup.class);
        return saveBatch(result);
    }

    /**
     * 判断单位群组排序号是否存在
     *
     * @param sort
     * @return
     */
    @Override
    public Boolean checkUserSortExist(Integer sort) {
        Integer integer = unitGroupMapper.selectCountOnlyAddTenantId(new LambdaQueryWrapper<UnitGroup>().eq(UnitGroup::getGroupSort, sort));
        return integer >= 1 ? true : false;
    }

    /**
     * 租户管理员分页查询群组单位
     *
     * @param groupId
     * @param basePageForm
     * @return
     */
    @Override
    public PageResult<GroupUnitDTO> queryGroupUnitPage(Long groupId, BasePageForm basePageForm) {
        if (Objects.isNull(groupId)) {
            throw new BusinessException("id不能为空！");
        }

        IPage<GroupUnit> page = groupUnitMapper.selectPageOnlyAddTenantId(PageHelperUtil.getMPlusPageByBasePage(basePageForm),
                new LambdaQueryWrapper<GroupUnit>().eq(GroupUnit::getGroupId, groupId));
        //返回
        IPage<GroupUnitDTO> data = page.convert(entity -> BeanConvertUtils.copyProperties(entity, GroupUnitDTO.class));

        return new PageResult(data.getRecords(),
                data.getTotal(), data.getCurrent());
    }

    /**
     * 租户管理员分页查询授权单位
     *
     * @param groupId
     * @param basePageForm
     * @return
     */
    @Override
    public PageResult<EmpowermentCompanyGroupDTO> queryAuthorizedUnitPage(Long groupId, BasePageForm basePageForm) {
        if (Objects.isNull(groupId)) {
            throw new BusinessException("id不能为空！");
        }

        IPage page = empowermentCompanyGroupMapper.selectPageOnlyAddTenantId(PageHelperUtil.getMPlusPageByBasePage(basePageForm),
                new LambdaQueryWrapper<EmpowermentCompanyGroup>().eq(EmpowermentCompanyGroup::getUnitGroupId, groupId));
        //返回
        IPage<EmpowermentCompanyGroupDTO> data = page.convert(entity -> BeanConvertUtils.copyProperties(entity, EmpowermentCompanyGroupDTO.class));

        return new PageResult(data.getRecords(),
                data.getTotal(), data.getCurrent());
    }


}
