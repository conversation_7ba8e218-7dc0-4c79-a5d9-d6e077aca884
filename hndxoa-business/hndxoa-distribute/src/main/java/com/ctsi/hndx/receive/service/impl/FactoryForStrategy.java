package com.ctsi.hndx.receive.service.impl;

import com.ctsi.hndx.receive.service.DistributeAbstract;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;


/**
 * <AUTHOR>
 * @Classname FactoryForStrategy
 * @Description
 * @Date 2021/12/23 11:07
 */
@Service
public class FactoryForStrategy {

    @Autowired
    Map<String, DistributeAbstract> strategyList = new ConcurrentHashMap<>(20);


    public DistributeAbstract getStrategy(String component) {
        DistributeAbstract strategy = null;

        // 普通分发
        strategy = strategyList.get(component);
        if (Objects.isNull(strategy)) {
            strategy = strategyList.get("defaultDistribute");
        }

        return strategy;
    }
}
