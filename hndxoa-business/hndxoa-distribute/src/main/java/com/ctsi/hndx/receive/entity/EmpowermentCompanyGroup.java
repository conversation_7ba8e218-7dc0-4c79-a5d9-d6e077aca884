package com.ctsi.hndx.receive.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.ctsi.hndx.common.BaseEntity;
import com.ctsi.hndx.common.ProcessBusinessBaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-02
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "t_empowerment_company_group")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "EmpowermentCompanyGroup对象", description = "")
public class EmpowermentCompanyGroup extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 单位群组id
     */
    @ApiModelProperty(value = "人群组id")
    private Long unitGroupId;

    /**
     * 单位群组名称
     */
    @ApiModelProperty(value = "人群组名称")
    private String unitGroupName;

    /**
     * 单位id
     */
    @ApiModelProperty(value = "单位id")
    private Long unitId;

    /**
     * 单位名称
     */
    @ApiModelProperty(value = "单位名称")
    private String unitName;


}
