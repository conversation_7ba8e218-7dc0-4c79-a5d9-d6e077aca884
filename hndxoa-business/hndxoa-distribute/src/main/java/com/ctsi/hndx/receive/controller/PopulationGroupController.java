package com.ctsi.hndx.receive.controller;

import com.ctsi.hndx.receive.entity.dto.EmpowermentPopulationGroupDTO;
import com.ctsi.hndx.receive.entity.dto.GroupPopulationDTO;
import com.ctsi.hndx.receive.service.IGroupPopulationService;
import com.ctsi.ssdc.model.PageResult;

import java.util.List;

import com.ctsi.hndx.receive.entity.dto.PopulationGroupDTO;
import com.ctsi.hndx.receive.service.IPopulationGroupService;
import com.ctsi.ssdc.model.ResResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import com.ctsi.hndx.common.BasePageForm;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.util.Assert;
import com.ctsi.hndx.common.BaseController;
import com.ctsi.hndx.annotations.ResponseResultVo;
import com.ctsi.hndx.result.ResultCode;
import com.ctsi.hndx.result.ResultVO;
import com.ctsi.ssdc.annotation.OperationLog;
import com.ctsi.hndx.enums.DBOperation;


/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-29
 */

@Slf4j
@RestController
@ResponseResultVo
@RequestMapping("/api/populationGroup")
@Api(value = "分发个人群组管理", tags = "分发个人群组管理接口")
public class PopulationGroupController extends BaseController {

    private static final String ENTITY_NAME = "tThisUnitGroup";

    @Autowired
    private IPopulationGroupService tThisUnitGroupService;

    @Autowired
    private IGroupPopulationService iGroupPopulationService;


    /**
     * 租户管理员新增分发个人群组数据.
     */
    @PostMapping("/create")
    @ApiOperation(value = "租户管理员新增分发个人群组数据", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.ADD, message = "新增数据")
    public ResultVO<PopulationGroupDTO> create(@RequestBody PopulationGroupDTO populationGroupDTO) {
        PopulationGroupDTO result = tThisUnitGroupService.create(populationGroupDTO);
        return ResultVO.success(result);
    }

    /**
     * 租户管理员修改个人群组数据.
     */
    @PostMapping("/update")
    @ApiOperation(value = "租户管理员修改个人群组数据", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.UPDATE, message = "租户管理员修改个人群组数据")
    public ResultVO update(@RequestBody PopulationGroupDTO populationGroupDTO) {
        Assert.notNull(populationGroupDTO.getId(), "general.IdNotNull");
        int count = tThisUnitGroupService.update(populationGroupDTO);
        return ResultVO.success();

    }

    /**
     * 租户管理员删除个人群组信息.
     */
    @DeleteMapping("/delete/{id}")
    @OperationLog(dBOperation = DBOperation.DELETE, message = "租户管理员删除个人群组信息")
    @ApiOperation(value = "租户管理员删除个人群组信息", notes = "传入参数")
    public ResultVO delete(@PathVariable Long id) {
        int count = tThisUnitGroupService.delete(id);
        if (count > 0) {
            return ResultVO.success();
        } else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }


    /**
     * 租户管理员分页查询个人群组信息.
     */
    @GetMapping("/queryTThisUnitGroupPage")
    @ApiOperation(value = "租户管理员分页查询个人群组信息", notes = "传入参数")
    public ResultVO<PageResult<PopulationGroupDTO>> queryTThisUnitGroupPage(PopulationGroupDTO populationGroupDTO, BasePageForm basePageForm) {
        return ResultVO.success(tThisUnitGroupService.queryListPage(populationGroupDTO, basePageForm));
    }


    /**
     * 查询人群对应的用户数据
     */
    @GetMapping("/queryPeoplePage/{groupId}")
    @ApiOperation(value = "查询人群对应的用户数据", notes = "传入参数")
    public ResultVO<PageResult<GroupPopulationDTO>> queryPeoplePage(@PathVariable Long groupId, BasePageForm basePageForm) {
        return ResultVO.success(tThisUnitGroupService.queryPeoplePage(groupId, basePageForm));
    }

    /**
     * 查询人群对应的授权单位
     */
    @GetMapping("/queryAuthorizedUnitPage/{groupId}")
    @ApiOperation(value = "查询人群对应的授权单位", notes = "传入参数")
    public ResultVO<PageResult<EmpowermentPopulationGroupDTO>> queryAuthorizedUnitPage(@PathVariable Long groupId, BasePageForm basePageForm) {
        return ResultVO.success(tThisUnitGroupService.queryAuthorizedUnitPage(groupId, basePageForm));
    }

    /**
     * 租户管理员分页查询个人群组信息.
     */
    @PostMapping("/updateGroupPopulationUserInfo")
    @ApiOperation(value = "通过userid修改人员信息（姓名，电话，职务）", notes = "传入参数")
    public ResultVO updateGroupPopulationUserInfo(@RequestBody GroupPopulationDTO groupPopulationDTO) {
        return ResultVO.success(iGroupPopulationService.updateGroupPopulationUserInfo(groupPopulationDTO));
    }

}
