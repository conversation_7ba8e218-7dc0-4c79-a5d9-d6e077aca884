package com.ctsi.hndx.receive.entity.dto;

import com.ctsi.hndx.common.BaseDtoEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.util.LinkedList;
import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-29
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "TGroupUserDTO对象", description = "")
public class GroupPopulationDTO extends BaseDtoEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 本单位群组id
     */
    @ApiModelProperty(value = "本单位群组id")
    private Long groupId;

    /**
     * 本单位群组名称
     */
    @ApiModelProperty(value = "本单位群组名称")
    private String groupName;


    /**
     * 用户id
     */
    @ApiModelProperty(value = "用户id")
    private Long templateBusinessId;

    /**
     * 用户名称
     */
    @ApiModelProperty(value = "用户名称")
    private String templateBusinessName;


    /**
     * 手机号码
     */
    @ApiModelProperty(value = "手机号码")
    private String mobile;


    /**
     * 单位名称
     */
    @ApiModelProperty(value = "单位名称")
    private String unitName;

    /**
     * 单位id
     */
    @ApiModelProperty(value = "单位id")
    private String unitId;

    /**
     * 部门名称
     */
    @ApiModelProperty(value = "部门名称")
    private String branchName;

    /**
     * 部门id
     */
    @ApiModelProperty(value = "部门id")
    private String branchId;

    /**
     * 职务
     */
    @ApiModelProperty(value = "职务")
    private String post;

    /**
     * 委托人
     */
    @ApiModelProperty(value = "委托人")
    private List<GroupPopulationDTO> groupPopulationDTO = new LinkedList<>();
}
