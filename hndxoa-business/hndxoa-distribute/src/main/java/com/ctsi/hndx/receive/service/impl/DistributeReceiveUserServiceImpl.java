package com.ctsi.hndx.receive.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.common.SysBaseServiceImpl;
import com.ctsi.hndx.enums.DocumentFormat;
import com.ctsi.hndx.exception.BusinessException;
import com.ctsi.hndx.receive.entity.DistributeReceiveUser;
import com.ctsi.hndx.receive.entity.Inbox;
import com.ctsi.hndx.receive.entity.dto.*;
import com.ctsi.hndx.receive.mapper.DistributeReceiveUserMapper;
import com.ctsi.hndx.receive.mapper.InboxMapper;
import com.ctsi.hndx.receive.service.IDistributeReceiveUserService;
import com.ctsi.hndx.result.ResultVO;
import com.ctsi.hndx.utils.*;
import com.ctsi.operation.domain.CscpDocumentFile;
import com.ctsi.operation.service.CscpDocumentFileService;
import com.ctsi.ssdc.admin.domain.dto.CscpUserDTO;
import com.ctsi.ssdc.admin.service.CscpOrgService;
import com.ctsi.ssdc.model.PageResult;
import com.ctsi.ssdc.security.SecurityUtils;
import com.ctsi.ssdc.util.SpringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Classname IDistributionModeService
 * @Description
 * @Date 2021/11/25 10:45
 */

@Slf4j
@Service
public class DistributeReceiveUserServiceImpl extends SysBaseServiceImpl<DistributeReceiveUserMapper, DistributeReceiveUser> implements IDistributeReceiveUserService {

    @Autowired
    private DistributeReceiveUserMapper distributeReceiveUserMapper;

    @Autowired
    private InboxMapper inboxMapper;

    @Autowired
    private FactoryForStrategy factoryForStrategy;

    @Autowired
    private CscpOrgService cscpOrgService;

    @Autowired
    private CscpDocumentFileService cscpDocumentFileService;

    /**
     * 分页查询待阅和已阅收文
     *
     * @param entityDTO
     * @param basePageForm
     * @return
     */
    @Override
    public PageResult<DistributeReceiveUserDTO> queryListPage(DistributeReceiveUserDTO entityDTO, BasePageForm basePageForm) {

//        //获取到当前用户未阅和已阅的收文id
//        IPage<DistributeReceiveUserDTO> distributeReceiveUserCount = distributeReceiveUserMapper.selectPageNoAdd(
//                //分页
//                PageHelperUtil.getMPlusPageByBasePage(basePageForm),
//                new LambdaQueryWrapper<DistributeReceiveUser>()
//                        //根据当前用户id查询对应的数据(必传)
//                        .eq(DistributeReceiveUser::getUserId, SecurityUtils.getCurrentUserId())
//                        //待阅和未阅(必传)
//                        .eq(DistributeReceiveUser::getReadNotRead, entityDTO.getReadNotRead())
//                        //批示状态  0-未批示  1-已批示
//                        .eq(DistributeReceiveUser::getInstructionStatus, entityDTO.getInstructionStatus())
////                        .eq(DistributeReceiveUser::getInstructionStatus, entityDTO.getInstructionStatus())
//                        //条件标题
//                        .like(StringUtils.isNotEmpty(entityDTO.getTitle()), DistributeReceiveUser::getTitle, entityDTO.getTitle())
//                        //来源单位
//                        .like(StringUtils.isNotEmpty(entityDTO.getSourceBusinessName()), DistributeReceiveUser::getSourceBusinessName, entityDTO.getSourceBusinessName())
//                        //类型
//                        .eq(StringUtils.isNotNull(entityDTO.getType()), DistributeReceiveUser::getType, entityDTO.getType())
//                        //日期
//                        .ge(StringUtils.isNotEmpty(entityDTO.getStartTime()), DistributeReceiveUser::getCreateTime, entityDTO.getStartTime())
//                        .le(StringUtils.isNotEmpty(entityDTO.getEndTime()), DistributeReceiveUser::getCreateTime, entityDTO.getEndTime())
//                        //时间排序
//                        .orderByDesc(DistributeReceiveUser::getCreateTime)
//        ).convert(i -> BeanConvertUtils.copyProperties(i, DistributeReceiveUserDTO.class));

        entityDTO.setUserId(SecurityUtils.getCurrentUserId());
        IPage<DistributeReceiveUserDTO> v1 = distributeReceiveUserMapper.queryListPage(PageHelperUtil.getMPlusPageByBasePage(basePageForm), entityDTO);

        //放回
        return new PageResult<DistributeReceiveUserDTO>(v1.getRecords(),
                v1.getTotal(), v1.getCurrent());
    }


    /**
     * 获取待阅角标数
     *
     * @return
     */
    @Override
    public Integer getAngleMarkDistribute() {
        LambdaQueryWrapper<DistributeReceiveUser> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.eq(DistributeReceiveUser::getUserId,SecurityUtils.getCurrentUserId());
        queryWrapper.eq(DistributeReceiveUser::getReadNotRead,1);
        return distributeReceiveUserMapper.selectCountNoAdd(queryWrapper);
    }


    /**
     * 列表查询
     *
     * @param entityDTO
     * @return
     */
    @Override
    public List<DistributeReceiveUserDTO> queryList(DistributeReceiveUserDTO entityDTO) {
        LambdaQueryWrapper<DistributeReceiveUser> queryWrapper = new LambdaQueryWrapper();
        List<DistributeReceiveUser> listData = distributeReceiveUserMapper.selectList(queryWrapper);
        List<DistributeReceiveUserDTO> DistributeReceiveUserDTOList = ListCopyUtil.copy(listData, DistributeReceiveUserDTO.class);
        return DistributeReceiveUserDTOList;
    }

    /**
     * 单个查询
     *
     * @param id the id of the entity
     * @return
     */
    @Override
    public DistributeReceiveUserDTO findOne(Long id) {
        DistributeReceiveUser tDistributeReceive = distributeReceiveUserMapper.selectById(id);
        return BeanConvertUtils.copyProperties(tDistributeReceive, DistributeReceiveUserDTO.class);
    }


    /**
     * 分发收文到个人或外部单位收件箱.
     *
     * @param dcp the entity to create
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer distribute(DistributeCompanyPopulationDTO dcp) {
        dcp.getUserIdList().stream().collect(Collectors.toMap(UserListDTO::getTemplateBusinessId, user -> user, (u1, u2) -> u1))
                .values()
                .stream()
                .collect(Collectors.toList());
        return factoryForStrategy.getStrategy(dcp.getSourceBusinessTable()).judge(dcp);
    }

    /**
     * 修改
     *
     * @param entity the entity to update
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int update(DistributeReceiveUserDTO entity) {
        DistributeReceiveUser tDistributeReceive = BeanConvertUtils.copyProperties(entity, DistributeReceiveUser.class);
        return distributeReceiveUserMapper.updateById(tDistributeReceive);
    }

    /**
     * 删除
     *
     * @param id the id of the entity
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int delete(Long id) {
        return distributeReceiveUserMapper.deleteById(id);
    }

    /**
     * 分页查询本单位用户分发的收发文
     *
     * @param formDataId
     * @param basePageForm
     * @return
     */
    @Override
    public PageResult<QuerySelfDistributedDTO> queryInternalDocuments(Long formDataId, BasePageForm basePageForm) {
        //分页查询自己分发的数据
        IPage<QuerySelfDistributedDTO> distributeDataCount = distributeReceiveUserMapper.selectPage(
                PageHelperUtil.getMPlusPageByBasePage(basePageForm),
                new LambdaQueryWrapper<DistributeReceiveUser>()
                        //查询自己创建的
//                        .eq(DistributeReceiveUser::getCreateBy, SecurityUtils.getCurrentUserId())
                        //对应收发文的id
                        .eq(DistributeReceiveUser::getFormDataId, formDataId)
        ).convert(i -> BeanConvertUtils.copyProperties(i, QuerySelfDistributedDTO.class));

        //放回
        return new PageResult<QuerySelfDistributedDTO>(distributeDataCount.getRecords(),
                distributeDataCount.getTotal(), distributeDataCount.getCurrent());
    }

    /**
     * 撤回
     *
     * @param id
     * @return
     */
    @Override
    public Integer withdraw(Long id) {
        int delete = distributeReceiveUserMapper.delete(new LambdaQueryWrapper<DistributeReceiveUser>().eq(DistributeReceiveUser::getId, id));
        return delete;
    }


    /**
     * 分页查询当前用户分发的内部公文
     *
     * @param formDataId
     * @return
     */
    @Override
    public ExternalDocumentsDTO queryExternalDocuments(Long formDataId) {
        //根据正文id查询收件箱中对应的数据
        List<Inbox> inboxList = inboxMapper.selectList(new LambdaQueryWrapper<Inbox>().eq(Inbox::getDocumentId, formDataId));
        if (inboxList.isEmpty()) {
            return null;
        }
        Inbox inbox = inboxList.get(0);

        //收件箱基础信息
        ExternalDocumentsDTO externalDocumentsDTO = BeanConvertUtils.copyProperties(inbox, ExternalDocumentsDTO.class);
        //分发份数
        externalDocumentsDTO.setDistributeCount(inboxList.size());
        //已签收数量
        externalDocumentsDTO.setSignForCount(inboxList.stream().filter(i -> "1".equals(i.getInboxStatus())).collect(Collectors.toList()).size());


        //一、组装对应的群组和群组下面的单位信息
        //1、查询对应的群信息
        //.获取所有的单位名称
        Set<SetUnitGroupDTO> set = new HashSet();
        inboxList.stream().forEach(i -> {
            SetUnitGroupDTO sug = new SetUnitGroupDTO();
            sug.setUnitGroupId(i.getUnitGroupId());
            sug.setUnitGroupName(i.getUnitGroupName());
            set.add(sug);
        });


        Iterator<SetUnitGroupDTO> iterator = set.iterator();
        List<ExternalDocumentsGroupDTO> edg = new LinkedList<>();
        while (iterator.hasNext()) {
            SetUnitGroupDTO next = iterator.next();
            ExternalDocumentsGroupDTO build = ExternalDocumentsGroupDTO.builder().groupName(next.getUnitGroupName()).build();
            List<ExternalDocumentsGroupListDTO> edgList = new LinkedList<>();

            inboxList.stream().forEach(i -> {
                if (i.getUnitGroupId().longValue() == i.getUnitGroupId().longValue()) {
                    edgList.add(ExternalDocumentsGroupListDTO.builder()
                            .receivingTextCompanyName(i.getReceiveCompanyName())
                            .inboxStatus(i.getInboxStatus())
                            .userName(i.getOperationName())
                            .signForTime(i.getSigningTime()).build());
                }
            });
            build.setCompanyCount(edgList.size());
            build.setExternalDocumentsGroupListDTOList(edgList);
            edg.add(build);
        }
        externalDocumentsDTO.setExternalDocumentsGroupList(edg);

        return externalDocumentsDTO;
    }

    /**
     * 已阅
     *
     * @param id
     * @return
     */
    @Override
    public Integer read(Long id) {
        DistributeReceiveUser dru = distributeReceiveUserMapper
                .selectOneNoAdd(new LambdaQueryWrapper<DistributeReceiveUser>().select(DistributeReceiveUser::getId, DistributeReceiveUser::getReadNotRead).eq(DistributeReceiveUser::getId, id));
        if (!Objects.isNull(dru.getReadNotRead()) && !dru.getReadNotRead().equals("0") && !dru.getReadNotRead().equals("2")) {
            DistributeReceiveUser distributeReceiveUser = new DistributeReceiveUser();
            distributeReceiveUser.setId(id);
            distributeReceiveUser.setReadNotRead("0");
            int count = distributeReceiveUserMapper.updateById(distributeReceiveUser);
            return count;
        }
        return 1;
    }

    @Override
    public Integer owerCompanyDistribute(OwerCompanyDistributeDTO dcp) {
        String userType = dcp.getUserType();
        DistributeCompanyPopulationDTO distributeCompanyPopulationDTO = new DistributeCompanyPopulationDTO();
        BeanUtils.copyProperties(dcp,distributeCompanyPopulationDTO);
        distributeCompanyPopulationDTO.setReceiveCompanyIdList(null);
        if ("2".equals(userType)){
            List<CscpUserDTO> cscpUserDTOList = cscpOrgService.selectCompayAllUserByCompanyId(SecurityUtils.getCurrentCompanyId());
            List<UserListDTO> allUserId = new ArrayList<>();
            cscpUserDTOList.forEach(cscpUserDTO -> {
                UserListDTO userListDTO = new UserListDTO();
                userListDTO.setTemplateBusinessId(cscpUserDTO.getId());
                userListDTO.setTemplateBusinessName(cscpUserDTO.getRealName());
                allUserId.add(userListDTO);
            });
            distributeCompanyPopulationDTO.setUserIdList(allUserId);
        }else {
            throw new BusinessException("分发类型错误");
        }
        Integer distribute = SpringUtil.getBean(IDistributeReceiveUserService.class).distribute(distributeCompanyPopulationDTO);
        return distribute;
    }

    /**
     * 已批示
     *
     * @param id
     * @return
     */
    @Override
    public Integer instruction(Long id) {
        DistributeReceiveUser distributeReceiveUser = new DistributeReceiveUser();
        distributeReceiveUser.setId(id);
        distributeReceiveUser.setInstructionStatus(1);
        distributeReceiveUser.setInstructionTime(LocalDateTime.now());
        int count = distributeReceiveUserMapper.updateById(distributeReceiveUser);
        return count;
    }

}
