package com.ctsi.hndx.receive.entity.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Classname UnitListDTO
 * @Description
 * @Date 2021/12/18 23:26
 */

@Data
@ApiModel(value = "UnitListDTO", description = "")
public class UnitListDTO {

    /**
     * 接收单位id
     */
    @ApiModelProperty(value = "接收单位id")
    private Long templateBusinessId;


    /**
     * 接收单位名称
     */
    @ApiModelProperty(value = "接收单位名称")
    private String templateBusinessName;
}
