package com.ctsi.hndx.receive.entity.dto;

import com.ctsi.hndx.common.BaseDtoEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-02
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "EmpowermentCompanyGroupDTO对象", description = "")
public class EmpowermentCompanyGroupDTO extends BaseDtoEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 单位群组id
     */
    @ApiModelProperty(value = "人群组id")
    private Long unitGroupId;

    /**
     * 单位群组名称
     */
    @ApiModelProperty(value = "人群组名称")
    private String unitGroupName;

    /**
     * 单位id
     */
    @ApiModelProperty(value = "单位id")
    private Long unitId;

    /**
     * 单位名称
     */
    @ApiModelProperty(value = "单位名称")
    private String unitName;


}
