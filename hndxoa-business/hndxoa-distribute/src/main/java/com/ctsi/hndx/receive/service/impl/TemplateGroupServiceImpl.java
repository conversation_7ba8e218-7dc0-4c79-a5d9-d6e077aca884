package com.ctsi.hndx.receive.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.common.SysBaseServiceImpl;
import com.ctsi.hndx.receive.entity.DistributionTemplate;
import com.ctsi.hndx.receive.entity.TemplateGroup;
import com.ctsi.hndx.receive.entity.dto.DistributionTemplateDTO;
import com.ctsi.hndx.receive.entity.dto.TemplateGroupDTO;
import com.ctsi.hndx.receive.mapper.TemplateGroupMapper;
import com.ctsi.hndx.receive.service.ITemplateGroupService;
import com.ctsi.hndx.utils.BeanConvertUtils;
import com.ctsi.hndx.utils.ListCopyUtil;
import com.ctsi.hndx.utils.PageHelperUtil;
import com.ctsi.ssdc.model.PageResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-06
 */

@Slf4j
@Service
public class TemplateGroupServiceImpl extends SysBaseServiceImpl<TemplateGroupMapper, TemplateGroup> implements ITemplateGroupService {

    @Autowired
    private TemplateGroupMapper templateGroupMapper;

    /**
     * 翻页
     *
     * @param entityDTO
     * @param basePageForm
     * @return
     */
    @Override
    public PageResult<TemplateGroupDTO> queryListPage(TemplateGroupDTO entityDTO, BasePageForm basePageForm) {
        //设置条件
        LambdaQueryWrapper<TemplateGroup> queryWrapper = new LambdaQueryWrapper();

        IPage<TemplateGroup> pageData = templateGroupMapper.selectPage(
                PageHelperUtil.getMPlusPageByBasePage(basePageForm), queryWrapper);
        //返回
        IPage<TemplateGroupDTO> data = pageData.convert(entity -> BeanConvertUtils.copyProperties(entity, TemplateGroupDTO.class));

        return new PageResult<TemplateGroupDTO>(data.getRecords(),
                data.getTotal(), data.getCurrent());
    }

    /**
     * 列表查询
     *
     * @param entityDTO
     * @return
     */
    @Override
    public List<TemplateGroupDTO> queryList(TemplateGroupDTO entityDTO) {
        LambdaQueryWrapper<TemplateGroup> queryWrapper = new LambdaQueryWrapper();
        List<TemplateGroup> listData = templateGroupMapper.selectList(queryWrapper);
        List<TemplateGroupDTO> TemplateGroupDTOList = ListCopyUtil.copy(listData, TemplateGroupDTO.class);
        return TemplateGroupDTOList;
    }

    /**
     * 单个查询
     *
     * @param id the id of the entity
     * @return
     */
    @Override
    public TemplateGroupDTO findOne(Long id) {
        TemplateGroup templateGroup = templateGroupMapper.selectById(id);
        return BeanConvertUtils.copyProperties(templateGroup, TemplateGroupDTO.class);
    }


    /**
     * 新增
     *
     * @param entityDTO the entity to create
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public TemplateGroupDTO create(TemplateGroupDTO entityDTO) {
        TemplateGroup templateGroup = BeanConvertUtils.copyProperties(entityDTO, TemplateGroup.class);
        save(templateGroup);
        return BeanConvertUtils.copyProperties(templateGroup, TemplateGroupDTO.class);
    }

    /**
     * 修改
     *
     * @param entity the entity to update
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int update(TemplateGroupDTO entity) {
        TemplateGroup templateGroup = BeanConvertUtils.copyProperties(entity, TemplateGroup.class);
        return templateGroupMapper.updateById(templateGroup);
    }

    /**
     * 删除
     *
     * @param id the id of the entity
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int delete(Long id) {
        return templateGroupMapper.deleteById(id);
    }


    /**
     * 验证是否存在
     *
     * @param TemplateGroupId
     * @return
     */
    @Override
    public boolean existByTemplateGroupId(Long TemplateGroupId) {
        if (TemplateGroupId != null) {
            LambdaQueryWrapper<TemplateGroup> queryWrapper = new LambdaQueryWrapper();
            queryWrapper.eq(TemplateGroup::getId, TemplateGroupId);
            List<TemplateGroup> result = templateGroupMapper.selectList(queryWrapper);
            return result.size() > 0;
        }
        return true;
    }

    /**
     * 批量新增
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertBatch(List<TemplateGroupDTO> dataList) {
        List<TemplateGroup> result = ListCopyUtil.copy(dataList, TemplateGroup.class);
        return saveBatch(result);
    }

    @Override
    public int insertBatchGroup(DistributionTemplate distributionTemplate, DistributionTemplateDTO entityDTO) {
        List<TemplateGroup> collect = entityDTO.getTemplateGroupList().stream().map(i -> {
            return TemplateGroup.builder()
                    .groupId(i.getGroupId())
                    .groupName(i.getGroupName())
                    .templateId(distributionTemplate.getId())
                    .templateName(distributionTemplate.getTemplateName()).build();
        }).collect(Collectors.toList());
        return this.saveBatch(collect) ? 1 : 0;
    }


}
