package com.ctsi.sfmeeting.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.ctsi.hndx.common.BaseEntity;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 三服会议
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-02
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("t_sf_meeting")
@ApiModel(value="TSfMeeting对象", description="三服会议")
public class TSfMeeting extends BaseEntity {

    private static final long serialVersionUID = 1L;


    @ApiModelProperty(value = "<统计>顶级部门id")
    private Long topOrgId;


    @ApiModelProperty(value = "<统计>顶级部门名称")
    private String topOrgName;

    /**
     * 会议名称
     */
    @ApiModelProperty(value = "会议名称")
    private String title;

    /**
     * 会议开始时间
     */
    @ApiModelProperty(value = "会议开始时间")
    private LocalDateTime meetingStartTime;

    /**
     * 会议结束时间
     */
    @ApiModelProperty(value = "会议结束时间")
    private LocalDateTime meetingEndTime;

    private String meetingStartTimeStr;

    private String meetingEndTimeStr;

    /**
     * 地点
     */
    @ApiModelProperty(value = "地点")
    private String place;

    /**
     * 参会人
     */
    @ApiModelProperty(value = "参会人:用逗号隔开")
    private String attendeesUser;

    /**
     * 会议内容
     */
    @ApiModelProperty(value = "会议内容")
    private String content;

    /**
     * 会议状态
     */
    @ApiModelProperty(value = "会议状态: 10 待开展: 20 开展中: 30 已开展 ")
    private String status;

    /**
     * 密级code
     */
    @ApiModelProperty(value = "密级code")
    private String durationClassification;

    /**
     * 密级名称
     */
    @ApiModelProperty(value = "密级名称")
    private String durationClassificationName;

    @ApiModelProperty(value = " 1 : 会议上线 发布了; 2 : 下线了")
    private Integer pushStatus;
    @ApiModelProperty(value = "参与人员信息json")
    private String userListJson;

    @ApiModelProperty(value = "部门名称")
    private String departmentName;

}
