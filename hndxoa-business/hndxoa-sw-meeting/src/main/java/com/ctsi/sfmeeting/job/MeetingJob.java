package com.ctsi.sfmeeting.job;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ctsi.hndx.entity.TTopOrgRelation;
import com.ctsi.hndx.service.ITTopOrgRelationService;
import com.ctsi.sfmeeting.entity.TSfMeeting;
import com.ctsi.sfmeeting.service.ITSfMeetingService;
import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;


import java.time.LocalDateTime;

import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 *
 * 同步会议状态 每 1分钟执行一次
 *
 **/
@Service
@Slf4j
public class MeetingJob {
	private static final String LOCK_KEY = "sfmeetingJobLock";
	private static final String LOCK_KEY_SYNC_UPDATE_MEETING_TOP_ORG = "sfmeetingJobLock_syncUpdateMeetingTopOrg_new";
	@Autowired
	private RedisTemplate<String, Object> redisTemplate;

	@Autowired
	private com.ctsi.sfmeeting.mapper.TSfMeetingMapper meetingMapper;
	@Autowired
	private com.ctsi.sfmeeting.mapper.TSfMeetingUserMapper meetingUserMapper;
	@Autowired
	private ITSfMeetingService meetingService;


	@Autowired
	private ITTopOrgRelationService tTopOrgRelationService;



	// 同步会议状态 每 1分钟执行一次
	@Scheduled(cron ="0/69 * * * * ?")
	public void syncUpdateMeetingStatus() {

		LocalDateTime dateTime = LocalDateTime.now();
		log.info("==============同步会议状态开始============== [{}]", dateTime);
		if (redisTemplate.opsForValue().setIfAbsent(LOCK_KEY, "1", 1, TimeUnit.MINUTES)) {
			try {
				meetingService.syncUpdateMeetingStatus(dateTime);


			} catch (Exception e) {
				log.error("同步会议状态异常 异常", e);
			} finally {
				redisTemplate.delete(LOCK_KEY);
			}
		} else {
			log.info("同同步会议状态锁正被占用");
			return;
		}

	}



	// 同步顶级机构 每 5分钟执行一次
	@Scheduled(cron ="0 0/10 * * * ? ")
	public void syncUpdateMeetingTopOrg() {
		LocalDateTime dateTime = LocalDateTime.now();
		log.info("==============同步部门数据开始 [{}]==============", LocalDateTime.now());
		if (redisTemplate.opsForValue().setIfAbsent(LOCK_KEY_SYNC_UPDATE_MEETING_TOP_ORG, "1", 1, TimeUnit.MINUTES)) {

			Integer integer = tTopOrgRelationService.selectCountNoAdd(new LambdaQueryWrapper<TTopOrgRelation>());
			if(integer == null || integer.intValue() <= 0  ){
				log.info("==============初始化部门数据开始 [{}]===表TTopOrgRelation===========", LocalDateTime.now());
				tTopOrgRelationService.initData(111L);
				try {
					Thread.sleep(3000);
				} catch (InterruptedException e) {
					e.printStackTrace();
				}
				log.info("==============初始化部门数据结束 [{}]===表TTopOrgRelation===========", LocalDateTime.now());
			}

			try {
				List<TSfMeeting> tSfMeetings = meetingService.selectListNoAdd(new LambdaQueryWrapper<TSfMeeting>().isNull(TSfMeeting::getTopOrgId));
				if(!tSfMeetings.isEmpty()){
					// 更新
					boolean initFlag= false;
					for (TSfMeeting meeting : tSfMeetings) {
						// 设置顶级 统计部门 方便统计
						List<TTopOrgRelation> topOrgs =
								tTopOrgRelationService.selectListNoAdd(new LambdaQueryWrapper<TTopOrgRelation>().eq(TTopOrgRelation::getId , meeting.getDepartmentId()));
						if(!topOrgs.isEmpty()){
							TTopOrgRelation topOrg = topOrgs.get(0);
							meeting.setTopOrgId(topOrg.getTopOrgId());
							meeting.setTopOrgName(topOrg.getTopOrgName());
							meetingService.updateById(meeting);
						}else {
							initFlag = true;
							log.info("==============机构数据异常 [{}:{}]==============", meeting.getDepartmentName(),
									meeting.getDepartmentId());
						}
					}
					if(initFlag){
						tTopOrgRelationService.initData(111L);
						try {
							Thread.sleep(3000);
						} catch (InterruptedException e) {
							e.printStackTrace();
						}
						for (TSfMeeting meeting : tSfMeetings) {
							// 设置顶级 统计部门 方便统计
							List<TTopOrgRelation> topOrgs =
									tTopOrgRelationService.selectListNoAdd(new LambdaQueryWrapper<TTopOrgRelation>().eq(TTopOrgRelation::getId , meeting.getDepartmentId()));
							if(!topOrgs.isEmpty()){
								TTopOrgRelation topOrg = topOrgs.get(0);
								meeting.setTopOrgId(topOrg.getTopOrgId());
								meeting.setTopOrgName(topOrg.getTopOrgName());
								meetingService.updateById(meeting);
							}
						}

					}

				}

			} catch (Exception e) {
				log.error("同步部门数据 异常", e);
			} finally {
				redisTemplate.delete(LOCK_KEY_SYNC_UPDATE_MEETING_TOP_ORG);
			}
		} else {
			log.info("同步部门数据 锁正被占用");
			return;
		}
		log.info("==============同步部门数据结束 [{}]==============", LocalDateTime.now());

	}


}
