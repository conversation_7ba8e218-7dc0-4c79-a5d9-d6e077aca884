package com.ctsi.sfmeeting.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.ctsi.hndx.common.BaseEntity;
import java.io.Serializable;
import java.math.BigInteger;
import java.time.LocalDateTime;
import java.util.Date;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-02
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("t_sf_meeting_user")
@ApiModel(value="TSfMeetingUser对象", description="")
public class TSfMeetingUser extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 会议id
     */
    @ApiModelProperty(value = "会议id")
    private Long mid;

    private Long uid;

    /**
     * 机构id
     */
    @ApiModelProperty(value = "机构id")
    private Long orgId;

    /**
     * 部门名称
     */
    @ApiModelProperty(value = "部门名称")
    private String orgName;

    /**
     * 会议名称
     */
    @ApiModelProperty(value = "会议名称")
    private String title;

    /**
     * 会议开始时间
     */
    @ApiModelProperty(value = "会议开始时间")
    private String meetingStartTimeStr;

    /**
     * 会议结束时间
     */
    @ApiModelProperty(value = "会议结束时间")
    private String meetingEndTimeStr;

    /**
     * 会议开始时间
     */
    @ApiModelProperty(value = "会议开始时间")
    private LocalDateTime meetingStartTime;

    /**
     * 会议结束时间
     */
    @ApiModelProperty(value = "会议结束时间")
    private LocalDateTime meetingEndTime;


    /**
     * 地点
     */
    @ApiModelProperty(value = "地点")
    private String place;

    /**
     * 参会人员
     */
    @ApiModelProperty(value = "参会人员")
    private String attendeesUser;


    private String userName;

    private LocalDateTime firstOpenTime;

    private String replyContent;

    /**
     * 会议状态
     */
    @ApiModelProperty(value = "会议状态: 10 待开展: 20 开展中: 30 已开展 ")
    private String status;

    /**
     * 签收状态: \"10\" 已签收
     */
    @ApiModelProperty(value = "签收状态: \"10\" 已签收 ; 0 未签收")
    private String notSigned;

    /**
     * 签收时间
     */
    @ApiModelProperty(value = "签收时间")
    private LocalDateTime signedTime;

    @ApiModelProperty(value = " 1 : 会议上线 发布了; 2 : 下线了")
    private Integer pushStatus;


    @ApiModelProperty(value = " 1: 回复了附件; 0 : 没有回复附件 ")
    private Integer replayAnnexFlag;

    /**
     * 密级code
     */
    @ApiModelProperty(value = "密级code")
    private String durationClassification;

    /**
     * 密级名称
     */
    @ApiModelProperty(value = "密级名称")
    private String durationClassificationName;


    @ApiModelProperty(value = "是否打开: \"10\" 已打开 ; 0 未打开")
    private String notOpen;
}
