package com.ctsi.outingask.entity.dto;

import com.ctsi.hndx.common.BaseDtoEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <p>
 * 外出统计
 * </p>
 *
 * <AUTHOR>
 * @since 2022-12-29
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value="BizOutingAskStatisticsDTO 对象", description="外出统计")
public class BizOutingAskStatisticsDTO extends BaseDtoEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 外出领导
     */
    @ApiModelProperty(value = "外出领导")
    private String outingLeader;

    /**
     * 外出领导单位
     */
    @ApiModelProperty(value = "外出领导单位")
    private String outingLeaderDepartmentName;

    /**
     * 外出领导职务
     */
    @ApiModelProperty(value = "外出领导职务")
    private String outingLeaderDuty;

    /**
     * 外出次数
     */
    @ApiModelProperty(value = "外出次数")
    private Integer outingCounts;

    /**
     * 外出天数
     */
    @ApiModelProperty(value = "外出天数")
    private Integer outingDays;
}
