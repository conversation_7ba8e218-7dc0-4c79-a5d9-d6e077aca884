package com.ctsi.fusion.service;

import com.ctsi.ssdc.admin.domain.dto.CscpOrgDTO;
import com.ctsi.ssdc.admin.domain.dto.CscpUserDTO;
import com.ctsi.ssdc.admin.entity.dto.TYJGSyncUserOrgIdDTO;

import java.util.List;

public interface IForeignOrgUserService {

    List<CscpOrgDTO> selectPullCscpOrg(Long appId, List<TYJGSyncUserOrgIdDTO> list);

    List<CscpUserDTO> selectPullCscpUser(List<TYJGSyncUserOrgIdDTO> list);
}
