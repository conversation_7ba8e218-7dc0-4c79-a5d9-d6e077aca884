package com.ctsi.fusion.controller;

import com.ctsi.fusion.common.SyncPushUtils;
import com.ctsi.hndx.annotations.ResponseResultVo;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.enums.DBOperation;
import com.ctsi.hndx.result.ResultVO;
import com.ctsi.ssdc.admin.domain.CscpOrgChangeHistory;
import com.ctsi.ssdc.admin.domain.dto.CscpOrgAuditRemoveDTO;
import com.ctsi.ssdc.admin.service.CscpOrgChangeHistoryService;
import com.ctsi.ssdc.annotation.ImpowerLog;
import com.ctsi.ssdc.annotation.OperationLog;
import com.ctsi.ssdc.model.PageResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;

@RestController
@ResponseResultVo
@RequestMapping("/api/system")
@Api(value = "组织机构-机构变更记录管理", tags = "组织机构-机构变更记录管理")
public class SysOrgChangeHistoryController {

    @Autowired
    private CscpOrgChangeHistoryService cscpOrgChangeHistoryService;

    @Autowired
    private SyncPushUtils syncPushUtils;

    @ApiOperation(value = "机构-变更记录-删除")
    @DeleteMapping("/orgChangeHistory/delete")
    public ResultVO<CscpOrgChangeHistory> deleteHistory(@RequestBody CscpOrgChangeHistory dto) {
        cscpOrgChangeHistoryService.removeById(dto.getId());
        return ResultVO.success();
    }

    @ApiOperation(value = "机构-查询变更记录")
    @GetMapping("/orgChangeHistory/page")
    public ResultVO<PageResult<CscpOrgChangeHistory>> pageCscpOrgChangeHistory(CscpOrgChangeHistory dto, BasePageForm basePageForm) {
        return ResultVO.success(cscpOrgChangeHistoryService.pageCscpOrgChangeHistory(dto, basePageForm));
    }

    @ApiOperation(value = "机构-推送变更记录")
    @PostMapping("/orgChangeHistory/push")
    public ResultVO<CscpOrgChangeHistory> pushOrgChangeHistory(@RequestBody CscpOrgChangeHistory dto) {
        List<Long> ids = dto.parsePosterityIds();
        syncPushUtils.changeHisSyncBusiness(1, "update", ids, dto.getId());
        return ResultVO.success();
    }
}
