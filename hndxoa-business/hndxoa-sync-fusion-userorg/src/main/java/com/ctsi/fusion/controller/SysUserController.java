package com.ctsi.fusion.controller;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.net.URLDecoder;
import cn.hutool.extra.pinyin.PinyinUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ctsi.fusion.common.SyncPushUtils;
import com.ctsi.hndx.addrbook.service.ITAddressBookService;
import com.ctsi.hndx.annotations.ResponseResultVo;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.constant.HeaderConstants;
import com.ctsi.hndx.constant.SysConfigConstant;
import com.ctsi.hndx.constant.UserConstant;
import com.ctsi.hndx.enums.DBOperation;
import com.ctsi.hndx.enums.SystemRole;
import com.ctsi.hndx.exception.BusinessException;
import com.ctsi.hndx.result.ResultVO;
import com.ctsi.hndx.tsysconfig.service.ISysConfigService;
import com.ctsi.hndx.utils.IpUtil;
import com.ctsi.hndx.westone.WestoneEncryptService;
import com.ctsi.hndxoa.service.ITSyncAppSystemManageService;
import com.ctsi.ssdc.admin.domain.CscpUser;
import com.ctsi.ssdc.admin.domain.dto.*;
import com.ctsi.ssdc.admin.entity.CscpUserLockRecord;
import com.ctsi.ssdc.admin.repository.CscpUserLockRecordMapper;
import com.ctsi.ssdc.admin.service.CscpUserOrgService;
import com.ctsi.ssdc.admin.service.CscpUserPasswordChangeLogService;
import com.ctsi.ssdc.admin.service.CscpUserService;
import com.ctsi.ssdc.admin.service.impl.CscpUserServiceImpl;
import com.ctsi.ssdc.annotation.OperationLog;
import com.ctsi.ssdc.dto.QuerySystemLogDTO;
import com.ctsi.ssdc.dto.QueryUserExamineDTO;
import com.ctsi.ssdc.dto.QueryUserLockDTO;
import com.ctsi.ssdc.model.PageResult;
import com.ctsi.ssdc.security.SecurityUtils;
import com.ctsi.ssdc.security.jwt.JWTConfigurer;
import com.ctsi.ssdc.security.jwt.TokenProvider;
import com.ctsi.ssdc.util.RedisUtil;
import com.ctsi.ssdc.vo.*;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.zdww.biyi.component.sdk.aop.BeanExposeMethodAble;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.beans.propertyeditors.CustomDateEditor;
import org.springframework.http.HttpHeaders;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URISyntaxException;
import java.nio.charset.StandardCharsets;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

import static com.ctsi.ssdc.admin.consts.ComponentConstant.ADMIN;
import static com.ctsi.ssdc.admin.consts.ComponentConstant.METHOD;

@RestController
@RequestMapping("/api/system")
@Api(value = "用户管理接口", tags = "用户管理接口")
@Slf4j
public class SysUserController {

    private static final String ENTITY_NAME = "cscpUser";

    private final CscpUserService cscpUserService;

    @Value("${ctsi.RSA-prikey:}")
    private String rsaPrikey = "";

    @Value("${ctsi.password-check.check-min-length}")
    private String minLength;

    @Value("${ctsi.password-check.check-max-length}")
    private String maxLength;

    @Value("${ctsi.password-check.check-lowercase}")
    private boolean lowercase;

    @Value("${ctsi.password-check.check-uppercase}")
    private boolean uppercase;

    @Value("${ctsi.password-check.check-digit}")
    private boolean digit;

    @Value("${ctsi.password-check.check-special-character}")
    private boolean specialChar;

    @Autowired
    CscpUserPasswordChangeLogService cscpUserPasswordChangeLogService;

    @Autowired
    PasswordEncoder passwordEncoder;

    @Autowired
    AuthenticationManager authenticationManager;

    @Autowired
    TokenProvider tokenProvider;

    @Autowired
    private ITAddressBookService itAddressBookService;

    @Autowired
    private CscpUserOrgService cscpUserOrgService;

    @Autowired
    private ISysConfigService sysConfigService;

    @Autowired
    private WestoneEncryptService westoneEncryptService;

    @Autowired
    private RedisUtil redisUtil;

    @Autowired
    private CscpUserLockRecordMapper userLockRecordMapper;

    @Autowired
    private SyncPushUtils syncPushUtils;

    @Autowired
    private ITAddressBookService tAddressBookService;

    public SysUserController(CscpUserService cscpUserService) {
        this.cscpUserService = cscpUserService;
    }

    @InitBinder
    public void initBinder(WebDataBinder binder) {
        DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        dateFormat.setLenient(true);
        binder.registerCustomEditor(Date.class, new CustomDateEditor(dateFormat, true));
    }

    /**
     * POST  /cscpUsers : Create a new cscpUser.
     *
     * @param cscpUserDTO the cscpUserDTO to create
     * @return the ResponseEntity with status 201 (Created) and with body the new cscpUserDTO, or with status 400 (Bad Request) if the cscpUser has already an ID
     * @throws URISyntaxException if the Location URI syntax is incorrect
     */
    @ApiOperation(value = "新增用户")
    @PostMapping("/createCscpUser")
    @BeanExposeMethodAble(component = ADMIN, method = METHOD)
    @ResponseResultVo
    @OperationLog(dBOperation = DBOperation.ADD, message = "新增用户")
    public ResultVO<CscpUserDTO> createCscpUser(@RequestBody @Validated CscpUserDTO cscpUserDTO) {
        cscpUserDTO.setId(null);

        //如果用户未设置密码，则使用默认密码
        String defaultPassword = cscpUserDTO.getPassword();
        if (StringUtils.isBlank(defaultPassword)) {
            defaultPassword = sysConfigService.getSysConfigValueByCode(SysConfigConstant.DEFAULT_PASSWORD);
        }
        cscpUserDTO.setPassword(passwordEncoder.encode(defaultPassword));
        CscpUserDTO result = cscpUserService.insert(cscpUserDTO);

        syncPushUtils.createSyncUserToApp(result);
        syncPushUtils.passiveSyncUser(result);
        return ResultVO.success(result);
    }

    /**
     * PUT  /cscpUsers : Updates an existing cscpUser.
     *
     * @param cscpUserDTO the cscpUserDTO to update
     * @return the ResponseEntity with status 200 (OK) and with body the updated cscpUserDTO,
     * or with status 400 (Bad Request) if the cscpUserDTO is not valid,
     * or with status 500 (Internal Server Error) if the cscpUserDTO couldn't be updated
     * @throws URISyntaxException if the Location URI syntax is incorrect
     */
    @ApiOperation(value = "更新用户信息")
    @PutMapping("/updateCscpUser")
    @BeanExposeMethodAble(component = ADMIN, method = METHOD)
    @ResponseResultVo
    @OperationLog(dBOperation = DBOperation.UPDATE, message = "更新用户信息")
    public ResultVO<CscpUserDTO> updateCscpUser(@RequestBody CscpUserDTO cscpUserDTO) throws Exception {

        CscpUserDTO result = cscpUserService.update(cscpUserDTO);
        syncPushUtils.updateSyncUserToApp(result);
        syncPushUtils.passiveSyncUser(result);
        return ResultVO.success(result);
    }

    @ApiOperation(value = "激活和锁定用户，激活status=1，锁定用户status=0")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "用户id"),
            @ApiImplicitParam(name = "status", value = "激活status=1，锁定用户status=0")
    })
    @PostMapping("/updateUserStatus/{id}/{status}")
    @BeanExposeMethodAble(component = ADMIN, method = METHOD)
    @ResponseResultVo
    @OperationLog(dBOperation = DBOperation.UPDATE, message = "激活和锁定用户，激活status=1，锁定用户status=0")
    public ResultVO<Boolean> updateUserStatus(@PathVariable Long id, @PathVariable int status, HttpServletRequest request) throws Exception {
        CscpUser cscpUser = cscpUserService.getById(id);
        cscpUser.setStatus(status);
        cscpUserService.updateById(cscpUser);
        String userLockKey = "loginFailed:" + cscpUser.getLoginName();
        //同步通讯录状态
        tAddressBookService.updateUserStatus(ListUtil.of(id), status);
        if (status == 1) {
            // 激活用户 删除redis
            redisUtil.delete(userLockKey);
        } else {
            // 锁定用户
            JSONObject jsonObject = JSON.parseObject(sysConfigService.getSysConfigValueByCode(SysConfigConstant.SECURITY_AUTHENTICATION));
            // 密码登录失败锁定时间 单位为s
            int lockoutTime = jsonObject.getIntValue("loginLockTime") * 60;
            int badPasswordAttempts = jsonObject.getIntValue("passwordFailedLimit");
            redisUtil.set(userLockKey, badPasswordAttempts + 1, lockoutTime, TimeUnit.SECONDS);
            // 维护锁定记录
            CscpUserLockRecord cscpUserLockRecord = new CscpUserLockRecord();
            cscpUserLockRecord.setUserId(cscpUser.getId());
            cscpUserLockRecord.setLockTime(LocalDateTime.now());
            cscpUserLockRecord.setLockIp(IpUtil.getRealIp(request));
            userLockRecordMapper.insert(cscpUserLockRecord);

            // 刪除所有账号下的token
            redisUtil.batchDel("Bearer:pc*" + "(" + cscpUser.getId() + "*");
        }
        return ResultVO.success(true);
    }


    @PostMapping("/resetPassword/{id}")
    @OperationLog(dBOperation = DBOperation.UPDATE, message = "重置密码")
    public ResultVO<Boolean> resetPassword(@PathVariable Long id) throws Exception {
        CscpUser cscpUser = cscpUserService.getById(id);
        String defaultPassword = sysConfigService.getSysConfigValueByCode(SysConfigConstant.DEFAULT_PASSWORD);
        cscpUser.setPassword(passwordEncoder.encode(defaultPassword));

        if (westoneEncryptService.isCipherMachine()) {
            // TODO 计算SM3HMAC
            String src = cscpUser.getLoginName() + cscpUser.getPassword() + cscpUser.getMobile();
            cscpUser.setHmacMobile(westoneEncryptService.calculateSM3HMAC(src));
        }

        cscpUserService.updateById(cscpUser);
        // 清楚用户信息缓存
        cscpUserService.clearUserCache(cscpUser.getLoginName());
        return ResultVO.success(true);
    }

    @PostMapping("/resetAllPassword")
    @BeanExposeMethodAble(component = ADMIN, method = METHOD)
    @OperationLog(dBOperation = DBOperation.UPDATE, message = "重置所有用户（系统管理员除外）密码")
    public ResultVO<Boolean> resetAllPassword(@RequestBody CscpUserPasswordUpdate cscpUserPasswordUpdate) throws Exception {
        boolean isReset = false;
        String loginName = SecurityUtils.getCurrentUserName();
        if ("admin".equals(loginName)) {
            isReset = cscpUserService.resetAllPassword(cscpUserPasswordUpdate);
        } else {
            throw new BusinessException("没有权限，请联系管理员");
        }
        return ResultVO.success(isReset);
    }

    @GetMapping("/queryUserUpdatePassword/{loginName}")
    @ApiOperation(value = "获取用户密码修改提示")
    public ResultVO<Map<Boolean, String>> queryUserUpdatePassword(@PathVariable("loginName") String loginName) {
        return ResultVO.success(cscpUserService.queryUserUpdatePassword(loginName));
    }

    @PostMapping("/queryUserLock")
    @ApiOperation(value = "查询用户锁定列表", notes = "传入参数")
    public ResultVO<PageResult<QueryUserLockDTO>> queryUserLock(@RequestBody QueryUserLockVO vo, BasePageForm basePageForm) {
        return ResultVO.success(cscpUserService.queryUserLock(vo, basePageForm));
    }

    @PostMapping("/queryUserExamine")
    @ApiOperation(value = "查询用户审核列表", notes = "传入参数")
    public ResultVO<PageResult<QueryUserExamineDTO>> queryUserExamine(@RequestBody QueryUserLockVO vo, BasePageForm basePageForm) {
        return ResultVO.success(cscpUserService.queryUserExamine(vo, basePageForm));
    }


    @PostMapping("/updateUserExamine")
    @ApiOperation(value = "修改用户审核", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.UPDATE, message = "批量审核用户账号")
    public ResultVO<Boolean> updateUserExamine(@Validated @RequestBody UpdateUserExamineVO vo) {
        return ResultVO.success(cscpUserService.updateUserExamine(vo));
    }

    @PostMapping("/updateUserSecurityClassification")
    @ApiOperation(value = "修改用户涉密类别", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.UPDATE, message = "修改用户涉密类别")
    public ResultVO<Boolean> updateUserSecurityClassification(@Validated @RequestBody UpdateUserSecurityClassificationVO vo) {
        return ResultVO.success(cscpUserService.updateUserSecurityClassification(vo));
    }

    @PostMapping("/querySystemLog")
    @ApiOperation(value = "查询日志管理", notes = "传入参数")
    public ResultVO<PageResult<QuerySystemLogDTO>> querySystemLog(@Validated @RequestBody QuerySystemLogVO vo, BasePageForm basePageForm) throws ParseException {
        return ResultVO.success(cscpUserService.querySystemLog(vo, basePageForm));
    }

    @PostMapping("/queryUserSelected")
    @ApiOperation(value = "获取用户选中列表", notes = "传入参数")
    public ResultVO<List<QueryUserSelectedVO.QueryUserSelectedInfoVO>> queryUserSelected(@RequestBody QueryUserSelectedVO vo) {
        return ResultVO.success(cscpUserService.queryUserSelected(vo));
    }


    @PostMapping("/checkUserSecurityLevel")
    @ApiOperation(value = "校验用户密级", notes = "传入参数")
    public ResultVO<Boolean> checkUserSecurityLevel(@RequestBody CheckUserSecretLevelVO vo) {
        return cscpUserService.checkUserSecurityLevel(vo);
    }



    /**
     * PUT  /cscpUserPassword : 修改密码
     *
     * @param cscpUserPasswordUpdate the cscpUserPasswordUpdate to update
     * @return the ResponseEntity with status 200 (OK) and with body the updated cscpUserDTO,
     * or with status 400 (Bad Request) if the cscpUserDTO is not valid,
     * or with status 500 (Internal Server Error) if the cscpUserDTO couldn't be updated
     * @throws URISyntaxException if the Location URI syntax is incorrect
     */
    @PutMapping("/cscpUserPassword")
    @BeanExposeMethodAble(component = ADMIN, method = METHOD)
    @OperationLog(dBOperation = DBOperation.UPDATE, message = "修改用户密码")
    public ResultVO<SysUserController.JwtToken> updateCscpUserPassword(@RequestBody CscpUserPasswordUpdate cscpUserPasswordUpdate, HttpServletRequest request) throws URISyntaxException {
        log.debug("REST request to update cscpUserPassword : {}", cscpUserPasswordUpdate);
        CscpUserServiceImpl.UpdatePasswordResult updatePasswordResult = null;
        try {
//            cscpUserPasswordUpdate.setOldPassword(new StringBuilder(cscpUserPasswordUpdate.getOldPassword()).reverse().toString());
//            cscpUserPasswordUpdate.setNewPassword(new StringBuilder(cscpUserPasswordUpdate.getNewPassword()).reverse().toString());
            updatePasswordResult = cscpUserService.updatePassword(cscpUserPasswordUpdate, true);
        } catch (Exception e) {
            throw new BusinessException(e.getMessage());
        }

        // security
        UsernamePasswordAuthenticationToken authenticationToken = new UsernamePasswordAuthenticationToken(updatePasswordResult.getUserName(),
                updatePasswordResult.getPassword());
        Authentication authentication = null;
        try {
            authentication = this.authenticationManager.authenticate(authenticationToken);
        } catch (AuthenticationException e) {
            throw e;
        }

        SecurityContextHolder.getContext().setAuthentication(authentication);
        String jwt = tokenProvider.createToken(authentication, false, request.getHeader(HeaderConstants.CALL_SOURCE));


        HttpHeaders httpHeaders = new HttpHeaders();
        String token = jwt;

        httpHeaders.add(JWTConfigurer.AUTHORIZATION_HEADER, token);
        return ResultVO.success(new SysUserController.JwtToken(token));

    }

    @PutMapping("/updateUserPassword")
    public ResultVO<Boolean> updateUserPassword(@RequestBody CscpUserPasswordUpdate cscpUserPasswordUpdate, HttpServletRequest request) throws Exception {
        log.debug("REST request to update cscpUserPassword : {}", cscpUserPasswordUpdate);
        return ResultVO.success(cscpUserService.updateUserPassword(cscpUserPasswordUpdate));
    }

    /**
     * 移动端修改VPN的密码 同时更新PC端的登录密码
     * @param cscpUserPasswordUpdate
     * @param request
     * @return
     * @throws URISyntaxException
     */
    @PostMapping("/modifyVpnPwd")
    @BeanExposeMethodAble(component = ADMIN, method = METHOD)
    @OperationLog(dBOperation = DBOperation.UPDATE, message = "移动端修改VPN的密码 同时更新PC端的登录密码")
    public ResultVO<SysUserController.JwtToken> modifyVPNPwd(@RequestBody CscpUserPasswordUpdate cscpUserPasswordUpdate,
                                                              HttpServletRequest request) throws URISyntaxException {
        // TODO 保证业务系统PC端和APP移动端的登录密码一直。但是做不到密九云客户端的密码不能同步更新
        // TODO 密九云客户端的密码还是老密码，有个机制，多输入几次新密码 会提示是否同步服务端的新密码，本身没有接口能力同步密码
        return updateCscpUserPassword(cscpUserPasswordUpdate, request);
    }

    /**
     * GET  /cscpUsers : get the cscpUsers.
     *
     * @return the ResponseEntity with status 200 (OK) and the list of cscpUsers in body
     */
    @GetMapping("/getCscpUsers")
    @BeanExposeMethodAble(component = ADMIN, method = METHOD)
    @ApiOperation(value = "分页查询用户信息")
    @ResponseResultVo
    public ResultVO<PageResult<CscpUserDTO>> getCscpUsers(CscpUserDTO cscpUserDTO, BasePageForm page) {
        log.debug("REST request to get CscpUsers");
        cscpUserDTO.setStatus(1);
        PageResult<CscpUserDTO> result = cscpUserService.findByCscpUserDTOAndDesensitized(cscpUserDTO, page);
        return ResultVO.success(result);
    }

    /**
     * 查询用户列表(包含已禁用的用户)
     *
     * @param cscpUserDTO
     * @param page
     * @return
     */
    @GetMapping("/getCscpUserList")
    @BeanExposeMethodAble(component = ADMIN, method = METHOD)
    @ApiOperation(value = "查询用户列表(包含已禁用的用户)")
    @ResponseResultVo
    public ResultVO<PageResult<CscpUserDTO>> getCscpUserList(CscpUserDTO cscpUserDTO, BasePageForm page) {
        log.debug("REST request to get CscpUsers");
        PageResult<CscpUserDTO> result = cscpUserService.findByCscpUserDTOAndDesensitized(cscpUserDTO, page);
        return ResultVO.success(result);
    }

    /**
     * 根据orgId查询用户列表
     *
     * @param cscpUserDTO
     * @param page
     * @return
     */
    @GetMapping("/getCscpUserListByOrgId")
    @BeanExposeMethodAble(component = ADMIN, method = METHOD)
    @ApiOperation(value = "根据orgId查询用户列表")
    @ResponseResultVo
    public ResultVO<PageResult<CscpUserDTO>> getCscpUserListByOrgId(CscpUserDTO cscpUserDTO, BasePageForm page) {
        PageResult<CscpUserDTO> result = cscpUserService.findByOrgId(cscpUserDTO, page);
        return ResultVO.success(result);
    }

    /**
     * 根据orgId查询用户列表
     *
     * @param cscpUserDTO
     * @param page
     * @return
     */
    @GetMapping("/getNewCscpUserListByOrgId")
    @BeanExposeMethodAble(component = ADMIN, method = METHOD)
    @ApiOperation(value = "根据orgId查询用户列表")
    @ResponseResultVo
    public ResultVO<PageResult<CscpUserDTO>> getNewCscpUserListByOrgId(CscpUserDTO cscpUserDTO, BasePageForm page) {
        PageResult<CscpUserDTO> result = cscpUserService.findByOrgIdNew(cscpUserDTO, page);
        return ResultVO.success(result);
    }

    @GetMapping("/selectDivisionUserById")
    @BeanExposeMethodAble(component = ADMIN, method = METHOD)
    @ApiOperation(value = "区划用户管理")
    @ResponseResultVo
    public ResultVO<PageResult<CscpUserDTO>> selectDivisionUserById(CscpUserDTO cscpUserDTO, BasePageForm page) {
        PageResult<CscpUserDTO> result = cscpUserService.selectDivisionUserById(cscpUserDTO, page);
        return ResultVO.success(result);
    }



    @PostMapping("/getCscpUserListByIds")
    @BeanExposeMethodAble(component = ADMIN, method = METHOD)
    @ApiOperation(value = "查询用户列表(包含已禁用的用户)")
    @ResponseResultVo
    public ResultVO<List<CscpUser>> getCscpUserListByIds(@RequestParam("ids") List<Long> ids) {
        log.debug("REST request to get CscpUsers");
        LambdaQueryWrapper<CscpUser> lambdaQueryWrapper=new LambdaQueryWrapper<>();
        lambdaQueryWrapper.in(CscpUser::getId,ids);
        List<CscpUser> result = cscpUserService.selectListNoAdd(lambdaQueryWrapper);
        return ResultVO.success(result);
    }


    /**
     * GET  /cscpUsers/:id : get the "id" cscpUser.
     *
     * @param id the id of the cscpUserDTO to retrieve
     * @return the ResponseEntity with status 200 (OK) and with body the cscpUserDTO, or with status 404 (Not Found)
     */
    @GetMapping("/cscpUsers/{id}")
    @BeanExposeMethodAble(component = ADMIN, method = METHOD)
    @ApiOperation(value = "根据id获取单个用户信息")
    @ResponseResultVo
    public ResultVO<CscpUserDTO> getCscpUser(@PathVariable Long id) {
        CscpUserDTO cscpUserDTO = cscpUserService.findByUserId(id);
        return ResultVO.success(cscpUserDTO);
    }

    @GetMapping("/getCscpUserMoile/{id}")
    @BeanExposeMethodAble(component = ADMIN, method = METHOD)
    @ApiOperation(value = "根据id获取单个用户的手机号码")
    @ResponseResultVo
    public ResultVO<String> getCscpUserMoile(@PathVariable Long id) {
        CscpUser cscpUser = cscpUserService.getById(id);
        if (Objects.isNull(cscpUser)) {
            throw new BusinessException("用户不存在");
        }
        if (!UserConstant.USER_ACTIVE_STATUS.equals(cscpUser.getStatus())) {
            throw new BusinessException("用户已被禁用");
        }
        return ResultVO.success(cscpUser.getMobile());
    }


    @GetMapping("/getUserNameByRealName")
    @BeanExposeMethodAble(component = ADMIN, method = METHOD)
    @ApiOperation(value = "传入用户的真实姓名，产生一个唯一的用户名")
    @ResponseResultVo
    public ResultVO<String> getUserNameByRealName(@RequestParam String realName) {
        realName= URLDecoder.decode(realName, StandardCharsets.UTF_8);
        String firstLetter = PinyinUtil.getPinyin(realName, "");
        String userNameMaxNumber = cscpUserService.getUserNameMaxNumber(firstLetter);
        return ResultVO.success(userNameMaxNumber);
    }

    @PostMapping("/getCscpUserMoileAll")
    @BeanExposeMethodAble(component = ADMIN, method = METHOD)
    @ApiOperation(value = "根据多个用户id获取用户的手机号码")
    @ResponseResultVo
    public ResultVO<List<String>> getCscpUserMoileAll(@RequestBody List<Long> userId) {
        List<String> moileList = cscpUserService.getCscpUserMoileAll(userId);
        return ResultVO.success(moileList);
    }


    @GetMapping("/cscpCurrentUser")
    @BeanExposeMethodAble(component = ADMIN, method = METHOD)
    @ApiOperation(value = "获取当前登录用户的基本信息")
    @ResponseResultVo
    public ResultVO<CurrentCscpUserDTO> getCscpUser() {
        long uid = SecurityUtils.getCurrentUserId();
        CurrentCscpUserDTO cscpUserDTO = cscpUserService.findCurrentUserDto(uid);
        return ResultVO.success(cscpUserDTO);
    }

    /**
     * DELETE  /cscpUsers/:id : delete the "id" cscpUser.
     *
     * @param id the id of the cscpUserDTO to delete
     * @return the ResponseEntity with status 200 (OK)
     */
    @DeleteMapping("/deleteCscpUser")
    @BeanExposeMethodAble(component = ADMIN, method = METHOD)
    @ApiOperation(value = "根据id删除用户")
    @ResponseResultVo
    @OperationLog(dBOperation = DBOperation.DELETE, message = "根据id删除用户")
    public ResultVO deleteCscpUser(@RequestParam Long id, @RequestParam(required = false) Long orgId) {
        log.debug("REST request to delete CscpUser : {}", id);
        cscpUserService.delete(id, orgId);
        return ResultVO.success();
    }

    /**
     * 用户禁用
     * @param ids
     * @return
     */
    @PutMapping("/disableCscpUser")
    @BeanExposeMethodAble(component = ADMIN, method = METHOD)
    @ApiOperation(value = "根据id禁用用户")
    @ResponseResultVo
    @OperationLog(dBOperation = DBOperation.UPDATE, message = "根据id禁用用户")
    public ResultVO disableCscpUser(@RequestBody List<Long> ids) {
        log.debug("REST request to disable CscpUser : {}", ids);
        cscpUserService.judgeUserStatus(ids);
        List<Long> resultIds = cscpUserService.disable(ids);
        syncPushUtils.syncBusiness(2,"delete", resultIds);
        return ResultVO.success();
    }

    /**
     * 用户启用
     * @param ids
     * @return
     */
    @PutMapping("/enableCscpUser")
    @BeanExposeMethodAble(component = ADMIN, method = METHOD)
    @ApiOperation(value = "根据id启用用户")
    @ResponseResultVo
    @OperationLog(dBOperation = DBOperation.UPDATE, message = "根据id启用用户")
    public ResultVO enableCscpUser(@RequestBody List<Long> ids) {
        log.debug("REST request to enable CscpUser : {}", ids);
        List<Long> resultIds = cscpUserService.enable(ids);
        syncPushUtils.syncBusiness(2,"update", resultIds);
        return ResultVO.success();
    }

    @PostMapping("/auto/syncUserBusiness")
    @ApiOperation(value = "同步用户至开启自动推送的系统", notes = "传入userIds")
    public ResultVO syncCompanyUsers(@RequestBody List<Long> userIds) {
        if (userIds == null || userIds.isEmpty()) {
            throw new BusinessException("请勾选用户");
        }
        syncPushUtils.syncBusiness(2,"update", userIds);
        return ResultVO.success();
    }

    /**
     * 用户名是否存在
     */
    @GetMapping("/cscpUserExistByUsername")
    @BeanExposeMethodAble(component = ADMIN, method = METHOD)
    @ApiOperation(value = "根据用户的登录信息判断用户是否存在")
    @ResponseResultVo
    public ResultVO<Boolean> existByUsername(String userName) {
        log.debug("REST request to vilidate cscpUserUserName : {}", userName);
        boolean exists = cscpUserService.existByUsername(userName);
        return ResultVO.success(exists);
    }

    /**
     * 判断手机号码是否存在
     */
    @GetMapping("/cscpUserExistByMobile")
    @BeanExposeMethodAble(component = ADMIN, method = METHOD)
    @ApiOperation(value = "判断手机号码是否存在")
    @ResponseResultVo
    public ResultVO<Boolean> existByMobile(String mobile) {
        boolean exists = cscpUserService.existByMobile(mobile);
        return ResultVO.success(exists);
    }

    @GetMapping("/selectBranchLeader/{orgId}")
    @BeanExposeMethodAble(component = ADMIN, method = METHOD)
    @ApiOperation(value = "根据部门ID查询分管领导")
    @ResponseResultVo
    public ResultVO<CscpUserDTO> selectBranchLeader(@PathVariable Long orgId) {
        CscpUserDTO userDTO = cscpUserService.selectBranchLeader(orgId);
        return ResultVO.success(userDTO);
    }

    @GetMapping("/selectDepartmentHead/{orgId}")
    @BeanExposeMethodAble(component = ADMIN, method = METHOD)
    @ApiOperation(value = "根据部门ID查询部门领导")
    @ResponseResultVo
    public ResultVO<List<CscpUserDTO>> selectDepartmentHead(@PathVariable Long orgId) {
        List<CscpUserDTO> userDTOList = cscpUserService.selectDepartmentHead(orgId);
        return ResultVO.success(userDTOList);
    }

    @GetMapping("/selectBranchLeaderAndDepartmentHead/{orgId}")
    @BeanExposeMethodAble(component = ADMIN, method = METHOD)
    @ApiOperation(value = "根据部门ID查询分管领导和部门领导")
    @ResponseResultVo
    public ResultVO<CscpUserDTO> selectBranchLeaderAndDepartmentHead(@PathVariable Long orgId) {
        CscpUserDTO userDTO = cscpUserService.selectBranchLeaderAndDepartmentHead(orgId);
        return ResultVO.success(userDTO);
    }

    @PostMapping("/saveDepartmentHead")
    @BeanExposeMethodAble(component = ADMIN, method = METHOD)
    @ApiOperation(value = "设置单个部门领导")
    @ResponseResultVo
    @OperationLog(dBOperation = DBOperation.ADD, message = "设置单个部门领导")
    public ResultVO saveDepartmentHead(@RequestParam Long orgId, @RequestParam Long userId) {
        cscpUserService.saveDepartmentHead(orgId, userId);
        return ResultVO.success();
    }

    @PostMapping("/deleteDepartmentHead")
    @BeanExposeMethodAble(component = ADMIN, method = METHOD)
    @ApiOperation(value = "删除单个部门领导")
    @ResponseResultVo
    @OperationLog(dBOperation = DBOperation.DELETE, message = "删除单个部门领导")
    public ResultVO deleteDepartmentHead(@RequestParam Long orgId, @RequestParam Long userId) {
        cscpUserService.deleteDepartmentHead(orgId, userId);
        return ResultVO.success();
    }

    @GetMapping("/checkUserSortExist")
    @BeanExposeMethodAble(component = ADMIN, method = METHOD)
    @ApiOperation(value = "判断该租户下的用户表排序号是否存在")
    @ResponseResultVo
    public ResultVO checkUserSortExist(@RequestParam Long userId, @RequestParam Integer sort) {
        boolean b = cscpUserService.checkUserSortExist(userId, sort);
        return ResultVO.success(b);
    }

    @GetMapping("/selectUserByTenantId")
    @BeanExposeMethodAble(component = ADMIN, method = METHOD)
    @ApiOperation(value = "查询租户下所有用户")
    @ResponseResultVo
    public ResultVO selectUserByTenantId(CscpUserDTO cscpUserDTO) {
        Long tenantId = SecurityUtils.getCurrentCscpUserDetail().getTenantId();
        cscpUserDTO.setTenantId(tenantId);
        List<CscpUserDTO> cscpUserDTOList = cscpUserService.selectUserByTenantId(cscpUserDTO);
        return ResultVO.success(cscpUserDTOList);
    }


    @GetMapping("/checkUserOrgSortExist")
    @BeanExposeMethodAble(component = ADMIN, method = METHOD)
    @ApiOperation(value = "判断该用户在该部门的排序号是否存在")
    @ResponseResultVo
    public ResultVO checkUserOrgSortExist(@RequestParam Long userId, @RequestParam Long orgId, @RequestParam Integer sort) {
        boolean b = cscpUserOrgService.checkUserOrgSortExist(userId, orgId, sort);
        return ResultVO.success(b);
    }

    @PostMapping("/deleteUserOrg")
    @BeanExposeMethodAble(component = ADMIN, method = METHOD)
    @ApiOperation(value = "删除用户单位关系")
    @ResponseResultVo
    @OperationLog(dBOperation = DBOperation.DELETE, message = "删除用户单位关系")
    public ResultVO deleteUserOrg(@RequestParam Long orgId, @RequestParam Long userId) {
        cscpUserService.deleteUserOrg(orgId, userId);
        return ResultVO.success();
    }

    @GetMapping("/queryOtherDepartmentUsers")
    @BeanExposeMethodAble(component = ADMIN, method = METHOD)
    @ApiOperation(value = "查询本单位除指定部门外的所有用户信息")
    @ResponseResultVo
    public ResultVO<PageResult<CscpUserDTO>> queryOtherDepartmentUsers(CscpUserDTO cscpUserDTO, BasePageForm page) {
        PageResult<CscpUserDTO> result = cscpUserService.queryOtherDepartmentUsers(cscpUserDTO, page);
        return ResultVO.success(result);
    }

    @PostMapping("/saveUserOrg")
    @BeanExposeMethodAble(component = ADMIN, method = METHOD)
    @ApiOperation(value = "保存用户部门关系")
    @ResponseResultVo
    @OperationLog(dBOperation = DBOperation.ADD, message = "保存用户部门关系")
    public ResultVO<Boolean> saveUserOrg(@RequestBody List<CscpUserOrgDTO> cscpUserOrgDTOList) {
        //List<String> failUserList = new ArrayList<>();
        for (CscpUserOrgDTO cscpUserOrgDTO : cscpUserOrgDTOList) {
            try {
                cscpUserOrgService.saveUserOrg(cscpUserOrgDTO);
            } catch (RuntimeException e) {
                throw new BusinessException("保存用户部门关系失败，用户名：{}，错误信息：{}", cscpUserOrgDTO.getUserName(), e.getMessage());
            }
        }
        return ResultVO.success();
    }

    @GetMapping("/getNewDate")
    @ApiOperation(value = "获取当前日期")
    @ResponseResultVo
    public ResultVO<String> getNewDate() {
        SimpleDateFormat format = new SimpleDateFormat("yyyy/MM/dd");
        Date date = new Date();
        return ResultVO.success(format.format(date));
    }

    @GetMapping("/getNewDateTime")
    @ApiOperation(value = "获取当前时间")
    @ResponseResultVo
    public ResultVO<String> getNewDateTime() {
        SimpleDateFormat format = new SimpleDateFormat("yyyy/MM/dd HH:mm:ss");
        Date date = new Date();
        return ResultVO.success(format.format(date));
    }

    @GetMapping("/cscpUserPasswordRule")
    @BeanExposeMethodAble(component = ADMIN, method = METHOD)
    public String passwordRule() {
        StringBuilder sb = new StringBuilder();
        sb.append("密码长度应大于等于");
        sb.append(10);
        sb.append("小于等于");
        sb.append(maxLength);
        sb.append("，需要包括");
        if (lowercase) {
            sb.append("小写字母、");
        }
        if (uppercase) {
            sb.append("大写字母、");
        }
        if (digit) {
            sb.append("数字、");
        }
        if (specialChar) {
            sb.append("特殊字符4项");
        }
        return sb.toString();
    }

    /**
     * 修复重复登录名接口。
     */
    @GetMapping("/fixDuplicateLogin")
    @ApiOperation(value = "修复重复的登录名")
    public ResultVO<String> fixDuplicateLoginNames() {
        try {
            int updatedCount = cscpUserService.fixDuplicateLoginNames();
            return ResultVO.success("成功更新了 " + updatedCount + " 个重复的登录名");
        } catch (Exception e) {
            log.error("处理重复用户登录名失败，login_name", e);
            return ResultVO.error("修复重复登录名时出错: " + e.getMessage());
        }
    }

    /**
     * 根据 BIZ_HRS_USER_INFO 表的数据生成更新指定机构下 CSCP_USER 表身份证号的 SQL 语句，并直接下载到客户端。
     *
     * @param companyId 机构 ID
     * @param response  HttpServletResponse 对象，用于设置响应头和输出流
     * @throws IOException 文件写入或网络传输异常
     */
    @GetMapping("/downloadUpdateIdCardNoSql")
    @ApiOperation(value = "下载更新指定机构用户身份证号的 SQL 文件")
    public void downloadUpdateIdCardNoSql(@RequestParam("companyId") Long companyId, HttpServletResponse response) throws IOException {
        try {
            cscpUserService.generateUpdateSqlAndDownload(companyId, response);
        } catch (IOException e) {
            log.error("下载更新机构 {} 用户身份证号的 SQL 文件失败", companyId, e);
            // 可以根据需要设置响应状态码或返回错误信息
            response.sendError(HttpServletResponse.SC_INTERNAL_SERVER_ERROR, "下载 SQL 文件失败：" + e.getMessage());
        }
    }

    /**
     * 处理单位无单位管理员接口。
     */
    @GetMapping("/dealAdminForOrgs")
    @ApiOperation(value = "处理单位无单位管理员接口")
    public ResultVO<String> dealAdminForOrgs(@RequestParam(value = "orgId", required = false) Long orgId) {
        try {
            SecurityContext securityContext = SecurityContextHolder.getContext();
            CompletableFuture.runAsync(() -> {
                try {
                    SecurityContextHolder.setContext(securityContext);
                    int updatedCount = cscpUserService.dealAdminForOrgs(orgId);
                    log.info("处理单位无单位管理员数量：" + updatedCount);
                } catch (Exception e) {
                    log.error("处理单位无单位管理员失败", e);
                }
            });
            return ResultVO.success("处理单位无单位管理员接口运行中");
        } catch (Exception e) {
            log.error("处理无单位管理员失败", e);
            return ResultVO.error("处理无单位管理员失败: " + e.getMessage());
        }
    }

    /**
     * Object to return as body in JWT Authentication.
     */
    public static class JwtToken {

        private String token;


        private String msg;

        JwtToken(String token) {
            this.token = token;
        }

        public JwtToken(String token, String msg) {
            this.token = token;
            this.msg = msg;
        }

        @JsonProperty("token")
        String getToken() {
            return token;
        }

        void setToken(String token) {
            this.token = token;
        }


        @JsonProperty("msg")
        public String getMsg() {
            return msg;
        }

        public void setMsg(String msg) {
            this.msg = msg;
        }
    }

    @GetMapping("/queryUserByTreeIdAndRoleId")
    @ApiOperation(value = "查询角色的用户", notes = "传入参数")
    public ResultVO<PageResult<CscpUserDTO>> queryUserByTreeIdAndRoleId(@RequestParam("orgId") Long orgId, BasePageForm basePageForm){
        basePageForm.setPageSize(100);
        PageResult<CscpUserDTO> pageResult = cscpUserService.queryUserByTreeIdAndRoleId(orgId, Long.valueOf(SystemRole.COMPANY_ROLE.getId()),basePageForm);
        // 根据角色机构id 找用户
        return ResultVO.success(pageResult);
    }

    @GetMapping("/queryUserByTreeIdAndRoleCode")
    @ApiOperation(value = "查询角色的用户", notes = "传入参数")
    public ResultVO<PageResult<CscpUserDTO>> queryUserByTreeIdAndRoleCode(@RequestParam("orgId") Long orgId,
                                                                          @RequestParam("roleCodes") String roleCodes,
                                                                          BasePageForm basePageForm){
        basePageForm.setPageSize(100);
        PageResult<CscpUserDTO> pageResult = cscpUserService.queryUserByTreeIdAndRoleCode(orgId, Arrays.asList(roleCodes.split(",")),basePageForm);
        // 根据角色机构id 找用户
        return ResultVO.success(pageResult);
    }

    @PutMapping("/getUserByOrgId/alter")
    @ApiOperation(value = "根据orgId变更用户关联信息", notes = "传入参数")
    public ResultVO alterUserOrg(@RequestBody CscpUserOrgAlterDTO record) {
        if (Objects.isNull(record.getNewOrgId())) {
            return ResultVO.error("变更机构不能为空");
        }
        record.setFlag("alter");
        Pair<Boolean, String> success = cscpUserService.checkUserOrgByOrgId(record);
        if(success.getKey()){
            return ResultVO.success();
        }else {
            return ResultVO.error(success.getValue());
        }
    }

    @PutMapping("/getUserByOrgId/remove")
    @ApiOperation(value = "根据orgId移除用户关联信息", notes = "传入参数")
    public ResultVO removeUserOrg(@RequestBody CscpUserOrgAlterDTO record) {
        record.setFlag("remove");
        cscpUserService.checkUserOrgByOrgId(record);
        record.setFlag("alter");
        Pair<Boolean, String> success = cscpUserService.checkUserOrgByOrgId(record);
        if(success.getKey()){
            return ResultVO.success();
        }else {
            return ResultVO.error(success.getValue());
        }
    }




}
