package com.ctsi.fusion.common;

import com.ctsi.hndx.utils.StringUtils;
import com.ctsi.hndxoa.entity.TSyncAppSystemManage;
import com.ctsi.hndxoa.entity.TSyncUserHistroyRecord;
import com.ctsi.hndxoa.entity.dto.SyncOrgUserDTO;
import com.ctsi.hndxoa.pull.entity.dto.PassiveSyncMessage;
import com.ctsi.hndxoa.service.ITSyncAppSystemManageService;
import com.ctsi.ssdc.admin.domain.dto.CscpOrgDTO;
import com.ctsi.ssdc.admin.domain.dto.CscpUserDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@Slf4j
public class SyncPushUtils {

    private static final Integer ORG_TYPE = 1;

    @Autowired
    private ITSyncAppSystemManageService itSyncAppSystemManageService;
    @Autowired
    private ApplicationEventPublisher eventPublisher;

    public void syncBusiness(Integer type, String flag, List<Long> ids) {

        if (CollectionUtils.isNotEmpty(ids)) {
            for (Long id : ids) {
                try {
                    SyncOrgUserDTO syncOrgUserDTO = new SyncOrgUserDTO();
                    syncOrgUserDTO.setFlag(flag);
                    syncOrgUserDTO.setIsAutoPushFlag(true);
                    if (ORG_TYPE.equals(type)) {
                        syncOrgUserDTO.setOrgId(id);
                        // 调用自动推送
                        itSyncAppSystemManageService.syncOrgBusiness(syncOrgUserDTO);
                    } else {
                        syncOrgUserDTO.setUserId(id);
                        // 调用自动推送
                        itSyncAppSystemManageService.syncUserBusiness(syncOrgUserDTO);
                    }
                } catch (Exception e) {
                    log.error("同步业务失败，ID: {}", id, e); // 自动记录堆栈信息
                }
            }
        }
    }

    public void changeHisSyncBusiness(Integer type, String flag, List<Long> ids, Long sourceId) {
        if (CollectionUtils.isNotEmpty(ids)) {
            for (Long id : ids) {
                try {
                    SyncOrgUserDTO syncOrgUserDTO = new SyncOrgUserDTO();
                    syncOrgUserDTO.setFlag(flag);
                    syncOrgUserDTO.setIsAutoPushFlag(true);
                    if (ORG_TYPE.equals(type)) {
                        syncOrgUserDTO.setOrgId(id);
                        syncOrgUserDTO.setSourceId(sourceId);
                        // 调用自动推送
                        itSyncAppSystemManageService.syncOrgBusiness(syncOrgUserDTO);
                    } else {
                        syncOrgUserDTO.setUserId(id);
                        // 调用自动推送
                        itSyncAppSystemManageService.syncUserBusiness(syncOrgUserDTO);
                    }
                } catch (Exception e) {
                    log.error("异步处理推送错误: {}", e.getMessage());
                }
            }
        }
    }

    public void createSyncUserToApp(CscpUserDTO cscpUserDTO) {
        if (cscpUserDTO.getId() == null || StringUtils.isEmpty(cscpUserDTO.getPushAppCode())) {
            return;
        }
        cscpUserDTO.buildAddAppCodes(null);
        itSyncAppSystemManageService.autoUserAppDetail(cscpUserDTO, false);
    }

    public void updateSyncUserToApp(CscpUserDTO cscpUserDTO) {
        if (cscpUserDTO.getId() == null || StringUtils.isEmpty(cscpUserDTO.getPushAppCode())) {
            return;
        }
        itSyncAppSystemManageService.autoUserAppDetail(cscpUserDTO, true);
    }

    public void createSyncOrgToApp(CscpOrgDTO cscpOrgDTO) {
        if (cscpOrgDTO.getId() == null || StringUtils.isEmpty(cscpOrgDTO.getPushAppCode())) {
            return;
        }
        cscpOrgDTO.buildAddAppCodes(null);
        itSyncAppSystemManageService.autoOrgAppDetail(cscpOrgDTO, false);
    }

    public void updateSyncOrgToApp(CscpOrgDTO cscpOrgDTO) {
        if (cscpOrgDTO.getId() == null || StringUtils.isEmpty(cscpOrgDTO.getPushAppCode())) {
            return;
        }
        itSyncAppSystemManageService.autoOrgAppDetail(cscpOrgDTO, true);
    }


    public void passiveSyncUser(CscpUserDTO cscpUserDTO) {
        List<TSyncAppSystemManage> apps = itSyncAppSystemManageService.getPassiveActiveApp();
        if (CollectionUtils.isEmpty(apps)) {
            return;
        }
        for (TSyncAppSystemManage app : apps) {
            PassiveSyncMessage message = new PassiveSyncMessage();
            message.setAppSystemId(app.getId());
            message.setType(1);
            message.setUserId(cscpUserDTO.getId());
            // 插入推送记录
            TSyncUserHistroyRecord record = itSyncAppSystemManageService.insertUserSyncRecord(cscpUserDTO, app);
            message.setSyncHistoryId(record.getId());
            eventPublisher.publishEvent(message);
        }

    }
}
