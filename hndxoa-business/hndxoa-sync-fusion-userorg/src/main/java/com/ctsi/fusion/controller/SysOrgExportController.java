package com.ctsi.fusion.controller;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.read.listener.PageReadListener;
import com.ctsi.hndx.annotations.ResponseResultVo;
import com.ctsi.hndx.enums.DBOperation;
import com.ctsi.hndx.exception.BusinessException;
import com.ctsi.hndx.result.ResultVO;
import com.ctsi.hndxoa.entity.dto.SyncOrgUserDTO;
import com.ctsi.hndxoa.service.ITSyncAppSystemManageService;
import com.ctsi.ssdc.admin.domain.dto.*;
import com.ctsi.ssdc.admin.service.CscpOrgService;
import com.ctsi.ssdc.admin.service.OrgImportAndExportService;
import com.ctsi.ssdc.annotation.OperationLog;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;

@RestController
@ResponseResultVo
@RequestMapping("/api/orgImportAndExport")
@Api(value = "组织机构导入导出接口", tags = "组织机构导入导出接口")
@Slf4j
public class SysOrgExportController {


    @Autowired
    private OrgImportAndExportService orgImportAndExportService;

    @Autowired
    private CscpOrgService cscpOrgService;

    @Autowired
    private ITSyncAppSystemManageService itSyncAppSystemManageService;


    @PostMapping("/uploadDepartment/{id}")
    @ApiOperation(value = "部门导入接口", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.ADD,message = "组织机构（部门）导入")
    public ResultVO uploadDepartment(@PathVariable Long id, MultipartFile file) throws IOException {
        if (Objects.isNull(id)) {
            throw new BusinessException("ID不能为空");
        }
        cscpOrgService.clearCache(id);
        List<Long> ids = new ArrayList<>();
        EasyExcel.read(file.getInputStream(), DepartmentImportDTO.class, new PageReadListener<DepartmentImportDTO>(
                dataList -> {
                    List<Long> dataId = orgImportAndExportService.saveDepartment(id, dataList);
                    ids.addAll(dataId);
                })).sheet().doReadSync();
        this.syncExportOrgBatch(ids);
        return ResultVO.success(true);
    }

    @PostMapping("/uploadCompany/{id}")
    @ApiOperation(value = "单位导入接口", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.ADD,message = "组织机构（单位）导入")
    public ResultVO uploadCompany(@PathVariable Long id, MultipartFile file) throws IOException {
        if (Objects.isNull(id)) {
            throw new BusinessException("ID不能为空");
        }
        cscpOrgService.clearCache(id);
        List<Long> ids = new ArrayList<>();
        String appCodeStr =itSyncAppSystemManageService.selectEnableAppCodeStr();
        EasyExcel.read(file.getInputStream(), CompanyImportDTO.class, new PageReadListener<CompanyImportDTO>(
                dataList -> {
                    List<Long> dataId = orgImportAndExportService.saveCompany(id, dataList, appCodeStr);
                    ids.addAll(dataId);
                })).sheet().doReadSync();
        this.syncExportOrgBatch(ids);
        return ResultVO.success(true);
    }

    @GetMapping("/exportDepartment/{id}")
    @ApiOperation(value = "部门导出接口", notes = "传入参数")
    public ResultVO exportDepartment(@PathVariable Long id, HttpServletResponse response) throws IOException {
        if (Objects.isNull(id)) {
            throw new BusinessException("ID不能为空");
        }
        Boolean bool = orgImportAndExportService.exportDepartmentToExcel(id, response);
        return ResultVO.success(bool);
    }

    @GetMapping("/exportOrgById/{id}")
    @ApiOperation(value = "机构树扁平化导出", notes = "传入参数")
    public ResultVO exportOrgById(@PathVariable Long id){
        if (Objects.isNull(id)) {
            throw new BusinessException("ID不能为空");
        }
        return ResultVO.success(orgImportAndExportService.exportOrgById(id));
    }

    @PostMapping("/uploadLevelCompany")
    @ApiOperation(value = "多层级导入接口", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.ADD,message = "组织机构（单位）导入")
    public ResultVO uploadLevelCompany(MultipartFile file) throws IOException {
        List<Long> ids = new ArrayList<>();
        String appCodeStr =itSyncAppSystemManageService.selectEnableAppCodeStr();
        EasyExcel.read(file.getInputStream(), CompanyLevelImportDTO.class, new PageReadListener<CompanyLevelImportDTO>(
                dataList -> {
                    List<Long> dataId = orgImportAndExportService.saveLevelCompany(dataList, appCodeStr);
                    ids.addAll(dataId);
                })).sheet().doReadSync();
        this.syncExportOrgBatch(ids);
        return ResultVO.success(true);
    }

    @PostMapping("/uploadLevelAll/{id}")
    @ApiOperation(value = "组织机构路径导入接口", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.ADD,message = "组织机构路径导入")
    public ResultVO uploadLevelAll(@PathVariable Long id,MultipartFile file) throws IOException {
        //不分页
        List<OrgAllLevelImportDTO> dataList =  EasyExcel.read(file.getInputStream())
                .head(OrgAllLevelImportDTO.class).doReadAllSync();
        String appCodeStr =itSyncAppSystemManageService.selectEnableAppCodeStr();
        List<Long> ids = orgImportAndExportService.uploadLevelAll(id, dataList, appCodeStr);
        this.syncExportOrgBatch(ids);
        return ResultVO.success(true);
    }

    @PostMapping("/uploadOrgRegion")
    @ApiOperation(value = "导入行政区划数据", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.ADD,message = "导入行政区划数据")
    public ResultVO uploadOrgRegion(MultipartFile file) throws IOException {
        List<CscpOrgDTO> orgList = new ArrayList<>();
        EasyExcel.read(file.getInputStream(), OrgRegionImportDTO.class, new PageReadListener<OrgRegionImportDTO>(dataList -> {
            orgImportAndExportService.saveOrgRegion( dataList,orgList);
        })).sheet().doRead();
        for (CscpOrgDTO cscpOrgDTO : orgList) {
            cscpOrgService.insert(cscpOrgDTO);
        }
        return ResultVO.success(true);
    }

    private void syncExportOrgBatch(List<Long> ids) {
        if (null != ids && !ids.isEmpty()) {
            SecurityContext securityContext = SecurityContextHolder.getContext();
            long startTime = System.currentTimeMillis();
            log.info("开始 >>>>>> 导入机构-推送: {}", ids);
            CompletableFuture.runAsync((() -> {
                SecurityContextHolder.setContext(securityContext);
                for (Long l : ids) {
                    SyncOrgUserDTO syncOrgUserDTO = new SyncOrgUserDTO();
                    syncOrgUserDTO.setFlag("add");
                    syncOrgUserDTO.setOrgId(l);
                    syncOrgUserDTO.setIsAutoPushFlag(true);
                    itSyncAppSystemManageService.syncOrgBusiness(syncOrgUserDTO);
                }
            }));
            log.info("结束 >>>>>> 导入机构-推送:{}", ids);
            log.info("耗时: {} ms", System.currentTimeMillis() - startTime);
        }
    }

}
