package com.ctsi.fusion.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ctsi.fusion.service.IForeignOrgUserService;
import com.ctsi.hndx.exception.BusinessException;
import com.ctsi.hndx.utils.ListCopyUtil;
import com.ctsi.hndx.utils.StringUtils;
import com.ctsi.hndxoa.entity.TSyncAppSystemManage;
import com.ctsi.hndxoa.service.ITSyncAppSystemManageService;
import com.ctsi.hndxoa.service.ITSyncOrgHistroyRecordService;
import com.ctsi.hndxoa.service.ITSyncUserHistroyRecordService;
import com.ctsi.ssdc.admin.domain.CscpOrg;
import com.ctsi.ssdc.admin.domain.dto.CscpOrgDTO;
import com.ctsi.ssdc.admin.domain.dto.CscpUserDTO;
import com.ctsi.ssdc.admin.entity.dto.TYJGSyncUserOrgIdDTO;
import com.ctsi.ssdc.admin.service.CscpOrgService;
import com.ctsi.ssdc.admin.service.CscpUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class ForeignOrgUserServiceImpl implements IForeignOrgUserService {

    @Autowired
    private ITSyncAppSystemManageService tSyncAppSystemManageService;

    @Autowired
    private ITSyncUserHistroyRecordService tSyncUserHistroyRecordService;

    @Autowired
    private ITSyncOrgHistroyRecordService tSyncOrgHistroyRecordService;

    @Autowired
    private CscpUserService cscpUserService;

    @Autowired
    private CscpOrgService cscpOrgService;


    @Override
    public List<CscpOrgDTO> selectPullCscpOrg(Long appId, List<TYJGSyncUserOrgIdDTO> list) {
        if (null == appId) {
            throw new BusinessException("请求异常, 缺少header");
        }
        if (CollectionUtil.isEmpty(list)) {
            throw new BusinessException("请求异常, 参数错误");
        }

        for (TYJGSyncUserOrgIdDTO dto : list) {
            // 如果配置redis; 这里要查推送记录剩余可查询次数
            if (null == dto.getSyncHistoryId()) {
                throw new BusinessException("请求异常, 参数缺少记录id");
            }
        }

        // 根据请求头获取应用信息
        TSyncAppSystemManage appSystemManage = tSyncAppSystemManageService.getById(appId);
        if (null == appSystemManage) {
            throw new BusinessException("请求异常, 授权应用错误");
        }
        String inOwnedOrgId = appSystemManage.getInOwnedOrgId();

        List<Long> orgIds = list.stream().map(TYJGSyncUserOrgIdDTO::getOrgId).collect(Collectors.toList());
        LambdaQueryWrapper<CscpOrg> orgLqw = Wrappers.lambdaQuery();
        orgLqw.in(CscpOrg::getId, orgIds);
        List<CscpOrg> resultOrgList = cscpOrgService.selectListNoAdd(orgLqw);

        // 根据限定的机构id,获取数据
        List<CscpOrg> filteredOrgList = resultOrgList;
        if (StringUtils.isNotEmpty(appSystemManage.getInOwnedOrgId())) {
            CscpOrg rootNodeOrg = cscpOrgService.getById(inOwnedOrgId);
            String rootOrgCode = rootNodeOrg.getOrgCode();

            // 使用并行流和谓词优化过滤
            filteredOrgList = resultOrgList.parallelStream()
                    .filter(o -> o.getOrgCodePath().contains(rootOrgCode))
                    .collect(Collectors.toList());

            if (resultOrgList.size() != filteredOrgList.size()) {
                throw new BusinessException("请求异常, 不能获取应用范围外的数据");
            }
        }

        List<CscpOrgDTO> resultDataList = ListCopyUtil.copy(filteredOrgList, CscpOrgDTO.class);

        if (CollectionUtil.isEmpty(resultDataList)) {
            return Collections.emptyList();
        }

        // 优化：批量获取所有需要的父节点和子节点
        Map<Long, List<CscpOrgDTO>> parentNodesMap = batchLoadParentNodes(resultDataList);
        Map<Long, List<CscpOrgDTO>> childrenMap = batchLoadChildrenNodes(resultDataList);

        // 组装数据
        for (CscpOrgDTO dto : resultDataList) {
            dto.setParentNodes(parentNodesMap.getOrDefault(dto.getId(), Collections.emptyList()));
            dto.setChildNodes(childrenMap.getOrDefault(dto.getId(), Collections.emptyList()));
        }

        return resultDataList;
    }

    // 批量加载所有父节点
    private Map<Long, List<CscpOrgDTO>> batchLoadParentNodes(List<CscpOrgDTO> orgDTOs) {
        // 收集所有需要查询的父节点OrgCode
        Set<String> allParentOrgCodes = new HashSet<>();
        for (CscpOrgDTO dto : orgDTOs) {
            List<String> codes = Arrays.asList(dto.getOrgCodePath().split("\\|"));
            int size = codes.size();
            if (size > 1) { // 至少有一个父节点
                allParentOrgCodes.addAll(codes.subList(0, size - 1));
            }
        }

        if (allParentOrgCodes.isEmpty()) {
            return Collections.emptyMap();
        }

        // 批量查询所有父节点
        LambdaQueryWrapper<CscpOrg> parentOrgLqw = new LambdaQueryWrapper<>();
        parentOrgLqw.in(CscpOrg::getOrgCode, allParentOrgCodes);
        List<CscpOrg> allParentOrgs = cscpOrgService.selectListNoAdd(parentOrgLqw);
        Map<String, CscpOrg> parentOrgMap = allParentOrgs.stream()
                .collect(Collectors.toMap(CscpOrg::getOrgCode, org -> org));

        // 构建返回结果
        Map<Long, List<CscpOrgDTO>> resultMap = new HashMap<>();
        for (CscpOrgDTO dto : orgDTOs) {
            List<String> codes = Arrays.asList(dto.getOrgCodePath().split("\\|"));
            int size = codes.size();
            if (size > 1) {
                List<CscpOrg> parentOrgs = new ArrayList<>();
                for (String code : codes.subList(0, size - 1)) {
                    CscpOrg parent = parentOrgMap.get(code);
                    if (parent != null) {
                        parentOrgs.add(parent);
                    }
                }
                resultMap.put(dto.getId(), ListCopyUtil.copy(parentOrgs, CscpOrgDTO.class));
            }
        }

        return resultMap;
    }

    // 批量加载所有子节点
    private Map<Long, List<CscpOrgDTO>> batchLoadChildrenNodes(List<CscpOrgDTO> orgDTOs) {
        // 收集所有需要查询的父节点ID
        Set<Long> allParentIds = orgDTOs.stream()
                .map(CscpOrgDTO::getId)
                .collect(Collectors.toSet());

        if (allParentIds.isEmpty()) {
            return Collections.emptyMap();
        }

        // 批量查询所有子节点
        LambdaQueryWrapper<CscpOrg> childOrgLqw = new LambdaQueryWrapper<>();
        childOrgLqw.in(CscpOrg::getParentId, allParentIds);
        childOrgLqw.orderByAsc(CscpOrg::getOrderBy);
        List<CscpOrg> allChildOrgs = cscpOrgService.selectListNoAdd(childOrgLqw);

        // 按父节点ID分组
        Map<Long, List<CscpOrg>> childrenByParentId = allChildOrgs.stream()
                .collect(Collectors.groupingBy(CscpOrg::getParentId));

        // 转换为DTO并构建结果Map
        Map<Long, List<CscpOrgDTO>> resultMap = new HashMap<>();
        for (Long parentId : allParentIds) {
            List<CscpOrg> children = childrenByParentId.getOrDefault(parentId, Collections.emptyList());
            resultMap.put(parentId, ListCopyUtil.copy(children, CscpOrgDTO.class));
        }

        return resultMap;
    }

    @Override
    public List<CscpUserDTO> selectPullCscpUser(List<TYJGSyncUserOrgIdDTO> list) {
        return cscpUserService.selectPullCscpUser(list);
    }
}
