package com.ctsi.sms.syssms.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 短信催办发送模板
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-20
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("t_sys_sms_template")
@ApiModel(value="TSysSmsTemplate 短信催办发送模板", description="短信催办发送模板")
public class TSysSmsTemplate implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键id，新增不设置，修改或其它时设值")
    private Long id;
    /**
     * 业务id
     */
    @ApiModelProperty(value = "业务id")
    private String taskId;
    /**
     * 文件标题
     */
    @ApiModelProperty(value = "文件标题")
    private String title;

    /**
     * 发送人单位
     */
    @ApiModelProperty(value = "发送人单位")
    private String sendUnit;

    /**
     * 发送人单位id
     */
    @ApiModelProperty(value = "发送人单位id")
    private Long companyId;

    /**
     * 短信发送人
     */
    @ApiModelProperty(value = "短信发送人")
    private String sendUserName;

    /**
     * 短信发送人号码
     */
    @ApiModelProperty(value = "短信发送人号码")
    private String sendUserPhone;

    /**
     * 短信接收人
     */
    @ApiModelProperty(value = "短信接收人")
    private String receiveUserName;

    /**
     * 短信接收人id
     */
    @ApiModelProperty(value = "短信接收人id")
    private Long receiveUserId;


    /**
     * 短信接收人号码
     */
    @ApiModelProperty(value = "短信接收人号码")
    private String receiveUserPhone;


    /**
     * 短信接收人备用号码
     */
    @ApiModelProperty(value = "短信接收人备用号码")
    private String receiveUserBackupPhone;

    /**
     * 短信内容
     */
    @ApiModelProperty(value = "短信内容")
    private String smsContent;

    /**
     * 来源（OA、督办）
     */
    @ApiModelProperty(value = "来源（OA、督办）")
    private String sourceType;

    /**
     * 通知时间
     */
    @ApiModelProperty(value = "通知时间")
    private LocalDateTime createTime;

    /**
     * 功能
     */
    @ApiModelProperty(value = "功能）")
    private String functionStr;
}
