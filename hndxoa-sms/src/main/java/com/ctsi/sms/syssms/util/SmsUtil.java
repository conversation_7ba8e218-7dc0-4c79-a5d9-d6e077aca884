package com.ctsi.sms.syssms.util;

import cn.hutool.core.util.StrUtil;
import com.ctsi.hndx.encryption.IEncryption;
import com.ctsi.hndx.encryption.KeyCenterUtils;
import com.ctsi.sms.syssms.entity.TSysSmsTemplate;
import com.ctsi.sms.syssms.mapper.TSysSmsTemplateMapper;
import com.ctsi.ssdc.util.SpringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * @description
 * @author: Tan<PERSON>ie
 * @create: 2024-07-10
 **/
public class SmsUtil {
	public static final Logger logger = LoggerFactory.getLogger(SmsUtil.class);


	private static TSysSmsTemplateMapper sysSmsTemplateMapper = SpringUtil.getBean(TSysSmsTemplateMapper.class);
	/**
	 * // TODO 对文件名进行敏感信息屏蔽处理。文件标题，只取前4个字+...+后四个字。＞8字：前4个字+...+后4个字；≤8字：前1个字+... +后1个字,例如《关...文》
	 * @param title
	 * @return
	 */
	public static String buildTitle(String title) {
		if(StrUtil.isNotEmpty(title)){
			int length = title.length();
			// 后期 8 需要配置 4 也是配置 todo
			if(length <= 8){
				if(length > 2){

					String firster = StrUtil.subWithLength(title , 0 , 1);
					String laster = StrUtil.subWithLength(title , title.length()-1 , 1);
					title =  firster+"..."+laster;
				}

			}
			if(length > 8){
				String firster = StrUtil.subWithLength(title , 0 , 4);
				String laster = StrUtil.subWithLength(title , title.length()-4 , 4);
				title =  firster+"..."+laster;
			}
		}
		return title;
	}

	/**
	 *【1】短信中，把省委办公厅替代为:xx处，
	 * (1)其中，敏感部门用拼音代替，如:
	 * ga一处、ga二处、ga三处、省委jyj、省bmj，省专用txj
	 *
	 * 要考虑传阅 通知公告
	 * 通信局 txj
	 * 机要局 jyj
	 * 保密局 bmj
	 * 国安  ga
	 */
	public static String buildSendUnit(TSysSmsTemplate sysSmsTemplate) {
		String newString = sysSmsTemplate.getSendUnit();
		// 公文传阅 短信通知调整 可能需要放开 tanjie 07/11
		if(StrUtil.isEmpty(newString)){
			// 需要从系统获取 接收人电话   备用号码查不到
			String send = sysSmsTemplate.getSendUserPhone();
			// 通过 电话获取 机构id, 和名称
			//this.division(sendUserPhone)
			List<Map<String,Object>> sendData =
					sysSmsTemplateMapper.getUserDeptInfoByMobile(KeyCenterUtils.encrypt(send));
			if(sendData.isEmpty()){ // 备用手机号  或者解析
				sendData = sysSmsTemplateMapper.getUserDeptInfoByMobile(send);
			}
			if(!sendData.isEmpty()){
				Map<String, Object> map = sendData.get(0);
				newString = (String) map.get("orgName");
				sysSmsTemplate.setSendUnit(newString);
				sysSmsTemplate.setSendUserName((String) map.get("realName"));
			}
		}
		if(StrUtil.isEmpty(newString)){
			return newString;
		}
		newString = newString.replace("国安" , "ga");
		newString = newString.replace("通信局" , "txj");
		newString = newString.replace("保密局" , "bmj");
		newString = newString.replace("机要局" , "jyj");
		return newString;
	}
}
