package com.ctsi.sms.syssms.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.common.SysBaseServiceImpl;
import com.ctsi.hndx.exception.BusinessException;
import com.ctsi.hndx.utils.BeanConvertUtils;
import com.ctsi.hndx.utils.ListCopyUtil;
import com.ctsi.hndx.utils.PageHelperUtil;
import com.ctsi.hndx.utils.StringUtils;
import com.ctsi.sms.syssms.entity.TSysSms;
import com.ctsi.sms.syssms.entity.dto.SmsExportDTO;
import com.ctsi.sms.syssms.entity.dto.TSendSmsMobileListDto;
import com.ctsi.sms.syssms.entity.dto.TSendSmsMoileDto;
import com.ctsi.sms.syssms.entity.dto.TSysSmsDTO;
import com.ctsi.sms.syssms.mapper.TSysSmsMapper;
import com.ctsi.sms.syssms.service.ITSysSmsService;
import com.ctsi.sms.syssmssucess.entity.TSysSmsSucess;
import com.ctsi.sms.syssmssucess.service.ITSysSmsSucessService;
import com.ctsi.ssdc.service.ExportToExcelService;
import com.ctsi.ssdc.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 系统的短信业务表 系统的短信业务表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-18
 */

@Slf4j
@Service
public class TSysSmsServiceImpl extends SysBaseServiceImpl<TSysSmsMapper, TSysSms> implements ITSysSmsService {

    @Autowired
    private TSysSmsMapper tSysSmsMapper;

    @Autowired
    private ExportToExcelService exportToExcelService;
    @Autowired
    private ITSysSmsSucessService tSysSmsSucessService;

   /* @Autowired
    private CscpUserService cscpUserService;*/

    /**
     * 翻页
     *
     * @param entityDTO
     * @param basePageForm
     * @return
     */
    @Override
    public IPage<TSysSmsDTO> queryListPage(TSysSmsDTO entityDTO, BasePageForm basePageForm) {
        //设置条件
        LambdaQueryWrapper<TSysSms> queryWrapper = new LambdaQueryWrapper();
        //发送人名字
        queryWrapper.like(StringUtils.isNotEmpty(entityDTO.getCreateName()),TSysSms::getCreateName,entityDTO.getCreateName());
        //短信发送时间
        queryWrapper.ge(StringUtils.isNotEmpty(entityDTO.getCreateTimeStart()),TSysSms::getCreateTime,entityDTO.getCreateTimeStart());
        queryWrapper.le(StringUtils.isNotEmpty(entityDTO.getCreateTimeEnd()),TSysSms::getCreateTime,entityDTO.getCreateTimeEnd());
        //按时间逆序
        queryWrapper.orderByDesc(TSysSms::getCreateTime);

        IPage<TSysSms> pageData = tSysSmsMapper.selectPage(
                PageHelperUtil.getMPlusPageByBasePage(basePageForm), queryWrapper);
        //返回
        IPage<TSysSmsDTO> data = pageData.convert(entity -> BeanConvertUtils.copyProperties(entity, TSysSmsDTO.class));
        return data;
    }

    /**
     * 列表查询
     *
     * @param entity
     * @return
     */
    @Override
    public List<TSysSmsDTO> queryList(TSysSmsDTO entityDTO) {
        LambdaQueryWrapper<TSysSms> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.orderByDesc(TSysSms::getCreateTime);
        List<TSysSms> listData = tSysSmsMapper.selectList(queryWrapper);
        List<TSysSmsDTO> TSysSmsDTOList = ListCopyUtil.copy(listData, TSysSmsDTO.class);
        return TSysSmsDTOList;
    }

    /**
     * 单个查询
     *
     * @param id the id of the entity
     * @return
     */
    @Override
    public TSysSmsDTO findOne(Long id) {
        TSysSms tSysSms = tSysSmsMapper.selectById(id);
        return BeanConvertUtils.copyProperties(tSysSms, TSysSmsDTO.class);
    }


    /**
     * 新增
     *
     * @param entity the entity to create
     * @return
     */
    @Override
    @Transactional
    public TSysSmsDTO create(TSysSmsDTO entityDTO) {
        TSysSms tSysSms = BeanConvertUtils.copyProperties(entityDTO, TSysSms.class);
        save(tSysSms);
        return BeanConvertUtils.copyProperties(tSysSms, TSysSmsDTO.class);
    }

    /**
     * 修改
     *
     * @param entity the entity to update
     * @return
     */
    @Override
    @Transactional
    public int update(TSysSmsDTO entity) {
        TSysSms tSysSms = BeanConvertUtils.copyProperties(entity, TSysSms.class);
        return tSysSmsMapper.updateById(tSysSms);
    }

    /**
     * 删除
     *
     * @param id the id of the entity
     */
    @Override
    @Transactional
    public int delete(Long id) {
        return tSysSmsMapper.deleteById(id);
    }


    /**
     * 验证是否存在
     *
     * @param TSysSmsId
     * @return
     */
    @Override
    public boolean existByTSysSmsId(Long TSysSmsId) {
        if (TSysSmsId != null) {
            LambdaQueryWrapper<TSysSms> queryWrapper = new LambdaQueryWrapper();
            queryWrapper.eq(TSysSms::getId, TSysSmsId);
            List<TSysSms> result = tSysSmsMapper.selectList(queryWrapper);
            return result.size() > 0;
        }
        return true;
    }

    /**
     * 批量新增
     */
    @Override
    @Transactional
    public Boolean insertBatch(List<TSysSmsDTO> dataList) {
        List<TSysSms> result = ListCopyUtil.copy(dataList, TSysSms.class);
        return saveBatch(result);
    }

    @Override
    public boolean tSendSmsMobile(TSendSmsMoileDto sendSmsDto) {
        String moilePhone = sendSmsDto.getReceivePhone();
        if (StringUtils.isMobile(moilePhone)) {
            TSysSms tSysSms = new TSysSms();
            tSysSms.setReceivePhone(moilePhone);
            tSysSms.setSendContent("你有一份标题为：" + sendSmsDto.getTitile() + "的业务待处理，请前往OA处理");
            tSysSmsMapper.insert(tSysSms);
            return true;
        } else {
            throw new BusinessException("手机号码错误");
        }

    }

    @Override
    public boolean tSendSmsUserIdDto(TSendSmsMobileListDto tSendSmsDto) {

        String moilePhone = null;//cscpUserService.getById(tSendSmsDto.getUserId()).getMobile();
        if (StringUtils.isMobile(moilePhone)) {
            TSysSms tSysSms = new TSysSms();
            tSysSms.setReceivePhone(moilePhone);
            tSysSms.setSendContent("你有一份标题为：" + tSendSmsDto.getTitile() + "的业务待处理，请前往OA处理");
            tSysSmsMapper.insert(tSysSms);
            return true;
        } else {
            throw new BusinessException("手机号码错误");
        }
    }

    @Override
    public void exportSwSmsExport(HttpServletResponse response) {
        LambdaQueryWrapper<TSysSms> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TSysSms::getFailNumber,0);
        List<TSysSms> bizSysSmsList = tSysSmsMapper.selectListNoAdd(queryWrapper);
        List<TSysSmsSucess> list =new  ArrayList<>();
        List<SmsExportDTO> smsExportDTOList = new ArrayList<>();
        List<Long> idList = new ArrayList<>();
        if (bizSysSmsList != null){
            bizSysSmsList.forEach(tSysSms -> {
                TSysSmsSucess tSysSmsSucess = new TSysSmsSucess();
                tSysSmsSucess.setReceivePhone(tSysSms.getReceivePhone());
                tSysSmsSucess.setSendContent(tSysSmsSucess.getSendContent());
                tSysSmsSucess.setSendStatus(1);
                list.add(tSysSmsSucess);
                idList.add(tSysSms.getId());
                SmsExportDTO smsExportDTO = new SmsExportDTO();
                smsExportDTO.setReceivePhone(tSysSms.getReceivePhone());
                smsExportDTO.setSendContent(tSysSms.getSendContent());
                smsExportDTOList.add(smsExportDTO);
            });
        }
        if (tSysSmsSucessService.saveBatch(list)){
            tSysSmsMapper.deleteBatchIds(idList);
        }
        Boolean bool = true;
        try {
            String fileName = "sw_duanxin_"+ DateUtil.getCurrentTime();
            bool = exportToExcelService.exportToExcel(fileName,smsExportDTOList, SmsExportDTO.class, response);
        } catch (IOException e) {

            bool = false;
        }

    }


}
