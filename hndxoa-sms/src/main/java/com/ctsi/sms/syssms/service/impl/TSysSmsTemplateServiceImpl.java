package com.ctsi.sms.syssms.service.impl;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.common.SysBaseServiceImpl;
import com.ctsi.hndx.exception.BusinessException;
import com.ctsi.hndx.tsysconfig.service.ISysConfigService;
import com.ctsi.hndx.utils.BeanConvertUtils;
import com.ctsi.hndx.utils.ListCopyUtil;
import com.ctsi.hndx.utils.PageHelperUtil;
import com.ctsi.hndx.utils.StringUtils;
import com.ctsi.sms.smssend.SwSmsSendUtil;
import com.ctsi.sms.syssms.entity.TSysSmsTemplate;
import com.ctsi.sms.syssms.entity.dto.SmsUserMobileDTO;
import com.ctsi.sms.syssms.entity.dto.TSysSmsTemplateExcelDTO;
import com.ctsi.sms.syssms.mapper.TSysSmsTemplateMapper;
import com.ctsi.sms.syssms.service.ITSysSmsTemplateService;
import com.ctsi.sms.syssms.util.SmsUtil;
import com.ctsi.ssdc.entity.SwSmsNotificationRecords;
import com.ctsi.ssdc.model.PageResult;
import com.ctsi.ssdc.repository.SwSmsNotificationRecordsMapper;
import com.ctsi.ssdc.security.SecurityUtils;
import com.ctsi.ssdc.service.ExportToExcelService;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <p>
 * 短信催办发送模板 ����ʵ����
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-20
 */
@Slf4j
@Service
public class TSysSmsTemplateServiceImpl extends SysBaseServiceImpl<TSysSmsTemplateMapper, TSysSmsTemplate> implements ITSysSmsTemplateService {

    @Autowired
    private TSysSmsTemplateMapper tSysSmsTemplateMapper;
    @Autowired
    private ExportToExcelService exportToExcelService;
    @Autowired
    private ISysConfigService sysConfigService;
    @Autowired
    private SwSmsNotificationRecordsMapper smsNotificationRecordsMapper;

    /*
    * 批量更新催办通知次数
    * */
    private static final int BATCH_SIZE = 200;


    /**
     * ��ҳ
     *
     * @param entityDTO
     * @param basePageForm
     * @return
     */
    @Override
    public PageResult<TSysSmsTemplateExcelDTO> queryListPage(TSysSmsTemplateExcelDTO entityDTO, BasePageForm basePageForm) {
        //��������
        LambdaQueryWrapper<TSysSmsTemplate> queryWrapper = new LambdaQueryWrapper();

        IPage<TSysSmsTemplate> pageData = tSysSmsTemplateMapper.selectPage(
             PageHelperUtil.getMPlusPageByBasePage(basePageForm), queryWrapper);
        //����
        IPage<TSysSmsTemplateExcelDTO> data  = pageData.convert(entity -> BeanConvertUtils.copyProperties(entity, TSysSmsTemplateExcelDTO.class));

        return new PageResult<TSysSmsTemplateExcelDTO>(data.getRecords(),
            data.getTotal(), data.getCurrent());
    }

    /**
     * �б��ѯ
     *
     * @param entityDTO
     * @return
     */
    @Override
    public List<TSysSmsTemplateExcelDTO> queryList(TSysSmsTemplateExcelDTO entityDTO) {
        LambdaQueryWrapper<TSysSmsTemplate> queryWrapper = new LambdaQueryWrapper();
            List<TSysSmsTemplate> listData = tSysSmsTemplateMapper.selectList(queryWrapper);
            List<TSysSmsTemplateExcelDTO> TSysSmsTemplateExcelDTOList = ListCopyUtil.copy(listData, TSysSmsTemplateExcelDTO.class);
        return TSysSmsTemplateExcelDTOList;
    }

    /**
     * ������ѯ
     *
     * @param id the id of the entity
     * @return
     */
    @Override
    public TSysSmsTemplateExcelDTO findOne(Long id) {
        TSysSmsTemplate  tSysSmsTemplate =  tSysSmsTemplateMapper.selectById(id);
        return  BeanConvertUtils.copyProperties(tSysSmsTemplate, TSysSmsTemplateExcelDTO.class);
    }


    /**
     * ����
     *
     * @param entityDTO the entity to create
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public TSysSmsTemplateExcelDTO create(TSysSmsTemplateExcelDTO entityDTO) {
       TSysSmsTemplate tSysSmsTemplate =  BeanConvertUtils.copyProperties(entityDTO,TSysSmsTemplate.class);
        save(tSysSmsTemplate);
        return  BeanConvertUtils.copyProperties(tSysSmsTemplate, TSysSmsTemplateExcelDTO.class);
    }

    /**
     * �޸�
     *
     * @param entity the entity to update
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int update(TSysSmsTemplateExcelDTO entity) {
        TSysSmsTemplate tSysSmsTemplate = BeanConvertUtils.copyProperties(entity,TSysSmsTemplate.class);
        return tSysSmsTemplateMapper.updateById(tSysSmsTemplate);
    }

    /**
     * ɾ��
     *
     * @param id the id of the entity
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int delete(Long id) {
        return tSysSmsTemplateMapper.deleteById(id);
    }


    /**
     * ��֤�Ƿ����
     *
     * @param TSysSmsTemplateId
     * @return
     */
    @Override
    public boolean existByTSysSmsTemplateId(Long TSysSmsTemplateId) {
        if (TSysSmsTemplateId != null) {
            LambdaQueryWrapper<TSysSmsTemplate> queryWrapper = new LambdaQueryWrapper();
            queryWrapper.eq(TSysSmsTemplate::getId, TSysSmsTemplateId);
            List<TSysSmsTemplate> result = tSysSmsTemplateMapper.selectList(queryWrapper);
            return result.size() > 0;
        }
        return true;
    }

    /**
    * ��������
    *
    */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertBatch(List<TSysSmsTemplateExcelDTO> dataList) {
        List<TSysSmsTemplate> result = ListCopyUtil.copy(dataList, TSysSmsTemplate.class);
        return saveBatch(result);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void synchronizeSms() {
        // 查询呈批件未发送短信的任务
        List<TSysSmsTemplate> tSysSmsTemplateList = tSysSmsTemplateMapper.selectSmsTemplateList();

        // 匹配中国手机号码的正则表达式
        String regex = "(1[3-9]\\d{9})";
        Pattern pattern = Pattern.compile(regex);
        long currentUserId = SecurityUtils.getCurrentUserId();
        String realName = SecurityUtils.getCurrentRealName();

        ArrayList<TSysSmsTemplate> smsTemplateBackupPhoneList = new ArrayList<>();
        List<SwSmsNotificationRecords> smsNotificationRecords = new ArrayList<>();
        for (TSysSmsTemplate tSysSmsTemplate : tSysSmsTemplateList) {
            SwSmsNotificationRecords records = new SwSmsNotificationRecords();
            records.setDocumentId(0L);
            records.setNotifiCount(1);
            records.setType(null);
            records.setUrgingTime(new Date());
            records.setUrgingContent(tSysSmsTemplate.getSmsContent());
            records.setNoticePeople(tSysSmsTemplate.getReceiveUserPhone()+","+tSysSmsTemplate.getReceiveUserBackupPhone());
            records.setTaskId(Convert.toLong(tSysSmsTemplate.getTaskId()));
            records.setNotifyPersonnelList(Lists.newArrayList());
            records.setUpdateTime(LocalDateTime.now());
            records.setCreateBy(currentUserId);
            records.setCreateName(realName);
            records.setCreateTime(LocalDateTime.now());
            records.setDeleted(0);
            smsNotificationRecords.add(records);

            //配置备用号码
            if (StrUtil.isNotBlank(tSysSmsTemplate.getReceiveUserBackupPhone())) {
                String[] backupMobileStrList = tSysSmsTemplate.getReceiveUserBackupPhone().split(",");
                for (String backupMobile : backupMobileStrList) {
                    TSysSmsTemplate tSysSmsTemplateBackPhone = BeanConvertUtils.copyProperties(tSysSmsTemplate, TSysSmsTemplate.class);
                    tSysSmsTemplateBackPhone.setReceiveUserPhone(backupMobile);
                    smsTemplateBackupPhoneList.add(tSysSmsTemplateBackPhone);
                }
            }
        }

        // 添加呈批件接收人备用号码
        tSysSmsTemplateList.addAll(smsTemplateBackupPhoneList);

        // 查询传阅和通知公告催办短信
        LambdaQueryWrapper<SwSmsNotificationRecords> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.isNotNull(SwSmsNotificationRecords::getType)
                .eq(SwSmsNotificationRecords::getNotifiCount,0)
                .orderByDesc(SwSmsNotificationRecords::getCreateTime)
        ;

        // 传阅和通知的待催办记录列表
        List<SwSmsNotificationRecords> circulateNoticeRecordList = smsNotificationRecordsMapper.selectListNoAdd( queryWrapper);
        if(CollectionUtils.isNotEmpty(circulateNoticeRecordList)) {
            List<Long> documentIdList = circulateNoticeRecordList.stream().map(i -> i.getDocumentId()).collect(Collectors.toList());
            // 待催办用户id和电话
            List<SmsUserMobileDTO> smsUserMobileDTOList = tSysSmsTemplateMapper.selectCirculateNoticeList(documentIdList);
            Map<Long, String> userMobileMap = smsUserMobileDTOList.stream()
                    .collect(Collectors.toMap(
                            SmsUserMobileDTO::getUserId,
                            SmsUserMobileDTO::getBackupMobile,
                            (v1, v2) -> {
                                if (v1 != null) {
                                    return v1;
                                } else if (v2 != null) {
                                    return v2;
                                } else {
                                    return "";
                                }
                            }
                    ));
            List<Long> userids = smsUserMobileDTOList.stream().map(i -> i.getUserId()).collect(Collectors.toList());

            // 组装传阅和通知公告的催办短信模板
            for (SwSmsNotificationRecords record : circulateNoticeRecordList) {
                // 校验短信发送人号码是否存在。(短信存在与催办内容中，需正则匹配)
                String sendUserPhone = null;
                if (StrUtil.isNotBlank(record.getUrgingContent())) {
                    Matcher matcher = pattern.matcher(record.getUrgingContent());
                    if (matcher.find()) {
                        sendUserPhone = matcher.group(1);
                    }
                }

                // 过滤掉已经被催办的用户
                List<SwSmsNotificationRecords.NotifyPersonnel> notifyPersonnelList = JSON.parseArray(record.getNoticePeople(), SwSmsNotificationRecords.NotifyPersonnel.class);
                notifyPersonnelList = notifyPersonnelList.stream().filter(i -> userids.contains(i.getUserID())).collect(Collectors.toList());

                ArrayList<String> receiveUserPhoneList = new ArrayList<>();
                for (SwSmsNotificationRecords.NotifyPersonnel notifyPersonnel : notifyPersonnelList) {
                    //添加催办通知里的号码
                    receiveUserPhoneList.add(notifyPersonnel.getPhone());
                    String backupMobileStr = userMobileMap.get(notifyPersonnel.getUserID());
                    // 手机好没有问题设值
                    if (StringUtils.checkFormatMobiles(backupMobileStr)) {
                        String[] split = backupMobileStr.split("[，,]");
                        for (String backupMobile : split) {
                            // 如果存在备用号码, 则添加
                            receiveUserPhoneList.add(backupMobile);
                        }
                    }
                }


                for (String receiveUserPhone : receiveUserPhoneList) {
                    TSysSmsTemplate tSysSmsTemplate = new TSysSmsTemplate();

                    tSysSmsTemplate.setTaskId(record.getId().toString());
                    tSysSmsTemplate.setSendUserPhone(sendUserPhone);
                    tSysSmsTemplate.setReceiveUserPhone(receiveUserPhone);
                    tSysSmsTemplate.setSmsContent(record.getUrgingContent());

                    tSysSmsTemplate.setSourceType("1");
                    if (ObjectUtil.equal(record.getType(), 2)) {
                        tSysSmsTemplate.setFunctionStr("信息传阅");
                    } else {
                        tSysSmsTemplate.setFunctionStr("通知公告");
                    }

                    // 传阅和通知公告催办人员添加
                    tSysSmsTemplateList.add(tSysSmsTemplate);
                }
            }
        }

        // 同步待发短信到短信模板表 t_sys_sms_template
        saveBatch(tSysSmsTemplateList);

        //更新传阅和通知公告，催办次数
        List<Long> idList = circulateNoticeRecordList.stream().map(i -> i.getId()).collect(Collectors.toList());
        List<Long> batchIds = new ArrayList<>();
        for (int i = 0; i < idList.size(); i++) {
            batchIds.add(idList.get(i));
            if (batchIds.size() == BATCH_SIZE || i == idList.size() - 1) {
                tSysSmsTemplateMapper.batchUpdateCount(batchIds);
                batchIds.clear();
            }
        }

        // 插入呈批件发送催办记录
        for (SwSmsNotificationRecords smsNotificationRecord : smsNotificationRecords) {
            smsNotificationRecordsMapper.insert(smsNotificationRecord);
        }
    }


    @Override
    public void exportSmsTemplate(HttpServletResponse response) {
        //导出短信模板表 t_sys_sms_template 中的数据到excel
        LambdaQueryWrapper<TSysSmsTemplate> queryWrapper = new LambdaQueryWrapper<>();
        List<TSysSmsTemplate> sysSmsTemplateList = tSysSmsTemplateMapper.selectListNoAdd(queryWrapper);
        if(CollectionUtils.isEmpty(sysSmsTemplateList)){
            throw new BusinessException("请先同步数据后再进行导出操作！");
        }

        // 查找 厅级省级领导 角色 含有的用户id
        List<Long> ids = tSysSmsTemplateMapper.selectRoleUserIdsByRoleName("厅级省级领导");

        ArrayList<TSysSmsTemplateExcelDTO> smsExportDTOList = new ArrayList<>();
        // 配置短信发送内容
        String smsContent = sysConfigService.getSysConfigValueByCode(SwSmsSendUtil.QIANPI_SMS_CONTENT_NEW);
        for (TSysSmsTemplate tSysSmsTemplate : sysSmsTemplateList) {


            // TODO 对文件名进行敏感信息屏蔽处理。文件标题，只取前4个字+...+后四个字。＞8字：前4个字+...+后4个字；≤8字：前1个字+... +后1个字,例如《关...文》
            //报送给厅领导的：
            //【政务短信】您有文件《关于勤工...县的报告》待签批，请登录内网协同办公系统进行处理。
            //报送给其他的：
            //【政务短信】您有文件待签批，请登录内网协同办公系统进行处理。
            //报送给其他的：
            if (StrUtil.equals(tSysSmsTemplate.getFunctionStr(),"呈批件催办")) {
                String sendUnit = SmsUtil.buildSendUnit(tSysSmsTemplate);
                String title  = SmsUtil.buildTitle(tSysSmsTemplate.getTitle()) ;
                if(ids.contains(tSysSmsTemplate.getReceiveUserId())){ // 领导
                    String content = String.format(smsContent,"《"+title+"》", sendUnit ,
                            tSysSmsTemplate.getSendUserName(), tSysSmsTemplate.getSendUserPhone());
                    tSysSmsTemplate.setSmsContent(content);
                }else {
                    String content = String.format(smsContent,"", sendUnit ,
                            tSysSmsTemplate.getSendUserName(), tSysSmsTemplate.getSendUserPhone());
                    tSysSmsTemplate.setSmsContent(content);
                }
            }



            TSysSmsTemplateExcelDTO excelDTO = new TSysSmsTemplateExcelDTO();
            excelDTO.setId(tSysSmsTemplate.getId());
            excelDTO.setTaskId(tSysSmsTemplate.getTaskId());
            excelDTO.setReceiveUserPhone(tSysSmsTemplate.getReceiveUserPhone());
            excelDTO.setSmsContent(tSysSmsTemplate.getSmsContent());
            excelDTO.setSendUserPhone(tSysSmsTemplate.getSendUserPhone());
            excelDTO.setSourceType(tSysSmsTemplate.getSourceType());
            excelDTO.setFunctionStr(tSysSmsTemplate.getFunctionStr());
            smsExportDTOList.add(excelDTO);
        }

        tSysSmsTemplateMapper.deleteBatchIds(sysSmsTemplateList.stream().map(i->i.getId()).collect(Collectors.toList()));

        Boolean bool = true;
        String fileName = "短信催办记录表";
        try {
            bool = exportToExcelService.exportToExcel(fileName,smsExportDTOList, TSysSmsTemplateExcelDTO.class, response);
        } catch (IOException e) {
            bool = false;
        }
    }


}
