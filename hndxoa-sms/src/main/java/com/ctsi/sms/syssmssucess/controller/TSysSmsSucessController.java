package com.ctsi.sms.syssmssucess.controller;

import com.ctsi.hndx.annotations.ResponseResultVo;
import com.ctsi.hndx.common.BaseController;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.enums.DBOperation;
import com.ctsi.hndx.result.ResultCode;
import com.ctsi.hndx.result.ResultVO;
import com.ctsi.sms.syssmssucess.entity.dto.TSysSmsSucessDTO;
import com.ctsi.sms.syssmssucess.service.ITSysSmsSucessService;
import com.ctsi.ssdc.annotation.OperationLog;
import com.ctsi.ssdc.model.PageResult;
import com.ctsi.ssdc.model.ResResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;

import java.net.URISyntaxException;
import java.util.List;


/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-26
 *
 */

@Slf4j
@RestController
@ResponseResultVo
@RequestMapping("/api/tSysSmsSucess")
@Api(value = "短信发送成功后的记录表", tags = "短信发送成功后的记录表接口")
public class TSysSmsSucessController extends BaseController {

    private static final String ENTITY_NAME = "tSysSmsSucess";

    @Autowired
    private ITSysSmsSucessService tSysSmsSucessService;



    /**
     *  新增短信发送成功后的记录表批量数据.
     */
    @PostMapping("/createBatch")
    @ApiOperation(value = "新增批量", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.ADD,message = "新增短信发送成功后的记录表批量数据")
    public ResultVO createBatch(@RequestBody List<TSysSmsSucessDTO> tSysSmsSucessList) {
       Boolean  result = tSysSmsSucessService.insertBatch(tSysSmsSucessList);
       if(result){
           return ResultVO.success();
       }else {
           return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
       }
    }

     /**
     *  新增数据.
     */
    @PostMapping("/create")
    @ApiOperation(value = "新增", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.ADD,message = "新增短信发送成功后的记录表数据")
    public ResultVO<TSysSmsSucessDTO> create(@RequestBody TSysSmsSucessDTO tSysSmsSucessDTO) throws URISyntaxException {
        TSysSmsSucessDTO result = tSysSmsSucessService.create(tSysSmsSucessDTO);
        return ResultVO.success(result);
    }

    /**
     *  更新存在数据.
     */
    @PostMapping("/update")
    @ApiOperation(value = "更新存在数据", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.UPDATE,message = "更新短信发送成功后的记录表数据")
    public ResultVO update(@RequestBody TSysSmsSucessDTO tSysSmsSucessDTO) {
	    Assert.notNull(tSysSmsSucessDTO.getId(), "general.IdNotNull");
        int count = tSysSmsSucessService.update(tSysSmsSucessDTO);
        if(count > 0 ){
            return ResultVO.success();
        }else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }

     /**
     *  删除存在数据.
     */
    @DeleteMapping("/delete/{id}")
    @ApiOperation(value = "删除存在数据", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.DELETE,message = "删除短信发送成功后的记录表数据")
    public ResultVO delete(@PathVariable Long id) {
        int count = tSysSmsSucessService.delete(id);
        if(count > 0 ){
            return ResultVO.success();
        }else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }

    /**
     * 查询单条数据.
     */
    @GetMapping("/get/{id}")
    @ApiOperation(value = "查询单条数据", notes = "传入参数")
    public ResultVO get(@PathVariable Long id) {
        TSysSmsSucessDTO tSysSmsSucessDTO = tSysSmsSucessService.findOne(id);
        return ResultVO.success(tSysSmsSucessDTO);
    }

    /**
    *  分页查询多条数据.
    */
    @GetMapping("/queryTSysSmsSucessPage")
    @ApiOperation(value = "翻页查询多条数据", notes = "传入参数")
    public ResultVO<PageResult<TSysSmsSucessDTO>> queryTSysSmsSucessPage(TSysSmsSucessDTO tSysSmsSucessDTO, BasePageForm basePageForm) {
        return ResultVO.success(tSysSmsSucessService.queryListPage(tSysSmsSucessDTO, basePageForm));
    }

   /**
    * 查询多条数据.不分页
    */
   @GetMapping("/queryTSysSmsSucess")
   @ApiOperation(value = "查询多条数据", notes = "传入参数")
   public ResultVO<ResResult<TSysSmsSucessDTO>> queryTSysSmsSucess(TSysSmsSucessDTO tSysSmsSucessDTO) {
       List<TSysSmsSucessDTO> list = tSysSmsSucessService.queryList(tSysSmsSucessDTO);
       return ResultVO.success(new ResResult<TSysSmsSucessDTO>(list));
   }

}
