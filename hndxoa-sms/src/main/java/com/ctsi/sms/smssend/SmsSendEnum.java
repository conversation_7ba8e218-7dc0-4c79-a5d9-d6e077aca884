package com.ctsi.sms.smssend;

/**
 * 短信发送的模板
 */
public enum SmsSendEnum {

    /**
     * 自定义的短信模板
     */
    CUST_PROCESS_SMS("您现有一份标题为{0}的{1}，请您前往协同办公处理"),

    CUST_PROCESS_PS_SMS("您有一份标题为《{0}》的文件{1}已批示，请您前往协同办公查看详情"),

    PROCESS_SMS("您有一份标题为《{}》的待办，请您前往协同办公处理"),

    NOTICE_SMS("您有一份标题为《{}》的通知公告，请您前往协同办公查阅"),

    DOCUMENT_CIRCULATE_SMS("您有一份标题为《{}》的公文传阅，请您前往协同办公查阅"),

    WITHDRAW_SMS("您有一份标题为《{0}》的待办文件已被撤回。发送人：{1}"),
    /**
     * 短信催办
     */
    URGE_SMS("您有一份标题为《{}》的通告待查阅，请您前往协同办公查阅"),

    DEFAULT_SMS("您有一份标题为《{}》的待办，请您前往协同办公处理"),

    USER_CAPTCHA("验证码{}，5分钟内有效"),

    //每日汇报
    DAILY_REPORT_SMS("您有一份标题为《{}》的文件待查阅，请您前往协同办公处理"),

    /**
     * 怀化政务效能的模板
     */
    HH_TASK_SMS("您有一个任务名称为{}待反馈，请您前往协同办公处理"),

    HH_TASK_SMS_SIGN("您有一个任务名称为{}待签收，请您前往协同办公处理");


    private String message;

    SmsSendEnum(String message) {
        this.message = message;
    }

    public String getMessage() {
        return message;
    }
}