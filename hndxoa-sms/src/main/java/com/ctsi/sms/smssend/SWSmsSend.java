package com.ctsi.sms.smssend;

import cn.hutool.core.collection.CollectionUtil;
import com.ctsi.hndx.constant.SysConfigConstant;
import com.ctsi.hndx.constant.SysConstant;
import com.ctsi.hndx.tsysconfig.service.ISysConfigService;
import com.ctsi.sms.syssms.entity.TSysSms;
import com.ctsi.sms.syssms.entity.dto.SmsSendInfoDto;
import com.ctsi.sms.syssms.service.ITSysSmsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

/**
 * 先将短信放入失败表， tSysSms.setSendStatus(1);
 *                 tSysSms.setFailNumber(0);
 *                 然后从失败表钟导出failNumber为0的，状态为1的，放入成功表
 */
@Slf4j
public class SWSmsSend implements SmsSend {



    @Autowired
    private ITSysSmsService tSysSmsService;
    @Autowired
    private ISysConfigService sysConfigService;


    @Override
    @Async(value = SysConstant.EXECUTOR_ASYNC_NAME)
    public void sysSmsAddAndSend(String mobile, String smsContent, Authentication authentication) {
        SmsConditionEntity build = SmsConditionEntity.builder().mobile(mobile).smsContent(smsContent).authentication(authentication).smsSendInfoDto(new SmsSendInfoDto()).build();
        sysSmsAddAndSend(build);
    }

    @Override
    @Async(value = SysConstant.EXECUTOR_ASYNC_NAME)
    public void sysSmsAddAndSend(SmsConditionEntity smsConditionEntity) {
        if (smsConditionEntity.getAuthentication() != null) {
            SecurityContextHolder.getContext().setAuthentication(smsConditionEntity.getAuthentication());
        }
        //  先 异步发送
        boolean hasUserSmsSend = hasUserSmsSend();
        if (hasUserSmsSend) {
            Collection<String> mobileList = new ArrayList<>();
            mobileList.add(smsConditionEntity.getMobile());
            failSave(mobileList,smsConditionEntity.getSmsContent());
        }
    }


    @Override
    @Async(value = SysConstant.EXECUTOR_ASYNC_NAME)
    public void sysBatchSmsAddAndSend(Collection<String> mobileList, String smsContent, Authentication authentication) {
        if (authentication != null) {
            SecurityContextHolder.getContext().setAuthentication(authentication);
        }
        //  先 异步发送
        boolean hasUserSmsSend = hasUserSmsSend();
        if (hasUserSmsSend) {
            failSave(mobileList, smsContent);
        }
    }


    /**
     * 是否启用短信
     *
     * @return
     */
    @Override
    public final boolean hasUserSmsSend() {
        String sysConfigValueByCode = sysConfigService.getSysConfigValueByCode(SysConfigConstant.HAS_USE_SMS);
        if ("1".equalsIgnoreCase(sysConfigValueByCode)) {
            return true;
        } else {
            return false;
        }
    }

    // 发送失败入库
    @Transactional(rollbackFor = Exception.class)
    public void failSave(Collection<String> smsResultList, String smsContent) {
        List<TSysSms> list = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(smsResultList)) {
            smsResultList.forEach(smsResult -> {
                TSysSms tSysSms = new TSysSms();
                tSysSms.setReceivePhone(smsResult);
                tSysSms.setSendContent(smsContent);
                tSysSms.setSendStatus(1);
                tSysSms.setFailNumber(0);
                tSysSms.setFailReason("先暂存，再扫码");
                list.add(tSysSms);
            });
        }
        tSysSmsService.saveBatch(list);
    }




}
