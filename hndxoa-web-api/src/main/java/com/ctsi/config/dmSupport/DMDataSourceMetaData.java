package com.ctsi.config.dmSupport;

import com.google.common.base.Strings;
import lombok.Getter;
import org.apache.shardingsphere.spi.database.metadata.DataSourceMetaData;
import org.apache.shardingsphere.underlying.common.database.metadata.UnrecognizedDatabaseURLException;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Data source meta data for Oracle.
 */
@Getter
public final class DMDataSourceMetaData implements DataSourceMetaData {

    private static final int DEFAULT_PORT = 5236;

    private final String hostName;

    private final int port;

    private final String catalog;

    private final String schema;

    private final Pattern pattern = Pattern.compile("jdbc:dm://([\\w\\-\\.]+):?([0-9]*)(/?)([\\w\\-]*)", Pattern.CASE_INSENSITIVE);
//    private final Pattern pattern = Pattern.compile("jdbc:oracl([\\w\\-\\.]+):?([0-9]*)[:/]([\\w\\-]+)", Pattern.CASE_INSENSITIVE);

    public DMDataSourceMetaData(final String url, final String username) {
        System.out.println("url-----------------"+url);
        Matcher matcher = pattern.matcher(url);
        if (!matcher.find()) {
            throw new UnrecognizedDatabaseURLException(url, pattern.pattern());
        }
        hostName = matcher.group(1);
        port = Strings.isNullOrEmpty(matcher.group(2)) ? DEFAULT_PORT : Integer.valueOf(matcher.group(2));
        catalog = matcher.group(4); // 修复：使用第4组作为catalog（数据库名）
        schema = username;
    }

    public DMDataSourceMetaData(final String url) {
        this(url, null);
    }
}
