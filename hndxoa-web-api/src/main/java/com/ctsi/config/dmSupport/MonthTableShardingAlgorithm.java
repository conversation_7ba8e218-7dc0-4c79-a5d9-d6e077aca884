package com.ctsi.config.dmSupport;

import com.google.common.collect.Range;
import org.apache.shardingsphere.api.sharding.standard.PreciseShardingAlgorithm;
import org.apache.shardingsphere.api.sharding.standard.PreciseShardingValue;
import org.apache.shardingsphere.api.sharding.standard.RangeShardingAlgorithm;
import org.apache.shardingsphere.api.sharding.standard.RangeShardingValue;

import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.stream.Collectors;

/**
 * 按月分表算法 - 适配ShardingSphere 4.1.1
 * 实现精确分片和范围分片接口
 * 使用Timestamp类型以兼容ShardingSphere 4.1.1
 */
public class MonthTableShardingAlgorithm implements PreciseShardingAlgorithm<Timestamp>, RangeShardingAlgorithm<Timestamp> {

    private static final String DATE_PATTERN = "yyyyMM";
    private static final int MIN_YEAR_MONTH = 202504;

    // 动态获取当前年月
    private int getCurrentYearMonth() {
        LocalDateTime now = LocalDateTime.now();
        return now.getYear() * 100 + now.getMonthValue();
    }

    /**
     * 精确分片算法
     */
    @Override
    public String doSharding(Collection<String> tableNames, PreciseShardingValue<Timestamp> shardingValue) {
        Timestamp value = shardingValue.getValue();
        if (value == null) {
            throw new IllegalArgumentException("分片值不能为空");
        }

        // 将Timestamp转换为LocalDateTime，然后格式化
        LocalDateTime localDateTime = value.toLocalDateTime();
        String yearMonthStr = localDateTime.format(DateTimeFormatter.ofPattern(DATE_PATTERN));
        int currentYearMonth = Integer.parseInt(yearMonthStr);

        // 获取当前系统时间的年月
        int systemYearMonth = getCurrentYearMonth();

        // 如果分片值对应的年月 > 当前系统年月，禁止分到未来表
        if (currentYearMonth > systemYearMonth) {
            throw new IllegalArgumentException("不能分片到未来月份: " + yearMonthStr);
        }

        // 处理早于 202504 的情况，使用最小年月
        int targetYearMonth = Math.max(currentYearMonth, MIN_YEAR_MONTH);
        String targetSuffix = String.valueOf(targetYearMonth);

        // 查找匹配的表名
        for (String tableName : tableNames) {
            if (tableName.endsWith("_" + targetSuffix)) {
                return tableName;
            }
        }

        // 如果没有找到匹配的表，抛出异常
        throw new IllegalArgumentException("未找到匹配的分片表，分片值: " + value +
                ", 目标后缀: " + targetSuffix + ", 可用表: " + tableNames);
    }

    /**
     * 范围分片算法
     */
    @Override
    public Collection<String> doSharding(Collection<String> tableNames, RangeShardingValue<Timestamp> shardingValue) {
        Collection<String> result = new java.util.ArrayList<>();

        // 获取范围的起始和结束时间
        Range<Timestamp> valueRange = shardingValue.getValueRange();
        Timestamp lowerEndpoint = valueRange.hasLowerBound() ? valueRange.lowerEndpoint() : null;
        Timestamp upperEndpoint = valueRange.hasUpperBound() ? valueRange.upperEndpoint() : null;

        // 如果没有范围限制，返回所有表
        if (lowerEndpoint == null && upperEndpoint == null) {
            return tableNames;
        }

        // 获取当前系统时间的年月
        int systemYearMonth = getCurrentYearMonth();

        // 计算涉及的年月范围
        int startYearMonth = MIN_YEAR_MONTH;
        int endYearMonth = systemYearMonth; // 最大年月限制为当前年月

        if (lowerEndpoint != null) {
            LocalDateTime lowerDateTime = lowerEndpoint.toLocalDateTime();
            int lowerYearMonth = Integer.parseInt(lowerDateTime.format(DateTimeFormatter.ofPattern(DATE_PATTERN)));
            startYearMonth = Math.max(lowerYearMonth, MIN_YEAR_MONTH);
        }

        if (upperEndpoint != null) {
            LocalDateTime upperDateTime = upperEndpoint.toLocalDateTime();
            int upperYearMonth = Integer.parseInt(upperDateTime.format(DateTimeFormatter.ofPattern(DATE_PATTERN)));
            // 限制最大年月为当前系统时间
            endYearMonth = Math.min(upperYearMonth, systemYearMonth);
        }

        // 查找涉及的表
        for (int yearMonth = startYearMonth; yearMonth <= endYearMonth; yearMonth++) {
            // 跳过不存在的月份（如13月）
            int month = yearMonth % 100;
            if (month > 12 || month < 1) {
                continue;
            }

            String suffix = String.valueOf(yearMonth);
            for (String tableName : tableNames) {
                if (tableName.endsWith("_" + suffix)) {
                    result.add(tableName);
                    break;
                }
            }
        }

        // 去重并排序
        return result.stream().distinct().sorted(Comparator.naturalOrder()).collect(Collectors.toList());
    }
}