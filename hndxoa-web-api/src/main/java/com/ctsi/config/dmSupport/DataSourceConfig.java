package com.ctsi.config.dmSupport;

import com.baomidou.dynamic.datasource.DynamicRoutingDataSource;
import com.baomidou.dynamic.datasource.provider.AbstractDataSourceProvider;
import com.baomidou.dynamic.datasource.provider.DynamicDataSourceProvider;
import com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DataSourceProperty;
import com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties;
import com.ctsi.hndx.config.DbConst;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Lazy;
import org.springframework.context.annotation.Primary;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.annotation.EnableTransactionManagement;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.Resource;
import javax.sql.DataSource;
import java.util.Map;

@Configuration
@MapperScan(basePackages = {"com.ctsi.**.mapper", "com.ctsi.**.*Repository", "com.ctsi.ssdc.admin.repository"})
@EnableTransactionManagement
public class DataSourceConfig {

    /**
     * 动态数据源配置项
     */
    @Resource
    private DynamicDataSourceProperties properties;

    /**
     * 使用shardingSphereDataSource 自动装载的 DataSource
     * 4.1.1版本自动装载的shardingSphereDataSource beanName="shardingSphereDataSource"
     * 要加@Lazy
     */
    @Lazy
    @Autowired
    private DataSource shardingSphereDataSource;


    /**
     * 将sharding数据源添加到dynamic-datasource管理
     * 关键：ShardingSphere数据源作为一个命名数据源，只有在需要分表查询时才使用
     * @return
     */
    @Bean
    public DynamicDataSourceProvider dynamicDataSourceProvider() {
        Map<String, DataSourceProperty> datasourceMap = properties.getDatasource();
        return new AbstractDataSourceProvider() {
            @Override
            public Map<String, DataSource> loadDataSources() {
                Map<String, DataSource> map = createDataSourceMap(datasourceMap);
                // 将ShardingSphere数据源注册为"sharding"命名数据源
                // 只有使用@DS("sharding")时才会使用这个数据源进行分表查询
                map.put(DbConst.SHARDING, shardingSphereDataSource);
                return map;
            }
        };
    }

    /**
     * 将dynamic-datasource设置为主数据源
     * 默认使用master数据源，普通表查询走这里
     * 分表查询需要显式使用@DS("sharding")注解
     * @return
     */
    @Primary
    @Bean
    public DynamicRoutingDataSource dataSource() {
        DynamicRoutingDataSource dataSource = new DynamicRoutingDataSource();
        // 设置默认数据源为master，普通表查询使用这个
        dataSource.setPrimary(DbConst.MASTER);
        dataSource.setStrict(properties.getStrict());
        dataSource.setStrategy(properties.getStrategy());
        dataSource.setP6spy(properties.getP6spy());
        dataSource.setSeata(properties.getSeata());
        return dataSource;
    }

    /**
     * 配置事务，防止一个事务中去操作多个数据源，统一走默认数据源
     * PlatformTransactionManager是Spring的事务管理接口，用于抽象底层的事务管理机制，有多个实现，分别用于不同的数据访问技术
     * 主要用途：
     *    提供底层的事务能力
     *    Transaction注解的使用
     * @param dynamicRoutingDataSource
     * @return
     */
    @Bean(name = "platformTransactionManager")
    public PlatformTransactionManager transactionManager(DynamicRoutingDataSource dynamicRoutingDataSource) {
        return new DataSourceTransactionManager(dynamicRoutingDataSource);
    }


    /**
     * TransactionTemplate:
     * 提供更为细化粒度的事务能力，依赖于PlatformTransactionManager能力
     *
     * @param platformTransactionManager
     * @return
     */
    @Bean
    public TransactionTemplate transactionTemplate(@Qualifier("platformTransactionManager") PlatformTransactionManager platformTransactionManager) {
        return new TransactionTemplate(platformTransactionManager);
    }

}