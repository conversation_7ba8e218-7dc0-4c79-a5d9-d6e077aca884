# ===================================================================
# Standard Spring Boot properties.
# Full reference is available at:
# http://docs.spring.io/spring-boot/docs/current/reference/html/common-application-properties.html
# ===================================================================

spring:
  application:
    name: ssdc-admin
  profiles: #test需要的时候切换
    active: xcy
#    active: prod
  jackson:
    serialization.write_dates_as_timestamps: false
    #格式化输出
    serialization.indent_output: true
    #时区
    time-zone: GMT+8
  mvc:
    favicon:
      enabled: false
  #国际化
  messages:
    #配置路径
    basename: i18n/messages
    #字符集
    encoding: UTF-8

management:
  endpoints:
    web:
      exposure:
        include: "info,health,prometheus"

ctsi:
  systemconfig:
    webfiles: /Users/<USER>/allSelf/server/webfiles

  jwtfilter:
    enable: false
    userService: userServiceImpl
    sqlService: sqlServiceImpl
    metaDataService: metaDataServiceImpl
    menuService: menuServiceImpl
    bigDataService:

