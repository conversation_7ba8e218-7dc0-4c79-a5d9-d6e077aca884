-- SQL提交规范
-- 格式：日期，姓名，功能作用说明

-- 案例：
-- 1、张三 2024年12月02日 新建XXX表，XX某块
-- 2、张三 2024年12月02日 XX表新增XX字段

-- 周龙 2025年05月23日 通讯录表增加新增类型字段
alter table "t_address_book" add add_type int;
 comment on column "t_address_book".add_type is '新增类型 1:系统人员新增 2:通讯录新增';

 alter table "t_address_book" add display int DEFAULT 1;
  comment on column "t_address_book".display is '通讯录是否显示，0-否，1-是';

   alter table "t_address_book" add status int DEFAULT 1;
    comment on column "t_address_book".status is '用户状态：1表示激活，0表示锁定，默认激活';

delete from t_address_book_authorize where 1=1;commit ;
delete from t_address_book_label where 1=1;commit ;
delete from t_label_org where 1=1;commit ;
delete from t_address_book where 1=1;commit;


-- 通讯录表设置默认值
update t_address_book set deleted = 0, add_type = 1;

 CREATE TABLE "cscp_org_LOG"
 (
     "id" NUMBER(32,0),
     "change_type" VARCHAR(10),
     "table_name" VARCHAR(50),
     "primary_key" BIGINT,
     "old_data" TEXT,
     "new_data" TEXT,
     "change_time" TIMESTAMP(6),
     "processed" INT DEFAULT 0,
     "CREATE_BY" CHAR(10),
     "CREATE_TIME" TIMESTAMP(6),
     "UPDATE_BY" BIGINT,
     "UPDATE_TIME" TIMESTAMP(6),
     "COMPANY_ID" BIGINT,
     "TENANT_ID" BIGINT,
     "DEPARTMENT_ID" BIGINT,
     "CREATE_NAME" VARCHAR(50),
     "UPDATE_NAME" VARCHAR(50),
     "DELETED" INT,
     "error_message" TEXT,
     "SFW_PROCESSED" INT DEFAULT 0);

create unique index INDEX33563718
    on "cscp_org_LOG" (id);

 -- 机构日志-序列
 CREATE SEQUENCE "cscp_org_LOG_SEQ" INCREMENT BY 1 START WITH 1 MAXVALUE 9223372036854775807 MINVALUE 1;



 -- 机构日志-触发器
 create or replace trigger "cscp_org_TRIGGER"
     after INSERT or DELETE or UPDATE
     on "cscp_org"
     referencing OLD ROW AS "OLD" NEW ROW AS "NEW"
     for each row
 BEGIN
     INSERT INTO cscp_org_LOG (
         id,
         change_type,
         table_name,
         primary_key,
         old_data,
         new_data,
         change_time,
         deleted,
         processed,
         SFW_PROCESSED
     )
     VALUES (
                cscp_org_LOG_SEQ.NEXTVAL,
                CASE
                    WHEN INSERTING THEN 'INSERT'
                    WHEN UPDATING THEN 'UPDATE'
                    WHEN DELETING THEN 'DELETE'
                    END,
                'cscp_org',
                CASE
                    WHEN INSERTING THEN :NEW.id
                    WHEN UPDATING THEN :NEW.id
                    WHEN DELETING THEN :OLD.id
                    END,
                NULL,
                NULL,
                SYSDATE,
                0,
                0,
                0
            );
 END;


 CREATE TABLE "cscp_user_LOG"
 (
     "id" NUMBER(32,0),
     "change_type" VARCHAR(10),
     "table_name" VARCHAR(50),
     "primary_key" BIGINT,
     "old_data" TEXT,
     "new_data" TEXT,
     "change_time" TIMESTAMP(6),
     "processed" INT DEFAULT 0,
     "CREATE_BY" CHAR(10),
     "CREATE_TIME" TIMESTAMP(6),
     "UPDATE_BY" BIGINT,
     "UPDATE_TIME" TIMESTAMP(6),
     "COMPANY_ID" BIGINT,
     "TENANT_ID" BIGINT,
     "DEPARTMENT_ID" BIGINT,
     "CREATE_NAME" VARCHAR(50),
     "UPDATE_NAME" VARCHAR(50),
     "DELETED" INT,
     "error_message" TEXT,
     "SFW_PROCESSED" INT DEFAULT 0);

create unique index INDEX33563718
    on "cscp_user_LOG" (id);

 -- 用户日志-序列
 CREATE SEQUENCE "cscp_user_LOG_SEQ" INCREMENT BY 1 START WITH 1 MAXVALUE 9223372036854775807 MINVALUE 1;



 -- 用户日志-触发器
 create or replace trigger "cscp_user_TRIGGER"
     after INSERT or DELETE or UPDATE
     on "cscp_user"
     referencing OLD ROW AS "OLD" NEW ROW AS "NEW"
     for each row
 BEGIN
     INSERT INTO cscp_user_LOG (
         id,
         change_type,
         table_name,
         primary_key,
         old_data,
         new_data,
         change_time,
         deleted,
         processed,
         SFW_PROCESSED
     )
     VALUES (
                cscp_user_LOG_SEQ.NEXTVAL,
                CASE
                    WHEN INSERTING THEN 'INSERT'
                    WHEN UPDATING THEN 'UPDATE'
                    WHEN DELETING THEN 'DELETE'
                    END,
                'cscp_user',
                CASE
                    WHEN INSERTING THEN :NEW.id
                    WHEN UPDATING THEN :NEW.id
                    WHEN DELETING THEN :OLD.id
                    END,
                NULL,
                NULL,
                SYSDATE,
                0,
                0,
                0
            );
 END;

-- 触发器触发往通讯录表添加/修改数据，用于修复老数据
update cscp_org set deleted=0 where deleted=0;
-- 触发器触发往通讯录表添加/修改数据，用于修复老数据
update cscp_user set deleted = 0 where deleted = 0;