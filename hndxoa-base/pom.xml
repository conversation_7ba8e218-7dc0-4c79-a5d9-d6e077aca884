<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.ctsi.hndxoa</groupId>
        <artifactId>hndxoa</artifactId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>

    <groupId>com.ctsi.hndxoa</groupId>
    <artifactId>hndxoa-base</artifactId>
    <version>${hndxoa.base.version}</version>
    <packaging>jar</packaging>
    <description>基础核心模块</description>
    <properties>
        <failOnMissingWebXml>false</failOnMissingWebXml>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
    </properties>


    <dependencies>
        <!-- com.ctbiyi 相关 -->
        <dependency>
            <groupId>com.ctsi.hndxoa</groupId>
            <artifactId>hndxoa-basebiyicorepom</artifactId>
            <exclusions>
                <!--<exclusion>
                    <artifactId>liquibase-core</artifactId>
                    <groupId>org.liquibase</groupId>
                </exclusion>-->
                <exclusion>
                    <groupId>org.mybatis.spring.boot</groupId>
                    <artifactId>mybatis-spring-boot-starter</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>mapstruct</artifactId>
                    <groupId>org.mapstruct</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>jsqlparser</artifactId>
                    <groupId>com.github.jsqlparser</groupId>
                </exclusion>
            </exclusions>
        </dependency>

       <!-- <dependency>
            <groupId>io.minio</groupId>
            <artifactId>minio</artifactId>
        </dependency>-->

       <!-- <dependency>
            <groupId>com.ctsi.hndx</groupId>
            <artifactId>oss-java-sdk</artifactId>
        </dependency>-->

        <!-- 二维码支持包 -->
        <!-- <dependency>
             <groupId>com.google.zxing</groupId>
             <artifactId>core</artifactId>
         </dependency>-->

        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
        </dependency>

        <!-- <dependency>
             <groupId>org.modelmapper</groupId>
             <artifactId>modelmapper</artifactId>
         </dependency>-->

        <dependency>
            <groupId>com.belerweb</groupId>
            <artifactId>pinyin4j</artifactId>
        </dependency>

        <dependency>
            <groupId>com.ctbiyi</groupId>
            <artifactId>biyi-util</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ctbiyi</groupId>
            <artifactId>biyi-captcha</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.ctbiyi</groupId>
                    <artifactId>biyi-util</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.ctbiyi</groupId>
                    <artifactId>component-core</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.ctbiyi</groupId>
                    <artifactId>component-sdk</artifactId>
                </exclusion>
            </exclusions>
        </dependency>


        <!-- com.alibaba -->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
        </dependency>

        <dependency>
            <groupId>org.mybatis</groupId>
            <artifactId>mybatis-typehandlers-jsr310</artifactId>
            <version>1.0.2</version>
        </dependency>
        <!-- mybatis-plus -->
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-boot-starter</artifactId>
            <!-- <exclusions>
                 <exclusion>
                     <artifactId>jsqlparser</artifactId>
                     <groupId>com.github.jsqlparser</groupId>
                 </exclusion>
             </exclusions>-->
        </dependency>
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-core</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
            <version>5.7.15</version>
        </dependency>
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-extra</artifactId>
        </dependency>

        <!-- websocket模块 -->
        <!--<dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-websocket</artifactId>
        </dependency>

        <dependency>
            <groupId>com.xuxueli</groupId>
            <artifactId>xxl-job-core</artifactId>
        </dependency>-->


        <!-- 操作word模板 -->
        <dependency>
            <groupId>fr.opensagres.xdocreport</groupId>
            <artifactId>fr.opensagres.xdocreport.document.docx</artifactId>
        </dependency>
        <dependency>
            <groupId>fr.opensagres.xdocreport</groupId>
            <artifactId>fr.opensagres.xdocreport.template.freemarker</artifactId>
        </dependency>

        <dependency>
            <groupId>org.bouncycastle</groupId>
            <artifactId>bcprov-jdk15to18</artifactId>
        </dependency>

        <!--<dependency>
            <groupId>com.dameng</groupId>
            <artifactId>jdbc8</artifactId>
            <version>1.8</version>
            <scope>system</scope>
            <systemPath>${pom.basedir}/lib/jdbc8-1.8.jar</systemPath>
        </dependency>-->
<!--        <dependency>-->
<!--            <groupId>com.dameng</groupId>-->
<!--            <artifactId>DmJdbcDriver18</artifactId>-->
<!--            <version>8.1.3.62</version>-->
<!--            <scope>system</scope>-->
<!--            <systemPath>${pom.basedir}/lib/DmJdbcDriver18.jar</systemPath>-->
<!--        </dependency>-->
        <!-- https://mvnrepository.com/artifact/com.dameng/DmJdbcDriver11 -->
        <dependency>
            <groupId>com.dameng</groupId>
            <artifactId>DmJdbcDriver18</artifactId>
            <version>8.1.3.140</version>
        </dependency>

        <dependency>
            <groupId>com.hndx</groupId>
            <artifactId>DmDialect-for-hibernate5.3</artifactId>
            <version>5.3</version>
            <scope>system</scope>
            <systemPath>${pom.basedir}/lib/DmDialect-for-hibernate5.3-5.3.jar</systemPath>
        </dependency>

       <!-- <dependency>
            <groupId>com.ctg.itrdc.cache</groupId>
            <artifactId>ctg-cache-nclient</artifactId>
        </dependency>
-->
        <dependency>
            <groupId>redis.clients</groupId>
            <artifactId>jedis</artifactId>
        </dependency>

        <!--多数据源切换-->
        <!--<dependency>-->
        <!--    <groupId>com.baomidou</groupId>-->
        <!--    <artifactId>dynamic-datasource-spring-boot-starter</artifactId>-->
        <!--</dependency>-->

        <!-- https://mvnrepository.com/artifact/com.baomidou/dynamic-datasource-spring-boot-starter -->
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>dynamic-datasource-spring-boot-starter</artifactId>
            <version>3.5.2</version>
        </dependency>

        <dependency>
            <groupId>org.apache.shardingsphere</groupId>
            <artifactId>sharding-jdbc-spring-boot-starter</artifactId>
            <version>4.1.1</version>
        </dependency>


        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-crypto</artifactId>
            <version>5.8.5</version>
            <scope>system</scope>
            <systemPath>${pom.basedir}/lib/hutool-crypto-5.8.5.jar</systemPath>
        </dependency>

        <!--  IText PDF文档操作JAVA类库-->
        <dependency>
            <groupId>com.itextpdf</groupId>
            <artifactId>itextpdf</artifactId>
            <version>5.5.13.3</version>
            <scope>system</scope>
            <systemPath>${pom.basedir}/lib/itextpdf-5.5.13.3.jar</systemPath>
        </dependency>

        <dependency>
            <groupId>org.apache.pdfbox</groupId>
            <artifactId>pdfbox</artifactId>
            <version>2.0.4</version>
            <scope>system</scope>
            <systemPath>${pom.basedir}/lib/pdfbox-2.0.4.jar</systemPath>
        </dependency>

        <!--<dependency>
            <groupId>com.alym.smw</groupId>
            <artifactId>dbwrap4j</artifactId>
            <version>2.0.1-SNAPSHOT</version>
            <scope>system</scope>
            <systemPath>${pom.basedir}/lib/dbwrap4j-2.0.1-jar-with-dependencies.jar</systemPath>
        </dependency>-->

      <!--  <dependency>
            <groupId>com.dop</groupId>
            <artifactId>dop-api-sdk</artifactId>
            <version>1.0</version>
            <scope>system</scope>
            <systemPath>${pom.basedir}/lib/dop-api-sdk-1.0.jar</systemPath>
        </dependency>-->

        <!--<dependency>
            <groupId>com.yh</groupId>
            <artifactId>agent</artifactId>
            <version>1.0.0</version>
            <scope>system</scope>
            <systemPath>${pom.basedir}/lib/agent-1.0.0.jar</systemPath>
        </dependency>-->

        <dependency>
            <groupId>org.jsoup</groupId>
            <artifactId>jsoup</artifactId>
            <version>1.14.3</version>
        </dependency>

        <!-- 卫士通密码机库文件 -->
        <dependency>
            <groupId>com.westone</groupId>
            <artifactId>mina-core</artifactId>
            <version>2.0.5</version>
            <scope>system</scope>
            <systemPath>${pom.basedir}/../lib/WST/mina-core-2.0.5.jar</systemPath>
        </dependency>

        <dependency>
            <groupId>com.westone</groupId>
            <artifactId>cetccst-hsm-api</artifactId>
            <version>1.10.10</version>
            <scope>system</scope>
            <systemPath>${pom.basedir}/../lib/WST/cetccst-hsm-api-1.10.10.jar</systemPath>
        </dependency>

    </dependencies>

    <build>
        <plugins>
        </plugins>
    </build>
</project>
