package com.ctsi.hndx.westone;

import cn.com.westone.common.array.ByteArrayUtil;
import com.ctsi.hndx.constant.SysConfigConstant;
import com.ctsi.hndx.tsysconfig.service.ISysConfigService;
import com.westone.pboc.hsm.HSMConstant;
import com.westone.pboc.hsm.constants.FPEStyleEnum;
import com.westone.pboc.hsm.constants.FPETypeEnum;
import com.westone.pboc.hsm.constants.ProtectModeEnum;
import com.westone.pboc.mina.client.Client;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.Base64;
import java.util.stream.Collectors;

/**
 * <p>
 * 卫士通服务器数据密码机API接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-03
 *
 */
@Slf4j
@Service
public class WestoneEncryptService {

    @Autowired
    private ISysConfigService sysConfigService;

    /**
     * 根据系统动态参数配置，判断是否使用密码机
     * @return
     */
    public boolean isCipherMachine() {
        boolean isCipherMachine = this.sysConfigService.getSysConfigBoolValueByCode(SysConfigConstant.WESTONE_CIPHER_MACHINE_DEPLOY);
        return isCipherMachine;
    }

    /**
     * 手机号码分割字符串加密
     * @param mobilePhone 手机号码
     * @return
     */
    public String divisionEncryptMobileWithFPE(String mobilePhone, String keyCipherText, String password) {
        String str = "";
        if (null != mobilePhone && !"".equals(mobilePhone)) {
            for (int i = 0; i < mobilePhone.length(); i++) {
                str = str.concat(encryptMobilePhoneWithFPE(mobilePhone.substring(i, i + 1), keyCipherText, password));
                str = str.concat(",");
            }
            str = str.substring(0, str.length() - 1);
        }
        return str;
    }

    /**
     * 手机号码分割字符串加密
     * @param mobilePhone 手机号码
     * @return
     */
    public String divisionEncryptMobileWithFPE(String mobilePhone) {
        String keyCipherText = WestoneClient.getConfigByCode(this.sysConfigService, WestoneEncryptConstant.HSM_KEY_CIPHER_TEXT, WestoneEncryptConstant.HSM_KEY_CIPHER_TEXT_DEFAULT);
        String password = WestoneClient.getConfigByCode(this.sysConfigService, WestoneEncryptConstant.HSM_KEY_CIPHER_PASSWORD, WestoneEncryptConstant.HSM_KEY_CIPHER_PASSWORD_DEFAULT);
        return divisionEncryptMobileWithFPE(mobilePhone, keyCipherText, password);
    }

    /**
     * 手机号码分割字符串解密
     * @param mobilePhone 手机号码
     * @return
     */
    public String divisionDecryptMobileWithFPE(String mobilePhone, String keyCipherText, String password) {
        if (null == mobilePhone || "".equals(mobilePhone)) {
            return "";
        }
        String str = Arrays.asList(mobilePhone.split(","))
                .stream().map(v -> decryptMobilePhoneWithFPE(v, keyCipherText, password))
                .collect(Collectors.joining(""));
        return str;
    }

    /**
     * 手机号码分割字符串解密
     * @param mobilePhone 手机号码
     * @return
     */
    public String divisionDecryptMobileWithFPE(String mobilePhone) {
        String keyCipherText = WestoneClient.getConfigByCode(this.sysConfigService, WestoneEncryptConstant.HSM_KEY_CIPHER_TEXT, WestoneEncryptConstant.HSM_KEY_CIPHER_TEXT_DEFAULT);
        String password = WestoneClient.getConfigByCode(this.sysConfigService, WestoneEncryptConstant.HSM_KEY_CIPHER_PASSWORD, WestoneEncryptConstant.HSM_KEY_CIPHER_PASSWORD_DEFAULT);
        return divisionDecryptMobileWithFPE(mobilePhone, keyCipherText, password);
    }

    /**
     * 姓名分割字符串加密
     * @param realName 姓名
     * @return
     */
    public String divisionEncryptRealNameWithFPE(String realName, String keyCipherText, String password) {
        String str = "";
        if (null != realName && !"".equals(realName)) {
            for (int i = 0; i < realName.length(); i++) {
                str = str.concat(encryptBySM4ECB_WithKeyProtectedBySM2(realName.substring(i, i + 1), keyCipherText, password));
                str = str.concat(",");
            }
            str = str.substring(0, str.length() - 1);
        }
        return str;
    }

    /**
     * 姓名分割字符串加密
     * @param realName 姓名
     * @return
     */
    public String divisionEncryptRealNameWithFPE(String realName) {
        String keyCipherText = WestoneClient.getConfigByCode(this.sysConfigService, WestoneEncryptConstant.HSM_KEY_CIPHER_TEXT, WestoneEncryptConstant.HSM_KEY_CIPHER_TEXT_DEFAULT);
        String password = WestoneClient.getConfigByCode(this.sysConfigService, WestoneEncryptConstant.HSM_KEY_CIPHER_PASSWORD, WestoneEncryptConstant.HSM_KEY_CIPHER_PASSWORD_DEFAULT);
        return divisionEncryptRealNameWithFPE(realName, keyCipherText, password);
    }

    /**
     * 姓名分割字符串解密
     * @param realName 姓名
     * @return
     */
    public String divisionDecryptRealNameWithFPE(String realName, String keyCipherText, String password) {
        if (null == realName || "".equals(realName)) {
            return "";
        }
        String str = Arrays.asList(realName.split(","))
                .stream().map(v -> decryptBySM4ECB_WithKeyProtectedBySM2(v, keyCipherText, password))
                .collect(Collectors.joining(""));
        return str;
    }

    /**
     * 姓名分割字符串解密
     * @param realName 姓名
     * @return
     */
    public String divisionDecryptRealNameWithFPE(String realName) {
        String keyCipherText = WestoneClient.getConfigByCode(this.sysConfigService, WestoneEncryptConstant.HSM_KEY_CIPHER_TEXT, WestoneEncryptConstant.HSM_KEY_CIPHER_TEXT_DEFAULT);
        String password = WestoneClient.getConfigByCode(this.sysConfigService, WestoneEncryptConstant.HSM_KEY_CIPHER_PASSWORD, WestoneEncryptConstant.HSM_KEY_CIPHER_PASSWORD_DEFAULT);
        return divisionDecryptRealNameWithFPE(realName, keyCipherText, password);
    }

    /**
     * 手机号码加密
     * @param mobilePhone 手机号码
     * @param keyCipherText 秘钥密文
     * @param password 访问密码
     * @return result 加密后的手机号码
     */
    public String encryptMobilePhoneWithFPE(String mobilePhone, String keyCipherText, String password) {
        String result = null;
        if (null == mobilePhone || "".equals(mobilePhone)) {
            return mobilePhone;
        }
        // 判断是否为数字字符串，如果不是则不进行加密操作
        if (!mobilePhone.matches("\\d+")) {
            result = Base64.getEncoder().encodeToString(mobilePhone.getBytes());
            return result;
        }
        int SM2KeyIndex = 1;
        Client client = null;
        byte[] data = mobilePhone.getBytes(StandardCharsets.UTF_8);
        try {
            client = WestoneClient.CLIENT_THREAD_POOL.getClient();
            byte[] keyCipher = Base64.getDecoder().decode(keyCipherText);
            byte[] phoneCipher = WestoneClient.HSMWST_API_SERVICE.hsmFpeEncrypt(ProtectModeEnum.PROTECT_MODE_KEY_BY_SM2.getValue(), SM2KeyIndex,
                    null, password, keyCipher, FPETypeEnum.SCM_FPE_STRING.getValue(),
                    FPEStyleEnum.SCM_FPE_STRING_STYLE_NUMBER_STR.getValue(), data, client);
//            ByteBuffer phoneBuffer = ByteBuffer.allocate(data.length);
//            phoneBuffer.put(phoneCipher);
//            phoneBuffer.flip();
//            byte[] cipherText = new byte[phoneBuffer.limit()];
//            phoneBuffer.get(cipherText);
//            phoneBuffer.clear();
//            result = ByteArrayUtil.toHexString(cipherText);
//            System.out.println("cipherText = " + result);
            result = new String(phoneCipher, StandardCharsets.UTF_8);
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            WestoneClient.CLIENT_THREAD_POOL.release(client);
        }
        return result;
    }

    /**
     * 手机号码加密
     * @param mobilePhone 手机号码
     * @return result 加密后的手机号码
     */
    public String encryptMobilePhoneWithFPE(String mobilePhone) {
        String keyCipherText = WestoneClient.getConfigByCode(this.sysConfigService, WestoneEncryptConstant.HSM_KEY_CIPHER_TEXT, WestoneEncryptConstant.HSM_KEY_CIPHER_TEXT_DEFAULT);
        String password = WestoneClient.getConfigByCode(this.sysConfigService, WestoneEncryptConstant.HSM_KEY_CIPHER_PASSWORD, WestoneEncryptConstant.HSM_KEY_CIPHER_PASSWORD_DEFAULT);
        return encryptMobilePhoneWithFPE(mobilePhone, keyCipherText, password);
    }

    /**
     * 解密手机号码
     * @param encryptMobilePhone 加密后的手机号码
     * @param keyCipherText 秘钥密文
     * @param password 访问密码
     * @return result 解密后的手机号码
     */
    public String decryptMobilePhoneWithFPE(String encryptMobilePhone, String keyCipherText, String password) {
        String result = null;
        if (null == encryptMobilePhone || "".equals(encryptMobilePhone)) {
            return encryptMobilePhone;
        }
        int SM2KeyIndex = 1;
        Client client = null;
        byte[] data = encryptMobilePhone.getBytes(StandardCharsets.UTF_8);
        try {
            client = WestoneClient.CLIENT_THREAD_POOL.getClient();
            byte[] keyCipher = Base64.getDecoder().decode(keyCipherText);
            byte[] phonePlain = WestoneClient.HSMWST_API_SERVICE.hsmFpeDecrypt(ProtectModeEnum.PROTECT_MODE_KEY_BY_SM2.getValue(), SM2KeyIndex,
                    null, password, keyCipher, FPETypeEnum.SCM_FPE_STRING.getValue(),
                    FPEStyleEnum.SCM_FPE_STRING_STYLE_NUMBER_STR.getValue(), data, client);
//            ByteBuffer phoneBuffer = ByteBuffer.allocate(data.length);
//            phoneBuffer.put(phonePlain);
//            phoneBuffer.flip();
//            byte[] plainText = new byte[phoneBuffer.limit()];
//            phoneBuffer.get(plainText);
//            phoneBuffer.clear();
//            result = new String(plainText, StandardCharsets.UTF_8);
//            System.out.println("plainText = " + result);
            result = new String(phonePlain, StandardCharsets.UTF_8);
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            WestoneClient.CLIENT_THREAD_POOL.release(client);
        }
        return result;
    }

    /**
     * 手机号码加密
     * @param mobilePhone 手机号码
     * @return result 加密后的手机号码
     */
    public String decryptMobilePhoneWithFPE(String mobilePhone) {
        String keyCipherText = WestoneClient.getConfigByCode(this.sysConfigService, WestoneEncryptConstant.HSM_KEY_CIPHER_TEXT, WestoneEncryptConstant.HSM_KEY_CIPHER_TEXT_DEFAULT);
        String password = WestoneClient.getConfigByCode(this.sysConfigService, WestoneEncryptConstant.HSM_KEY_CIPHER_PASSWORD, WestoneEncryptConstant.HSM_KEY_CIPHER_PASSWORD_DEFAULT);
        return decryptMobilePhoneWithFPE(mobilePhone, keyCipherText, password);
    }

    /**
     * 加密SM4 ECB数据
     * @param src 原始字符串
     * @param keyCipherText 密钥密文
     * @param password 访问密码
     * @return result 加密后的字符串
     */
    public String encryptBySM4ECB_WithKeyProtectedBySM2(String src, String keyCipherText, String password) {
        String result = null;
        if (null == src || "".equals(src)) {
            return src;
        }
        int keyIndex = 1;
        Client client = null;
        int encryptAlgID = HSMConstant.SGD_SM4_ECB;
        byte[] data = null;
        data = src.getBytes(StandardCharsets.UTF_8);
        byte[] cipher;
        try {
            client = WestoneClient.CLIENT_THREAD_POOL.getClient();
            byte[] keyCipher = Base64.getDecoder().decode(keyCipherText);

            /**【加解密处理阶段】
             /*以上，我们准备产生了一个会话密钥密文keyCipher，这个密钥密文是由1号索引的SM2加密的

             *下面的代码展示使用这个会话密钥对数据进行处理时的接口调用
             */
            /**1、将会话密钥密文keyCipher导入到密码机的某个会话通道client中，得到对应的会话密钥句柄keyHandle
             *   这里将使用私钥对会话密钥进行解密，从而获得密钥句柄。
             *   使用私钥进行运算时需要先获取私钥使用权限，本例在通过密码机控制台软件进行SM2密钥产生时设置的访问口令为“11111111”
             */
            WestoneClient.HSMWST_API_SERVICE.hsmGetPrivateKeyAccessRight(keyIndex, password, client);
            byte[] keyHandle = WestoneClient.HSMWST_API_SERVICE.hsmSM2ImportKeyWithISK(keyIndex, keyCipher, client);
            WestoneClient.HSMWST_API_SERVICE.hsmReleasePrivateKeyAccessRight(keyIndex, client);

            /**2、使用keyhandle加密明文数据data*/
            cipher = WestoneClient.HSMWST_API_SERVICE.hsmEncrypt(keyHandle, encryptAlgID, null, data, client);

            /**3、销毁keyHandleA：当会话密钥不再使用时，应及时销毁该句柄，以释放密钥句柄资源*/
            boolean isOk = WestoneClient.HSMWST_API_SERVICE.hsmDestoryKey(keyHandle, client);
            if (isOk) {
                result = ByteArrayUtil.toHexString(cipher);
            } else {
                result = src;
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            WestoneClient.CLIENT_THREAD_POOL.release(client);
        }
        return result;
    }

    /**
     * 加密SM4 ECB数据
     * @param src 原始字符串
     * @return result 加密后的字符串
     */
    public String encryptBySM4ECB_WithKeyProtectedBySM2(String src) {
        String keyCipherText = WestoneClient.getConfigByCode(this.sysConfigService, WestoneEncryptConstant.HSM_KEY_CIPHER_TEXT, WestoneEncryptConstant.HSM_KEY_CIPHER_TEXT_DEFAULT);
        String password = WestoneClient.getConfigByCode(this.sysConfigService, WestoneEncryptConstant.HSM_KEY_CIPHER_PASSWORD, WestoneEncryptConstant.HSM_KEY_CIPHER_PASSWORD_DEFAULT);
        return encryptBySM4ECB_WithKeyProtectedBySM2(src, keyCipherText, password);
    }

    /**
     * 解密SM4 ECB加密数据
     * @param encryptSrc 原始密文字符串
     * @param keyCipherText 密钥密文
     * @param password 访问密码
     * @return result 解密后的字符串
     */
    public String decryptBySM4ECB_WithKeyProtectedBySM2(String encryptSrc, String keyCipherText, String password) {
        String result = null;
        if (null == encryptSrc || "".equals(encryptSrc)) {
            return encryptSrc;
        }
        int keyIndex = 1;
        Client client = null;
        int encryptAlgID = HSMConstant.SGD_SM4_ECB;
        byte[] data = ByteArrayUtil.hexString2Bytes(encryptSrc);
        byte[] plain;
        try {
            client = WestoneClient.CLIENT_THREAD_POOL.getClient();
            byte[] keyCipher = Base64.getDecoder().decode(keyCipherText);
            /**【加解密处理阶段】
             /*以上，我们准备产生了一个会话密钥密文keyCipher，这个密钥密文是由1号索引的SM2加密的

             *下面的代码展示使用这个会话密钥对数据进行处理时的接口调用
             */

            /**1、将会话密钥密文keyCipher导入到密码机的某个会话通道client中，得到对应的会话密钥句柄keyHandle
             *   这里将使用私钥对会话密钥进行解密，从而获得密钥句柄。
             *   使用私钥进行运算时需要先获取私钥使用权限，本例在通过密码机控制台软件进行SM2密钥产生时设置的访问口令为“11111111”
             */
            WestoneClient.HSMWST_API_SERVICE.hsmGetPrivateKeyAccessRight(keyIndex, password, client);
            byte[] keyHandle = WestoneClient.HSMWST_API_SERVICE.hsmSM2ImportKeyWithISK(keyIndex, keyCipher, client);
            WestoneClient.HSMWST_API_SERVICE.hsmReleasePrivateKeyAccessRight(keyIndex, client);

            /**2、使用keyhandle解密明文数据data*/
            plain = WestoneClient.HSMWST_API_SERVICE.hsmDecrypt(keyHandle, encryptAlgID, null, data, client);

            /**3、销毁keyHandleA：当会话密钥不再使用时，应及时销毁该句柄，以释放密钥句柄资源*/
            boolean isOk = WestoneClient.HSMWST_API_SERVICE.hsmDestoryKey(keyHandle, client);
            if (isOk) {
                result = new String(plain, StandardCharsets.UTF_8);
            } else {
                result = encryptSrc;
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            WestoneClient.CLIENT_THREAD_POOL.release(client);
        }
        return result;
    }

    /**
     * 解密SM4 ECB加密数据
     * @param encryptSrc 原始密文字符串
     * @return result 加密后的字符串
     */
    public String decryptBySM4ECB_WithKeyProtectedBySM2(String encryptSrc) {
        String keyCipherText = WestoneClient.getConfigByCode(this.sysConfigService, WestoneEncryptConstant.HSM_KEY_CIPHER_TEXT, WestoneEncryptConstant.HSM_KEY_CIPHER_TEXT_DEFAULT);
        String password = WestoneClient.getConfigByCode(this.sysConfigService, WestoneEncryptConstant.HSM_KEY_CIPHER_PASSWORD, WestoneEncryptConstant.HSM_KEY_CIPHER_PASSWORD_DEFAULT);
        return decryptBySM4ECB_WithKeyProtectedBySM2(encryptSrc, keyCipherText, password);
    }

    /**
     * 计算SM3 HMAC
     * @param src 原始字符串
     * @param keyCipherText 密钥密文
     * @param password 访问密码
     * @return result HMAC结果字符串
     */
    public String calculateSM3HMAC(String src, String keyCipherText, String password) {
        String result = null;
        int sm2KeyIndex = 1;
        Client client = null;
        /**数据如果有中文，应约定将数据转码成byte数组时的转码方式*/
        byte[] data = src.getBytes(StandardCharsets.UTF_8);
        try {
            client = WestoneClient.CLIENT_THREAD_POOL.getClient();
            byte[] keyCipher = Base64.getDecoder().decode(keyCipherText);

            /**
             * 从这里开始，下面的接口调用序列为计算HMAC的完整的日常过程，
             其中的keyCipher就是初始化阶段产生的随机密钥密文，将其导入后重新得到一个密钥句柄，并用这个句柄进行HMAC运算，
             运算结束后必须回收该句柄资源*/
            WestoneClient.HSMWST_API_SERVICE.hsmGetPrivateKeyAccessRight(sm2KeyIndex, password, client);
            byte[] keyHandleNew = WestoneClient.HSMWST_API_SERVICE.hsmSM2ImportKeyWithISK(sm2KeyIndex, keyCipher, client);
            WestoneClient.HSMWST_API_SERVICE.hsmReleasePrivateKeyAccessRight(sm2KeyIndex, client);

            byte[] context = WestoneClient.HSMWST_API_SERVICE.hsmHashCrossSessionInit(HSMConstant.SGD_HASH_HMAC_SM3, keyHandleNew,
                    null, null, null, null, client);
            byte[] context11 = WestoneClient.HSMWST_API_SERVICE.hsmHashCrossSessionUpdate(context, data, client);
            byte[] hashResult = WestoneClient.HSMWST_API_SERVICE.hsmHashCrossSessionFinal(context11, client);

            /**
             * ！！！！注意！！！！*/
            /**
             * 计算完成后一定要释放密钥句柄资源*/
            boolean isOk = WestoneClient.HSMWST_API_SERVICE.hsmDestoryKey(keyHandleNew, client);
            if (isOk) {
                result = ByteArrayUtil.toHexString(hashResult);
            } else {
                result = src;
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (null != client) {
                WestoneClient.CLIENT_THREAD_POOL.release(client);
            }
        }
        return result;
    }

    /**
     * 计算SM3 HMAC
     * @param src 原始字符串
     * @return result HMAC结果字符串
     */
    public String calculateSM3HMAC(String src) {
        String keyCipherText = WestoneClient.getConfigByCode(this.sysConfigService, WestoneEncryptConstant.HSM_KEY_CIPHER_HMAC_TEXT, WestoneEncryptConstant.HSM_KEY_CIPHER_HMAC_TEXT_DEFAULT);
        String password = WestoneClient.getConfigByCode(this.sysConfigService, WestoneEncryptConstant.HSM_KEY_CIPHER_PASSWORD, WestoneEncryptConstant.HSM_KEY_CIPHER_PASSWORD_DEFAULT);
        return calculateSM3HMAC(src, keyCipherText, password);
    }

    /**
     * 计算SM3 HMAC
     * @param src 原始字符串
     * @param hmacSrc HMAC字符串
     * @param keyCipherText 密钥密文
     * @param password 访问密码
     * @return result HMAC结果字符串
     */
    public boolean compareSM3HMAC(String src, String hmacSrc, String keyCipherText, String password) {
        if ((null == src && null == hmacSrc) || ("".equals(src) && "".equals(hmacSrc))) {
            return true;
        } else {
            if (null == src || null == hmacSrc) {
                return false;
            }
        }
        String newHmacStr = calculateSM3HMAC(src, keyCipherText, password);
        return hmacSrc.equals(newHmacStr);
    }

    /**
     * 计算SM3 HMAC
     * @param src 原始字符串
     * @param hmacSrc HMAC字符串
     * @return result HMAC结果字符串
     */
    public boolean compareSM3HMAC(String src, String hmacSrc) {
        String keyCipherText = WestoneClient.getConfigByCode(this.sysConfigService, WestoneEncryptConstant.HSM_KEY_CIPHER_HMAC_TEXT, WestoneEncryptConstant.HSM_KEY_CIPHER_HMAC_TEXT_DEFAULT);
        String password = WestoneClient.getConfigByCode(this.sysConfigService, WestoneEncryptConstant.HSM_KEY_CIPHER_PASSWORD, WestoneEncryptConstant.HSM_KEY_CIPHER_PASSWORD_DEFAULT);
        return compareSM3HMAC(src, hmacSrc, keyCipherText, password);
    }

    /**
     * 加密SM4 ECB文件流
     * @param data 原始文件流
     * @param keyCipherText 密钥密文
     * @param password 访问密码
     * @return result 加密后的文件流
     */
    public byte[] encryptFileBySM4ECB_WithKeyProtectedBySM2(byte[] data, String keyCipherText, String password) {
        byte[] result = null;
        int keyIndex = 1;
        Client client = null;
        int encryptAlgID = HSMConstant.SGD_SM4_ECB;
        byte[] cipher;
        try {
            client = WestoneClient.CLIENT_THREAD_POOL.getClient();
            byte[] keyCipher = Base64.getDecoder().decode(keyCipherText);

            /**【加解密处理阶段】
             /*以上，我们准备产生了一个会话密钥密文keyCipher，这个密钥密文是由1号索引的SM2加密的

             *下面的代码展示使用这个会话密钥对数据进行处理时的接口调用
             */
            /**1、将会话密钥密文keyCipher导入到密码机的某个会话通道client中，得到对应的会话密钥句柄keyHandle
             *   这里将使用私钥对会话密钥进行解密，从而获得密钥句柄。
             *   使用私钥进行运算时需要先获取私钥使用权限，本例在通过密码机控制台软件进行SM2密钥产生时设置的访问口令为“11111111”
             */
            WestoneClient.HSMWST_API_SERVICE.hsmGetPrivateKeyAccessRight(keyIndex, password, client);
            byte[] keyHandle = WestoneClient.HSMWST_API_SERVICE.hsmSM2ImportKeyWithISK(keyIndex, keyCipher, client);
            WestoneClient.HSMWST_API_SERVICE.hsmReleasePrivateKeyAccessRight(keyIndex, client);

            /**2、使用keyhandle加密明文数据data*/
            cipher = WestoneClient.HSMWST_API_SERVICE.hsmEncrypt(keyHandle, encryptAlgID, null, data, client);

            /**3、销毁keyHandleA：当会话密钥不再使用时，应及时销毁该句柄，以释放密钥句柄资源*/
            boolean isOk = WestoneClient.HSMWST_API_SERVICE.hsmDestoryKey(keyHandle, client);
            if (isOk) {
                result = cipher;
            } else {
                result = data;
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            WestoneClient.CLIENT_THREAD_POOL.release(client);
        }
        return result;
    }

    /**
     * 加密SM4 ECB文件流
     * @param data 原始文件流
     * @return result 加密后的文件流
     */
    public byte[] encryptFileBySM4ECB_WithKeyProtectedBySM2(byte[] data) {
        String keyCipherText = WestoneClient.getConfigByCode(this.sysConfigService, WestoneEncryptConstant.HSM_KEY_CIPHER_TEXT, WestoneEncryptConstant.HSM_KEY_CIPHER_TEXT_DEFAULT);
        String password = WestoneClient.getConfigByCode(this.sysConfigService, WestoneEncryptConstant.HSM_KEY_CIPHER_PASSWORD, WestoneEncryptConstant.HSM_KEY_CIPHER_PASSWORD_DEFAULT);
        return encryptFileBySM4ECB_WithKeyProtectedBySM2(data, keyCipherText, password);
    }

    /**
     * 解密SM4 ECB加密文件流
     * @param data 原始加密文件流
     * @param keyCipherText 密钥密文
     * @param password 访问密码
     * @return result 解密后的文件流
     */
    public byte[] decryptFileBySM4ECB_WithKeyProtectedBySM2(byte[] data, String keyCipherText, String password) {
        byte[] result = null;
        int keyIndex = 1;
        Client client = null;
        int encryptAlgID = HSMConstant.SGD_SM4_ECB;
        byte[] plain;
        try {
            client = WestoneClient.CLIENT_THREAD_POOL.getClient();
            byte[] keyCipher = Base64.getDecoder().decode(keyCipherText);
            /**【加解密处理阶段】
             /*以上，我们准备产生了一个会话密钥密文keyCipher，这个密钥密文是由1号索引的SM2加密的

             *下面的代码展示使用这个会话密钥对数据进行处理时的接口调用
             */

            /**1、将会话密钥密文keyCipher导入到密码机的某个会话通道client中，得到对应的会话密钥句柄keyHandle
             *   这里将使用私钥对会话密钥进行解密，从而获得密钥句柄。
             *   使用私钥进行运算时需要先获取私钥使用权限，本例在通过密码机控制台软件进行SM2密钥产生时设置的访问口令为“11111111”
             */
            WestoneClient.HSMWST_API_SERVICE.hsmGetPrivateKeyAccessRight(keyIndex, password, client);
            byte[] keyHandle = WestoneClient.HSMWST_API_SERVICE.hsmSM2ImportKeyWithISK(keyIndex, keyCipher, client);
            WestoneClient.HSMWST_API_SERVICE.hsmReleasePrivateKeyAccessRight(keyIndex, client);

            /**2、使用keyhandle解密明文数据data*/
            plain = WestoneClient.HSMWST_API_SERVICE.hsmDecrypt(keyHandle, encryptAlgID, null, data, client);

            /**3、销毁keyHandleA：当会话密钥不再使用时，应及时销毁该句柄，以释放密钥句柄资源*/
            boolean isOk = WestoneClient.HSMWST_API_SERVICE.hsmDestoryKey(keyHandle, client);
            if (isOk) {
                result = plain;
            } else {
                result = data;
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            WestoneClient.CLIENT_THREAD_POOL.release(client);
        }
        return result;
    }

    /**
     * 解密SM4 ECB加密文件流
     * @param data 原始加密文件流
     * @return result 解密后的文件流
     */
    public byte[] decryptFileBySM4ECB_WithKeyProtectedBySM2(byte[] data) {
        String keyCipherText = WestoneClient.getConfigByCode(this.sysConfigService, WestoneEncryptConstant.HSM_KEY_CIPHER_TEXT, WestoneEncryptConstant.HSM_KEY_CIPHER_TEXT_DEFAULT);
        String password = WestoneClient.getConfigByCode(this.sysConfigService, WestoneEncryptConstant.HSM_KEY_CIPHER_PASSWORD, WestoneEncryptConstant.HSM_KEY_CIPHER_PASSWORD_DEFAULT);
        return decryptFileBySM4ECB_WithKeyProtectedBySM2(data, keyCipherText, password);
    }

}
