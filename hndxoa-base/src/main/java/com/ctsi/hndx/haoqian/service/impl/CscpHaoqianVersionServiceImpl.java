package com.ctsi.hndx.haoqian.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ctsi.hndx.constant.SysConstant;
import com.ctsi.hndx.haoqian.entity.CscpHaoqianVersion;
import com.ctsi.hndx.haoqian.entity.dto.CscpHaoqianSignLogDTO;
import com.ctsi.hndx.haoqian.entity.dto.CscpHaoqianVersionDTO;
import com.ctsi.hndx.haoqian.mapper.CscpHaoqianVersionMapper;
import com.ctsi.hndx.haoqian.service.ICscpHaoqianSignLogService;
import com.ctsi.hndx.haoqian.service.ICscpHaoqianVersionService;
import com.ctsi.hndx.utils.*;
import com.ctsi.ssdc.model.PageResult;
import com.ctsi.hndx.common.SysBaseServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ctsi.hndx.common.BasePageForm;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import java.util.List;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

/**
 * <p>
 * 好签签批版本管理表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-10
 */
@Slf4j
@Service
public class CscpHaoqianVersionServiceImpl extends SysBaseServiceImpl<CscpHaoqianVersionMapper, CscpHaoqianVersion> implements ICscpHaoqianVersionService {

    @Autowired
    private CscpHaoqianVersionMapper cscpHaoqianVersionMapper;


    @Autowired
    private ICscpHaoqianSignLogService cscpHaoqianSignLogService;

//    @Autowired
//    private CscpDocumentFileService cscpDocumentFileService;

    @Autowired
    private HaoQianUtils haoQianUtils;

    /**
     * 翻页
     *
     * @param entityDTO
     * @param basePageForm
     * @return
     */
    @Override
    public PageResult<CscpHaoqianVersionDTO> queryListPage(CscpHaoqianVersionDTO entityDTO, BasePageForm basePageForm) {
        //设置条件
        LambdaQueryWrapper<CscpHaoqianVersion> queryWrapper = new LambdaQueryWrapper();
        if(null!=entityDTO.getFormDataId()){
            queryWrapper.eq(CscpHaoqianVersion::getFormDataId, entityDTO.getFormDataId());
        }
        if(null!=entityDTO.getSignId()){
            queryWrapper.eq(CscpHaoqianVersion::getSignId, entityDTO.getSignId());
        }
        queryWrapper.like(StringUtils.isNotEmpty(entityDTO.getProcTypeName()),CscpHaoqianVersion::getProcTypeName, entityDTO.getProcTypeName());
        queryWrapper.like(StringUtils.isNotEmpty(entityDTO.getTaskLink()),CscpHaoqianVersion::getTaskLink, entityDTO.getTaskLink());
        queryWrapper.eq(StringUtils.isNotEmpty(entityDTO.getIsSign()),CscpHaoqianVersion::getIsSign, entityDTO.getIsSign());
        IPage<CscpHaoqianVersion> pageData = cscpHaoqianVersionMapper.selectPageNoAdd(
             PageHelperUtil.getMPlusPageByBasePage(basePageForm), queryWrapper);
        //返回
        IPage<CscpHaoqianVersionDTO> data  = pageData.convert(entity -> BeanConvertUtils.copyProperties(entity,CscpHaoqianVersionDTO.class));

        return new PageResult<CscpHaoqianVersionDTO>(data.getRecords(),
            data.getTotal(), data.getCurrent());
    }

    /**
     * 列表查询
     *
     * @param entityDTO
     * @return
     */
    @Override
    public List<CscpHaoqianVersionDTO> queryList(CscpHaoqianVersionDTO entityDTO) {
        LambdaQueryWrapper<CscpHaoqianVersion> queryWrapper = new LambdaQueryWrapper();
        if(null!=entityDTO.getFormDataId()){
            queryWrapper.eq(CscpHaoqianVersion::getFormDataId, entityDTO.getFormDataId());
        }
        if(null!=entityDTO.getSignId()){
            queryWrapper.eq(CscpHaoqianVersion::getSignId, entityDTO.getSignId());
        }
        queryWrapper.like(StringUtils.isNotEmpty(entityDTO.getProcTypeName()),CscpHaoqianVersion::getProcTypeName, entityDTO.getProcTypeName());
        queryWrapper.like(StringUtils.isNotEmpty(entityDTO.getTaskLink()),CscpHaoqianVersion::getTaskLink, entityDTO.getTaskLink());
        queryWrapper.eq(StringUtils.isNotEmpty(entityDTO.getIsSign()),CscpHaoqianVersion::getIsSign, entityDTO.getIsSign());
        queryWrapper.orderByAsc(CscpHaoqianVersion::getCreateTime);
        List<CscpHaoqianVersion> listData = cscpHaoqianVersionMapper.selectListNoAdd(queryWrapper);
        List<CscpHaoqianVersionDTO> CscpHaoqianVersionDTOList = ListCopyUtil.copy(listData, CscpHaoqianVersionDTO.class);
        return CscpHaoqianVersionDTOList;
    }

    /**
     * 获取最新一条好签版本记录
     *
     * @param entityDTO
     * @return
     */
    @Override
    public CscpHaoqianVersionDTO queryOneNewsInfo(CscpHaoqianVersionDTO entityDTO) {
        LambdaQueryWrapper<CscpHaoqianVersion> queryWrapper = new LambdaQueryWrapper();
        if(null!=entityDTO.getFormDataId()){
            queryWrapper.eq(CscpHaoqianVersion::getFormDataId, entityDTO.getFormDataId());
        }
        if(null!=entityDTO.getSignId()){
            queryWrapper.eq(CscpHaoqianVersion::getSignId, entityDTO.getSignId());
        }
        queryWrapper.like(StringUtils.isNotEmpty(entityDTO.getProcTypeName()),CscpHaoqianVersion::getProcTypeName, entityDTO.getProcTypeName());
        queryWrapper.like(StringUtils.isNotEmpty(entityDTO.getTaskLink()),CscpHaoqianVersion::getTaskLink, entityDTO.getTaskLink());
        queryWrapper.eq(StringUtils.isNotEmpty(entityDTO.getIsSign()),CscpHaoqianVersion::getIsSign, entityDTO.getIsSign());
        queryWrapper.orderByDesc(CscpHaoqianVersion::getCreateTime,CscpHaoqianVersion::getId);
        List<CscpHaoqianVersion> listData = cscpHaoqianVersionMapper.selectListNoAdd(queryWrapper);
        List<CscpHaoqianVersionDTO> CscpHaoqianVersionDTOList = ListCopyUtil.copy(listData, CscpHaoqianVersionDTO.class);
        if(CscpHaoqianVersionDTOList.size()>0){
            return CscpHaoqianVersionDTOList.get(0);
        }else {
            return null;
        }
    }

    /**
     * 单个查询
     *
     * @param id the id of the entity
     * @return
     */
    @Override
    public CscpHaoqianVersionDTO findOne(Long id) {
        CscpHaoqianVersion  cscpHaoqianVersion =  cscpHaoqianVersionMapper.selectById(id);
        return  BeanConvertUtils.copyProperties(cscpHaoqianVersion,CscpHaoqianVersionDTO.class);
    }


    /**
     * 新增
     *
     * @param entityDTO the entity to create
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public CscpHaoqianVersionDTO create(CscpHaoqianVersionDTO entityDTO) {
        String signId = entityDTO.getSignId();
        try {
            //获取好签的下载地址
            String fileUrl = haoQianUtils.downloadFileUrl(signId);
            entityDTO.setFileUrl(fileUrl);
        } catch (Exception e) {
            e.printStackTrace();
        }
        CscpHaoqianVersion cscpHaoqianVersion = BeanConvertUtils.copyProperties(entityDTO, CscpHaoqianVersion.class);
        save(cscpHaoqianVersion);
        return  BeanConvertUtils.copyProperties(cscpHaoqianVersion,CscpHaoqianVersionDTO.class);
    }

    /**
     * 修改
     *
     * @param entity the entity to update
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int update(CscpHaoqianVersionDTO entity) {
        //如果是修改好签签批状态，改成已签批状态，那么就往好签签批历史中插入记录
        if(null!=entity.getIsSign()&&"1".equals(entity.getIsSign())){
            CscpHaoqianVersionDTO cscpHaoqianVersionDTO =  this.findOne(entity.getId());
            CscpHaoqianSignLogDTO cscpHaoqianSignLogDTO = new CscpHaoqianSignLogDTO();
            cscpHaoqianSignLogDTO.setSignId(cscpHaoqianVersionDTO.getSignId());
            cscpHaoqianSignLogDTO.setFormDataId(cscpHaoqianVersionDTO.getFormDataId());
            cscpHaoqianSignLogDTO.setIsRead("0");
            cscpHaoqianSignLogService.create(cscpHaoqianSignLogDTO);
        }
        String signId = entity.getSignId();
        try {
            //获取好签的下载地址
            String fileUrl = haoQianUtils.downloadFileUrl(signId);
            entity.setFileUrl(fileUrl);
        } catch (Exception e) {
            e.printStackTrace();
        }
        CscpHaoqianVersion cscpHaoqianVersion = BeanConvertUtils.copyProperties(entity, CscpHaoqianVersion.class);
        return cscpHaoqianVersionMapper.updateById(cscpHaoqianVersion);
    }

    /**
     * 修改是否更新好签pdf的状态
     *
     * @param entity the entity to update
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateIsupdate(CscpHaoqianVersionDTO entity) {
        //获取最新的好签版本，修改为需要更新状态
        CscpHaoqianVersionDTO cscpHaoqianVersionDTO = queryOneNewsInfo(entity);
        int count = 0;
        if(null!=cscpHaoqianVersionDTO && (StringUtils.isEmpty(cscpHaoqianVersionDTO.getIsUpdate())||"0".equals(cscpHaoqianVersionDTO.getIsUpdate()))){
            cscpHaoqianVersionDTO.setIsUpdate("1");
            CscpHaoqianVersion cscpHaoqianVersion = BeanConvertUtils.copyProperties(cscpHaoqianVersionDTO,CscpHaoqianVersion.class);
            count = cscpHaoqianVersionMapper.updateById(cscpHaoqianVersion);
            log.info("业务id为："+cscpHaoqianVersionDTO.getFormDataId()+"业务，进行修改好签签批状态，好签id为："+cscpHaoqianVersionDTO.getSignId());
        }
        if(count>0){
            return count;
        }else {
            return 200;
        }
    }

    /**
     * 删除
     *
     * @param id the id of the entity
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int delete(Long id) {
        return cscpHaoqianVersionMapper.deleteById(id);
    }

    /**
     * 通过业务id删除合并版本好签
     *
     * @param id the id of the entity
     */
    @Override
    public int deleteMergeVersionByFormDataId(Long formDataId) {
        LambdaQueryWrapper<CscpHaoqianVersion> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.eq(CscpHaoqianVersion::getFormDataId, formDataId);
        queryWrapper.eq(CscpHaoqianVersion::getIsMerge, "1");
        queryWrapper.orderByDesc(CscpHaoqianVersion::getCreateTime).last("limit 1");
        CscpHaoqianVersion cscpHaoqianVersion = cscpHaoqianVersionMapper.selectOneNoAdd(queryWrapper);
        if(cscpHaoqianVersion!=null){
            return cscpHaoqianVersionMapper.deleteById(cscpHaoqianVersion.getId());
        }else{
            return 0;
        }

    }

    /**
     * 验证是否存在
     *
     * @param CscpHaoqianVersionId
     * @return
     */
    @Override
    public boolean existByCscpHaoqianVersionId(Long CscpHaoqianVersionId) {
        if (CscpHaoqianVersionId != null) {
            LambdaQueryWrapper<CscpHaoqianVersion> queryWrapper = new LambdaQueryWrapper();
            queryWrapper.eq(CscpHaoqianVersion::getId, CscpHaoqianVersionId);
            List<CscpHaoqianVersion> result = cscpHaoqianVersionMapper.selectList(queryWrapper);
            return result.size() > 0;
        }
        return true;
    }

    /**
    * 批量新增
    *
    */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertBatch(List<CscpHaoqianVersionDTO> dataList) {
        List<CscpHaoqianVersion> result = ListCopyUtil.copy(dataList, CscpHaoqianVersion.class);
        return saveBatch(result);
    }


}
