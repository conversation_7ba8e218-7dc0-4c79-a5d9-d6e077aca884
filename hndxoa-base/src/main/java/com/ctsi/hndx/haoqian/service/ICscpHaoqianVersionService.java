package com.ctsi.hndx.haoqian.service;

import com.ctsi.hndx.constant.SysConstant;
import com.ctsi.hndx.haoqian.entity.dto.CscpHaoqianVersionDTO;
import com.ctsi.hndx.haoqian.entity.CscpHaoqianVersion;
import com.ctsi.hndx.common.SysBaseServiceI;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.ssdc.model.PageResult;
import org.springframework.scheduling.annotation.Async;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <p>
 * 好签签批版本管理表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-10
 */
public interface ICscpHaoqianVersionService extends SysBaseServiceI<CscpHaoqianVersion> {


    /**
     * 分页查询
     *
     * @param entityDTO
     * @param page
     * @return
     */
    PageResult<CscpHaoqianVersionDTO> queryListPage(CscpHaoqianVersionDTO entityDTO, BasePageForm page);

    /**
     * 获取所有不分页
     *
     * @param entity
     * @return
     */
    List<CscpHaoqianVersionDTO> queryList(CscpHaoqianVersionDTO entity);

    /**
     * 根据主键id获取单个对象
     *
     * @param id
     * @return
     */
    CscpHaoqianVersionDTO findOne(Long id);

    /**
     * 新增
     *
     * @param entity
     * @return
     */
    CscpHaoqianVersionDTO create(CscpHaoqianVersionDTO entity);


    /**
     * 更新
     *
     * @param entity
     * @return
     */
    int update(CscpHaoqianVersionDTO entity);

    /**
     * 删除
     *
     * @param id
     * @return
     */
    int delete(Long id);

     /**
     * 是否存在
     *
     * existByCscpHaoqianVersionId
     * @param code
     * @return
     */
    boolean existByCscpHaoqianVersionId(Long code);

    /**
    * 批量新增
    *
    * create batch
    * @param dataList
    * @return
    */
    Boolean insertBatch(List<CscpHaoqianVersionDTO> dataList);

    /**
     * 获取最新一条好签版本记录
     *
     * @param entityDTO
     * @return
     */
    public CscpHaoqianVersionDTO queryOneNewsInfo(CscpHaoqianVersionDTO entityDTO);

//    /**
//     * 异步复制正文pdf到好签
//     *
//     * @param entityDTO the entity to create
//     * @return
//     */
//    public void copyPdfToHaoqian(CscpHaoqianVersionDTO entityDTO);

    /**
     * 修改是否更新好签pdf的状态
     *
     * @param entity the entity to update
     * @return
     */
    int updateIsupdate(CscpHaoqianVersionDTO entity);

    /**
     * 通过业务id删除合并版本好签
     *
     * @param id the id of the entity
     */
    public int deleteMergeVersionByFormDataId(Long formDataId);


}
