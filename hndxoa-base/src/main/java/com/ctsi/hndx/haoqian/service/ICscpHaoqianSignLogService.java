package com.ctsi.hndx.haoqian.service;

import com.ctsi.hndx.haoqian.entity.dto.CscpHaoqianSignLogDTO;
import com.ctsi.hndx.haoqian.entity.CscpHaoqianSignLog;
import com.ctsi.hndx.common.SysBaseServiceI;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.ssdc.model.PageResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <p>
 * 好签签批日志 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-19
 */
public interface ICscpHaoqianSignLogService extends SysBaseServiceI<CscpHaoqianSignLog> {

    /**
     * 分页查询
     *
     * @param entityDTO
     * @param page
     * @return
     */
    PageResult<CscpHaoqianSignLogDTO> queryListPage(CscpHaoqianSignLogDTO entityDTO, BasePageForm page);

    /**
     * 获取所有不分页
     *
     * @param entity
     * @return
     */
    List<CscpHaoqianSignLogDTO> queryList(CscpHaoqianSignLogDTO entity);

    /**
     * 根据主键id获取单个对象
     *
     * @param id
     * @return
     */
    CscpHaoqianSignLogDTO findOne(Long id);

    /**
     * 新增
     *
     * @param entity
     * @return
     */
    CscpHaoqianSignLogDTO create(CscpHaoqianSignLogDTO entity);


    /**
     * 更新
     *
     * @param entity
     * @return
     */
    int update(CscpHaoqianSignLogDTO entity);

    /**
     * 删除
     *
     * @param id
     * @return
     */
    int delete(Long id);

     /**
     * 是否存在
     *
     * existByCscpHaoqianSignLogId
     * @param code
     * @return
     */
    boolean existByCscpHaoqianSignLogId(Long code);

    /**
    * 批量新增
    *
    * create batch
    * @param dataList
    * @return
    */
    Boolean insertBatch(List<CscpHaoqianSignLogDTO> dataList);

    /**
     * 通过业务id获取未阅的列表查询
     *
     * @param formDataId
     * @return
     */
    List<CscpHaoqianSignLogDTO> queryListNotReadByFormDataId(Long formDataId);

    /**
     * 通过业务id，修改阅读状态为已读
     *
     * @param formDataId
     * @return
     */
    int updateReadStartByFormDataId(Long formDataId);

}
