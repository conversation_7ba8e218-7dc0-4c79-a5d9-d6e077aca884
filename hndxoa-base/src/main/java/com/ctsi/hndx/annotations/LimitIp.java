package com.ctsi.hndx.annotations;

import java.lang.annotation.*;

/**
 * @Description IP白名单限制注解，添加该注解的接口只能接受白名单内的IP请求
 *              使用说明：将注解添加在接口方法上，标注白名单系统配置参数。
 *              如：@LimitIp(ipName=***********) 或 @LimitIp(ipName=常量参数)
 * <AUTHOR>
 * @date 2023-04-18
 */

@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
@Documented
@Inherited
public @interface LimitIp {

    /**
     * ip常量名
     * */
    String ipName() ;
}
