package com.ctsi.hndx.common;

import com.alibaba.excel.annotation.ExcelIgnore;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
public class BaseDtoEntity implements Serializable {
    private static final long serialVersionUID = 1L;


    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cscp_menus.id
     *
     * @mbg.generated Tue Aug 28 09:53:39 CST 2018
     */
    @ApiModelProperty(value = "主键id，新增不设置，修改或其它时设值")

    private Long id;
}
