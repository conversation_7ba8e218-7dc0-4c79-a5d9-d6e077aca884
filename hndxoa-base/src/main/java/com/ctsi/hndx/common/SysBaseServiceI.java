package com.ctsi.hndx.common;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface SysBaseServiceI<T> extends IService<T> {

    /**
     * 根据 entity 条件，查询一条记录
     * 只加上租户id
     * @param queryWrapper 实体对象封装操作类（可以为 null）
     */
     T selectOneOnlyAddTenantId( Wrapper<T> queryWrapper);

    /**
     * 根据 Wrapper 条件，查询总记录数
     *只加上租户id
     * @param queryWrapper 实体对象封装操作类（可以为 null）
     */
    Integer selectCountOnlyAddTenantId( Wrapper<T> queryWrapper);

    /**
     * 根据 entity 条件，查询全部记录
     *只加上租户id
     * @param queryWrapper 实体对象封装操作类（可以为 null）
     */
    List<T> selectListOnlyAddTenantId( Wrapper<T> queryWrapper);

    /**
     * 根据 Wrapper 条件，查询全部记录
     *只加上租户id
     * @param queryWrapper 实体对象封装操作类（可以为 null）
     */
    List<Map<String, Object>> selectMapsOnlyAddTenantId( Wrapper<T> queryWrapper);

    /**
     * 根据 Wrapper 条件，查询全部记录 只加上租户id
     * <p>注意： 只返回第一个字段的值</p>
     *
     * @param queryWrapper 实体对象封装操作类（可以为 null）
     */
    List<Object> selectObjsOnlyAddTenantId( Wrapper<T> queryWrapper);

    /**
     * 根据 entity 条件，查询全部记录（并翻页）,只按照租户过滤，不按照单位过滤
     *
     * @param page         分页查询条件（可以为 RowBounds.DEFAULT）
     * @param queryWrapper 实体对象封装操作类（可以为 null）
     */
    <E extends IPage<T>> E selectPageOnlyAddTenantId(E page,  Wrapper<T> queryWrapper);

    /**
     * 根据 Wrapper 条件，查询全部记录（并翻页）
     *只加上租户id
     * @param page         分页查询条件
     * @param queryWrapper 实体对象封装操作类
     */
    <E extends IPage<Map<String, Object>>> E selectMapsPageOnlyAddTenantId(E page,Wrapper<T> queryWrapper);




    /**
     * 根据 entity 条件，查询一条记录
     * 不加上租户id也不加上单位id，按照自身的逻辑过滤，请慎用
     * @param queryWrapper 实体对象封装操作类（可以为 null）
     */
    T selectOneNoAdd( Wrapper<T> queryWrapper);

    /**
     * 根据 Wrapper 条件，查询总记录数
     *不加上租户id也不加上单位id，按照自身的逻辑过滤，请慎用
     * @param queryWrapper 实体对象封装操作类（可以为 null）
     */
    Integer selectCountNoAdd( Wrapper<T> queryWrapper);

    /**
     * 根据 entity 条件，查询全部记录
     *不加上租户id也不加上单位id，按照自身的逻辑过滤，请慎用
     * @param queryWrapper 实体对象封装操作类（可以为 null）
     */
    List<T> selectListNoAdd(Wrapper<T> queryWrapper);

    /**
     * 根据 Wrapper 条件，查询全部记录
     *不加上租户id也不加上单位id，按照自身的逻辑过滤，请慎用
     * @param queryWrapper 实体对象封装操作类（可以为 null）
     */
    List<Map<String, Object>> selectMapsNoAdd( Wrapper<T> queryWrapper);

    /**
     * 根据 Wrapper 条件，查询全部记录 不加上租户id也不加上单位id，按照自身的逻辑过滤，请慎用
     * <p>注意： 只返回第一个字段的值</p>
     *
     * @param queryWrapper 实体对象封装操作类（可以为 null）
     */
    List<Object> selectObjsNoAdd(Wrapper<T> queryWrapper);

    /**
     * 根据 entity 条件，查询全部记录（并翻页）,不加上租户id也不加上单位id，按照自身的逻辑过滤，请慎用
     *
     * @param page         分页查询条件（可以为 RowBounds.DEFAULT）
     * @param queryWrapper 实体对象封装操作类（可以为 null）
     */
    <E extends IPage<T>> E selectPageNoAdd(E page,  Wrapper<T> queryWrapper);

    /**
     * 根据 Wrapper 条件，查询全部记录（并翻页）不加上租户id也不加上单位id，按照自身的逻辑过滤，请慎用
     *只加上租户id
     * @param page         分页查询条件
     * @param queryWrapper 实体对象封装操作类
     */
    <E extends IPage<Map<String, Object>>> E selectMapsPageNoAdd(E page, Wrapper<T> queryWrapper);
}
