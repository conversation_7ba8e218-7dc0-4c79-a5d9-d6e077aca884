package com.ctsi.hndx.esignature.controller;
import com.ctsi.ssdc.model.PageResult;
import java.util.List;
import java.util.Optional;
import com.ctsi.hndx.esignature.entity.CscpESignature;
import com.ctsi.hndx.esignature.entity.dto.CscpESignatureDTO;
import com.ctsi.hndx.esignature.service.ICscpESignatureService;
import com.ctsi.ssdc.security.SecurityUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.ctsi.ssdc.model.ResResult;
import org.springframework.security.access.prepost.PreAuthorize;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import com.ctsi.hndx.common.BasePageForm;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.InitBinder;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.util.Assert;
import com.ctsi.hndx.common.BaseController;
import com.ctsi.hndx.annotations.ResponseResultVo;
import com.ctsi.hndx.result.ResultCode;
import com.ctsi.hndx.result.ResultVO;
import com.ctsi.ssdc.annotation.OperationLog;
import com.ctsi.hndx.enums.DBOperation;


/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-18
 *
 */

@Slf4j
@RestController
@ResponseResultVo
@RequestMapping("/api/cscpESignature")
@Api(value = "电子签名管理表", tags = "电子签名管理表接口")
public class CscpESignatureController extends BaseController {

    private static final String ENTITY_NAME = "cscpESignature";

    @Autowired
    private ICscpESignatureService cscpESignatureService;



    /**
     *  新增电子签名管理表批量数据.
     */
    @PostMapping("/createBatch")
    @ApiOperation(value = "新增批量(权限code码为：cscp.cscpESignature.add)", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.ADD,message = "新增电子签名管理表批量数据")
//    @PreAuthorize("@permissionService.hasPermi('cscp.cscpESignature.add')")
    public ResultVO createBatch(@RequestBody List<CscpESignatureDTO> cscpESignatureList) {
       Boolean  result = cscpESignatureService.insertBatch(cscpESignatureList);
       if(result){
           return ResultVO.success();
       }else {
           return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
       }
    }

     /**
     *  新增数据.
     */
    @PostMapping("/create")
    @ApiOperation(value = "新增(权限code码为：cscp.cscpESignature.add)", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.ADD,message = "新增电子签名管理表数据")
//    @PreAuthorize("@permissionService.hasPermi('cscp.cscpESignature.add')")
    public ResultVO<CscpESignatureDTO> create(@RequestBody CscpESignatureDTO cscpESignatureDTO)  {
        Long signBy = cscpESignatureDTO.getSignBy();
        if(cscpESignatureService.existBySignBy(signBy)){
            return ResultVO.error("已存在该"+cscpESignatureDTO.getSignName()+"用户电子签名，不能重复添加。");
        }else {
            CscpESignatureDTO result = cscpESignatureService.create(cscpESignatureDTO);
            return ResultVO.success(result);
        }
    }

    /**
     *  更新存在数据.
     */
    @PostMapping("/update")
    @ApiOperation(value = "更新存在数据(权限code码为：cscp.cscpESignature.update)", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.UPDATE,message = "更新电子签名管理表数据")
//    @PreAuthorize("@permissionService.hasPermi('cscp.cscpESignature.update')")
    public ResultVO update(@RequestBody CscpESignatureDTO cscpESignatureDTO) {
	    Assert.notNull(cscpESignatureDTO.getId(), "general.IdNotNull");
        int count = cscpESignatureService.update(cscpESignatureDTO);
        if(count > 0 ){
            return ResultVO.success();
        }else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }

     /**
     *  删除存在数据.
     */
    @DeleteMapping("/delete/{id}")
    @OperationLog(dBOperation = DBOperation.DELETE,message = "删除电子签名管理表数据")
    @ApiOperation(value = "删除存在数据(权限code码为：cscp.cscpESignature.delete)", notes = "传入参数")
//    @PreAuthorize("@permissionService.hasPermi('cscp.cscpESignature.delete')")
    public ResultVO delete(@PathVariable Long id) {
        int count = cscpESignatureService.delete(id);
        if(count > 0 ){
            return ResultVO.success();
        }else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }

    /**
     * 查询单条数据.
     */
    @GetMapping("/get/{id}")
    @ApiOperation(value = "查询单条数据", notes = "传入参数")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
    public ResultVO get(@PathVariable Long id) {
        CscpESignatureDTO cscpESignatureDTO = cscpESignatureService.findOne(id);
        return ResultVO.success(cscpESignatureDTO);
    }

    /**
    *  分页查询多条数据.
    */
    @GetMapping("/queryCscpESignaturePage")
    @ApiOperation(value = "翻页查询多条数据", notes = "传入参数")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
    public ResultVO<PageResult<CscpESignatureDTO>> queryCscpESignaturePage(CscpESignatureDTO cscpESignatureDTO, BasePageForm basePageForm) {
        return ResultVO.success(cscpESignatureService.queryListPage(cscpESignatureDTO, basePageForm));
    }

   /**
    * 查询多条数据.不分页
    */
   @GetMapping("/queryCscpESignature")
   @ApiOperation(value = "查询多条数据", notes = "传入参数")
   //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
   public ResultVO<ResResult<CscpESignatureDTO>> queryCscpESignature(CscpESignatureDTO cscpESignatureDTO) {
       List<CscpESignatureDTO> list = cscpESignatureService.queryList(cscpESignatureDTO);
       return ResultVO.success(new ResResult<CscpESignatureDTO>(list));
   }

    /**
     * 通过用户Id查询电子签名信息
     */
    @GetMapping("/queryCscpESignatureByUseId")
    @ApiOperation(value = "通过当前用户Id查询电子签名信息", notes = "传入参数")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
    public ResultVO<ResResult<CscpESignatureDTO>> queryCscpESignatureByUseId(CscpESignatureDTO cscpESignatureDTO) {
        cscpESignatureDTO.setUseIds(SecurityUtils.getCurrentUserId()+",");
        List<CscpESignatureDTO> list = cscpESignatureService.queryByUseIdList(cscpESignatureDTO);
        return ResultVO.success(new ResResult<CscpESignatureDTO>(list));
    }

}
