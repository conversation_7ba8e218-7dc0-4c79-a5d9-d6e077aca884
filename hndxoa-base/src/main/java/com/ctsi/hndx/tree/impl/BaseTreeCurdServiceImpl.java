package com.ctsi.hndx.tree.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.tree.Tree;
import cn.hutool.core.lang.tree.TreeNode;
import cn.hutool.core.lang.tree.TreeNodeConfig;
import cn.hutool.core.lang.tree.TreeUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ctsi.hndx.common.MybatisBaseMapper;
import com.ctsi.hndx.common.SysBaseServiceImpl;
import com.ctsi.hndx.tree.Node;
import com.ctsi.hndx.tree.TreeCrudService;
import com.ctsi.hndx.tree.TreePO;
import com.ctsi.hndx.tree.TreeQueryDto;
import com.ctsi.ssdc.util.RedisUtil;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

@Slf4j
public abstract class BaseTreeCurdServiceImpl<E extends TreePO, D, M extends MybatisBaseMapper<D>> extends
        SysBaseServiceImpl<M, D>
        implements TreeCrudService<E, D> {


    private static int MAX_TREE_HIGH = 10;

    @Autowired
    private RedisUtil redisUtil;

    // 使用 ConcurrentHashMap 来存储缓存
    private final Map<Long, List<Node<E>>> cache = new ConcurrentHashMap<>();

    /**
     * 根据父节点的id找到子节点
     *
     * @param parentId
     * @return
     */
    @Override
    public List<E> selectChildren(Long parentId) {
        Assert.notNull(parentId, "parentId is null");

        try {
            QueryWrapper<D> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("parent_id", parentId);
            queryWrapper.orderByAsc("order_by");
            List<D> list = baseMapper.selectList(queryWrapper);
            return getDataDtOFromDomin(list);
        } catch (Exception e) {
            log.error("selectChildren occurs error, caused by: ", e);
            throw new RuntimeException("selectChildren occurs error", e);
        }
    }


    /**
     * 类型转换
     * @param list
     * @return
     */
    public abstract List<E> getDataDtOFromDomin(List<D> list);

    /**
     * 类型转换
     * @param d
     * @param e
     * @return
     */
    public abstract E copyDto(D d, E e);


    @Override
    public Node<E> selectNodeByParentId(Long parentId) {
        Node<E> node = this.selectNodeByParentId(parentId, null);
        return node;
    }

    public Node<E> selectNodeByParentId(Long parentId, Set<Long> checkedIds) {
        Assert.notNull(parentId, "parentId is null");

        int currentTreeHigh = 1;

        Node<E> tree = new Node<>();

        D da = (D) baseMapper.selectById(parentId);//super.selectByPk(parentId);
        E parent = null;
        E e = copyDto(da, parent);
        if (e != null) {
            Node<E> eNode = wrapNode(e);
            tree = buildTree(eNode, currentTreeHigh, checkedIds);
        }

        return tree;
    }


    @Override
    public Node<E> selectCheckedInNodeByParentId(Long parentId, Set<Long> checkedIds) {
        Assert.notNull(parentId, "parentId is null");

        int currentTreeHigh = 1;

        Node<E> tree = new Node<>();

        D da = (D) baseMapper.selectById(parentId);//super.selectByPk(parentId);
        E parent = null;
        E e = copyDto(da, parent);
        if (e != null) {
            Node<E> eNode = wrapNode(e);
            tree = buildCheckedInTree(eNode, currentTreeHigh, checkedIds);
        }

        return tree;
    }


    @Override
    public List<Node<E>> selectChildrenListNodeByParentId(Long parentId) {
        // 尝试从内存缓存中获取节点
        List<Node<E>> cachedNode = cache.get(parentId);
        if (CollectionUtil.isNotEmpty(cachedNode)) {
            return cachedNode; // 如果缓存存在，直接返回
        }

        // 如果缓存不存在，从数据库查询
        List<Node<E>> nodes = selectChildrenListNodeByParentId(parentId, 0);

        // 将构建好的节点存入内存缓存
        if(nodes!=null&&!nodes.isEmpty()) {
            cache.put(parentId, nodes);
        }
        return nodes;
    }
    // 清除特定缓存的方法
    @Override
    public void clearCache(Long parentId) {
        cache.remove(parentId);
    }


    public List<Node<E>> selectChildrenListNodeByParentIdAndParam(Long parentId, int level,List<TreeQueryDto> treeQueryDtoList) {

        List<E> list = selectChildren(parentId);
        List<Node<E>> nodeList = new ArrayList<>();
        //0获取所有
        if (level == 0) {
            for (E e : list) {
                Node<E> eNode = selectNodeByParentId(e.getId());
                nodeList.add(eNode);
            }
        } else {
            for (E e : list) {
                Node<E> eNode = wrapNode(e);
                nodeList.add(eNode);
            }

        }

        return nodeList;
    }

    @Override
    public List<Node<E>> selectChildrenListNodeByParentId(Long parentId, int level) {

        List<E> list = selectChildren(parentId);
        List<Node<E>> nodeList = new ArrayList<>();
        //0获取所有
        if (level == 0) {
            for (E e : list) {
                Node<E> eNode = selectNodeByParentId(e.getId());
                nodeList.add(eNode);
            }
        } else {
            for (E e : list) {
                Node<E> eNode = wrapNode(e);
                nodeList.add(eNode);
            }

        }

        return nodeList;
    }

    @Override
    public List<Node<E>> selectChildrenListNodeByParentId(Long parentId, List<Long> checkedLongs) {
        // 查询父节点为parentId的所有子节点
        List<E> list = selectChildren(parentId);
        Set<Long> checkedIds = new HashSet<>(checkedLongs);
        List<Node<E>> nodeList = new ArrayList<>();
        if (CollectionUtils.isEmpty(checkedLongs)) {
            for (E e : list) {
                Node<E> eNode = selectNodeByParentId(e.getId());
                nodeList.add(eNode);
            }
        } else {
            for (E e : list) {
                Node<E> eNode = selectCheckedInNodeByParentId(e.getId(), checkedIds);
                if (checkedLongs.contains(eNode.getId())) {
                    eNode.setChecked(true);
                }
                nodeList.add(eNode);
            }
        }

        return nodeList;
    }

    @Override
    public List<Node<E>> listCheckedOrgNodeTree(Long companyId, List<Long> checkedLongs, Set<Long> checkedIds) {
        List<E> list = selectChildren(companyId);
        List<Node<E>> nodeList = new ArrayList<>();
        if (CollectionUtils.isEmpty(checkedLongs)) {
            return this.selectChildrenListNodeByParentId(companyId, checkedLongs);
        }
        for (E e : list) {
            if (checkedIds.contains(e.getId())) {
                Node<E> eNode = selectNodeByParentId(e.getId(), checkedIds);
                eNode.setChecked(true);
                nodeList.add(eNode);
            }
        }

        return nodeList;
    }

    /**
     * 将集合组装成树
     * @param assembledList
     * @return
     */
    @Override
    public List<Tree<String>> assembledTreeNodeLists(List<E> assembledList){
        //构建的整个树数据
        List<TreeNode<String>> treeNodeList = assembledList.stream().map(i -> {
            //单个树数据构建
            TreeNode<String> treeNode = new TreeNode<String>()
                    .setId(String.valueOf(i.getId()))
                    .setParentId(String.valueOf(i.getParentId()))
                    .setName(i.getTitle())
                    .setWeight(i.getOrderBy());

            //处理父id不为0就不组装树的问题
            if (!i.getParentId().equals(0L)) {
                List<E> collect = assembledList.stream().filter(j -> j.getId().equals(i.getParentId())).collect(Collectors.toList());
                if (!collect.isEmpty()) {
                    treeNode.setParentId(String.valueOf(i.getParentId()));
                } else {
                    treeNode.setParentId("0");
                }
            } else {
                treeNode.setParentId(String.valueOf(i.getParentId()));
            }
            return treeNode;
        }).collect(Collectors.toList());

        // 配置
        TreeNodeConfig treeNodeConfig = new TreeNodeConfig();
        // 自定义属性名 都要默认值的
        treeNodeConfig.setNameKey("title");
        treeNodeConfig.setChildrenKey("children");
        // 最大递归深度
        treeNodeConfig.setDeep(6);

        //转换器
        List<Tree<String>> treeNodes = TreeUtil.build(treeNodeList,"0",treeNodeConfig,
                (treeNode, tree) ->{
                    tree.setId(treeNode.getId());
                    tree.setParentId(treeNode.getParentId());
                    tree.setWeight(treeNode.getWeight());
                    tree.setName(treeNode.getName());
                });
        return treeNodes;
    }

    private Node<E> buildCheckedInTree(Node<E> eNode, int currentTreeHigh, Set<Long> checkedIds) {
        if (currentTreeHigh++ >= MAX_TREE_HIGH) {
            return eNode;
        }

        List<Node<E>> descendantNodes = getCheckedInDescendantNodes(eNode.getDetailsData().getId(), checkedIds);
        List<Node<E>> children = eNode.getChildren() == null ? Lists.newArrayList() : eNode.getChildren();
        children.addAll(descendantNodes);
        eNode.setChildren(children);

        for (Node<E> node : descendantNodes) {
            buildCheckedInTree(node, currentTreeHigh, checkedIds);
        }

        return eNode;
    }


    private Node<E> buildTree(Node<E> eNode, int currentTreeHigh, Set<Long> checkedIds) {
        // 设置缓存的 key，使用 Node 的 ID
        String cacheKey = "treeNode:" + eNode.getId();

        // 尝试从 Redis 中获取节点
        Node<E> cachedNode = (Node<E>) redisUtil.get(cacheKey);
        if (cachedNode != null) {
            return cachedNode; // 如果缓存存在，直接返回
        }

        // 如果缓存不存在，继续处理
        if (currentTreeHigh++ >= MAX_TREE_HIGH) {
            return eNode;
        }

        List<Node<E>> descendantNodes = getDescendantNodes(eNode.getDetailsData().getId(), checkedIds);
        List<Node<E>> children = eNode.getChildren() == null ? Lists.newArrayList() : eNode.getChildren();
        children.addAll(descendantNodes);
        eNode.setChildren(children);

        for (Node<E> node : descendantNodes) {
            buildTree(node, currentTreeHigh, checkedIds);
        }

        // 将构建好的节点存入 Redis
        redisUtil.set(cacheKey, eNode);

        return eNode;
    }


    private Node<E> buildTree(Node<E> eNode, int currentTreeHigh) {
        if (currentTreeHigh++ >= MAX_TREE_HIGH) {
            return eNode;
        }

        List<Node<E>> descendantNodes = getDescendantNodes(eNode.getDetailsData().getId());
        List<Node<E>> children = eNode.getChildren() == null ? Lists.newArrayList() : eNode.getChildren();
        children.addAll(descendantNodes);
        eNode.setChildren(children);

        for (Node<E> node : descendantNodes) {
            buildTree(node, currentTreeHigh);
        }

        return eNode;
    }

    private List<Node<E>> getDescendantNodes(long id, Set<Long> checkedIds) {
        if (CollectionUtils.isEmpty(checkedIds)) {
            return this.getDescendantNodes(id);
        }
        List<E> eList = this.selectChildren(id);

        List<Node<E>> list = Lists.newLinkedList();
        for (E parent : eList) {
            if (checkedIds.contains(parent.getId())) {
                Node<E> node = wrapNode(parent);
                node.setChecked(true);
                list.add(node);
            }
        }
        return list;
    }

    private List<Node<E>> getCheckedInDescendantNodes(long id, Set<Long> checkedIds) {
        List<E> eList = this.selectChildren(id);

        List<Node<E>> list = Lists.newLinkedList();
        for (E parent : eList) {
            Node<E> node = wrapNode(parent);
            if (checkedIds.contains(parent.getId())) {
                node.setChecked(true);
            }
            list.add(node);
        }

        return list;
    }

    private List<Node<E>> getDescendantNodes(long id) {
        List<E> eList = this.selectChildren(id);

        List<Node<E>> list = Lists.newLinkedList();
        for (E parent : eList) {
            Node<E> node = wrapNode(parent);
            list.add(node);
        }

        return list;
    }

    private Node<E> wrapNode(E parent) {
        Node<E> node = new Node<>();
        node.setDetailsData(parent);
        node.setId(parent.getId());
        node.setTitle(parent.getTitle());
        return node;
    }

}
