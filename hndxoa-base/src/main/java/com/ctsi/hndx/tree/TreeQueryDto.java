package com.ctsi.hndx.tree;

import com.ctsi.hndx.mybatisplus.query.QueryCondition;
import com.ctsi.hndx.mybatisplus.query.QueryConditionEnum;
import lombok.Data;

/**
 * <AUTHOR>
 * @Classname TreeQueryDto
 * @Description
 * @Date 2021/12/3 16:07
 */
@Data
public class TreeQueryDto {

    /**
     * 数据库字段
     */
    String field;

    /**
     * 查询的条件
     */
    QueryConditionEnum queryConditionEnum;


    /**
     * 具体的数值
     */
    String value;

}
