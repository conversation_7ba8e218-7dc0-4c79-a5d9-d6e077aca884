package com.ctsi.hndx.wps;

import com.itextpdf.text.pdf.PdfReader;
import lombok.extern.slf4j.Slf4j;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.rendering.PDFRenderer;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.File;
import java.util.UUID;

/**
 * <AUTHOR>
 * @Description 新版本 PDF文档操作工具类
 * @Date 2023/06/15
 */
@Slf4j
public class ItextPdfUtils {

    /**
     * 获取文件的总页数
     * @param byteFile
     */
    public static int getDocTotalPageCount(byte[] byteFile) {
        int totalPageCount = 0;
        try {
            PdfReader reader = new PdfReader(byteFile);
            totalPageCount = reader.getNumberOfPages();
            return totalPageCount;
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return totalPageCount;
    }

    /**
     * 提取PDF文件的第一页，并转成png图片
     * @param byteFile pdf文件字节流
     * @param targetFolderPath 目标文件夹（临时文件夹）
     * @return
     */
    public static String transferFirstPageToImg(byte[] byteFile, String targetFolderPath) {
        String imgFilePath = null;
        try {
            String imgExt = "png";
            PDDocument pdDocument = PDDocument.load(byteFile);
            PDFRenderer pdfRenderer = new PDFRenderer(pdDocument);
            PdfReader reader = new PdfReader(byteFile);
            int totalPageCount = reader.getNumberOfPages();
            if (totalPageCount > 0) {
                String fileName = UUID.randomUUID().toString().toLowerCase() + "." + imgExt;
                BufferedImage image = pdfRenderer.renderImage(0, 1.333f);
                imgFilePath = targetFolderPath + File.separator + fileName;
                File newImgFile = new File(imgFilePath);
                ImageIO.write(image, imgExt, newImgFile);
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return imgFilePath;
    }

}
