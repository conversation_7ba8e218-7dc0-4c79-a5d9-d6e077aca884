package com.ctsi.hndx.wps;

import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.StatusLine;
import org.apache.http.client.ClientProtocolException;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.io.*;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Map;

/**
 * @Author: lizuolang
 * @Description WPS接口服务工具类
 * @Date 2023/02/08 09:53
 */
public class WpsUtil {

    /**
     * 发送Get请求(如果https请求需要忽略证书检测，需自己实现)
     *
     * @param url
     * @param headers
     * @return
     * @throws IOException
     */
    public static Pair<StatusLine, String> sendGetRequest(String url, Map<String, String> headers) throws IOException {
        // 设置请求头
        HttpGet httpGet = new HttpGet(url);
        if (null != headers) {
            for (Map.Entry<String, String> entry : headers.entrySet()) {
                httpGet.addHeader(entry.getKey(), entry.getValue());
            }
        }
        // 发送httpGet请求
        try (CloseableHttpClient httpClient = HttpClients.createDefault();
             CloseableHttpResponse response = httpClient.execute(httpGet)) {
            HttpEntity entity = response.getEntity();
            if (null != entity) {
                String content = EntityUtils.toString(entity, StandardCharsets.UTF_8);
                // 关闭content stream
                EntityUtils.consume(entity);
                return Pair.of(response.getStatusLine(), content);
            }
            throw new IOException("No response");
        } catch (ClientProtocolException e) {
            throw e;
        } catch (IOException e) {
            throw e;
        }
    }

    /**
     * 发送Post请求(如果https请求需要忽略证书检测，需自己实现)
     *
     * @param url
     * @param headers
     * @return
     * @throws IOException
     */
    public static Pair<StatusLine, String> sendPostRequest(String url, Map<String, String> headers,
                                                           Map<String, Object> bodyParams) throws IOException {
        // 设置请求头
        HttpPost httpPost = new HttpPost(url);
        if (null != headers) {
            for (Map.Entry<String, String> entry : headers.entrySet()) {
                httpPost.addHeader(entry.getKey(), entry.getValue());
            }
        }
        // 发送httpPost请求
        try {
            CloseableHttpClient httpClient = HttpClients.createDefault();
            if (null == bodyParams || 0 == bodyParams.size()) {
                // no body params
            } else {
                StringEntity httpEntity = new StringEntity(new JSONObject(bodyParams).toJSONString(),
                        ContentType.APPLICATION_JSON);
                httpPost.setEntity(httpEntity);
            }
            CloseableHttpResponse response = httpClient.execute(httpPost);
            HttpEntity entity = response.getEntity();
            if (null != entity) {
                String content = EntityUtils.toString(entity, StandardCharsets.UTF_8);
                // 关闭content stream
                EntityUtils.consume(entity);
                return Pair.of(response.getStatusLine(), content);
            }
            throw new IOException("No response");
        } catch (ClientProtocolException e) {
            throw e;
        } catch (IOException e) {
            throw e;
        }
    }

    public static Pair<StatusLine, String> sendPostRequestConfigTimeout(String url, Map<String, String> headers,
                                                           Map<String, Object> bodyParams,
                                                           int connectTimeout,
                                                           int socketTimeout) throws IOException {
        // 配置超时参数
        RequestConfig config = RequestConfig.custom()
                .setConnectTimeout(0 == connectTimeout ? 5000 : connectTimeout)     // 连接超时（与服务器建立TCP连接的时间上限）ms/毫秒级
                .setSocketTimeout(0 == socketTimeout ? 10000 : socketTimeout)   // 响应超时（等待服务器返回数据的最大时间） ms/毫秒级
                .build();

        // 设置请求头
        HttpPost httpPost = new HttpPost(url);
        if (null != headers) {
            for (Map.Entry<String, String> entry : headers.entrySet()) {
                httpPost.addHeader(entry.getKey(), entry.getValue());
            }
        }
        // 发送httpPost请求
        try {
            CloseableHttpClient httpClient = HttpClients.custom()
                    .setDefaultRequestConfig(config).build();
            if (null == bodyParams || 0 == bodyParams.size()) {
                // no body params
            } else {
                StringEntity httpEntity = new StringEntity(new JSONObject(bodyParams).toJSONString(),
                        ContentType.APPLICATION_JSON);
                httpPost.setEntity(httpEntity);
            }
            CloseableHttpResponse response = httpClient.execute(httpPost);
            HttpEntity entity = response.getEntity();
            if (null != entity) {
                String content = EntityUtils.toString(entity, StandardCharsets.UTF_8);
                // 关闭content stream
                EntityUtils.consume(entity);
                return Pair.of(response.getStatusLine(), content);
            }
            throw new IOException("No response");
        } catch (ClientProtocolException e) {
            throw e;
        } catch (IOException e) {
            throw e;
        }
    }

    public static Pair<StatusLine, byte[]> sendPostRequestNew(String url, Map<String, String> headers,
                                                              Map<String, Object> bodyParams) throws IOException {
        // 设置请求头
        HttpPost httpPost = new HttpPost(url);
        if (null != headers) {
            for (Map.Entry<String, String> entry : headers.entrySet()) {
                httpPost.addHeader(entry.getKey(), entry.getValue());
            }
        }
        // 发送httpPost请求
        try {
            CloseableHttpClient httpClient = HttpClients.createDefault();
            if (null == bodyParams || 0 == bodyParams.size()) {
                // no body params
            } else {
                StringEntity httpEntity = new StringEntity(new JSONObject(bodyParams).toJSONString(),
                        ContentType.APPLICATION_JSON);
                httpPost.setEntity(httpEntity);
            }
            CloseableHttpResponse response = httpClient.execute(httpPost);
            HttpEntity entity = response.getEntity();
            if (null != entity) {

                byte[] byteArray = EntityUtils.toByteArray(entity);
                // 关闭content stream
                EntityUtils.consume(entity);
                return Pair.of(response.getStatusLine(), byteArray);
            }
            throw new IOException("No response");
        } catch (ClientProtocolException e) {
            throw e;
        } catch (IOException e) {
            throw e;
        }
    }

    /**
     * 发送Get请求(如果https请求需要忽略证书检测，需自己实现)
     *
     * 下载文件
     * @param url
     * @param headers
     * @return
     * @throws IOException
     */
    public static byte[] httpDownLoad(String url, Map<String, String> headers) throws IOException {
        // 设置请求头
        HttpGet httpGet = new HttpGet(url);
        if (null != headers) {
            for (Map.Entry<String, String> entry : headers.entrySet()) {
                httpGet.addHeader(entry.getKey(), entry.getValue());
            }
        }
        // 发送httpGet请求
        CloseableHttpClient httpClient = HttpClients.createDefault();
        InputStream in = null;
        BufferedInputStream bufferedInputStream = null;
        ByteArrayOutputStream outputStream = null;
        try {
            HttpResponse response = httpClient.execute(httpGet);
            HttpEntity entity = response.getEntity();
            in = entity.getContent();
            int len = -1;
            byte[] buffer = new byte[1024];
            bufferedInputStream = new BufferedInputStream(in);
            outputStream = new ByteArrayOutputStream();
            while ((len = bufferedInputStream.read(buffer)) != -1) {
                outputStream.write(buffer, 0, len);
            }
            return outputStream.toByteArray();
        } catch (ClientProtocolException e) {
            throw e;
        } catch (IOException e) {
            throw e;
        } finally {
            try {
                httpClient.close();
                if (in != null) {
                    in.close();
                }
                if (bufferedInputStream != null) {
                    bufferedInputStream.close();
                }
                if (outputStream != null) {
                    outputStream.close();
                }

            } catch (IOException e) {
            }
        }
    }

    public static String HMACSHA256(String data, String key) throws Exception {
        Mac sha256_HMAC = Mac.getInstance("HmacSHA256");
        SecretKeySpec secret_key = new SecretKeySpec(key.getBytes("UTF-8"), "HmacSHA256");
        sha256_HMAC.init(secret_key);
        byte[] array = sha256_HMAC.doFinal(data.getBytes("UTF-8"));
        StringBuilder sb = new StringBuilder();
        for (byte item : array) {
            sb.append(Integer.toHexString((item & 0xFF) | 0x100).substring(1, 3));
        }
        return sb.toString();
    }

    /**
     * 利用java原生的摘要实现SHA256加密
     * @param str 加密后的报文
     * @return
     */
    public static String getSHA256StrJava(byte[] str) {
        MessageDigest messageDigest;
        String encodeStr = "";
        try {
            messageDigest = MessageDigest.getInstance("SHA-256");
            messageDigest.update(str);
            encodeStr = byte2Hex(messageDigest.digest());
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
        }
        return encodeStr;
    }

    /**
     * 将byte转为16进制
     * @param bytes
     * @return
     */
    private static String byte2Hex(byte[] bytes) {
        StringBuffer stringBuffer = new StringBuffer();
        String temp = null;
        for (int i = 0; i < bytes.length; i++) {
            temp = Integer.toHexString(bytes[i] & 0xFF);
            if (temp.length() == 1) {
                //1得到一位的进行补0操作
                stringBuffer.append("0");
            }
            stringBuffer.append(temp);
        }
        return stringBuffer.toString();
    }

    public static String convertWpsFileType(String fileExtName) {
        String fileType = null;
        if (null == fileExtName || "".equals(fileExtName)) {
            fileType = "x";
            return fileType;
        }
        fileExtName = getExtendByDot(fileExtName);
        if (null == fileExtName || "".equals(fileExtName)) {
            fileType = "x";
            return fileType;
        }
        if (fileExtName.equalsIgnoreCase("xlsx")
                || fileExtName.equalsIgnoreCase("xls")
                || fileExtName.equalsIgnoreCase("xlsm")) {
            fileType = "s";
        } else if (fileExtName.equalsIgnoreCase("pdf")
                || fileExtName.equalsIgnoreCase("ofd")) {
            fileType = "f";
        } else if (fileExtName.equalsIgnoreCase("doc")
                || fileExtName.equalsIgnoreCase("docx")
                || fileExtName.equalsIgnoreCase("txt")) {
            fileType = "w";
        } else if (fileExtName.equalsIgnoreCase("ppt")
                || fileExtName.equalsIgnoreCase("pptx")) {
            fileType = "p";
        } else {
            fileType = "x";
        }
        return fileType;
    }

    public static String getExtendByDot(String filename) {
        int index = filename.lastIndexOf(".");
        if (index == -1) {
            return null;
        }
        String result = filename.substring(index + 1);
        return result;
    }

    /**
     * 根据byte数组，生成文件
     * @param bfile
     * @param absFileName
     */
    public static void byteArrayToFile(byte[] bfile, String absFileName) {
        BufferedOutputStream bos = null;
        FileOutputStream fos = null;
        File file = null;
        try {
            file = new File(absFileName);
            fos = new FileOutputStream(file);
            bos = new BufferedOutputStream(fos);
            bos.write(bfile);
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (bos != null) {
                try {
                    bos.close();
                } catch (IOException e1) {
                    e1.printStackTrace();
                }
            }
            if (fos != null) {
                try {
                    fos.close();
                } catch (IOException e1) {
                    e1.printStackTrace();
                }
            }
        }
    }

}
