package com.ctsi.hndx.wps.entity;

/**
 * <AUTHOR>
 * @Description wps文档中台平台配置的应用证书回调
 * @Date 2023/03/09
 */
public class WpsLicenseParam {

    /**
     * 证书过期时间，单位为秒的时间戳
     */
    private Integer dead_line;

    /**
     * 证书距离过期天数
     */
    private Integer licence_remaining_time;

    private String message;

    public Integer getDead_line() {
        return dead_line;
    }

    public void setDead_line(Integer dead_line) {
        this.dead_line = dead_line;
    }

    public Integer getLicence_remaining_time() {
        return licence_remaining_time;
    }

    public void setLicence_remaining_time(Integer licence_remaining_time) {
        this.licence_remaining_time = licence_remaining_time;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    @Override
    public String toString() {
        return "WpsLicenseParam{" +
                "dead_line='" + dead_line + '\'' +
                ", licence_remaining_time=" + licence_remaining_time +
                ", message='" + message + '\'' +
                '}';
    }
}
