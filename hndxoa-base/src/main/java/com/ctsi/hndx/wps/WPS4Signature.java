package com.ctsi.hndx.wps;

import com.ctsi.hndx.utils.StringUtils;

import java.text.SimpleDateFormat;
import java.util.*;

/**
 * @Author: lizuolang
 * @Description WPS中台服务：WPS-3签名
 * @Date 2023/02/08 09:53
 */
public class WPS4Signature {

    public final static String HTTP_HEADER_AUTHORIZATION = "Wps-Docs-Authorization";
    public final static String HTTP_HEADER_DATE = "Wps-Docs-Date";
    public final static String HTTP_HEADER_CONTENT_TYPE = "Content-Type";

    private String appId; // 应用id
    private String secretKey; // 应用秘钥

    public WPS4Signature(String appId, String secretKey) {
        this.appId = appId;
        this.secretKey = secretKey;
    }

    /**
     * 获取请求body MD5
     *
     * @param content 请求body
     * @return
     */
    public String getSHA256(String content) {
        if (StringUtils.isEmpty(content)) {
            return WpsUtil.getSHA256StrJava("".getBytes());
        } else {
            return WpsUtil.getSHA256StrJava(content.getBytes());
        }
    }

    /**
     * 获取日期字符串
     *
     * @param date
     * @return
     */
    public static String getGMTDateString(Date date) {
        SimpleDateFormat format = new SimpleDateFormat("E, dd MMM yyyy HH:mm:ss", Locale.US);
        format.setTimeZone(TimeZone.getTimeZone("GMT"));
        return format.format(date) + " GMT";
    }

    /**
     * 签名
     *
     * @param uriWithQuerystring
     * @param bodyContent        签名参数 sha256
     * @param dateString
     * @return
     */
    public String getSignature(String method, String uriWithQuerystring, String bodyContent,
                               String dateString, String contentType) throws Exception {
        return WpsUtil.HMACSHA256("WPS-4" + method + uriWithQuerystring + contentType +
                dateString + bodyContent, this.secretKey);
    }

    /**
     * 获取X-Auth
     *
     * @param uriWithQuerystring 请求url，带querystring
     * @param bodyContent        请求body sha256(body)
     * @param dateString         日期字符串，例如：Mon, 15 Nov 2021 02:34:04 GMT
     * @param contentType        application/json
     * @return
     */
    public String getAuthorization(String method, String uriWithQuerystring, String bodyContent, String dateString,
                                   String contentType) throws Exception {
        String authorization = String.format(Locale.US, "WPS-4 %s:%s",
                this.appId, getSignature(method, uriWithQuerystring, bodyContent, dateString, contentType));
        return authorization;
    }

    /**
     * 获取签名请求头
     *
     * @param uriWithQuerystring 请求url，带querystring
     * @param bodyContent        请求body
     * @param date               日期，默认为 new Date()
     * @param contentType        默认为 application/json
     * @return
     */
    public Map<String, String> getSignatureHeaders(String method, String uriWithQuerystring, String bodyContent,
                                                   Date date, String contentType) {
        if (uriWithQuerystring == null) {
            uriWithQuerystring = "";
        }
        if (bodyContent == null || StringUtils.isEmpty(bodyContent)) {
            bodyContent = "";
        } else {
            bodyContent = getSHA256(bodyContent);
        }
        if (date == null) {
            date = new Date();
        }
        if (contentType == null) {
            contentType = "application/json";
        }
        String dateString = getGMTDateString(date);
        String authorization = "";
        try {
            authorization = getAuthorization(method, uriWithQuerystring, bodyContent, dateString, contentType);
        } catch (Exception e) {

        }
        Map<String, String> headers = new HashMap<>();
        headers.put(HTTP_HEADER_AUTHORIZATION, authorization);
        headers.put(HTTP_HEADER_CONTENT_TYPE, contentType);
        headers.put(HTTP_HEADER_DATE, dateString);
        return headers;
    }

}
