package com.ctsi.hndx.wps;

import com.ctsi.hndx.wps.entity.WpsConvertStock;
import com.ctsi.hndx.wps.impl.WpsDocV6Service;
import lombok.extern.slf4j.Slf4j;

import java.text.MessageFormat;
import java.util.List;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * @Author: lizuolang
 * @Description WPS中台服务：消息队列多线程模式处理文档格式转换
 * @Date 2023/02/08 09:53
 */
@Slf4j
public class WpsDocOnlineConvertEngine implements Runnable {

    // 阻塞队列（BlockingQueue）
    // ArrayBlockingQueue：由数组支持的有界队列
    private static BlockingQueue<List<WpsConvertStock>> queue = new ArrayBlockingQueue<>(1024);

    private WpsDocOnlineConvertEngine() {}

    private static WpsDocOnlineConvertEngine engine;

    static {
        engine = new WpsDocOnlineConvertEngine();
        ExecutorService service = Executors.newSingleThreadExecutor();
        //ExecutorService service = Executors.newCachedThreadPool();
        service.execute(engine);
    }

    public static WpsDocOnlineConvertEngine getInstance() {
        return engine;
    }

    public void addBlockingQueueData(List<WpsConvertStock> dataList) {
        try {
            queue.put(dataList);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
    }

    /**
     * When an object implementing interface <code>Runnable</code> is used
     * to create a thread, starting the thread causes the object's
     * <code>run</code> method to be called in that separately executing
     * thread.
     * <p>
     * The general contract of the method <code>run</code> is that it may
     * take any action whatsoever.
     *
     * @see Thread#run()
     */
    @Override
    public void run() {
        while (true) {
            try {
                List<WpsConvertStock> dataLst = queue.take();
                execute(dataLst);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 执行转换
     * @param dataLst
     */
    private void execute(List<WpsConvertStock> dataLst) {
        for (WpsConvertStock data : dataLst) {
            WpsDocV6Service wpsDocV6Service = data.getWpsDocV6Service();
            String taskId = data.getTaskId();
            String ossFilePath = data.getOssFilePath();
            String targetFilePath = data.getTargetFilePath();
            String wpsFileFormat = data.getWpsFileFormat();
            String watermarkText = data.getWatermarkText();
            long convertStartTime = System.currentTimeMillis();
            // 调用wps文档中台异步转换
            wpsDocV6Service.asyncConvertOnline(taskId, ossFilePath, targetFilePath, wpsFileFormat, watermarkText);
            long convertEndTime = System.currentTimeMillis();
            log.info(MessageFormat.format("原文件：{0}, 目标文件：{1}，转换格式：{2}，WPS文档中台转换文档耗时：{3}",
                    ossFilePath, targetFilePath, wpsFileFormat, (((convertEndTime - convertStartTime) / 1000L) + "s")));
        }
    }

}
