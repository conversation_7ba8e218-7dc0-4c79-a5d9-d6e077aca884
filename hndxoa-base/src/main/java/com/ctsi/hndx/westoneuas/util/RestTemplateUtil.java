package com.ctsi.hndx.westoneuas.util;

import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.web.client.RestTemplate;

import javax.net.ssl.*;
import java.security.KeyManagementException;
import java.security.NoSuchAlgorithmException;
import java.security.cert.X509Certificate;

/**
 * @Author: lizuolang
 * @Description HTTP 请求工具 restTemplate默认的连接方式是java中的HttpConnection，
 * 可以使用ClientHttpRequestFactory指定不同的HTTP连接方式。
 * @Date 2024/12/25 09:53
 */
public class RestTemplateUtil {

    /**
     * 创建RestTemplate实例，卫士通统一身份认证同步组织机构和用户信息
     * @param isDisableSSL 是否检测HTTPS接口的SSL证书
     * @return restTemplate
     */
    public static RestTemplate createWestoneRestTemplate(boolean isDisableSSL) {
        return createRestTemplate(isDisableSSL, 10000, 10000);
    }

    /**
     * 创建RestTemplate实例
     * @param isDisableSSL 是否检测HTTPS接口的SSL证书
     * @param connectTimeout 连接超时时间，单位为毫秒
     * @param readTimeout 读取超时时间，单位为毫秒
     * @return restTemplate
     */
    public static RestTemplate createRestTemplate(boolean isDisableSSL, Integer connectTimeout, Integer readTimeout) {
        SimpleClientHttpRequestFactory factory = new SimpleClientHttpRequestFactory();
        factory.setConnectTimeout(connectTimeout);
        factory.setReadTimeout(readTimeout);
        RestTemplate restTemplate = new RestTemplate(factory);
        if (isDisableSSL) {
            disableSSLVerification();
        }
        return restTemplate;
    }

    /**
     * 不检测HTTPS证书
     */
    private static void disableSSLVerification() {
        TrustManager[] trustAllCerts = new TrustManager[]{new X509TrustManager() {
            @Override
            public X509Certificate[] getAcceptedIssuers() {
                return null;
            }

            @Override
            public void checkClientTrusted(X509Certificate[] certs, String authType) {

            }

            @Override
            public void checkServerTrusted(X509Certificate[] certs, String authType) {

            }
        }};
        try {
            SSLContext sc = SSLContext.getInstance("SSL");
            sc.init(null, trustAllCerts, new java.security.SecureRandom());
            HttpsURLConnection.setDefaultSSLSocketFactory(sc.getSocketFactory());
            HostnameVerifier allHostsValid = (hostname, session) -> true;
            // Install the all-trusting host verifier
            HttpsURLConnection.setDefaultHostnameVerifier(allHostsValid);
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
        } catch (KeyManagementException e) {
            e.printStackTrace();
        }
    }

}
