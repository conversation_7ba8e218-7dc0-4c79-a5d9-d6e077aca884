package com.ctsi.hndx.esigninfo.controller;
import com.ctsi.ssdc.model.PageResult;
import java.util.List;
import java.util.Optional;
import com.ctsi.hndx.esigninfo.entity.CscpEsignInfo;
import com.ctsi.hndx.esigninfo.entity.dto.CscpEsignInfoDTO;
import com.ctsi.hndx.esigninfo.service.ICscpEsignInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.ctsi.ssdc.model.ResResult;
import org.springframework.security.access.prepost.PreAuthorize;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import com.ctsi.hndx.common.BasePageForm;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.InitBinder;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.util.Assert;
import com.ctsi.hndx.common.BaseController;
import com.ctsi.hndx.annotations.ResponseResultVo;
import com.ctsi.hndx.result.ResultCode;
import com.ctsi.hndx.result.ResultVO;
import com.ctsi.ssdc.annotation.OperationLog;
import com.ctsi.hndx.enums.DBOperation;


/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-18
 *
 */

@Slf4j
@RestController
@ResponseResultVo
@RequestMapping("/api/cscpEsignInfo")
@Api(value = "电子签名信息表", tags = "电子签名信息表接口")
public class CscpEsignInfoController extends BaseController {

    private static final String ENTITY_NAME = "cscpEsignInfo";

    @Autowired
    private ICscpEsignInfoService cscpEsignInfoService;



    /**
     *  新增电子签名信息表批量数据.
     */
    @PostMapping("/createBatch")
    @ApiOperation(value = "新增批量(权限code码为：cscp.cscpEsignInfo.add)", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.ADD,message = "新增电子签名信息表批量数据")
//    @PreAuthorize("@permissionService.hasPermi('cscp.cscpEsignInfo.add')")
    public ResultVO createBatch(@RequestBody List<CscpEsignInfoDTO> cscpEsignInfoList) {
       Boolean  result = cscpEsignInfoService.insertBatch(cscpEsignInfoList);
       if(result){
           return ResultVO.success();
       }else {
           return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
       }
    }

     /**
     *  新增数据.
     */
    @PostMapping("/create")
    @ApiOperation(value = "新增(权限code码为：cscp.cscpEsignInfo.add)", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.ADD,message = "新增电子签名信息表数据")
//    @PreAuthorize("@permissionService.hasPermi('cscp.cscpEsignInfo.add')")
    public ResultVO<CscpEsignInfoDTO> create(@RequestBody CscpEsignInfoDTO cscpEsignInfoDTO)  {
        CscpEsignInfoDTO result = cscpEsignInfoService.create(cscpEsignInfoDTO);
        return ResultVO.success(result);
    }

    /**
     *  更新存在数据.
     */
    @PostMapping("/update")
    @ApiOperation(value = "更新存在数据(权限code码为：cscp.cscpEsignInfo.update)", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.UPDATE,message = "更新电子签名信息表数据")
//    @PreAuthorize("@permissionService.hasPermi('cscp.cscpEsignInfo.update')")
    public ResultVO update(@RequestBody CscpEsignInfoDTO cscpEsignInfoDTO) {
	    Assert.notNull(cscpEsignInfoDTO.getId(), "general.IdNotNull");
        int count = cscpEsignInfoService.update(cscpEsignInfoDTO);
        if(count > 0 ){
            return ResultVO.success();
        }else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }

     /**
     *  删除存在数据.
     */
    @DeleteMapping("/delete/{id}")
    @OperationLog(dBOperation = DBOperation.DELETE,message = "删除电子签名信息表数据")
    @ApiOperation(value = "删除存在数据(权限code码为：cscp.cscpEsignInfo.delete)", notes = "传入参数")
//    @PreAuthorize("@permissionService.hasPermi('cscp.cscpEsignInfo.delete')")
    public ResultVO delete(@PathVariable Long id) {
        int count = cscpEsignInfoService.delete(id);
        if(count > 0 ){
            return ResultVO.success();
        }else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }

    /**
     * 查询单条数据.
     */
    @GetMapping("/get/{id}")
    @ApiOperation(value = "查询单条数据", notes = "传入参数")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
    public ResultVO get(@PathVariable Long id) {
        CscpEsignInfoDTO cscpEsignInfoDTO = cscpEsignInfoService.findOne(id);
        return ResultVO.success(cscpEsignInfoDTO);
    }

    /**
     * 通过业务ID查询单条数据.
     */
    @GetMapping("/getByFormDataId/{formDataId}")
    @ApiOperation(value = "查询单条数据", notes = "传入参数")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
    public ResultVO getByFormDataId(@PathVariable Long formDataId) {
        CscpEsignInfoDTO cscpEsignInfoDTO = cscpEsignInfoService.findByFormDataId(formDataId);
        return ResultVO.success(cscpEsignInfoDTO);
    }

    /**
    *  分页查询多条数据.
    */
    @GetMapping("/queryCscpEsignInfoPage")
    @ApiOperation(value = "翻页查询多条数据", notes = "传入参数")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
    public ResultVO<PageResult<CscpEsignInfoDTO>> queryCscpEsignInfoPage(CscpEsignInfoDTO cscpEsignInfoDTO, BasePageForm basePageForm) {
        return ResultVO.success(cscpEsignInfoService.queryListPage(cscpEsignInfoDTO, basePageForm));
    }

   /**
    * 查询多条数据.不分页
    */
   @GetMapping("/queryCscpEsignInfo")
   @ApiOperation(value = "查询多条数据", notes = "传入参数")
   //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
   public ResultVO<ResResult<CscpEsignInfoDTO>> queryCscpEsignInfo(CscpEsignInfoDTO cscpEsignInfoDTO) {
       List<CscpEsignInfoDTO> list = cscpEsignInfoService.queryList(cscpEsignInfoDTO);
       return ResultVO.success(new ResResult<CscpEsignInfoDTO>(list));
   }

    /**
     *  通过signId获取电子签名编号
     */
    @GetMapping("/getSignNumberBySignId/{signId}")
    @ApiOperation(value = "通过signId获取电子签名编号", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.ADD,message = "通过signId获取电子签名编号")
    public ResultVO<CscpEsignInfoDTO> getSignNumberBySignId(@PathVariable Long signId)  {
        CscpEsignInfoDTO result = cscpEsignInfoService.getSignNumberBySignId(signId);
        return ResultVO.success(result);
    }

    /**
     *  通过signId,formDataId,signNumber,判断电子签名编号是否存在
     */
    @PostMapping("/existBySignNumber")
    @ApiOperation(value = "通过signId,formDataId,signNumber,判断电子签名编号是否存在", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.ADD,message = "通过signId,formDataId,signNumber,判断电子签名编号是否存在")
    public ResultVO<CscpEsignInfoDTO> existBySignNumber(@RequestBody CscpEsignInfoDTO cscpEsignInfoDTO)  {
        CscpEsignInfoDTO result = cscpEsignInfoService.existBySignNumber(cscpEsignInfoDTO);
        return ResultVO.success(result);
    }

}
