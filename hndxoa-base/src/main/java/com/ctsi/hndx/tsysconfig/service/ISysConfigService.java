package com.ctsi.hndx.tsysconfig.service;

import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.common.SysBaseServiceI;
import com.ctsi.hndx.tsysconfig.entity.SysConfig;
import com.ctsi.hndx.tsysconfig.entity.dto.SysConfigDTO;
import com.ctsi.ssdc.model.PageResult;

import java.util.List;

/**
 * <p>
 * 系统动态参数配置表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-23
 */
public interface ISysConfigService extends SysBaseServiceI<SysConfig> {


    /**
     * 分页查询
     * @param entityDTO
     * @param page
     * @return
     */

    PageResult<SysConfigDTO> queryListPage(SysConfigDTO entityDTO, BasePageForm page);

    /**
     * 获取所有不分页
     * @param entityDTO
     * @return
     */
    List<SysConfigDTO> queryList(SysConfigDTO entityDTO);

    /**
     * 根据主键id获取单个对象
     * @param id
     * @return
     */
    SysConfigDTO findOne(Long id);

    /**
     * 新增
     * @param entityDTO
     * @return
     */
    SysConfigDTO create(SysConfigDTO entityDTO);


    /**
     * 更新
     * @param entityDTO
     * @return
     */
    int update(SysConfigDTO entityDTO);

    /**
     * 删除
     * @param id
     * @return
     */
    int delete(Long id);

    /**
     * 是否存在
     * @param configId
     * @return
     */
    boolean existBySysConfigId(Long configId);

    /**
     * 批量新增
     * @param dataList
     * @return
     */
    Boolean insertBatch(List<SysConfigDTO> dataList);

    /**
     * 通过code查询配置表的记录
     * @param code
     * @return
     */
    SysConfigDTO getSysConfigByCode(String code);

    /**
     * 通过code查询配置表的记录的值
     * @param code
     * @return
     */
    String getSysConfigValueByCode(String code);

    /**
     * 通过code查询配置表记录的布尔值
     * @param code 配置项code
     * @return
     */
    boolean getSysConfigBoolValueByCode(String code);

    /**
     * 通过code查询配置表记录的布尔值
     * @param code 配置项code
     * @param defaultValue 默认值
     * @return
     */
    boolean getSysConfigBoolValueByCode(String code, boolean defaultValue);

    /**
     * 判断内外网标志：in-内网，out-外网
     * @return boolean
     */
    boolean isWwBySysConfig();

    /**
     * 判断code是否已存在
     * @param code
     * @return
     */
    boolean existByConfigCode(String code);

    String getSecretaryConfig();

    String getSwDomainAddressConfig();


}
