/*
package com.ctsi.hndx.filepreview.impl;

import cn.hutool.core.io.file.FileNameUtil;
import com.alibaba.fastjson.JSONObject;
import com.amazonaws.http.apache.request.impl.HttpGetWithBody;
import com.ctsi.hndx.constant.RedisKeyConstant;
import com.ctsi.hndx.constant.SysConfigConstant;
import com.ctsi.hndx.enums.ConvertFormatEnum;
import com.ctsi.hndx.enums.FileBasePathName;
import com.ctsi.hndx.exception.BusinessException;
import com.ctsi.hndx.filepreview.CscpFileNewWpsService;
import com.ctsi.hndx.filepreview.CscpFilePreviewService;
import com.ctsi.hndx.filepreview.FileNewWpsPreviewConstant;
import com.ctsi.hndx.filepreview.FilePreviewConstant;
import com.ctsi.hndx.filepreview.impl.entity.WpsConvertEntity;
import com.ctsi.hndx.filepreview.impl.entity.WpsMergeEntity;
import com.ctsi.hndx.filestore.FileStoreTemplateService;
import com.ctsi.hndx.tsysconfig.service.ISysConfigService;
import com.ctsi.hndx.utils.SnowflakeIdUtil;
import com.ctsi.hndx.utils.StringUtils;
import com.ctsi.ssdc.util.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.http.HttpResponse;
import org.apache.http.client.ClientProtocolException;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestTemplate;

import java.io.*;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;

*/
/**
 * <AUTHOR>
 * @date 2022/6/15
 * @apiNote 新版本 WPS 预览和转换接口实现类
 *//*

@Slf4j
public class CscpNewWpsPreviewServiceImpl implements CscpFileNewWpsService {

    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private RedisUtil redisUtil;

    @Autowired
    private FileStoreTemplateService fileStoreTemplateService;

    @Autowired
    private ISysConfigService sysConfigService;



    */
/**
     * 获取单个文件预览链接
     *
     * @param filePath 文件本地保存路径
     * @return
     *//*

    @Override
    public String getPreviewUrl(String filePath) {
        String fileId = SnowflakeIdUtil.getSnowFlakeId();
        try {
            // 获取token的uri
            String uriAppToken = FileNewWpsPreviewConstant.APP_TOKEN.replace("{appId}", FileNewWpsPreviewConstant.APP_ID).
                    replace("{scope}", FileNewWpsPreviewConstant.FILE_PREVIEW);
            // 获取预览的appToken
            String appToken = this.getAppToken(uriAppToken);
            // 获取预览的uri
            String uriPreview = FileNewWpsPreviewConstant.FILE_READER.replace("{appToken}", appToken).replace("{fileId}", fileId);
            // 调用本地工具方法，获取预览的header和签名
            HttpEntity<String> requestEntity = this.getHeaderAndXAuth(uriPreview, "");
            ResponseEntity<String> responseEntity = restTemplate.exchange(sysConfigService.getSysConfigValueByCode(SysConfigConstant.WPS_WENDANG_XHONGTAI) + uriPreview,
                    HttpMethod.GET, requestEntity, String.class);

            JSONObject jsonObject = JSONObject.parseObject(responseEntity.getBody());
            redisUtil.set(new StringBuffer(RedisKeyConstant.FILE_PREVIEW_ID).append(fileId).toString(), filePath, 23, TimeUnit.HOURS);
            String url = jsonObject.getString("url");
            if (StringUtils.isNotEmpty(url)) {
                return url.replace(":8090", ":10001");
            }
            return url;
        } catch (Exception e) {
            log.error("预览文件{}出错,异常原因{};", filePath, e);
        }
        return null;
    }

    */
/**
     * 预览所有附件
     *
     * @param entity 待预览附件地址的列表
     * @return
     *//*

    @Override
    public String getAttachmentsPreviewUrl(WpsMergeEntity entity) throws UnsupportedEncodingException {
        // 设置默认预览格式为 PDF
        ConvertFormatEnum convertFormatEnum = ConvertFormatEnum.PDF;
        // 创建待合并文档列表的内部类
        ArrayList<WpsMergeEntity.FileList> mergeFileList = new ArrayList<>();
        // 将所有文档转换成PDF格式
        List<WpsMergeEntity.FileList> file_list = entity.getMerged_file_list();
        for (WpsMergeEntity.FileList file : file_list) {
            if (!convertFormatEnum.equals(cn.hutool.core.io.file.FileNameUtil.getSuffix(file.getDoc_filename()).toUpperCase())) {
                String resultConvertFilePath = this.fileConversion(file.getDoc_url(), file.getDoc_filename(), convertFormatEnum);
                // 创建待合并文件地址和文件名
                WpsMergeEntity.FileList mergeFile = entity.new FileList();
                mergeFile.setDoc_filename(file.getDoc_filename());
                mergeFile.setDoc_url(sysConfigService.getSysConfigValueByCode(SysConfigConstant.WEB_API_URL) + FileNewWpsPreviewConstant.FILE_PATH + resultConvertFilePath);
                mergeFileList.add(mergeFile);
            }
        }
        // 创建文档合并请求体
        WpsMergeEntity mergeEntity = new WpsMergeEntity();
        mergeEntity.setTask_id(SnowflakeIdUtil.getSnowFlakeId());
        mergeEntity.setScene_id(FileNewWpsPreviewConstant.SCENE_ID);
        mergeEntity.setMerged_file_list(mergeFileList);
        // 调用合并接口，得到合并后的文件下载路径
        Map fileMap = this.wpsMergeMap(mergeEntity, convertFormatEnum.toString().toLowerCase());
        String filePath = (String) fileMap.get("filePath");
        // 调用预览方法，获取预览地址
        return this.getPreviewUrl(filePath);
    }


    */
/**
     * 文件格式转换
     *
     * @param docUrl            文件下载地址
     * @param fileName          文件名
     * @param convertFormatEnum 文件格式
     * @return Map              filePath：文件保存路径，fileSize：文件大小
     *//*

    @Override
    public Map fileConversionMap(String docUrl, String fileName, ConvertFormatEnum convertFormatEnum) {
        String format = null;
        if (convertFormatEnum == null) {
            format = ConvertFormatEnum.PDF.toString().toLowerCase();
        } else {
            format = convertFormatEnum.toString().toLowerCase();
        }
        // 得到文档格式处理token的 uri
        String uriAppToken = FileNewWpsPreviewConstant.APP_TOKEN.replace("{appId}", FileNewWpsPreviewConstant.APP_ID).replace("{scope}", FileNewWpsPreviewConstant.FILE_FORMAT_CONTROL);
        // 获文档格式处理的 appToken
        String appToken = this.getAppToken(uriAppToken);

        // 生成任务id
        String taskId = SnowflakeIdUtil.getSnowFlakeId();
        // 创建文档转换请求体
        WpsConvertEntity wpsConvertEntity = new WpsConvertEntity();
        wpsConvertEntity.setApp_token(appToken);
        wpsConvertEntity.setTask_id(taskId);
        wpsConvertEntity.setDoc_filename(fileName);
        String fileUrl = new StringBuffer().append(sysConfigService.getSysConfigValueByCode(SysConfigConstant.WEB_API_URL))
                .append(FilePreviewConstant.DOWN_LOAD_FILE_BY_FILE)
                .append("?filePath=")
                .append(docUrl).toString();
        // TODO 需修改为fileUrl
        wpsConvertEntity.setDoc_url(fileUrl);
        wpsConvertEntity.setScene_id(FileNewWpsPreviewConstant.SCENE_ID);
        wpsConvertEntity.setTarget_file_format(format);

        // 得到文档下载的 uri
        String uriConver = FileNewWpsPreviewConstant.FILE_CONVERT;
        HttpEntity<String> requestEntity = this.getHeaderAndXAuth(uriConver, JSONObject.toJSONString(wpsConvertEntity));
        // 发送文件转换请求
        String response = restTemplate.postForObject(sysConfigService.getSysConfigValueByCode(SysConfigConstant.WPS_WENDANG_XHONGTAI) + uriConver, requestEntity, String.class);
        String result = JSONObject.parseObject(response).getString("msg");
        // 判断文件转换请求是否成功，转换成功则继续查询下载文件id
        if ("Success".equals(result)) {
            // 查询和下载文件（格式转换、文档合并）
            return queryTaskAndDownload(appToken, taskId, format);
        } else {
            throw new BusinessException("文件转换失败");
        }
    }

    */
/**
     * 文件格式转换
     *
     * @param docUrl            文件下载地址
     * @param fileName          文件名
     * @param convertFormatEnum 文件格式
     * @return String 文件保存路径
     *//*

    @Override
    public String fileConversion(String docUrl, String fileName, ConvertFormatEnum convertFormatEnum){
        Map fileMap = fileConversionMap(docUrl, fileName, convertFormatEnum);
        return (String) fileMap.get("filePath");
    }


    */
/**
     * 文件格式转换
     *
     * @param filePath          文件本地保存路径
     * @param convertFormatEnum 文件格式
     * @return boolean 格式转换是否成功
     *//*

    @Override
    public boolean fileConversion(String filePath, ConvertFormatEnum convertFormatEnum) {
        // 根据路径获取文件名
        String fileName = FileNameUtil.getName(filePath);
        String resultFilePath = fileConversion(filePath, fileName, convertFormatEnum);
        return !StringUtils.isEmpty(resultFilePath);
    }

    */
/**
     * 获取在线编辑链接地址
     *
     * @param formDataId
     * @param fileType
     * @return
     *//*

    public String getOnlineEditLink(String formDataId, String fileType) {
        try {
            String uriAppToken = FileNewWpsPreviewConstant.APP_TOKEN.replace("{appId}", FileNewWpsPreviewConstant.APP_ID).replace("{scope}", FileNewWpsPreviewConstant.FILE_FORMAT_CONTROL);
            // 获 appToken
            String appToken = this.getAppToken(uriAppToken);
            //获取请求头
            HttpEntity<String> requestEntity = this.getHeaderAndXAuth(FileNewWpsPreviewConstant.ONLINE_EDIT_LINK, "");
            // http://*************:10001/open/weboffice/v2/url?file_id=1902081&app_token=8294863ae2d3fc61633bd4a0a453814e&scene_id=zyl_123456&type=s
            String uriPreview = FileNewWpsPreviewConstant.ONLINE_EDIT_LINK.replace("{appToken}", appToken).replace("{fileId}", formDataId).replace("{type}", fileType).replace("{sceneId}", formDataId);

            ResponseEntity<String> responseEntity = restTemplate.exchange(new StringBuilder(sysConfigService.getSysConfigValueByCode(SysConfigConstant.WPS_WENDANG_XHONGTAI)).append(uriPreview).toString(),
                    HttpMethod.GET, requestEntity, String.class);

            JSONObject jsonObject = JSONObject.parseObject(responseEntity.getBody());
            String url = jsonObject.getString("url");
            if (StringUtils.isNotEmpty(url)) {
                return url.replace(":8090", ":10001");
            }
            return url;
        } catch (Exception e) {
            log.error("获取在线编辑链接出错{0},异常原因{1};", formDataId, e);
        }
        return null;
    }

    */
/**
     * 保存在线编辑数据
     *
     * @param formDataId
     * @param fileType   w:文字文件  s:表格文件  p:演示文件  f:PDF文件
     * @return
     *//*

    public int saveOnlineEditData(String formDataId, String fileType) {
        try {
            String uriAppToken = FileNewWpsPreviewConstant.APP_TOKEN.replace("{appId}", FileNewWpsPreviewConstant.APP_ID).replace("{scope}", FileNewWpsPreviewConstant.FILE_FORMAT_CONTROL);
            // 获 appToken
            String appToken = this.getAppToken(uriAppToken);
            //获取url
            String url = FileNewWpsPreviewConstant.SAVE_ONLINE_EDIT_DATA.replace("{id}", formDataId);
            //获取请求头
            HttpEntity<String> requestEntity = this.getHeaderAndXAuth(url, "");
            //获取查询参数
            Map<String, String> map = new HashMap<>();
            map.put("file_id", formDataId);
            map.put("type", fileType);
            map.put("scene_id", formDataId);
            map.put("app_token", appToken);
            ResponseEntity<String> responseEntity = restTemplate.exchange(new StringBuilder(sysConfigService.getSysConfigValueByCode(SysConfigConstant.WPS_WENDANG_XHONGTAI)).append(map).toString(),
                    HttpMethod.POST, requestEntity, String.class);
            JSONObject jsonObject = JSONObject.parseObject(responseEntity.getBody());
            return Integer.valueOf(jsonObject.getString("result"));

        } catch (Exception e) {
            log.error("保存在线编辑数据出错{0},异常原因{1};", formDataId, e);
        }
        return -1;
    }

    */
/**
     * 文档合并
     * 支持格式，一次最大合并文档数为10：
     * 文字	DOC、DOCX、WPS
     * 表格	XLS、XLSX
     * 演示	PPT、PPTX
     * PDF	PDF
     *
     * @param entity    待合并文档
     * @return Map      filePath：文件保存路径，fileSize：文件大小
     *//*

    @Override
    public Map wpsMergeMap(WpsMergeEntity entity, String convertFormatEnum) {
        // 待合并文档数量不能为空，且文档数必须在2-10之间（WPS中台限定）。
        if (entity.getMerged_file_list() == null || (entity.getMerged_file_list().size() < 2)) {
            throw new BusinessException("待合并的文档数必须大于2");
        }

        // 得到文档合并token的 uri
        String uriAppToken = FileNewWpsPreviewConstant.APP_TOKEN.replace("{appId}", FileNewWpsPreviewConstant.APP_ID).replace("{scope}", FileNewWpsPreviewConstant.FILE_FORMAT_CONTROL);
        // 获文档合并的 appToken
        String appToken = this.getAppToken(uriAppToken);
        entity.setApp_token(appToken);
        // 生成任务id
        String taskId = entity.getTask_id();
        // 得到文档合并的 uri
        String uriMerge = FileNewWpsPreviewConstant.FILE_MERGE;
        String response = null;
        // 将对象转成json格式对象
        JSONObject jsonObject = (JSONObject) JSONObject.toJSON(entity);
        // 将json格式转成字符串类型，否则签名数据会出错。
        String body = jsonObject.toJSONString();
        // 判断待合并的文档数量是否超过10个
        if (entity.getMerged_file_list().size() < 10) {
            // 获取请求头及签名
            HttpEntity<String> requestEntity = this.getHeaderAndXAuth(uriMerge, jsonObject.toJSONString());
            // 发起文档合并请求
            response = restTemplate.postForObject(sysConfigService.getSysConfigValueByCode(SysConfigConstant.WPS_WENDANG_XHONGTAI) + uriMerge, requestEntity, String.class);
            JSONObject result = (JSONObject) JSONObject.parse(response);
            // 查询任务执行情况，并下载已合并的文档
            if ("Success".equals(result.getString("msg"))) {
                return queryTaskAndDownload(appToken, taskId, convertFormatEnum.toLowerCase());
            } else {
                throw new BusinessException("文件合并失败");
            }
        } else {
            // 创建二次合并集合
            ArrayList<WpsMergeEntity.FileList> mergeFileLists = new ArrayList<>();

            // 计算文档数量是否为10的整数倍
            int count = entity.getMerged_file_list().size() / 10;
            // 剩余文档数量
            int remain = entity.getMerged_file_list().size() % 10;
            if (remain > 0) {
                count = count + 1;
            }
            // 待合并的文档数量超过10个,则重复调用合并程序
            for (int i = 0; i < count; i++) {
                List<WpsMergeEntity.FileList> mergeFileList = null;
                if (remain > 0 && i == count) {
                    mergeFileList = entity.getMerged_file_list().subList(i, i + remain);
                } else {
                    mergeFileList = entity.getMerged_file_list().subList(i, i + 10);
                }
                WpsMergeEntity mergeEntity = new WpsMergeEntity();
                // 设置任务id和业务唯一标识
                mergeEntity.setTask_id(SnowflakeIdUtil.getSnowFlakeId());
                mergeEntity.setScene_id(FileNewWpsPreviewConstant.SCENE_ID);
                mergeEntity.setMerged_file_list(mergeFileList);
                // 获取请求头和签名
                HttpEntity<String> requestEntity = this.getHeaderAndXAuth(uriMerge, body);
                // 发起文档合并请求
                response = restTemplate.postForObject(sysConfigService.getSysConfigValueByCode(SysConfigConstant.WPS_WENDANG_XHONGTAI) + uriMerge, requestEntity, String.class);
                JSONObject result = JSONObject.parseObject(response);
                // 查询任务执行情况，并下载已合并的文档
                if ("Success".equals(result.getString("msg"))) {
                    Map fileMap = queryTaskAndDownload(appToken, taskId, convertFormatEnum.toLowerCase());
                    String filePath = (String) fileMap.get("filePath");
                    // 创建待合并文件地址和文件名
                    WpsMergeEntity.FileList mergeFile = entity.new FileList();
                    mergeFile.setDoc_filename(cn.hutool.core.io.file.FileNameUtil.getName(filePath));
                    mergeFile.setDoc_url(sysConfigService.getSysConfigValueByCode(SysConfigConstant.WEB_API_URL) + FileNewWpsPreviewConstant.FILE_PATH + filePath);
                    mergeFileLists.add(mergeFile);
                } else {
                    throw new BusinessException("文件合并失败");
                }
            }
            WpsMergeEntity mergeEntitys = new WpsMergeEntity();
            // 设置任务id和业务唯一标识
            mergeEntitys.setTask_id(SnowflakeIdUtil.getSnowFlakeId());
            mergeEntitys.setScene_id(FileNewWpsPreviewConstant.SCENE_ID);
            mergeEntitys.setMerged_file_list(mergeFileLists);
            HttpEntity<String> requestEntity = this.getHeaderAndXAuth(uriMerge, body);
            // 发起文档合并请求
            response = restTemplate.postForObject(sysConfigService.getSysConfigValueByCode(SysConfigConstant.WPS_WENDANG_XHONGTAI) + uriMerge, requestEntity, String.class);
            JSONObject result = JSONObject.parseObject(response);
            // 查询任务执行情况，并下载已合并的文档
            if ("Success".equals(result.getString("msg"))) {
                return queryTaskAndDownload(appToken, taskId, convertFormatEnum.toLowerCase());
            } else {
                throw new BusinessException("文件合并失败");
            }
        }
    }
    */
/**
     * 文档合并
     * 支持格式，一次最大合并文档数为10：
     * 文字	DOC、DOCX、WPS
     * 表格	XLS、XLSX
     * 演示	PPT、PPTX
     * PDF	PDF
     *
     * @param entity 待合并文档
     * @return boolean
     *//*

    @Override
    public String wpsMerge(WpsMergeEntity entity, String convertFormatEnum) {
        Map fileMap = wpsMergeMap(entity, convertFormatEnum);
        return (String) fileMap.get("filePath");
    }

    */
/**
     * 文档在线编辑
     *
     * @param filePath 文件本地保存路径
     * @return
     *//*

    @Override
    public String wpsOnlineEdit(String filePath) {
        return null;
    }

    */
/**
     * 任务查询和文件下载
     *
     * @param appToken          token
     * @param taskId            任务id
     * @param convertFormatEnum 转换的目标格式
     * @return
     *//*

    public Map queryTaskAndDownload(String appToken, String taskId, String convertFormatEnum) {
        JSONObject body = new JSONObject();
        body.put("app_token", appToken);
        body.put("task_id", taskId);
        String downloadId = null;

        // 循环等待任务执行完成，最多等待8秒
        for (int i = 0; i < 4; i++) {
            JSONObject taskResult = null;
            taskResult = this.taskQuery(FileNewWpsPreviewConstant.TASK_QUERY, body.toJSONString());
            log.info("查询结果：{}", taskResult);
            // 判断任务查询结果是否包含msg，若msg==success，则表示任务执行完成，可获取(文件下载id) download_id
            if (taskResult.containsKey("msg") && "success".equals(taskResult.getString("msg"))) {
                downloadId = (String) taskResult.get("download_id");
                // 获取文件下载id，结束循环
                break;
            } else {
                // 其他结果表示任务正在队列执行中，等待2秒钟
                try {
                    TimeUnit.SECONDS.sleep(2);
                } catch (InterruptedException ie) {
                    throw new BusinessException("任务查询失败，{}", ie);
                }
            }
        }

        // 配置 Json格式 的请求头
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("app_token", appToken);
        // 获取转换后的文件
        return this.wpsFileDownload(appToken, jsonObject, downloadId, convertFormatEnum);
    }


    */
/**
     * 格式处理 文档下载
     *
     * @param appToken   金山中台token
     * @param body       请求体
     * @param downloadId 文档下载id
     * @param extName    文档目标格式
     * @return 文件保存的相对路径
     *//*

    public Map wpsFileDownload(String appToken, JSONObject body, String downloadId, String extName) {
        // 生成文件路径
        String newFilePath = null;
        HashMap<String, String> map = new HashMap<>();
        try {
            // 获取金山中台文档下载的 uri
            String downloadUri = FileNewWpsPreviewConstant.FILE_DOWNLOAD.replace("{downloadId}", downloadId);
            // 获取请求头签名
            HashMap headerMap = this.getHeaderMap(downloadUri, body.toJSONString());
            // 拼接系统ip地址和端口号，得到金山中台文档下载url
            String downloadUrl = sysConfigService.getSysConfigValueByCode(SysConfigConstant.WPS_WENDANG_XHONGTAI) + downloadUri;
            // 调用本地方法，从金山WPS中台下载文件
            byte[] file = getFile(headerMap, appToken, downloadUrl);
            // 生成文件保存路径
            newFilePath = fileStoreTemplateService.createFileUrl(FileBasePathName.WPS_IMPORT, extName);
            // 将文件进行保存
            fileStoreTemplateService.uploadFile(newFilePath, file);
            // 保存文件大小和文件路径
            map.put("fileSize", String.valueOf(file.length));
            map.put("filePath", newFilePath);
            return map;
        } catch (Exception e) {
            log.error("{}路径无法转为{}格式,异常原因", newFilePath, e);
        }
        return map;
    }


    */
/**
     * 格式处理 任务查询
     *
     * @param uri  任务查询的uri
     * @param body 请求体
     * @return
     *//*

    public JSONObject taskQuery(String uri, String body) {
        // 获取appToken的header和签名
        HttpEntity<String> requestEntity = this.getHeaderAndXAuth(uri, body);
        String response = restTemplate.postForObject(sysConfigService.getSysConfigValueByCode(SysConfigConstant.WPS_WENDANG_XHONGTAI) + uri, requestEntity, String.class);
        JSONObject result = JSONObject.parseObject(response);

        // 获取任务查询结果，如：download_id，result
        return result;
    }

    */
/**
     * 获取 AppToken
     *
     * @param uri 获取token的uri
     * @return
     *//*

    public String getAppToken(String uri) {
        // 获取appToken的header和签名
        HttpEntity<String> requestEntity = this.getHeaderAndXAuth(uri, "");
        ResponseEntity<String> responseEntity = restTemplate.exchange(new StringBuilder(sysConfigService.getSysConfigValueByCode(SysConfigConstant.WPS_WENDANG_XHONGTAI)).
                append(uri).toString(), HttpMethod.GET, requestEntity, String.class);
        // 解析响应体中的数据
        JSONObject jsonObject = JSONObject.parseObject(responseEntity.getBody());
        return jsonObject.getJSONObject("token").getString("app_token");
    }

    */
/**
     * 获取请求头及签名， 返回 HttpEntity
     *
     * @param uri  签名的uri
     * @param body 签名的请求体
     * @return
     *//*

    public HttpEntity<String> getHeaderAndXAuth(String uri, String body) {
        // 获取请求头
        DateFormat df = new SimpleDateFormat("EEE, dd MMM yyyy hh:mm:ss 'GMT'", Locale.US);
        df.setTimeZone(TimeZone.getTimeZone("GMT"));
        // 得到当前 GMT 格式的时间
        String date = df.format(new Date());
        // 如果请求体中有数据，则需要做 MD5 加密处理；否则对空字符串 MD5 加密处理。
        String md5 = StringUtils.isEmpty(body) ? DigestUtils.md5Hex("") : DigestUtils.md5Hex(body);

        // 设置请求头信息
        HttpHeaders headers = new HttpHeaders();
        headers.add("Content-Type", "application/json");
        headers.add("Content-Md5", md5);
        headers.add("Date", date);
        String sha1 = DigestUtils.sha1Hex(FileNewWpsPreviewConstant.APP_KEY.toLowerCase() + md5.toLowerCase() + uri + "application/json" + date).toLowerCase();
        // 得到本次请求的签名信息
        String XAuth = "WPS-3:" + FileNewWpsPreviewConstant.APP_ID + ":" + sha1;
        headers.add("X-Auth", XAuth);
        String bodys = StringUtils.isEmpty(body) ? null : body;
        HttpEntity<String> requestEntity = new HttpEntity<>(bodys, headers);
        return requestEntity;
    }

    */
/**
     * 获取请求头及签名，返回 Map
     *
     * @param uri  签名的uri
     * @param body 签名的请求体
     * @return
     *//*

    public HashMap getHeaderMap(String uri, String body) {
        DateFormat df = new SimpleDateFormat("EEE, dd MMM yyyy hh:mm:ss 'GMT'", Locale.US);
        df.setTimeZone(TimeZone.getTimeZone("GMT"));
        // 得到当前 GMT 格式的时间
        String date = df.format(new Date());
        // 如果请求体中有数据，则需要做 MD5 加密处理；否则对空字符串 MD5 加密处理。
        String md5 = StringUtils.isEmpty(body) ? DigestUtils.md5Hex("") : DigestUtils.md5Hex(body);

        // 设置请求头信息
        HashMap map = new HashMap<String, String>();
        map.put("ContentType", "application/json");
        map.put("Md5", md5);
        map.put("Date", date);
        String sha1 = DigestUtils.sha1Hex(FileNewWpsPreviewConstant.APP_KEY.toLowerCase() + md5.toLowerCase() + uri + "application/json" + date).toLowerCase();
        // 得到本次请求的签名信息
        String XAuth = "WPS-3:" + FileNewWpsPreviewConstant.APP_ID + ":" + sha1;
        map.put("XAuth", XAuth);
        return map;
    }


    private static byte[] getFile(HashMap map, String appToken, String url) {
        // 生成一个httpclient对象
        CloseableHttpClient httpclient = HttpClients.createDefault();
        HttpGetWithBody kh = new HttpGetWithBody(url);
        JSONObject json = new JSONObject();
        json.put("app_token", appToken);
        // 设置请求体
        kh.setEntity(new StringEntity(json.toString(), "UTF-8"));
        kh.setHeader("Date", map.get("Date").toString());
        kh.setHeader("Content-Md5", map.get("Md5").toString());
        kh.setHeader("Content-Type", map.get("ContentType").toString());
        kh.setHeader("X-Auth", map.get("XAuth").toString());

        InputStream in = null;
        BufferedInputStream bufferedInputStream = null;
        ByteArrayOutputStream outputStream = null;
        try {
            HttpResponse response = httpclient.execute(kh);
            org.apache.http.HttpEntity entity2 = response.getEntity();
            in = entity2.getContent();
            int len = -1;
            byte[] buffer = new byte[1024];
            bufferedInputStream = new BufferedInputStream(in);
            outputStream = new ByteArrayOutputStream();
            while ((len = bufferedInputStream.read(buffer)) != -1) {
                outputStream.write(buffer, 0, len);
            }
            return outputStream.toByteArray();
        } catch (ClientProtocolException e) {
            log.error("异常原因{}", e);
        } catch (UnsupportedOperationException e) {
            log.error("异常原因{}", e);
        } catch (IOException e) {
            log.error("异常原因{}", e);
        } finally {
            try {
                httpclient.close();
                if (in != null) {
                    in.close();
                }
                if (bufferedInputStream != null) {
                    bufferedInputStream.close();
                }
                if (outputStream != null) {
                    outputStream.close();
                }

            } catch (IOException e) {
            }
        }
        return null;
    }

}
*/
