package com.ctsi.hndx.filepreview.impl.entity;

import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 任务转换完成通知
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-17
 */
@Data
@EqualsAndHashCode()
@ApiModel(value="WpsConvertNotify 对象", description="任务转换完成通知")
public class WpsConvertNotifyEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 转换任务id
     */
    private String task_id;

    /**
     * 转换结果
     */
    private Result result;

    @Data
    public class Result {
        /**
         * 转换是否成功
         */
        private Boolean success;

        /**
         * 转换信息
         */
        private String message;

        /**
         * 文件下载id
         */
        private String download_id;
    }
}
