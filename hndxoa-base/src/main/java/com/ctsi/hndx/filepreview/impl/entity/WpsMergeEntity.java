package com.ctsi.hndx.filepreview.impl.entity;

import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 文档合并请求体
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-20
 */
@Data
@EqualsAndHashCode()
@ApiModel(value="WpsMergeEntity 对象", description="文档合并请求体")
public class WpsMergeEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 任务ID
     */
    private String app_token;

    /**
     * 任务id
     */
    private String task_id;

    /**
     * 业务唯一标识，由应用自定义
     */
    private String scene_id;
    /**
     * 待合并文档列表
     */
    private List<FileList> merged_file_list;

    @Data
    public class FileList {
        /**
         * 文档地址
         */
        private String doc_url;

        /**
         * 文件名，必须带后缀
         */
        private String doc_filename;
    }
}
