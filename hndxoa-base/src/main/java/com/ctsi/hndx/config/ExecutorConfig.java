package com.ctsi.hndx.config;

import com.ctsi.ssdc.config.CtsiProperties;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;


@Configuration
@EnableAsync
@EnableConfigurationProperties(CtsiProperties.class)
public class ExecutorConfig {

    @Autowired
    private final CtsiProperties ctsiProperties;

    public ExecutorConfig(CtsiProperties ctsiProperties){
        this.ctsiProperties = ctsiProperties;
    }

    @Bean
    public Executor asyncServiceExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        //配置核心线程数
        executor.setCorePoolSize(ctsiProperties.getAsync().getCorePoolSize());
        //配置最大线程数
        executor.setMaxPoolSize(ctsiProperties.getAsync().getMaxPoolSize());
        //配置队列大小
        executor.setQueueCapacity(ctsiProperties.getAsync().getQueueCapacity());
        //配置线程池中的线程的名称前缀
        executor.setThreadNamePrefix("async-service-");
        // rejection-policy：当pool已经达到max size的时候，如何处理新任务
        // CALLER_RUNS：不在新线程中执行任务，而是有调用者所在的线程来执行
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        //执行初始化
        executor.initialize();
        return executor;
    }
}
