package com.ctsi.hndx.config;

import com.ctsi.hndx.encryption.Base64Encrypt;
import com.ctsi.hndx.encryption.IEncryption;
import com.ctsi.hndx.encryption.Sm4Encrypt;
import com.ctsi.hndx.utils.StringUtils;
import com.ctsi.ssdc.util.ValueUtil;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * @ProjectName: biyi-oa-server
 * @Package: com.ctsi.hndx.config
 * @ClassName: EncryptionConfig
 * @Author: houting
 * @Description: 加密方式的处理
 * @Date: 2022/6/20 17:17
 * @Version: 1.0
 */


@Configuration
public class EncryptionConfig {

    /**
     * 不启用 sharingsphere配置
     */
    @Value("${spring.datasource.url:}")
    private String springDriverClassName;

    /**
     * 启用读写分离配置
     */
    @Value("${spring.shardingsphere.datasource.master.jdbc-url:}")
    private String shardingsphereDriverClassName;

    @Value(value = "${encryption.type:}")
    private String encryptionType;

    @Bean
    public IEncryption iEncryption() {
        try {
            //测试环境
            String environment = ValueUtil.notEmpty(springDriverClassName, shardingsphereDriverClassName);
            if (environment.contains("myappkaifa")) {
                return new Base64Encrypt();
            } else if (environment.contains("myapp")) {
                return new Sm4Encrypt();
            } else {
                // 默认使用 base64
                if (StringUtils.isNotEmpty(encryptionType)) {
                    try {
                        // 通过类名获得Class
                        Class c = Class.forName(encryptionType);
                        // 实例化类
                        Object obj = c.newInstance();
                        return (IEncryption) obj;
                    } catch (Exception e) {
                        return new Base64Encrypt();
                    }
                }
                return new Base64Encrypt();
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return new Base64Encrypt();
    }

}
