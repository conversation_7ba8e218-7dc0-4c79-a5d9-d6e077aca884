//package com.ctsi.hndx.config;
//
//import com.baomidou.dynamic.datasource.DynamicRoutingDataSource;
//import com.baomidou.dynamic.datasource.provider.AbstractDataSourceProvider;
//import com.baomidou.dynamic.datasource.provider.DynamicDataSourceProvider;
//import com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DataSourceProperty;
//import com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//import org.springframework.context.annotation.Lazy;
//import org.springframework.context.annotation.Primary;
//import org.springframework.transaction.annotation.EnableTransactionManagement;
//
//import javax.sql.DataSource;
//import java.util.Map;
//
//@Configuration
//@EnableTransactionManagement
//public class DataSourceConfig {
//
//    /**
//     * 动态数据源配置项
//     */
//    @Autowired
//    private DynamicDataSourceProperties properties;
//
//    /**
//     * 使用shardingSphereDataSource 自动装载的 DataSource
//     * 5.1.1版本自动装载的shardingSphereDataSource beanName="shardingSphereDataSource"
//     * 要加@Lazy
//     */
////    @Lazy
////    @Autowired
////    private DataSource shardingSphereDataSource;
//
//
//    @Bean
//    public DynamicDataSourceProvider dynamicDataSourceProvider() {
//        Map<String, DataSourceProperty> datasourceMap = properties.getDatasource();
//        return new AbstractDataSourceProvider() {
//            @Override
//            public Map<String, DataSource> loadDataSources() {
//                Map<String, DataSource> map = createDataSourceMap(datasourceMap);
//                // 这里将shardingjdbc管理的数据源交给dynamic-datasource动态数据源去管理
////                map.put(DbConst.SHARDING, shardingSphereDataSource);
//                return map;
//            }
//        };
//    }
//
//    /**
//     * 将dynamic-datasource动态数据源设置为首选的
//     * 当spring存在多个数据源的时候，自动注入的是首选数据源
//     * 这样之后可以支持sharding-jdbc原生的配置方式
//     * @return
//     */
//    @Primary
//    @Bean
//    public DataSource dataSource() {
//        DynamicRoutingDataSource dataSource = new DynamicRoutingDataSource();
//        dataSource.setPrimary(DbConst.MASTER);
////        dataSource.setStrict(properties.getStrict());
////        dataSource.setStrategy(properties.getStrategy());
////        dataSource.setP6spy(properties.getP6spy());
////        dataSource.setSeata(properties.getSeata());
//        return dataSource;
//    }
//
//}