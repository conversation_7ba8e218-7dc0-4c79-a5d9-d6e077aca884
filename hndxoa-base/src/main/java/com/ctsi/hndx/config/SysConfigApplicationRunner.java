package com.ctsi.hndx.config;

import com.ctsi.hndx.constant.RedisKeyConstant;
import com.ctsi.hndx.encryption.IEncryption;
import com.ctsi.hndx.tsysconfig.entity.dto.SysConfigDTO;
import com.ctsi.hndx.tsysconfig.service.ISysConfigService;
import com.ctsi.ssdc.util.RedisUtil;
import com.ctsi.ssdc.util.SpringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * @Classname SysConfigApplicationRunner
 * @Description
 * @Date 2021/11/23/0023 10:12
 * <AUTHOR>
 */

@Order(999)
@Component
public class SysConfigApplicationRunner implements ApplicationRunner {

    @Autowired
    private ISysConfigService sysConfigService;

    @Autowired
    private RedisUtil redisUtil;

    @Override
    public void run(ApplicationArguments args) throws Exception {
        IEncryption iEncryption = SpringUtil.getBean(IEncryption.class);
        List<SysConfigDTO> sysConfigDTOList = sysConfigService.queryList(null);
        sysConfigDTOList.stream().map(i -> {
            redisUtil.set(RedisKeyConstant.SYS_CONFIG_INFO + i.getCode(), i);
            return i.getCode();
        }).collect(Collectors.toList());

    }
}
