package com.ctsi.hndx.utils;

import java.io.*;

/**
 * @Author: lizuolang
 * @Description 文件与byte流相互转换工具
 * @Date 2023/02/08 09:53
 */
public class ByteFileUtil {

    /**
     * 根据byte数组，生成文件
     * @param bfile
     * @param absFileName
     */
    public static void byteArrayToFile(byte[] bfile, String absFileName) {
        BufferedOutputStream bos = null;
        FileOutputStream fos = null;
        File file = null;
        try {
            file = new File(absFileName);
            fos = new FileOutputStream(file);
            bos = new BufferedOutputStream(fos);
            bos.write(bfile);
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (bos != null) {
                try {
                    bos.close();
                } catch (IOException e1) {
                    e1.printStackTrace();
                }
            }
            if (fos != null) {
                try {
                    fos.close();
                } catch (IOException e1) {
                    e1.printStackTrace();
                }
            }
        }
    }

    /**
     * 输入流转字节数组
     *
     * @param in 输入流
     * @return 字节数组
     */
    public static byte[] inputStreamToByteArray(InputStream in) {
        try {
            ByteArrayOutputStream out = new ByteArrayOutputStream(1024);
            byte[] buffer = new byte[1024 * 16];
            int n;
            while ((n = in.read(buffer)) > 0) {
                out.write(buffer, 0, n);
            }
            return out.toByteArray();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 根据文件，转byte数组
     * @param absFileName
     * @param absFileName
     */
    public static byte[] fileToByteArray(String absFileName) {
        FileInputStream fis = null;
        try {
            fis = new FileInputStream(absFileName);
            return inputStreamToByteArray(fis);
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (fis != null) {
                try {
                    fis.close();
                } catch (IOException e1) {
                    e1.printStackTrace();
                }
            }
        }
        return null;
    }

}
