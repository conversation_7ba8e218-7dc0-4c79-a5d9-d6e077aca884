package com.ctsi.hndx.utils;

import com.baomidou.mybatisplus.core.toolkit.IdWorker;

/**
 * 利用雪花算法产生全局id
 */
public class SnowflakeIdUtil {

    public static String getSnowFlakeId(){
        return IdWorker.getIdStr();
    }

    public static Long getSnowFlakeLongId(){
        return IdWorker.getId();
    }


//    public static void main(String[] args) {
//        System.out.println(getSnowFlakeId());
//    }
}
