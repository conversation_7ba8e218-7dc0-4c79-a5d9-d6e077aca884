package com.ctsi.hndx.utils;

import com.ctsi.hndx.exception.BusinessException;

public class StringSplitUtils {
    //截取名字
    public static RealNameSplitType realNameSplit(String realName) {
        if (StringUtils.isNotEmpty(realName)) {
            if (realName.length() == 2 || realName.length() == 3) {
                return RealNameSplitType.builder()
                        .realNameStart(realName.substring(0, 1))
                        .realNameEnd(realName.substring(1, realName.length())).build();
            } else if (realName.length() >= 4) {
                return RealNameSplitType.builder()
                        .realNameStart(realName.substring(0, 2))
                        .realNameEnd(realName.substring(2, realName.length())).build();
            } else {
                throw new BusinessException("不是合法的名称");
            }
        }
        return null;
    }

    //电话号码截取
    public static MobileSplitType mobileSplit(String moile) {
        if (StringUtils.isNotNull(moile) && StringUtils.isMobile(moile)) {
            return MobileSplitType.builder()
                    .mobileStart(moile.substring(0, 3))
                    .mobileMiddle(moile.substring(3, 7))
                    .mobileEnd(moile.substring(7, moile.length())).build();
        }
        throw new BusinessException("手机号码格式错误!");
    }

    //座机号码截取
    public static MobileSplitType LandlineNumberSplit(String moile) {
        if (StringUtils.isNotNull(moile)) {
            return MobileSplitType.builder()
                    .mobileStart(moile.substring(0, 4))
                    .mobileEnd(moile.substring(4, moile.length())).build();
        }
        throw new BusinessException("座机号码格式错误!");
    }
}





