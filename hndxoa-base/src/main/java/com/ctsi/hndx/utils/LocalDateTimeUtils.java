package com.ctsi.hndx.utils;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;


public class LocalDateTimeUtils {


    /**
     * 使用LocalDateTime获取两个时间点相隔几天几小时几分几秒
     *
     * @param localDateTime
     * @return
     */
    public static String getLoginDuration(LocalDateTime localDateTime) {
        //登录时间
        LocalDateTime fromDateTime = localDateTime;
        //当前时间
        LocalDateTime toDateTime = LocalDateTime.now();
        LocalDateTime tempDateTime = LocalDateTime.from(fromDateTime);
        long days = tempDateTime.until(toDateTime, ChronoUnit.DAYS);
        tempDateTime = tempDateTime.plusDays(days);
        long hours = tempDateTime.until(toDateTime, ChronoUnit.HOURS);
        tempDateTime = tempDateTime.plusHours(hours);
        long minutes = tempDateTime.until(toDateTime, ChronoUnit.MINUTES);
        tempDateTime = tempDateTime.plusMinutes(minutes);
        long seconds = tempDateTime.until(toDateTime, ChronoUnit.SECONDS);
        return hours + "小时" +
                minutes + "分" +
                seconds + "秒";
    }
}
