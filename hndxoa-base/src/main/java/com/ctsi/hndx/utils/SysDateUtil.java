package com.ctsi.hndx.utils;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.LocalDateTimeUtil;
import com.ctsi.hndx.exception.BusinessException;
import org.apache.commons.lang3.StringUtils;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 日期一些工具
 */
public class SysDateUtil {


    /**
     * 判断字符串是否对应的类型
     * @param str
     * @param formatStr
     * @return
     */
    public static boolean isValidDate(String str, String formatStr) {
        boolean convertSuccess=true;
        try {// 指定日期格式为四位年/两位月份/两位日期，注意yyyy/MM/dd区分大小写；
        SimpleDateFormat format = new SimpleDateFormat(formatStr);
            // 设置lenient为false. 否则SimpleDateFormat会比较宽松地验证日期，比如2007/02/29会被接受，并转换成2007/03/01
            format.setLenient(false);
            format.parse(str);
        } catch (Exception e) {
            // e.printStackTrace();
            // 如果throw java.text.ParseException或者NullPointerException，就说明格式不对
            convertSuccess=false;
        }
        return convertSuccess;
    }

    /**
     * 判断字符串是否对应的类型 yyyy-MM-dd
     * @param str
     * @param formatStr
     * @return
     */
    public static boolean isValidDate(String str) {
        boolean convertSuccess=true;
        try {// 指定日期格式为四位年/两位月份/两位日期，注意yyyy/MM/dd区分大小写；
            SimpleDateFormat format = new SimpleDateFormat(DatePattern.NORM_DATE_PATTERN);
            // 设置lenient为false. 否则SimpleDateFormat会比较宽松地验证日期，比如2007/02/29会被接受，并转换成2007/03/01
            format.setLenient(false);
            format.parse(str);
        } catch (Exception e) {
            // e.printStackTrace();
            // 如果throw java.text.ParseException或者NullPointerException，就说明格式不对
            convertSuccess=false;
        }
        return convertSuccess;
    }


    /**
     * 判断字符串是否对应的类型 yyyy-MM-dd HH:ss:mm
     * @param str
     * @param formatStr
     * @return
     */
    public static boolean isValidDateFullDate(String str) {
        boolean convertSuccess=true;
        try {// 指定日期格式为四位年/两位月份/两位日期，注意yyyy/MM/dd区分大小写；
            SimpleDateFormat format = new SimpleDateFormat(DatePattern.NORM_DATETIME_PATTERN);
            // 设置lenient为false. 否则SimpleDateFormat会比较宽松地验证日期，比如2007/02/29会被接受，并转换成2007/03/01
            format.setLenient(false);
            format.parse(str);
        } catch (Exception e) {
            // e.printStackTrace();
            // 如果throw java.text.ParseException或者NullPointerException，就说明格式不对
            convertSuccess=false;
        }
        return convertSuccess;
    }


    /**
     * 返回字符串对应的类型,目前是 yyyy-MM-dd HH:ss:mm 和yyyy-MM-dd
     * @param str
     * @param formatStr
     * @return
     */
    public static String  strDateTypeString (String str) {
        if (isValidDateFullDate(str)){
            return  DatePattern.NORM_DATETIME_PATTERN;
        }else if (isValidDate(str)){
            return  DatePattern.NORM_DATE_PATTERN;
        }else {
            throw new BusinessException("解析错误，只支持yyyy-MM-dd HH:ss:mm 和yyyy-MM-dd");
        }
    }


//    public static void main(String[] args) {
//        String startDate ="2021-02-02 12:00:00";
//        String endDate = "2021-02-02 13:00:00";
//        if (org.apache.commons.lang3.StringUtils.isNotEmpty(startDate) && StringUtils.isNotEmpty(endDate)){
//            if (startDate.length() != endDate.length()){
//                throw new BusinessException("开始时间和结束时间格式不一致");
//            }else{
//                String  formatStart = SysDateUtil.strDateTypeString(startDate);
//                String  formatEnd = SysDateUtil.strDateTypeString(endDate);
//                LocalDateTime startLocalDateTime = LocalDateTimeUtil.parse(startDate, formatStart);
//                LocalDateTime endLocalDateTime = LocalDateTimeUtil.parse(endDate, formatEnd);
//                Duration between = LocalDateTimeUtil.between(startLocalDateTime, endLocalDateTime);
//                if (between.toDays() < 0){
//                    throw  new BusinessException("结束时间必须大于等于开始时间");
//                }
//                String format = LocalDateTimeUtil.format(startLocalDateTime, DatePattern.NORM_DATE_PATTERN);
//                System.out.println(format);
//            }
//        }
//    }

}
