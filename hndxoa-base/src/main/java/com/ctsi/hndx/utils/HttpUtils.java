package com.ctsi.hndx.utils;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.http.Consts;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.StatusLine;
import org.apache.http.client.ClientProtocolException;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.entity.mime.HttpMultipartMode;
import org.apache.http.entity.mime.MultipartEntityBuilder;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.text.MessageFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * @Author: wubin (HttpUtils)
 * @Description
 * @Date 2023/3/29 20:38
 */
@Slf4j
public class HttpUtils {
    private static final ContentType STRING_CONTENT_TYPE = ContentType.create("text/plain", StandardCharsets.UTF_8);

    /**
     * 发送Get请求(如果https请求需要忽略证书检测，需自己实现)
     *
     * @param url
     * @param headers
     * @return
     * @throws IOException
     */
    public static Pair<StatusLine, String> sendGetRequest(String url, Map<String, String> headers) throws IOException {
        // 设置请求头
        HttpGet httpGet = new HttpGet(url);
        if (null != headers) {
            for (Map.Entry<String, String> entry : headers.entrySet()) {
                httpGet.addHeader(entry.getKey(), entry.getValue());
            }
        }
        // 发送httpGet请求
        try (CloseableHttpClient httpClient = HttpClients.createDefault();
             CloseableHttpResponse response = httpClient.execute(httpGet)) {
            HttpEntity entity = response.getEntity();
            if (null != entity) {
                String content = EntityUtils.toString(entity, StandardCharsets.UTF_8);
                // 关闭content stream
                EntityUtils.consume(entity);
                return Pair.of(response.getStatusLine(), content);
            }
            throw new IOException("No response");
        } catch (ClientProtocolException e) {
            throw e;
        } catch (IOException e) {
            throw e;
        }
    }

    /**
     * 发送Post请求(如果https请求需要忽略证书检测，需自己实现)
     *
     * @param url
     * @param headers
     * @return
     * @throws IOException
     */
    public static Pair<StatusLine, String> sendPostRequest(String url, Map<String, String> headers,
                                                           Map<String, Object> bodyParams) throws IOException {
        // 设置请求头
        HttpPost httpPost = new HttpPost(url);
        if (null != headers) {
            for (Map.Entry<String, String> entry : headers.entrySet()) {
                httpPost.addHeader(entry.getKey(), entry.getValue());
            }
        }
        // 发送httpPost请求
        try {
            CloseableHttpClient httpClient = HttpClients.createDefault();
            if (null == bodyParams || 0 == bodyParams.size()) {
                // no body params
            } else {
                StringEntity httpEntity = new StringEntity(new JSONObject(bodyParams).toJSONString(),
                        ContentType.APPLICATION_JSON);
                httpPost.setEntity(httpEntity);
            }
            CloseableHttpResponse response = httpClient.execute(httpPost);
            HttpEntity entity = response.getEntity();
            if (null != entity) {
                String content = EntityUtils.toString(entity, StandardCharsets.UTF_8);
                // 关闭content stream
                EntityUtils.consume(entity);
                return Pair.of(response.getStatusLine(), content);
            }
            throw new IOException("No response");
        } catch (ClientProtocolException e) {
            throw e;
        } catch (IOException e) {
            throw e;
        }
    }

    /**
     * 发送Get请求(如果https请求需要忽略证书检测，需自己实现)
     *
     * 下载文件
     * @param url
     * @param headers
     * @return
     * @throws IOException
     */
    public static byte[] httpDownLoad(String url, Map<String, String> headers) throws IOException {
        // 设置请求头
        HttpGet httpGet = new HttpGet(url);
        if (null != headers) {
            for (Map.Entry<String, String> entry : headers.entrySet()) {
                httpGet.addHeader(entry.getKey(), entry.getValue());
            }
        }
        // 发送httpGet请求
        CloseableHttpClient httpClient = HttpClients.createDefault();
        InputStream in = null;
        BufferedInputStream bufferedInputStream = null;
        ByteArrayOutputStream outputStream = null;
        try {
            HttpResponse response = httpClient.execute(httpGet);
            HttpEntity entity = response.getEntity();
            in = entity.getContent();
            int len = -1;
            byte[] buffer = new byte[1024];
            bufferedInputStream = new BufferedInputStream(in);
            outputStream = new ByteArrayOutputStream();
            while ((len = bufferedInputStream.read(buffer)) != -1) {
                outputStream.write(buffer, 0, len);
            }
            return outputStream.toByteArray();
        } catch (ClientProtocolException e) {
            throw e;
        } catch (IOException e) {
            throw e;
        } finally {
            try {
                httpClient.close();
                if (in != null) {
                    in.close();
                }
                if (bufferedInputStream != null) {
                    bufferedInputStream.close();
                }
                if (outputStream != null) {
                    outputStream.close();
                }

            } catch (IOException e) {
            }
        }
    }

    /**
     * 发送Post请求参数采用FormData格式请求
     *
     * @param url
     * @param headers
     * @return
     * @throws IOException
     */
    public static Pair<StatusLine, String> postFormDataRequest(String url, Map<String, String> headers,
                                                             Map<String, Object> bodyParams) throws IOException {
        // 设置请求头
        HttpPost httpPost = new HttpPost(url);
        if (MapUtils.isNotEmpty(headers)) {
            for (Map.Entry<String, String> entry : headers.entrySet()) {
                String key = entry.getKey();
                String value = entry.getValue();
                httpPost.setHeader(key, value);
            }
        }
        // 设置请求参数
        if (MapUtils.isNotEmpty(bodyParams)) {
            // 使用 MultipartEntityBuilder 构造请求体
            MultipartEntityBuilder builder = MultipartEntityBuilder.create();
            //设置浏览器兼容模式
            builder.setMode(HttpMultipartMode.BROWSER_COMPATIBLE);
            //设置请求的编码格式
            builder.setCharset(Consts.UTF_8);
            // 设置 Content-Type
            builder.setContentType(ContentType.MULTIPART_FORM_DATA);
            for (Map.Entry<String, Object> entry : bodyParams.entrySet()) {
                String key = entry.getKey();
                Object value = bodyParams.get(key);
                // 添加请求参数
                addMultipartBody(builder, key, value);
            }
            HttpEntity entity = builder.build();
            // 将构造好的 entity 设置到 HttpPost 对象中
            httpPost.setEntity(entity);

            // 发送httpPost请求
            try {
                CloseableHttpClient httpClient = HttpClients.createDefault();
                CloseableHttpResponse response = httpClient.execute(httpPost);
                HttpEntity httpEntity = response.getEntity();
                if (null != httpEntity) {
                    String content = EntityUtils.toString(httpEntity, StandardCharsets.UTF_8);
                    // 关闭content stream
                    EntityUtils.consume(httpEntity);
                    return Pair.of(response.getStatusLine(), content);
                }
                throw new IOException("No response");
            } catch (ClientProtocolException e) {
                throw e;
            } catch (IOException e) {
                throw e;
            }
        }
        return null;
    }

    private static void addMultipartBody(MultipartEntityBuilder builder, String key, Object value){
        if (value == null) {
            return;
        }
        // MultipartFile 是 spring mvc 接收到的文件。
        if (value instanceof MultipartFile) {
            MultipartFile file = (MultipartFile) value;
            try {
                builder.addBinaryBody(key, file.getInputStream(), ContentType.MULTIPART_FORM_DATA, file.getOriginalFilename());
            } catch (IOException e) {
                log.info(MessageFormat.format("获取文件错误： {0}", e));
            }
        } else if (value instanceof File) {
            File file = (File) value;
            builder.addBinaryBody(key, file, ContentType.MULTIPART_FORM_DATA, file.getName());
        } else if (value instanceof List) {
            // 列表形式的参数，要一个一个 add
            List<?> list = (List<?>) value;
            for (Object o : list) {
                addMultipartBody(builder, key, o);
            }
        } else if (value instanceof Date) {
            // 日期格式的参数，使用约定的格式
            builder.addTextBody(key, new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(value));
        } else {
            // 使用 UTF_8 编码的 ContentType，否则可能会有中文乱码问题
            builder.addTextBody(key, value.toString(), STRING_CONTENT_TYPE);
        }
    }

}
