package com.ctsi.hndx.utils;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.util.Base64;

/**
 * @Author: lizuolang (cpyfbAdmin)
 * @Description AES、MD5加密工具类
 * @Date 2022/05/17 16:46
 */
public class UserAESUtil {

    private final static Integer AES_KEY_LENGTH = 16;

    private final static Integer AES_IV_LENGTH = 16;

    private final static String AEC_CBC_PKCS5Padding =  "AES/CBC/PKCS5Padding";

    private final static String ALGORITHM = "AES";

    private static final String CHARACTERS = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";

    public static final String AES_KEY;

    public static final String INITIAL_VECTOR;

    private static final String MD5_SALT;

    static {
        AES_KEY = "FXiJPu13yqz32XY5";
        INITIAL_VECTOR = "hpK3EuQkTIiyRewY";
        MD5_SALT = "DWyRtccFGGVItg78";
    }

    public static String encryptAES(String plainText) {
        return encryptAES(plainText, AES_KEY, INITIAL_VECTOR);
    }

    public static String decryptAES(String encryptedData) {
        return decryptAES(encryptedData, AES_KEY, INITIAL_VECTOR);
    }

    public static String encryptAES(String plainText, String key, String iv) {
        try {
            IvParameterSpec ivParameterSpec = new IvParameterSpec(iv.getBytes(StandardCharsets.UTF_8));
            SecretKeySpec secretKey = new SecretKeySpec(key.getBytes(StandardCharsets.UTF_8), ALGORITHM);
            Cipher cipher = Cipher.getInstance(AEC_CBC_PKCS5Padding);
            cipher.init(Cipher.ENCRYPT_MODE, secretKey, ivParameterSpec);
            byte[] encryptedBytes = cipher.doFinal(plainText.getBytes());
            return Base64.getEncoder().encodeToString(encryptedBytes);
        } catch (Exception e) {
            System.out.println("encryptAES加密失败，导致的原因是：" + e.getMessage());
        }
        return plainText;
    }

    public static String decryptAES(String encryptedData, String key, String iv) {
        try {
            byte[] encryptedBytes = Base64.getDecoder().decode(encryptedData);
            Cipher cipher = Cipher.getInstance(AEC_CBC_PKCS5Padding);
            SecretKeySpec secretKey = new SecretKeySpec(key.getBytes(StandardCharsets.UTF_8), ALGORITHM);
            IvParameterSpec ivParameterSpec = new IvParameterSpec(iv.getBytes(StandardCharsets.UTF_8));
            cipher.init(Cipher.DECRYPT_MODE, secretKey, ivParameterSpec);
            byte[] decryptedBytes = cipher.doFinal(encryptedBytes);
            return new String(decryptedBytes);
        } catch (Exception e) {
            System.out.println("decryptAES解密失败，导致的原因是：" + e.getMessage());
        }
        return encryptedData;
    }

    public static String generateRandomKey() {
        return generateRandomString(AES_KEY_LENGTH);
    }

    public static String generateRandomIV() {
        return generateRandomString(AES_IV_LENGTH);
    }

    public static String generateRandomString(int length) {
        SecureRandom random = new SecureRandom();
        StringBuilder sb = new StringBuilder(length);
        for (int i = 0; i < length; i++) {
            int randomIndex = random.nextInt(CHARACTERS.length());
            char randomChar = CHARACTERS.charAt(randomIndex);
            sb.append(randomChar);
        }
        return sb.toString();
    }

    /**
     * MD5加盐
     * 使用MD5算法和一个随机盐进行哈希运算
     * @param plainText
     * @return
     */
    public static String md5WithSalt(String plainText) {
        return md5WithSalt(plainText, MD5_SALT);
    }

    /**
     * MD5加盐
     * 使用MD5算法和一个随机盐进行哈希运算
     * @param plainText
     * @param salt
     * @return
     */
    public static String md5WithSalt(String plainText, String salt) {
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            md.update((plainText + salt).getBytes());
            byte[] digest = md.digest();
            // 将字节数组转换为十六进制字符串
            StringBuilder hexString = new StringBuilder();
            for (byte b : digest) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) {
                    hexString.append('0');
                }
                hexString.append(hex);
            }
            return hexString.toString();
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
            return plainText;
        }
    }

}
