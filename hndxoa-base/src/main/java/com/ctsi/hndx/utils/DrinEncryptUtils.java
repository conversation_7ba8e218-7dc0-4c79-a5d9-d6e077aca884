package com.ctsi.hndx.utils;

import com.ctsi.hndx.exception.BusinessException;

import java.util.Base64;

import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;

/**
 * @Description: 加解密工具类
 * @Auther: 曾力军
 */
public class DrinEncryptUtils {


    /**
     * 密钥
     */
    private static final String KEY = "zysofthnzx202002";
    private static final String ALGORITHMSTR = "AES/ECB/PKCS5Padding";
    private static final String ENCODING = "utf-8";

    public static String aesEncrypt(String content) {
        try {
            return base64Encode(aesEncryptToBytes(content, KEY));
        } catch (Exception e) {
            e.printStackTrace();
            throw new BusinessException("加密发生错误!");
        }
    }

    public static String aesDecrypt(String encryptStr) {
        try {
            return aesDecryptByBytes(base64Decode(encryptStr), KEY);
        } catch (Exception e) {
            e.printStackTrace();
            throw new BusinessException("解密发生错误!");
        }
    }

    /**
     * AES 加密
     *
     * @param content
     * @param encryptKey
     * @return
     * @throws Exception
     */
    private static byte[] aesEncryptToBytes(String content, String encryptKey) throws Exception {

        Cipher cipher = Cipher.getInstance(ALGORITHMSTR);
        cipher.init(Cipher.ENCRYPT_MODE, new SecretKeySpec(encryptKey.getBytes(ENCODING), "AES"));

        return cipher.doFinal(content.getBytes(ENCODING));
    }

    /**
     * AES 解密
     *
     * @param encryptBytes
     * @param decryptKey
     * @return
     * @throws Exception
     */
    private static String aesDecryptByBytes(byte[] encryptBytes, String decryptKey) throws Exception {

        Cipher cipher = Cipher.getInstance(ALGORITHMSTR);
        cipher.init(Cipher.DECRYPT_MODE, new SecretKeySpec(decryptKey.getBytes(ENCODING), "AES"));
        byte[] decryptBytes = cipher.doFinal(encryptBytes);

        return new String(decryptBytes, ENCODING);
    }

    private static String base64Encode(byte[] bytes) {
        return Base64.getEncoder().encodeToString(bytes);
    }

    private static byte[] base64Decode(String base64Code) throws Exception {
        return Base64.getDecoder().decode(base64Code);
    }

}