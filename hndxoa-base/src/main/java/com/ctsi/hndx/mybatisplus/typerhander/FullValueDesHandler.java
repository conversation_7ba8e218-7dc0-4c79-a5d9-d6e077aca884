package com.ctsi.hndx.mybatisplus.typerhander;

import com.ctsi.hndx.encryption.KeyCenterUtils;
import com.ctsi.hndx.utils.StringUtils;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Arrays;
import java.util.stream.Collectors;

/**
 * @ProjectName: hndxoa
 * @Package: com.ctsi.hndx.mybatisplus.typerhander
 * @ClassName: DivisionDesHandler
 * @Author: json
 * @Description: 全值加解密 例:张三 = 5byg5LiJ
 * @Date: 2022/12/1 10:09
 * @Version: 1.0
 */
@Service
public class FullValueDesHandler<T> extends BaseTypeHandler<T> {

    private final Logger log = LoggerFactory.getLogger(FullValueDesHandler.class);

    public FullValueDesHandler() {
    }

    /**
     * 加密
     *
     * @param ps
     * @param i
     * @param parameter
     * @param jdbcType
     * @throws SQLException
     */
    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, Object parameter, JdbcType jdbcType) throws SQLException {
        ps.setString(i, KeyCenterUtils.encrypt((String) parameter));
    }

    /**
     * 解密
     *
     * @param rs
     * @param columnName
     * @return
     * @throws SQLException
     */
    @Override
    public T getNullableResult(ResultSet rs, String columnName) throws SQLException {
        String columnValue = rs.getString(columnName);
        //有一些可能是空字符
        return StringUtils.isNull(columnValue) ? (T) columnValue : (T) Arrays.asList(columnValue.split(",")).stream().map(v -> KeyCenterUtils.decrypt(v)).collect(Collectors.joining(""));
        // return StringUtils.isNull(columnValue) ? (T) columnValue : (T) KeyCenterUtils.decrypt(columnValue);
    }

    @Override
    public T getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        String columnValue = rs.getString(columnIndex);
        return StringUtils.isNull(columnValue) ? (T) columnValue : (T) Arrays.asList(columnValue.split(",")).stream().map(v -> KeyCenterUtils.decrypt(v)).collect(Collectors.joining(""));
        // return StringUtils.isNull(columnValue) ? (T) columnValue : (T) KeyCenterUtils.decrypt(columnValue);
    }

    @Override
    public T getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        String columnValue = cs.getString(columnIndex);
        return StringUtils.isNull(columnValue) ? (T) columnValue : (T) Arrays.asList(columnValue.split(",")).stream().map(v -> KeyCenterUtils.decrypt(v)).collect(Collectors.joining(""));
        // return StringUtils.isNull(columnValue) ? (T) columnValue : (T) KeyCenterUtils.decrypt(columnValue);
    }

    // public static void main(String[] args) {
    //     String str = "gdsfgsafg";
    //     List<String> collect = Arrays.asList(str.split(",")).stream().map(key -> {
    //
    //         return key;
    //     }).collect(Collectors.toList());
    //     collect.forEach(i -> System.out.println(i));
    //     String[] split = str.split(",");
    //     List<String> list = Arrays.asList(split);
    // }
}


