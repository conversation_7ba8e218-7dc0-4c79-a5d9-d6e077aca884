package com.ctsi.hndx.mybatisplus.typerhander;

import com.ctsi.hndx.constant.SysConfigConstant;
import com.ctsi.hndx.encryption.KeyCenterUtils;
import com.ctsi.hndx.tsysconfig.service.ISysConfigService;
import com.ctsi.hndx.utils.StringUtils;
import com.ctsi.hndx.westone.WestoneEncryptService;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Arrays;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <p>
 * 卫士通服务器数据密码机API接口：字符串分割加密和解密
 * 适用于原来Base64加密的字段
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-13
 *
 */
@Service
public class WestoneStrDivisionFromBase64ValueDesHandler<T> extends BaseTypeHandler<T> {

    private final Logger log = LoggerFactory.getLogger(WestoneStrDivisionFromBase64ValueDesHandler.class);

    @Autowired
    private ISysConfigService sysConfigService;

    @Autowired
    private WestoneEncryptService westoneEncryptService;

    /**
     * 加密
     *
     * @param ps
     * @param i
     * @param parameter
     * @param jdbcType
     * @throws SQLException
     */
    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, Object parameter, JdbcType jdbcType) throws SQLException {
        if (!Objects.isNull(parameter)) {
            // TODO 根据系统动态参数配置，判断是否使用密码机
            boolean isCipherMachine = this.sysConfigService.getSysConfigBoolValueByCode(SysConfigConstant.WESTONE_CIPHER_MACHINE_DEPLOY);
            if (isCipherMachine) {
                String encryptStr = westoneEncryptService.divisionEncryptRealNameWithFPE((String) parameter);
                ps.setString(i, encryptStr);
            } else {
                String encryptStr = KeyCenterUtils.division((String) parameter);
                ps.setString(i, encryptStr);
            }
        }
    }

    /**
     * 解密
     *
     * @param rs
     * @param columnName
     * @return
     * @throws SQLException
     */
    @Override
    public T getNullableResult(ResultSet rs, String columnName) throws SQLException {
        String columnValue = rs.getString(columnName);
        // TODO 根据系统动态参数配置，判断是否使用密码机
        boolean isCipherMachine = this.sysConfigService.getSysConfigBoolValueByCode(SysConfigConstant.WESTONE_CIPHER_MACHINE_DEPLOY);
        if (isCipherMachine) {
            return (T) westoneEncryptService.divisionDecryptRealNameWithFPE(columnValue);
        } else {
            return StringUtils.isNull(columnValue) ? (T) columnValue : (T) Arrays.asList(columnValue.split(",")).stream().map(v -> KeyCenterUtils.decrypt(v)).collect(Collectors.joining(""));
        }
    }

    @Override
    public T getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        String columnValue = rs.getString(columnIndex);
        // TODO 根据系统动态参数配置，判断是否使用密码机
        boolean isCipherMachine = this.sysConfigService.getSysConfigBoolValueByCode(SysConfigConstant.WESTONE_CIPHER_MACHINE_DEPLOY);
        if (isCipherMachine) {
            return (T) westoneEncryptService.divisionDecryptRealNameWithFPE(columnValue);
        } else {
            return StringUtils.isNull(columnValue) ? (T) columnValue : (T) Arrays.asList(columnValue.split(",")).stream().map(v -> KeyCenterUtils.decrypt(v)).collect(Collectors.joining(""));
        }
    }

    @Override
    public T getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        String columnValue = cs.getString(columnIndex);
        // TODO 根据系统动态参数配置，判断是否使用密码机
        boolean isCipherMachine = this.sysConfigService.getSysConfigBoolValueByCode(SysConfigConstant.WESTONE_CIPHER_MACHINE_DEPLOY);
        if (isCipherMachine) {
            return (T) westoneEncryptService.divisionDecryptRealNameWithFPE(columnValue);
        } else {
            return StringUtils.isNull(columnValue) ? (T) columnValue : (T) Arrays.asList(columnValue.split(",")).stream().map(v -> KeyCenterUtils.decrypt(v)).collect(Collectors.joining(""));
        }
    }

}


