package com.ctsi.hndx.mybatisplus.sqlInjector;

import com.baomidou.mybatisplus.core.injector.AbstractMethod;
import com.baomidou.mybatisplus.core.injector.DefaultSqlInjector;
import com.baomidou.mybatisplus.core.injector.methods.SelectObjs;

import java.util.List;

public class MySqlInjector extends DefaultSqlInjector {
    @Override
    public List<AbstractMethod> getMethodList(Class<?> mapperClass) {
        List<AbstractMethod> list = super.getMethodList(mapperClass);
        Class[] classSuperclass = mapperClass.getInterfaces();
        if (classSuperclass != null && classSuperclass.length > 0 && "MybatisBaseMapper".equals(mapperClass.getInterfaces()[0].getSimpleName())) {
            list.add(new SelectCountOnlyAddTenantId());
            list.add(new SelectListOnlyAddTenantId());
            list.add(new SelectMapsOnlyAddTenantId());
            list.add(new SelectMapsPageOnlyAddTenantId());
            list.add(new SelectObjsOnlyAddTenantId());
            list.add(new SelectOneOnlyAddTenantId());
            list.add(new SelectPageOnlyAddTenantId());

            list.add(new SelectCountNoAdd());
            list.add(new SelectListNoAdd());
            list.add(new SelectMapsNoAdd());
            list.add(new SelectMapsPageNoAdd());
            list.add(new SelectObjsNoAdd());
            list.add(new SelectOneNoAdd());
            list.add(new SelectPageNoAdd());

            list.add(new UpdateTenantId());
        }

        return list;
    }
}
