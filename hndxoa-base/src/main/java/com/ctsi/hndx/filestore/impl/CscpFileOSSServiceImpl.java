/*
package com.ctsi.hndx.filestore.impl;


import com.amazonaws.AmazonServiceException;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.model.GetObjectRequest;
import com.amazonaws.services.s3.model.ObjectMetadata;
import com.amazonaws.services.s3.model.S3Object;
import com.amazonaws.services.s3.model.S3ObjectInputStream;
import com.ctsi.hndx.exception.BusinessException;
import com.ctsi.hndx.filestore.CscpFileStoreService;
import com.ctsi.hndx.filestore.SysConfigInitService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.net.URI;
import java.net.URISyntaxException;
import java.net.URL;
import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.Objects;

@Slf4j
public class CscpFileOSSServiceImpl implements CscpFileStoreService {

    @Autowired
    private SysConfigInitService sysConfigInitService;

    private static AmazonS3 s3Client;
    private String bucketName;
    private Integer expireDay;

    private void getS3Client(){
        s3Client = sysConfigInitService.getS3Client();
        bucketName = sysConfigInitService.getOSSBucketName();
        expireDay = sysConfigInitService.getExpireDay();
    }
    private AmazonS3 getOSSClient(){
        //获取s3Client，若为空，初始化后再获取，再为空，报错
        this.getS3Client();
        if (ObjectUtils.isEmpty(s3Client)){
            sysConfigInitService.ossInit();
            this.getS3Client();
            if (ObjectUtils.isEmpty(s3Client)){
                throw new BusinessException("s3Client不能为空");
            }
        }
        return s3Client;
    }

    */
/**
     * 判断文件对象是否存储 a/b/c.doc
     *
     * @param ossFilePath
     * @return
     *//*

    @Override
    public boolean existsFile(String ossFilePath) {
        S3Object o = null;
        try {
            AmazonS3 s3 = getOSSClient();
            bucketName = sysConfigInitService.getOSSBucketName();
            GetObjectRequest getObjectRequest = new GetObjectRequest(bucketName, ossFilePath);
            o = s3.getObject(getObjectRequest);
        } catch (AmazonServiceException e) {
            log.error("[天翼云OSS]>>>> 判断文件是否存在, 异常：" + ossFilePath + "=>", e.getMessage());
            return false;
        }
        if (Objects.isNull(o)) {
            return false;
        }
        return true;
    }

    @Override
    public boolean uploadFile(String filePath, MultipartFile mf) {
        try {
            AmazonS3 s3 = getOSSClient();
            ObjectMetadata meta = new ObjectMetadata();
            meta.setContentLength(mf.getSize());
            bucketName = sysConfigInitService.getOSSBucketName();
            s3.putObject(bucketName, filePath, mf.getInputStream(), meta);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return true;
    }

    @Override
    public boolean uploadFile(String filePath, byte[] data) {
        ByteArrayInputStream inStream = new ByteArrayInputStream(data);
        try {
            AmazonS3 s3 = getOSSClient();
            ObjectMetadata meta = new ObjectMetadata();
            meta.setContentLength(inStream.available());
            bucketName = sysConfigInitService.getOSSBucketName();
            s3.putObject(bucketName, filePath, inStream, meta);
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (inStream != null){
                try {
                    inStream.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return true;
    }

    @Override
    public byte[] downloadFile(String filePath) {
        try {
            bucketName = sysConfigInitService.getOSSBucketName();
            GetObjectRequest getObjectRequest = new GetObjectRequest(bucketName, filePath);
            S3Object o = getOSSClient().getObject(getObjectRequest);
            S3ObjectInputStream s3is = o.getObjectContent();
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            byte[] read_buf = new byte[1024];
            int read_len = 0;
            while ((read_len = s3is.read(read_buf)) > 0) {
                outputStream.write(read_buf, 0, read_len);
            }
            s3is.close();
            outputStream.close();
            return outputStream.toByteArray();
        }
        catch (AmazonServiceException e) {
            log.error(e.getErrorMessage());
        } catch (FileNotFoundException e) {
            log.error(e.getMessage());
        } catch (IOException e) {
            log.error(e.getMessage());
        }
        return null;
    }

    @Override
    public boolean downloadFile(String remoteFilePath, String localFilePath) {
        try {
            AmazonS3 s3 = getOSSClient();
            bucketName = sysConfigInitService.getOSSBucketName();
            GetObjectRequest getObjectRequest = new GetObjectRequest(bucketName, remoteFilePath);
            S3Object o = s3.getObject(getObjectRequest);
            S3ObjectInputStream s3is = o.getObjectContent();
            FileOutputStream fos = new FileOutputStream(new File(localFilePath));
            byte[] read_buf = new byte[1024];
            int read_len = 0;
            while ((read_len = s3is.read(read_buf)) > 0) {
                fos.write(read_buf, 0, read_len);
            }
            s3is.close();
            fos.close();
        }
        catch (AmazonServiceException e) {
            log.error(e.getErrorMessage());
        } catch (FileNotFoundException e) {
            log.error(e.getMessage());
        } catch (IOException e) {
            log.error(e.getMessage());
        }
        return true;
    }

    @Override
    public boolean deleteFile(String filePath){
        try {
            AmazonS3 s3 = getOSSClient();
            bucketName = sysConfigInitService.getOSSBucketName();
            s3.deleteObject(bucketName, filePath);
        } catch (AmazonServiceException e) {
            log.error(e.getErrorMessage());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return true;
    }

    @Override
    public String getFileURL(String filePath) {
        //获取expireDay与bucketName
        this.getOSSClient();
        Date date = new Date();
        Calendar calendar = new GregorianCalendar();
        calendar.setTime(date);
        calendar.add(Calendar.DATE, expireDay);
        date = calendar.getTime();
        //生成共享链接
        bucketName = sysConfigInitService.getOSSBucketName();
        URL url = getOSSClient().generatePresignedUrl(bucketName, filePath, date);
        if (!Objects.isNull(url)) {
            try {
                URI uri = url.toURI();
                return uri.toString();
            } catch (URISyntaxException e) {
                e.printStackTrace();
            }
        }
        return null;
    }


    */
/**
     * @param oldFilePath
     * @param newFilePath
     * @return
     *//*

    @Override
    public void fileCopy(String oldFilePath, String newFilePath) {
        try {
            AmazonS3 s3 = getOSSClient();
            bucketName = sysConfigInitService.getOSSBucketName();
            s3.copyObject(bucketName, oldFilePath, bucketName, newFilePath);
        } catch (AmazonServiceException e) {
            log.error(e.getErrorMessage());
        }

    }

    */
/**
     * 获取文件大小（内容长度）
     *
     * @param ossFilePath : 远程目录 + 文件名 + 文件后缀
     * @return
     *//*

    @Override
    public Long getObjectSize(String ossFilePath) {
        Long size = null;
        try {
            AmazonS3 s3 = getOSSClient();
            bucketName = sysConfigInitService.getOSSBucketName();
            size = s3.getObjectMetadata(bucketName, ossFilePath).getContentLength();
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return size;
    }
}
*/
