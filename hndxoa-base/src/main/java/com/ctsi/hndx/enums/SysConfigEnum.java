package com.ctsi.hndx.enums;

/**
 * @Classname SysConfigConstant
 * @Description
 * @Date 2021/11/23/0023 10:29
 * <AUTHOR>
 */
public enum SysConfigEnum {

    /**
     * 文件存储类型：1 minIO  2 oss  3 本地存储
     */
    FILE_STORAGE_TYPE("file:storageType"),

    /**
     * minio配置的endpoint
     */
    MINIO_ENDPOINT("minio:endpoint"),

    /**
     * minio配置的accessKey
     */
    MINIO_ACCESS_KEY("minio:accessKey"),

    /**
     * minio配置的secretKey
     */
    MINIO_SECRET_KEY("minio:secretKey"),

    /**
     * minio配置的bucketName
     */
   MINIO_BUCKET_NAME("minio:bucketName"),

    /**
     * oss配置的accessKey
     */
   OSS_ACCESS_KEY("oss:accessKey"),

    /**
     * oss配置的secretKey
     */
    OSS_SECRET_KEY("oss:secretKey"),

    /**
     * oss配置的endpoint
     */
    OSS_ENDPOINT("oss:endPoint"),

    /**
     * oss配置的bucketName
     */
    OSS_BUCKET_NAME("oss:bucketName"),

    /**
     * oss配置的expireDay
     */
    OSS_EXPIRE_DAY("oss:expireDay"),

    /**
     * 用户默认登录密码
     */
   DEFAULT_PASSWORD("login:defaultPassword"),

    /**
     * SMS用户名
     */
  SMS_SI_YUAN_USER_NAME("sms:siYuan:userName"),

    /**
     * SMS用户密码
     */
  SMS_SI_YUAN_PASSWORD("sms:siYuan:password"),

    /**
     * SMS url
     */
  SMS_SI_YUAN_URL("sms:siYuan:url"),



    /**
     * 给wps的请求地址，一般是后端地址
     */
   WEB_API_URL("web:api:url");

    SysConfigEnum(String value) {
        this.value = value;
    }

    private String value;


    public String getValue() {
        return value;
    }
}
