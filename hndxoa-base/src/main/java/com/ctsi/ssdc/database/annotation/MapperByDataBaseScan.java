package com.ctsi.ssdc.database.annotation;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

import org.springframework.context.annotation.Import;

import com.ctsi.ssdc.database.register.MapperByDatabaseScannerRegistrar;

/**
 * InjectByDataBaseType注解扫描类（参照@MapperScan）
 * 
 * <AUTHOR>
 *
 */
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.TYPE)
@Documented
@Import(MapperByDatabaseScannerRegistrar.class)
public @interface MapperByDataBaseScan {

	String[] basePackages() default {};

}
