package com.ctsi.ssdc.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeansException;
import org.springframework.boot.web.context.WebServerInitializedEvent;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.context.ApplicationListener;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

@Component
@Order(Ordered.HIGHEST_PRECEDENCE)
public class SpringUtil implements ApplicationContextAware, ApplicationListener<WebServerInitializedEvent> {

    private final Logger log = LoggerFactory.getLogger(SpringUtil.class);

    private final static String URL = "http://localhost:";

    private static int serverPort;

    private static ApplicationContext applicationContext;   

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {

        if(SpringUtil.applicationContext == null) {

            SpringUtil.applicationContext = applicationContext;

        }

        log.info("ApplicationContext配置成功,applicationContext对象："+SpringUtil.applicationContext);

    }

    @Override
    public void onApplicationEvent(WebServerInitializedEvent event) {
        serverPort = event.getWebServer().getPort();
    }



    public static ApplicationContext getApplicationContext() {

        return applicationContext;

    }

    public static Object getBean(String name) {

        return getApplicationContext().getBean(name);

    }

    public static <T> T getBean(Class<T> clazz) {

        return getApplicationContext().getBean(clazz);

    }

    public static <T> T getBean(String name,Class<T> clazz) {

        return getApplicationContext().getBean(name,clazz);

    }

    public static String[] getBeanNames() {
    	return getApplicationContext().getBeanDefinitionNames();
    }

    public static int getServerPort() {
        return serverPort;
    }


    /**
     * 获取本地请求路径我i  http://localhost:9003
     * @return
     */
    public static String getLocalUrlPort() {
        return URL + serverPort;
    }
}