package com.ctsi.ssdc.config;

public interface CtsiDefaults {

    interface Async {

        int corePoolSize = 2;
        int maxPoolSize = 5;
        int queueCapacity = 10000;
    }
    
    interface Swagger {

        String title = "Ctsi Application API";
        String description = "Ctsi API documentation";
        String version = "1.0.0";
        String termsOfServiceUrl = null;
        String contactName = null;
        String contactUrl = null;
        String contactEmail = null;
        String license = null;
        String licenseUrl = null;
        String defaultIncludePattern = "/api/.*";
        String host = null;
        String[] protocols = {};
    }

    interface Metrics {

        interface Jmx {

            boolean enabled = true;
        }

        interface Logs {

            boolean enabled = false;
            long reportFrequency = 60;

        }
    }

    interface Security {

        interface ClientAuthorization {

            String accessTokenUri = null;
            String tokenServiceId = null;
            String clientId = null;
            String clientSecret = null;
        }

        interface Authentication {

            interface Jwt {

                String secret = null;
                long tokenValidityInSeconds = 1800; // 0.5 hour
                long tokenValidityInSecondsForRememberMe = 2592000; // 30 hours;
            }
        }

        interface RememberMe {

            String key = null;
        }
    }

}
