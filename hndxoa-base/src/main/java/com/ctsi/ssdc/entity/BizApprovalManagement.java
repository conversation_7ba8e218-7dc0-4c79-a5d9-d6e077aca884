package com.ctsi.ssdc.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.ctsi.hndx.common.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

/**
 * <p>
 * 呈批件
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-27
 */
@Getter
@Setter
@TableName("biz_approval_management")
@ApiModel(value = "BizApprovalManagement对象", description = "呈批件")
public class BizApprovalManagement extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("文号")
    private String documentNo;

    @ApiModelProperty("缓急")
    private String urgency;

    @ApiModelProperty("正文")
    private String document;

    @ApiModelProperty("附件")
    private String annex;

    @ApiModelProperty("标题及其内容")
    private String title;

    @ApiModelProperty("状态")
    private String status;

    @ApiModelProperty("核稿人名称")
    private String reviewerName;

    @ApiModelProperty("流程实例")
    private Long processInstanceId;

    @ApiModelProperty("流程状态")
    private Integer bpmStatus;

    @ApiModelProperty("拟稿人手机号码")
    private String mobile;

    @ApiModelProperty("申报时间")
    private String declareTime;

    @ApiModelProperty("创建人及其手机号码")
    private String createNameMobile;

    @ApiModelProperty("创建人单位及其部门")
    private String companyDepartmentName;

    @ApiModelProperty("备注")
    private String remarks;

    @ApiModelProperty("信息编号")
    private String number;

    @ApiModelProperty("来文单位")
    private String incomingCompany;

    @ApiModelProperty("来文日期")
    private String incomingTime;

    @ApiModelProperty("内容提要")
    private String contentSummary;

    @ApiModelProperty("经办人")
    private String agentPeople;

    @ApiModelProperty("科室部门负责人")
    private String departmentLeader;

    @ApiModelProperty("拟办意见")
    private String proposedOpinion;

    @ApiModelProperty("公文密级")
    private String secret;

    @ApiModelProperty("公文密级名称")
    private String secretName;

    private LocalDateTime ycStarttime;

    @ApiModelProperty("处理单JSON")
    private String formJson;

    @ApiModelProperty("事由")
    private String reason;

    @ApiModelProperty("定密依据")
    private String classificationBasis;

    @ApiModelProperty("密级code")
    private String durationClassification;

    @ApiModelProperty("密级名称")
    private String durationClassificationName;

    private String urgencyName;

    @ApiModelProperty("是否共享  0-不是 1-是")
    private String share;

    @ApiModelProperty("是否共享的名称")
    private String shareName;

    @ApiModelProperty("收文类型")
    private String receiveDocumentType;

    @ApiModelProperty("收文类型名称")
    private String receiveDocumentTypeName;

    @ApiModelProperty("电报")
    private String telegraph;

    private String telegraphName;

    @ApiModelProperty("1:允许下载打印 0 是禁止")
    private String isPrint;

    @ApiModelProperty("1:允许下载打印 0 是禁止_中文")
    private String isPrintName;

    @ApiModelProperty("修复文号")
    private String changeDocumentNo;

    @ApiModelProperty("0 非直报秘书长  1 直报秘书长(不含秘书)   2 直报秘书长(含秘书)")
    private String directSecretaryGeneral;

 // ----------省委通报字段-----------------------
    /**
     * 印发范围
     */
    private String scopePublication;
    @ApiModelProperty(value = "处室内部文号")
    private String innerDocNumber;

    @ApiModelProperty(value = "来文/发文字号")
    private String comePostNumber;

    @ApiModelProperty(value = "序号")
    private String serialNumber;

    @ApiModelProperty(value = "类别")
    private String typeStr;

    @ApiModelProperty(value = "审核")
    private String auditStr;
    // ----------省委通报字段-----------------------


    // ----------电报相关字段-----------------------

    // docWordNumber 电报号
    private String docWordNumber;

    // 打印份数
    private Integer printCount;
    // ----------电报相关字段-----------------------
}
