package com.ctsi.ssdc.security;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @Classname CscpUserNumber
 * @Description
 * @Date 2022/1/7 17:02
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("本单位在线人数")
public class CscpUserNumberDTO implements Serializable {

    private static final long serialVersionUID = 3169182298684310412L;


    /**
     * 用户id
     */
    @ApiModelProperty(value = "用户id")
    private Long userId;

    /**
     * 用户名称
     */
    @ApiModelProperty(value = "姓名")
    private String userName;

    /**
     * 所属部门
     */
    @ApiModelProperty(value = "所属部门")
    private String orgName;

    /**
     * 登录时间
     */
    @ApiModelProperty(value = "登录时间")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime loginTime;


    /**
     * 登录时长
     */
    @ApiModelProperty(value = "登录时长")
    private String loginDuration;


    /**
     * 登录方式
     */
    @ApiModelProperty(value = "登录方式")
    private String loginMode;


    /**
     * 用户状态 type=1表示请假 type = 2 出差 type = 3 开会其他
     */
    @ApiModelProperty("type=1表示请假 type = 2 出差 type = 3 开会其他")
    private Integer userState;


    /**
     * 用户排序
     */
    @ApiModelProperty("用户排序")
    private Integer userOrderBy;


    /**
     * 部门排序
     */
    @ApiModelProperty("部门排序")
    private Integer orgOrderBy;

}
