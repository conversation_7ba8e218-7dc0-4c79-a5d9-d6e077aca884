package com.ctsi.ssdc.security;

import com.alibaba.fastjson.JSON;
import com.ctsi.hndx.constant.HeaderConstants;
import com.ctsi.hndx.enums.CallSourceEnum;
import com.ctsi.hndx.result.ResultVO;
import com.ctsi.hndx.utils.IpUtil;
import com.ctsi.hndx.utils.ServletUtils;
import com.ctsi.hndx.utils.StringUtils;
import com.ctsi.ssdc.model.SystemLogOperation;
import com.ctsi.ssdc.repository.SystemLogOperationMapper;
import com.ctsi.ssdc.security.jwt.TokenProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.core.Authentication;
import org.springframework.security.web.authentication.logout.LogoutSuccessHandler;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.net.InetAddress;
import java.net.UnknownHostException;

/**
 * 自定义退出处理类 返回成功
 *
 * <AUTHOR>
 */
@Configuration
public class LogoutSuccessHandlerImpl implements LogoutSuccessHandler {

    @Autowired
    private TokenProvider tokenProvider;

    @Autowired
    private SystemLogOperationMapper systemLogOperationMapper;

    /**
     * 退出处理
     *
     * @return
     */
    @Override
    public void onLogoutSuccess(HttpServletRequest request, HttpServletResponse response, Authentication authentication) throws UnknownHostException {
        CscpUserDetail loginUser = tokenProvider.getLoginUser(request);
        if (StringUtils.isNotNull(loginUser)) {
            // 删除用户缓存记录和在线人数记录
            tokenProvider.delLoginUser(CallSourceEnum.getLoginMode(request.getHeader(HeaderConstants.CALL_SOURCE)), loginUser);
            // 删除用户菜单缓存记录
            tokenProvider.deleteMenuCache(loginUser.getId());
            // todo: 维护退出记录
            SystemLogOperation systemLogOperation = new SystemLogOperation();
            systemLogOperation.setIp(IpUtil.getRealIp(request));
            if (response.getStatus() == 200) {
                systemLogOperation.setOperationType(0);
                systemLogOperation.setOperationResult("成功");
            } else {
                systemLogOperation.setOperationType(2);
                systemLogOperation.setOperationResult("失败");
            }
            systemLogOperation.setSystemType(1);
            systemLogOperation.setMainBody(org.apache.commons.lang3.StringUtils.isNotBlank(loginUser.getCompanyName()) ? loginUser.getUsername() + ":" + loginUser.getCompanyName() : loginUser.getUsername());
            systemLogOperation.setObjectBody("退出系统");
            systemLogOperation.setOperationSource("协同办公平台");
            systemLogOperationMapper.insert(systemLogOperation);

        }
        ServletUtils.renderString(response, JSON.toJSONString(ResultVO.success("退出成功")));
    }
}
